# ecology10



## Getting started

To make it easy for you to get started with Git<PERSON><PERSON>, here's a list of recommended next steps.

Already a pro? Just edit this README.md and make it your own. Want to make it easy? [Use the template at the bottom](#editing-this-readme)!

## Add your files

- [ ] [Create](https://docs.gitlab.com/ee/user/project/repository/web_editor.html#create-a-file) or [upload](https://docs.gitlab.com/ee/user/project/repository/web_editor.html#upload-a-file) files
- [ ] [Add files using the command line](https://docs.gitlab.com/ee/gitlab-basics/add-file.html#add-a-file-using-the-command-line) or push an existing Git repository with the following command:

```
cd existing_repo
git remote add origin https://gitlab.tools.cmic.site/xtgc/public/ztjs/mecology/ecology10.git
git branch -M main
git push -uf origin main
```

## Integrate with your tools

- [ ] [Set up project integrations](https://gitlab.tools.cmic.site/xtgc/public/ztjs/mecology/ecology10/-/settings/integrations)

## Collaborate with your team

- [ ] [Invite team members and collaborators](https://docs.gitlab.com/ee/user/project/members/)
- [ ] [Create a new merge request](https://docs.gitlab.com/ee/user/project/merge_requests/creating_merge_requests.html)
- [ ] [Automatically close issues from merge requests](https://docs.gitlab.com/ee/user/project/issues/managing_issues.html#closing-issues-automatically)
- [ ] [Enable merge request approvals](https://docs.gitlab.com/ee/user/project/merge_requests/approvals/)
- [ ] [Set auto-merge](https://docs.gitlab.com/ee/user/project/merge_requests/merge_when_pipeline_succeeds.html)

## Test and Deploy

Use the built-in continuous integration in GitLab.

- [ ] [Get started with GitLab CI/CD](https://docs.gitlab.com/ee/ci/quick_start/index.html)
- [ ] [Analyze your code for known vulnerabilities with Static Application Security Testing (SAST)](https://docs.gitlab.com/ee/user/application_security/sast/)
- [ ] [Deploy to Kubernetes, Amazon EC2, or Amazon ECS using Auto Deploy](https://docs.gitlab.com/ee/topics/autodevops/requirements.html)
- [ ] [Use pull-based deployments for improved Kubernetes management](https://docs.gitlab.com/ee/user/clusters/agent/)
- [ ] [Set up protected environments](https://docs.gitlab.com/ee/ci/environments/protected_environments.html)

***

# Editing this README

When you're ready to make this README your own, just edit this file and use the handy template below (or feel free to structure it however you want - this is just a starting point!). Thanks to [makeareadme.com](https://www.makeareadme.com/) for this template.

## Suggestions for a good README

Every project is different, so consider which of these sections apply to yours. The sections used in the template are suggestions for most open source projects. Also keep in mind that while a README can be too long and detailed, too long is better than too short. If you think your README is too long, consider utilizing another form of documentation rather than cutting out information.

## Name
Choose a self-explaining name for your project.

## Description
Let people know what your project can do specifically. Provide context and add a link to any reference visitors might be unfamiliar with. A list of Features or a Background subsection can also be added here. If there are alternatives to your project, this is a good place to list differentiating factors.

## Badges
On some READMEs, you may see small images that convey metadata, such as whether or not all the tests are passing for the project. You can use Shields to add some to your README. Many services also have instructions for adding a badge.

## Visuals
Depending on what you are making, it can be a good idea to include screenshots or even a video (you'll frequently see GIFs rather than actual videos). Tools like ttygif can help, but check out Asciinema for a more sophisticated method.

## Installation
Within a particular ecosystem, there may be a common way of installing things, such as using Yarn, NuGet, or Homebrew. However, consider the possibility that whoever is reading your README is a novice and would like more guidance. Listing specific steps helps remove ambiguity and gets people to using your project as quickly as possible. If it only runs in a specific context like a particular programming language version or operating system or has dependencies that have to be installed manually, also add a Requirements subsection.

## Usage
Use examples liberally, and show the expected output if you can. It's helpful to have inline the smallest example of usage that you can demonstrate, while providing links to more sophisticated examples if they are too long to reasonably include in the README.

## Support
Tell people where they can go to for help. It can be any combination of an issue tracker, a chat room, an email address, etc.

## Roadmap
If you have ideas for releases in the future, it is a good idea to list them in the README.

## Contributing
State if you are open to contributions and what your requirements are for accepting them.

For people who want to make changes to your project, it's helpful to have some documentation on how to get started. Perhaps there is a script that they should run or some environment variables that they need to set. Make these steps explicit. These instructions could also be useful to your future self.

You can also document commands to lint the code or run tests. These steps help to ensure high code quality and reduce the likelihood that the changes inadvertently break something. Having instructions for running tests is especially helpful if it requires external setup, such as starting a Selenium server for testing in a browser.

## Authors and acknowledgment
Show your appreciation to those who have contributed to the project.

## License
For open source projects, say how it is licensed.

## Project status
If you have run out of energy or time for your project, put a note at the top of the README saying that development has slowed down or stopped completely. Someone may choose to fork your project or volunteer to step in as a maintainer or owner, allowing your project to keep going. You can also make an explicit request for maintainers.


# 泛微Ecology二次开发规范

## 项目概述

- **技术栈**：Java Spring Boot + 泛微Ecology系统
- **主包路径**：`com.weaver.seconddev.zyhlw`
- **构建工具**：Gradle
- **核心业务**：发票管理、园区管理、法务管理、申诉处理、档案管理、拼车服务

## 项目架构规则

### 包结构强制要求

- **必须在 `com.weaver.seconddev.zyhlw` 包下进行所有开发**
- **禁止修改 `secDevLib/` 目录下的安全库文件**
- **严格遵循MVC分层结构**：action → controller → service → domain

### 新增业务模块协调规则

**新增完整业务模块时，必须同时创建以下文件结构：**

```
src/main/java/com/weaver/seconddev/zyhlw/
├── action/{模块名}/           # 业务动作类
├── controller/{模块名}/       # 控制器
├── service/impl/{模块名}/     # 服务实现
├── domain/{模块名}/          # 实体模型
```

**示例：新增"contract"模块时必须创建：**
- `action/contract/`
- `controller/contract/`
- `service/impl/contract/`
- `domain/contract/`

## 泛微Ecology特定规则

### Hook开发规范

- **Hook类必须继承泛微提供的基类**
- **登录Hook**：放置在 `hook/login/` 包下
- **工作流Hook**：放置在 `hook/workflow/` 包下
- **新增Hook时必须同时更新对应的service实现**

### EBuilder表单开发

- **EBuilder相关工具类**：使用 `util/ebuilder/` 包下的工具
- **表单数据处理**：统一使用 `controller/formdata/` 包下的控制器
- **必须配合对应的表单配置文件**

### 工作流开发约束

- **工作流相关请求处理**：使用 `domain/request/` 包下的实体
- **工作流响应处理**：使用 `domain/response/` 包下的实体
- **必须考虑审批流程的各个节点状态**

## 业务模块开发规则

### 发票管理模块（Invoice）

- **财务数据校验**：必须进行严格的数据校验和权限控制
- **相关文件协调**：
  - 修改 `domain/invoice/` 时，检查 `service/impl/invoice/`
  - 修改 `action/invoice/` 时，检查 `controller/invoice/`

### 园区管理模块（Park）

- **空间冲突检查**：园区资源分配时必须检查冲突
- **相关文件协调**：
  - 修改园区实体时，同时更新 `util/park/` 工具类
  - 园区测试文件：`src/test/java/.../park/`

### 导出功能模块（Export）

- **统一使用规则**：所有导出功能必须使用 `service/impl/export/` 模块
- **禁止重复实现**：不要在其他模块中重复实现导出逻辑
- **导出配置**：使用 `service/impl/export/constant/` 包下的常量配置

## 文件修改协调要求

### Domain实体修改

**修改任何domain实体时，必须检查以下文件：**
- 对应的converter（`util/converter/`）
- 相关的export配置（`service/impl/export/`）
- 对应的service实现
- 相关的测试文件

### Controller修改

**修改controller时，必须同步检查：**
- 对应的service接口定义
- 相关的domain实体
- 异常处理类（`exception/`）

### Service实现修改

**修改service实现时，必须同步检查：**
- 对应的controller调用
- 相关的RPC服务（`service/impl/rpc/`）
- 缓存实现（`util/cache/`）

## 技术实现约束

### 数据访问规则

- **禁止直接操作数据库**：必须通过service层进行数据访问
- **使用统一的分页工具**：`util/page/` 包下的分页工具
- **缓存使用**：统一使用 `util/cache/` 包下的缓存工具

### 安全规则

- **权限校验**：不要绕过既有的权限校验机制
- **SSO集成**：使用 `util/sso/` 包下的单点登录工具
- **加密处理**：使用 `util/encrypt/` 包下的加密工具

### 异常处理

- **统一异常处理**：使用 `exception/` 包下的自定义异常
- **必须实现 `@ControllerAdvice` 全局异常处理**
- **记录错误日志**：使用SLF4J进行错误日志记录

## HTTP接口开发规则

- **统一使用**：`util/http/` 包下的HTTP工具类
- **RESTful设计**：遵循RESTful API设计模式
- **请求响应**：使用 `domain/request/` 和 `domain/response/` 包下的标准实体

## 测试开发规范

### 测试文件位置

- **单元测试**：`src/test/java/com/weaver/seconddev/zyhlw/`
- **必须与业务包结构保持一致**
- **现有测试模块**：appeal、base、park等

### 测试要求

- **新功能必须编写对应的单元测试**
- **使用JUnit 5和Spring Boot Test**
- **Web层测试使用MockMvc**

## 禁止操作清单

### 严格禁止

- ❌ 修改 `secDevLib/` 目录下的任何文件
- ❌ 绕过权限校验机制
- ❌ 直接操作数据库（必须通过service层）
- ❌ 在业务模块中重复实现导出功能
- ❌ 修改已有的hook注册机制
- ❌ 破坏现有的包结构规范

### 必须遵循

- ✅ 所有开发在 `com.weaver.seconddev.zyhlw` 包下进行
- ✅ 新增模块时创建完整的分层结构
- ✅ 使用统一的工具类和服务
- ✅ 遵循泛微ecology的开发规范
- ✅ 实现完整的错误处理和日志记录

## AI决策标准

### 模块扩展优先级

1. **优先使用现有模块**：检查是否可以扩展现有功能
2. **创建新模块**：只有在现有模块无法满足时才创建
3. **工具类复用**：优先使用 `util/` 包下的工具类

### 文件修改决策树

```
需要修改文件时：
├── 是否为domain实体？
│   ├── 是 → 检查converter、export、service
│   └── 否 → 继续判断
├── 是否为controller？
│   ├── 是 → 检查service、domain、exception
│   └── 否 → 继续判断
└── 是否为service实现？
    ├── 是 → 检查controller、rpc、cache
    └── 否 → 按一般规则处理
```

### 技术选择原则

- **数据处理**：优先使用现有的converter
- **导出功能**：必须使用export模块
- **缓存需求**：使用统一的cache工具
- **HTTP请求**：使用统一的http工具
- **工作流处理**：使用workflow包下的工具 