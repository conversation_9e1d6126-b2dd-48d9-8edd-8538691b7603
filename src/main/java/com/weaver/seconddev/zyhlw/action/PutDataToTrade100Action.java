package com.weaver.seconddev.zyhlw.action;

import com.alibaba.fastjson.JSON;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface;
import com.weaver.seconddev.zyhlw.service.IChuChaiService;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
@Slf4j
@Service("cmicPutDataToTrade100Action")
public class PutDataToTrade100Action implements EsbServerlessRpcRemoteInterface {
    String simpleName =PutDataToTrade100Action.class.getSimpleName();
    @Autowired
   private IChuChaiService iChuChaiService;
    @Override
    public WeaResult<Map<String, Object>> execute(Map<String, Object> params) {
        String method = "调用"+simpleName+".execute()-->";
        log.info("{}",method);
        log.info("{}获取到的参数：{}",method, JSON.toJSONString(params));
        Map<String, Object> stringObjectMap = iChuChaiService.putDataToTrade100(JSONObject.fromObject(params));
        Integer code = Integer.valueOf(stringObjectMap.get("code").toString());
        if (code == 200) {

            return WeaResult.success(stringObjectMap,"请求成功");
        }else{
            return WeaResult.fail("请求失败");
        }

    }
}
