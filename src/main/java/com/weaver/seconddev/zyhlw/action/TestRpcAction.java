package com.weaver.seconddev.zyhlw.action;

import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service("test_zyhlw_rpc_action")
public class TestRpcAction implements EsbServerlessRpcRemoteInterface {
    @Override
    public WeaResult<Map<String, Object>> execute(Map<String, Object> params) {
        return null;
    }
}
