package com.weaver.seconddev.zyhlw.action;

import com.alibaba.fastjson.JSON;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface;
import com.weaver.seconddev.zyhlw.domain.request.todo.TodoRequest;
import com.weaver.seconddev.zyhlw.domain.response.todo.TodoResponse;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.service.ITodoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service("cmicTodoDataRpcAction")
public class TodoDataRpcAction implements EsbServerlessRpcRemoteInterface {
    @Resource
    private ITodoService todoService;
    String simpleName = TodoDataRpcAction.class.getSimpleName();
    @Override
    public WeaResult<Map<String, Object>> execute(Map<String, Object> params) {
        String methodName = "调用"+simpleName+".execute()";
        log.info("{}  请求参数：{}", methodName,JSON.toJSONString(params));
        TodoRequest todoRequest = new TodoRequest();
        if (params.containsKey("code")) {
            todoRequest.setCode(params.get("code").toString());
        }
        if (params.containsKey("userid")) {
            todoRequest.setUserId(Long.parseLong(params.get("userid").toString()));
        }
        if (params.containsKey("pageNo")) {
            todoRequest.setPageNo(params.get("pageNo").toString());
        }else{
            todoRequest.setPageNo("1");
        }
        if (params.containsKey("pageSize")) {
            todoRequest.setPageSize(params.get("pageSize").toString());
        }else{
            todoRequest.setPageSize("10");
        }
        TodoResponse data = todoService.getData(todoRequest);
        Map<String,Object> result = new HashMap<>();
        result.put("total",data.getTotal());
        result.put("data",data.getData());
        return WeaResult.success(result);
    }
}
