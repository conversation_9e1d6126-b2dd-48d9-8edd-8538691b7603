package com.weaver.seconddev.zyhlw.action;

import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface;
import com.weaver.seconddev.zyhlw.domain.request.todo.TodoGrayRequest;
import com.weaver.seconddev.zyhlw.service.ITodoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service("cmicTodoGrayRpcAction")
public class TodoGrayRpcAction implements EsbServerlessRpcRemoteInterface {
    String simpleName = TodoGrayRpcAction.class.getSimpleName();
    @Resource
    ITodoService iTodoService;
    @Override
    public WeaResult<Map<String, Object>> execute(Map<String, Object> params) {
        if (!params.containsKey("loginId")) {
            return WeaResult.fail("loginnId参数缺失");
        }
        if (!params.containsKey("uniqueId")) {
            return WeaResult.fail("uniqueId参数缺失");
        }
        if (!params.containsKey("todoType")) {
            return WeaResult.fail("todoType参数缺失");
        }
        if (!params.containsKey("employType")) {
            return WeaResult.fail("employType参数缺失");
        }
        TodoGrayRequest request = new TodoGrayRequest();
        request.setLoginId(params.get("loginId").toString());
        request.setUniqueId(params.get("uniqueId").toString());
        request.setTodoType(params.get("todoType").toString());
        request.setEmployType(Integer.valueOf(params.get("employType").toString()));
        Boolean b = iTodoService.updateGray(request);
        Map<String,Object> result = new HashMap<>();
        result.put("code",200);
        result.put("msg","置灰成功");
        result.put("status",b);
        return WeaResult.success(result,"请求成功");
    }
}
