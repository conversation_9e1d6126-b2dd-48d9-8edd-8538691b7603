package com.weaver.seconddev.zyhlw.action;

import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface;
import com.weaver.seconddev.zyhlw.domain.request.todo.TodoRequest;
import com.weaver.seconddev.zyhlw.domain.response.todo.TodoTabResponse;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.service.ITodoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service("cmicTodoTabRpcAction")
public class TodoTabRpcAction  implements EsbServerlessRpcRemoteInterface {
    @Resource
    private ITodoService iTodoService;
    @Override
    public WeaResult<Map<String, Object>> execute(Map<String, Object> params) {
        TodoRequest todoRequest = new TodoRequest();
        if (params.containsKey("code")) {
            todoRequest.setCode(params.get("code").toString());
        }
        if (params.containsKey("userid")) {
            todoRequest.setUserId(Long.parseLong(params.get("userid").toString()));
        }
        TodoTabResponse tab = iTodoService.getTab(todoRequest);
        Map<String,Object> result = new HashMap<>();
        result.put("tabCode",tab.getTabCode());
        result.put("tabName",tab.getTabName());
        result.put("count",tab.getCount());
        return WeaResult.success(result);
    }
}
