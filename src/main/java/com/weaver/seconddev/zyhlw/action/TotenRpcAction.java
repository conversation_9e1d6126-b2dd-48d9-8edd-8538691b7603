package com.weaver.seconddev.zyhlw.action;

import com.alibaba.fastjson.JSON;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface;
import com.weaver.openapi.pojo.basicserver.res.vo.AccountVo;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.service.ITokenService;
import com.weaver.seconddev.zyhlw.service.impl.TokenServiceImpl;
import com.weaver.teams.security.context.UserContext;
import com.weaver.teams.security.user.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service("cmicTokenRpcAction")
public class TotenRpcAction implements EsbServerlessRpcRemoteInterface {

   String simpleName = TotenRpcAction.class.getSimpleName();
    @Resource
    ITokenService iTokenService;
    @Resource
    IOpenPlatformService iOpenPlatformService;
    @Override
    public WeaResult<Map<String, Object>> execute(Map<String, Object> params) {

        String method = "调用"+simpleName+".execute()-->";

        iTokenService.init();
        String type = params.get("type").toString();
        String employeeId = params.get("employeeId").toString();
        AccountVo account = iOpenPlatformService.findAccount(Long.parseLong(employeeId));
        String token = "";
        if ("ticket".equals(type)) {
            // 统一用户ticket
            token = iTokenService.generateTicket(account.getLoginName());
            if (token == null || "".equals(token)) {
                return WeaResult.fail("获取统一用户ticket失败");
            }else{
                Map<String,Object> result = new HashMap<>();
                result.put("token",token);
                return WeaResult.success(result,"获取统一用户ticket成功");
            }

        } else if ("portalToken".equals(type)) {
            // 获取portalToken
            token = iTokenService.generatePortalToken(account.getLoginName());
            if (token == null || "".equals(token)) {
                return WeaResult.fail("获取PortalToken失败");
            }else {
                Map<String,Object> result = new HashMap<>();
                result.put("token",token);
                return WeaResult.success(result,"获取PortalToken成功");
            }
        } else if ("token".equals(type)) {
           token = iTokenService.generateToken(account.getLoginName());
            if (token == null || "".equals(token)) {
                return WeaResult.fail("获取Token失败");
            }else {
                Map<String,Object> result = new HashMap<>();
                result.put("token",token);
                return WeaResult.success(result,"获取Token成功");
            }
        } else {
            return WeaResult.fail("票据类型不存在");
        }

    }
}
