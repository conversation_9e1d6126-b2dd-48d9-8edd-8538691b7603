package com.weaver.seconddev.zyhlw.action.invoice;
import com.alibaba.fastjson.JSON;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface;
import com.weaver.seconddev.zyhlw.domain.request.eb.EbFormDataReq;
import com.weaver.seconddev.zyhlw.domain.response.form.EbFormDataResultVo;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IGetInvoiceInformationService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.service.impl.invoice.GetInvoiceInformationImpl;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import com.weaver.seconddev.zyhlw.util.ebuilder.EBuilderUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Service("cmicFinanceCheckedAction")
public class FinanceCheckedAction implements EsbServerlessRpcRemoteInterface {
    @Resource
    IDataSqlService dataSqlService;

    @Resource
    IDataBaseService iDataBaseService;

    @Resource
    IOpenPlatformService iOpenPlatformService;

    @Resource
    CmicProperties cmicProperties;
    @Resource
    IGetInvoiceInformationService iGetInvoiceInformationService;
    private final String method = "调用" + FinanceCheckedAction.class.getSimpleName() + ".execute()-->";

    @Override
    public WeaResult<Map<String, Object>> execute(Map<String, Object> params) {
        log.info("{}请求参数：{}", method, JSON.toJSONString(params));
        String tableName = iDataBaseService.getBaseValue("发票勾稽申请流程表名称", "发票管理");
        String ysTableId = iDataBaseService.getBaseValue("uf_ying_slb", "objId");
        String fpTableId = iDataBaseService.getBaseValue("uf_fa_pxxk", "objId");
        String fpGjTableId = iDataBaseService.getBaseValue("uf_fa_pgjb", "objId");
        try {
            //组合请求参数
            Map<String, Object> map = new HashMap<>();

            if (params.containsKey("userid")) {
                map.put("userid", params.get("userid"));
            }

            if (params.containsKey("type")) {
                map.put("type", StringUtils.nvl(String.valueOf(params.get("type")), ""));
            }

            if (params.containsKey("sfzx")){
                map.put("sfzx", StringUtils.nvl(String.valueOf(params.get("sfzx")), ""));
            }

            if (params.containsKey("requestid")) {
                String requestId = StringUtils.nvl(String.valueOf(params.get("requestid")), "");
                map.put("requestid", requestId);

                //查询明细信息
                if (!requestId.isEmpty()) {
                    String sql = "select * from " + tableName + "_dt1 where is_delete = 0 and tenant_key = 't6il1ypj4w' and FORM_DATA_ID = " + requestId;
                    List<Map<String, Object>> detailList = dataSqlService.workflowFromSqlAll(sql, SourceType.LOGIC);
                    for (Map<String, Object> detail : detailList) {
                        // 应收单选框改为从多选框中获取数据
                        String ysIdStr = StringUtils.isNotEmpty(String.valueOf(detail.get("ying_sd2")))
                                ? String.valueOf(detail.get("ying_sd2"))
                                : String.valueOf(detail.get("ying_sd"));

                        String collatedAmount = String.valueOf(detail.get("ben_cgjje"));
                        // 勾稽上限
                        BigDecimal gjLimit = parseBigDecimal(collatedAmount);

                        List<Map<String, Object>> ysList = GetYSList(ysIdStr);
                        List<Map<String, Object>> dataList = new ArrayList<>();
                        for (Map<String, Object> ys : ysList) {
                            String childYs_id = String.valueOf(ys.get("id"));
                            // 注意：每条勾稽金额不能超过gjLimit，因为每条明细的未勾稽金额为gjLimit
                            if (gjLimit.compareTo(BigDecimal.valueOf(0)) > 0) {
                                // 原有的勾稽行为，未勾稽金额
                                BigDecimal wei_gjje = parseBigDecimal(String.valueOf(ys.get("wei_gjje")));
                                // 冻结金额
                                BigDecimal do_jje = parseBigDecimal((String.valueOf(ys.get("do_jje"))));
                                //本次勾稽金额
                                BigDecimal ben_cgjje;
                                // 明细行发票金额大于等于勾稽中的应收单的未勾稽金额
                                if (gjLimit.compareTo(wei_gjje) >= 0) {
                                    // 发票勾稽金额能够完全勾稽完该应收单
                                    log.info("发票勾稽金额能够完全勾稽完该应收单，接下来进行下一张应收单的勾稽");
                                    ben_cgjje = wei_gjje;
                                } else {
                                    // 明细行发票金额不能够勾稽完该应收单，因此本次勾稽后不会对剩余应收单产生勾稽记录了
                                    log.info("明细行发票勾稽金额不能够勾稽完该应收单，因此本次勾稽后不会对剩余应收单产生勾稽记录了");
                                    ben_cgjje = gjLimit;
                                }
                                gjLimit = gjLimit.subtract(ben_cgjje);
                                if (map.get("type").equals("1")) {
                                    //如果是提交操作，则冻结金额+本次勾稽金额
                                    do_jje = do_jje.add(ben_cgjje);
                                } else {
                                    //如果是退回操作，则冻结金额-本次勾稽金额
                                    do_jje = do_jje.subtract(ben_cgjje);

                                }

                                //更新应收单冻结金额
                                Map<String, Object> data = new HashMap<>();
                                data.put("do_jje", do_jje);
                                data.put("id", childYs_id);
                                Map<String, Object> dataS = new HashMap<>();
                                dataS.put("mainTable", data);
                                dataList.add(dataS);

                            }
                        }
                        log.info("dataList===>{}", dataList);
                        if (!dataList.isEmpty()) {
                            //更新应收单冻结金额
                            UpdateYSInfo(String.valueOf(map.get("userid")),ysTableId,dataList);

                        }
                        String fpIdStr = String.valueOf(detail.get("fa_psqd"));

                        sql = "select do_jje from uf_fa_pxxk where is_delete = 0 and tenant_key = 't6il1ypj4w' and id =" + fpIdStr;
                        Map<String, Object> invoiceInformationMap = dataSqlService.eBuilderFromSqlOne(sql, SourceType.LOGIC);
                        //本次勾稽金额
                        BigDecimal ben_cgjje = parseBigDecimal(String.valueOf(detail.get("ben_cgjje")));
                        //发票冻结金额
                        BigDecimal do_jje2 = parseBigDecimal(String.valueOf(invoiceInformationMap.get("do_jje")));
                        log.info("本次勾稽金额:" + ben_cgjje + "发票冻结金额:" + do_jje2);

                        //计算后的冻结金额写入对应表
                        BigDecimal fpdo_jje;
                        if (map.get("type").equals("1")) {
                            fpdo_jje = do_jje2.add(ben_cgjje);
                        } else {
                            //退回操作  释放冻结金额
                            fpdo_jje = do_jje2.subtract(ben_cgjje);
                        }

                        //更新发票的冻结金额信息
                        updateInvoice(fpdo_jje, fpIdStr, String.valueOf(map.get("userid")),fpTableId);

                        if (map.get("sfzx").equals("1")) {
                            log.info("开始执行生成勾稽记录操作！");
                            //发票ID
                            String invcId = String.valueOf(detail.get("fa_psqd"));
                            Map<String, Map<String, Object>> invoiceMap = new HashMap<>();

                            sql = "select id, fa_phm, fa_pzt, nvl(fa_pkyje, 0) as fa_pkyje, jin_e, nvl(yi_gjje, 0) as yi_gjje, nvl(wei_gjje, 0) as wei_gjje from uf_fa_pxxk where is_delete = 0 and tenant_key = 't6il1ypj4w' and id= " + invcId + " order by fa_pkyje desc";
                            List<Map<String, Object>> invoiceInformationList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
                            for (Map<String, Object> invoiceInformation : invoiceInformationList) {
                                Map<String, Object> invoice_temp = new HashMap<>();
                                String id = String.valueOf(invoiceInformation.get("id"));
                                String fa_phm = String.valueOf(invoiceInformation.get("fa_phm"));
                                invoice_temp.put("id", id);
                                invoice_temp.put("fa_phm", fa_phm);
                                invoice_temp.put("fa_pzt", getIntValue(String.valueOf(invoiceInformation.get("fa_pzt"))));
                                invoice_temp.put("fa_pkyje", getIntValue(String.valueOf(invoiceInformation.get("fa_pkyje"))));
                                invoice_temp.put("jin_e", getIntValue(String.valueOf(invoiceInformation.get("jin_e"))));
                                invoice_temp.put("yi_gjje", getIntValue(String.valueOf(invoiceInformation.get("yi_gjje"))));
                                invoice_temp.put("wei_gjje", getIntValue(String.valueOf(invoiceInformation.get("wei_gjje"))));
                                //以发票ID为主键保存
                                invoiceMap.put(String.valueOf(id), invoice_temp);

                            }
                            log.info("============ 1、获取发票的信息invoiceMap：" + invoiceMap);
                            //获取应收集合
                            ysList = GetYSList(ysIdStr);
                            log.info("============ 2、从应收单获取发票开具的应收单信息 ysList：" + ysList);


                            List< List<Object>> operateIdList = new ArrayList<>();
                            Map<String, Object> recordMap = new HashMap<>();

                            // 这里可能有多个应收单号
                            recordMap.put("ying_sdh", ysIdStr);
                            // 明细表中本次勾稽金额,这个就是这条记录对应的勾稽金额上限，防止需要勾稽到其它应收的金额勾稽完，导致缺失一条勾稽记录
                            String detail_ben_cgjje = String.valueOf(detail.get("ben_cgjje"));
                            log.info("============ 3、发票与应收单勾稽并生成勾稽记录 ============");
                            List<Map<String, Object>> gjRecord = new ArrayList<>();
                            // 以明细行为准，确认勾稽记录，时间复杂度 O(n * m)， n为明细行数，m为本次勾稽流程选择的应收单数
                            // 勾稽上限
                            BigDecimal gjLimit2 = parseBigDecimal(detail_ben_cgjje);
                            log.info("记录中的应收单id:" + ysIdStr + ",记录中的本次勾稽金额:" + gjLimit.doubleValue() + ",是否大于0:" + (gjLimit.compareTo(BigDecimal.valueOf(0)) > 0));
                            // 根据当前明细行的发票号码，获取发票信息
                            Map<String, Object> invoice = invoiceMap.get(invcId);
                            // ysStr可能有多个应收单号，按时间升序获取应收单，进行勾稽行为
                            for (Map<String, Object> ys : ysList) {
                                // 日期
                                String ri_q = String.valueOf(ys.get("ri_q"));
                                // 由于ysList中的应收单是按照时间倒序排序的
                                // 注意：每条勾稽金额不能超过gjLimit，因为每条明细的未勾稽金额为gjLimit
                                if (gjLimit2.compareTo(BigDecimal.valueOf(0)) > 0) {
                                    log.info("勾稽发票：" + invoiceMap + ";勾稽应收：" + ys + ";勾稽金额上限为：" + gjLimit2.doubleValue());
                                    // 勾稽，但不会超过当前记录开票的金额数
                                    Map<String, Object> row = new HashMap<>(10);
                                    // 原有的勾稽行为
                                    // 未勾稽金额
                                    BigDecimal wei_gjje = parseBigDecimal(String.valueOf(ys.get("wei_gjje")));
                                    //保存应收中操作前的未勾稽金额
                                    BigDecimal tempwei_gjje = wei_gjje;

                                    // 发票可用金额
                                    BigDecimal fa_pkyje = parseBigDecimal(String.valueOf(invoice.get("fa_pkyje")));
                                    BigDecimal ben_cgjje2;    //本次勾稽金额
                                    // 明细行发票金额大于等于勾稽中的应收单的未勾稽金额
                                    if (gjLimit2.compareTo(wei_gjje) >= 0) {
                                        // 发票能够完全勾稽完该应收单
                                        log.info("发票能够完全勾稽完该应收单，接下来进行下一张应收单的勾稽");
                                        ben_cgjje2 = wei_gjje;
                                    } else {
                                        // 明细行发票金额不能够勾稽完该应收单，因此本次勾稽后不会对剩余应收单产生勾稽记录了
                                        log.info("明细行发票金额不能够勾稽完该应收单，因此本次勾稽后不会对剩余应收单产生勾稽记录了");
                                        ben_cgjje2 = gjLimit2;
                                    }
                                    gjLimit2 = gjLimit2.subtract(ben_cgjje2);
                                    wei_gjje = wei_gjje.subtract(ben_cgjje2);

                                    BigDecimal jin_e = parseBigDecimal(String.valueOf(invoice.get("jin_e")));
                                    ys.put("wei_gjje", String.valueOf(wei_gjje));
                                    invoice.put("fa_pkyje", String.valueOf(fa_pkyje));
                                    invoice.put("wei_gjje", String.valueOf(wei_gjje));
                                    //已勾稽金额-发票金额-未勾稽金额
                                    invoice.put("yi_gjje", String.valueOf(jin_e.subtract(wei_gjje)));

                                    // 组织勾稽记录行
                                    // 应收id

                                    row.put("ying_sd", ys.get("id"));
                                    row.put("fa_psqd", invoice.get("id"));
                                    row.put("ying_sje", ys.get("lie_zje"));
                                    row.put("wei_kpje", String.valueOf(tempwei_gjje));
                                    row.put("ben_cgjje", String.valueOf(ben_cgjje2));
                                    row.put("fa_pkyje", String.valueOf(fa_pkyje));

                                    log.info("当前勾稽信息: 日期：" + ri_q + ",row：" + row);
                                    gjRecord.add(row);

                                    // 记录本次产生的勾稽记录的应收单id和发票id
                                    List<Object> ysfp = new ArrayList<>();
                                    ysfp.add(ys.get("id"));
                                    ysfp.add(invoice.get("id"));
                                    operateIdList.add(ysfp);
                                }
                            }
                            invoiceMap.put(invcId, invoice);
                            log.info("总勾稽记录: " + gjRecord);

                            log.info("============ 4、向勾稽主表插入一条数据来关联勾稽信息并且将勾稽记录插入发票勾稽表 ============");
                            if (!gjRecord.isEmpty()) {
                                List<Map<String, Object>> dataList3 = new ArrayList<>();
                                //勾稽主表数据
                                Map<String, Object> data = new HashMap<>(2);
                                data.put("cai_wlc", map.get("requestid"));
                                data.put("shen_qr", map.get("userid"));
                                Map<String, Object> dataS = new HashMap<>();
                                dataS.put("mainTable", data);
                                //勾稽明细数据
                                dataS.put("detail1", gjRecord);
                                dataList3.add(dataS);

                                EbFormDataReq builder2 = new EbFormDataReq.Builder()
                                        .userId(String.valueOf(map.get("userid")))
                                        .objId(fpGjTableId)
                                        .needAdd("true")
                                        .updateType("updatePolicy")
                                        .datas(dataList3)
                                        .build();
                                EbFormDataResultVo ebFormDataResultVo2 = EBuilderUtil.updateFormDataV2(builder2, iOpenPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());
                                log.info("调用{}  更新数据返回结果：{}", method, JSON.toJSONString(ebFormDataResultVo2));

                                log.info("进入归档操作！");
                                //应收单  应收单为空的情况
                                String[] ying_sdh2 = deleteArrayNull(fpIdStr.split(","));
                                //发票号码
                                String fa_phm = String.valueOf(detail.get("fa_psqd"));
                                sql = "select id from uf_fa_pxxk where is_delete = 0 and tenant_key = 't6il1ypj4w' and id ='" + fa_phm + "'";
                                Map<String, Object> map3 = dataSqlService.eBuilderFromSqlOne(sql, SourceType.LOGIC);
                                fa_phm = String.valueOf(map3.get("id"));
                                for (String s : ying_sdh2) {
                                    updateInyslb(s, fa_phm, String.valueOf(map.get("userid")), ysTableId);
                                }
                                updateByIRId(dataList, String.valueOf(map.get("userid")));
                            }
                        }
                    }
                }
            }

            Map<String, Object> result = new HashMap<>(2);
            result.put("code", 200);
            result.put("msg", "操作成功");
            log.info("{}  返回结果：{}", "调用FinanceCheckedAction.execute()", JSON.toJSONString(result));
            return WeaResult.success(result);
        } catch (Exception e) {
            log.error("{}  系统内部错误：{}", "调用FinanceCheckedAction.execute()", e.getMessage());
            return WeaResult.fail("系统内部错误：" + e.getMessage());
        }
    }

    private void updateInvoice(BigDecimal fpdo_jje, String fpIdStr,String userid,String objId) {
        List<Map<String, Object>> dataList2 = new ArrayList<>();
        Map<String, Object> data = new HashMap<>(2);
        data.put("do_jje", fpdo_jje);
        data.put("id", fpIdStr);
        Map<String, Object> dataS = new HashMap<>(1);
        dataS.put("mainTable", data);
        dataList2.add(dataS);

        Map<String, Object> mainTableField = new HashMap<>(1);
        List<String> mainTableFields = new ArrayList<>();
        mainTableFields.add("id");
        mainTableField.put("mainTable", mainTableFields);
        EbFormDataReq builder = new EbFormDataReq.Builder()
                .userId(userid)
                .objId(objId)
                .needAdd("true")
                .updateType("updatePolicy")
                .updateField(mainTableField)
                .datas(dataList2)
                .build();
        EbFormDataResultVo ebFormDataResultVo = EBuilderUtil.updateFormDataV2(builder, iOpenPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());
        log.info("调用{}  更新数据返回结果：{}", method, JSON.toJSONString(ebFormDataResultVo));
    }

    public void UpdateYSInfo(String userid, String objId, List<Map<String, Object>> dataList) {

        Map<String, Object> mainTableField = new HashMap<>(1);
        List<String> mainTableFields = new ArrayList<>(1);
        mainTableFields.add("id");
        mainTableField.put("mainTable", mainTableFields);

        EbFormDataReq builder = new EbFormDataReq.Builder()
                .userId(userid)
                .objId(objId)
                .needAdd("true")
                .updateType("updatePolicy")
                .updateField(mainTableField)
                .datas(dataList)
                .build();
        EbFormDataResultVo ebFormDataResultVo = EBuilderUtil.updateFormDataV2(builder, iOpenPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());
        log.info("调用{}  更新数据返回结果：{}", method, JSON.toJSONString(ebFormDataResultVo));
    }

    public String updateByIRId(List<Map<String, Object>> detailData, String userid) {
        log.info("开始执行updateByIRId");
        String fpGjTableId = iDataBaseService.getBaseValue("uf_fa_pgjb", "objId");
        String invoiceId = "";
        String ysId = "";
        for (int i = 0; i < detailData.size(); i++) {
            Map<String, Object> temp = detailData.get(i);
            invoiceId += temp.get("fa_psqd");
            ysId += temp.get("ying_sd2");
            if (i < detailData.size() - 1) {
                invoiceId += ",";
                ysId += ",";
            }
        }

        Map<String, Object> resultMap = new HashMap<>();
        try {
            log.info("ysId====>{}", ysId);
            if (!ysId.isEmpty()) {
                iGetInvoiceInformationService.statisticYsInfo(ysId, userid);
                List<Map<String, Object>> dataList3 = new ArrayList<>();
                for (String ysIdStr : ysId.split(",")) {
                    Map<String, Object> data = new HashMap<>(2);
                    data.put("ying_synch", 1);
                    data.put("ying_sd", ysIdStr);
                    Map<String, Object> dataS = new HashMap<>(1);
                    dataS.put("mainTable", data);
                    dataList3.add(dataS);
                }

                Map<String, Object> mainTableField = new HashMap<>(1);
                List<String> mainTableFields = new ArrayList<>();
                mainTableFields.add("ying_sd");
                mainTableField.put("mainTable", mainTableFields);

                EbFormDataReq builder2 = new EbFormDataReq.Builder()
                        .userId(userid)
                        .objId(fpGjTableId)
                        .needAdd("true")
                        .updateType("updatePolicy")
                        .updateField(mainTableField)
                        .datas(dataList3)
                        .build();
                EbFormDataResultVo ebFormDataResultVo2 = EBuilderUtil.updateFormDataV2(builder2, iOpenPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());
                log.info("调用updateByIRId更新数据返回结果：{}", JSON.toJSONString(ebFormDataResultVo2));
            }

            log.info("invoiceId====>{}", invoiceId);
            if (!invoiceId.isEmpty()) {
                iGetInvoiceInformationService.statisticInvoiceInfo(invoiceId, userid);
                List<Map<String, Object>> dataList3 = new ArrayList<>();
                for (String invoiceIdStr : invoiceId.split(",")) {
                    Map<String, Object> data = new HashMap<>(2);
                    data.put("ying_synch", 1);
                    data.put("fa_psqd", invoiceIdStr);
                    Map<String, Object> dataS = new HashMap<>(1);
                    dataS.put("mainTable", data);
                    dataList3.add(dataS);
                }

                Map<String, Object> mainTableField = new HashMap<>(1);
                List<String> mainTableFields = new ArrayList<>();
                mainTableFields.add("fa_psqd");
                mainTableField.put("mainTable", mainTableFields);

                EbFormDataReq builder2 = new EbFormDataReq.Builder()
                        .userId(userid)
                        .objId(fpGjTableId)
                        .needAdd("true")
                        .updateType("updatePolicy")
                        .updateField(mainTableField)
                        .datas(dataList3)
                        .build();
                EbFormDataResultVo ebFormDataResultVo2 = EBuilderUtil.updateFormDataV2(builder2, iOpenPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());
                log.info("调用updateByIRId更新数据返回结果：{}", JSON.toJSONString(ebFormDataResultVo2));
            }
            resultMap.put("isSuccess", 1);
        } catch (Exception e) {
            resultMap.put("isSuccess", 0);
            e.printStackTrace();
            log.info("更新发票、应收单出错!  错误信息: " + e.getMessage());
        }

        return JSON.toJSONString(resultMap);
    }

    /**
     * 应收单对应发票
     * ying_sdh   应收单号
     * fa_phm    发票号码
     */
    public void updateInyslb(String ying_sdh, String fa_phm, String userid, String objId) {
        log.info("开始执行新增修改操作updateInyslb");
        String sql = "select fa_pbh from uf_ying_slb where is_delete = 0 and tenant_key = 't6il1ypj4w' and id = " + ying_sdh;
        List<Map<String, Object>> listData = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        List<Map<String, Object>> dataList = new ArrayList<>();
        for (Map<String, Object> rs : listData) {
            List<String> list = new ArrayList<>();
            //对空字符做过滤
            String[] fa_pbh = deleteArrayNull(String.valueOf(rs.get("fa_pbh")).split(","));
            list.add(fa_phm);
            for (String s : fa_pbh) {
                if (!list.contains(s)) {
                    list.add(s);
                }
            }

            Map<String, Object> data = new HashMap<>(2);
            data.put("fa_pbh", String.join(",", list));
            data.put("id", ying_sdh);
            Map<String, Object> dataS = new HashMap<>(1);
            dataS.put("mainTable", data);
            dataList.add(dataS);
        }

        Map<String, Object> mainTableField = new HashMap<>(1);
        List<String> mainTableFields = new ArrayList<>();
        mainTableFields.add("id");
        mainTableField.put("mainTable", mainTableField);

        EbFormDataReq builder = new EbFormDataReq.Builder()
                .userId(userid)
                .objId(objId)
                .needAdd("true")
                .updateType("updatePolicy")
                .updateField(mainTableField)
                .datas(dataList)
                .build();
        EbFormDataResultVo ebFormDataResultVo = EBuilderUtil.updateFormDataV2(builder, iOpenPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());
        log.info("调用updateInyslb更新数据返回结果：{}", JSON.toJSONString(ebFormDataResultVo));
    }

    /**
     * 去除数组中的空字符串
     * @param fa_phm2
     * @return
     */
    public static String[] deleteArrayNull(String[] fa_phm2) {
        if (fa_phm2 == null) {
            return new String[0];
        }
        return Arrays.stream(fa_phm2)
                .filter(str -> str != null && !str.trim().isEmpty())
                .toArray(String[]::new);
    }

    /**
     * 将字符串转换成int类型
     * 如果是空字符串返回 0
     *
     * @param value 字符串
     * @return 成功返回int值
     */
    public static int getIntValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            log.warn("getIntValue: 输入值为 null 或空字符串，返回默认值 0");
            return 0;
        }
        try {
            return Integer.parseInt(value.trim());
        } catch (NumberFormatException e) {
            log.warn("getIntValue: 输入值 {} 不是有效的整数，返回默认值 0", value, e);
            return 0;
        }
    }

    /**
     * 将字符串转换成BigDecimal类型
     * 如果是空字符串返回 0
     *
     * @param s 字符串
     * @return 成功返回BigDecimal值
     */
    public static BigDecimal parseBigDecimal(String s) {
        if (s == null || s.trim().isEmpty()) {
            log.warn("parseBigDecimal: 输入值为 null 或空字符串，返回默认值 0");
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(s.trim());
        } catch (NumberFormatException e) {
            log.warn("parseBigDecimal: 输入值 {} 不是有效的 BigDecimal，返回默认值 0", s, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 通过多选应收编号获取应收集合信息
     *
     * @param ying_sdh 选应收编号ID
     * @return
     */
    public List<Map<String, Object>> GetYSList(String ying_sdh) {
        String getAffectedYsSql = "select id, lie_zje, nvl(kai_pjebhs, 0) as kai_pjebhs,ri_q,nvl(do_jje, 0) as do_jje from uf_ying_slb where is_delete = 0 and tenant_key = 't6il1ypj4w' and id in (" + ying_sdh + ") order by ri_q asc";
        return dataSqlService.eBuilderFromSqlAll(getAffectedYsSql, SourceType.LOGIC);
    }
}
