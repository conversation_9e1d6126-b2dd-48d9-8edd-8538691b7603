package com.weaver.seconddev.zyhlw.action.invoice;
import com.alibaba.fastjson.JSON;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface;
import com.weaver.seconddev.zyhlw.domain.request.eb.EbFormDataReq;
import com.weaver.seconddev.zyhlw.domain.response.form.EbFormDataResultVo;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IGetInvoiceInformationService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.ebuilder.EBuilderUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service("cmicTheCheckedFlowAction")
public class TheCheckedFlowAction implements EsbServerlessRpcRemoteInterface {
    private final String method = "调用" + TheCheckedFlowAction.class.getSimpleName() + ".execute()-->";
    @Resource
    IDataBaseService iDataBaseService;
    @Resource
    IOpenPlatformService iOpenPlatformService;
    @Resource
    CmicProperties cmicProperties;
    @Resource
    IGetInvoiceInformationService iGetInvoiceInformationService;
    @Resource
    IDataSqlService dataSqlService;


    @Override
    public WeaResult<Map<String, Object>> execute(Map<String, Object> params) {
        log.info("{}请求参数：{}", method, JSON.toJSONString(params));

        //获取明细表
        List<Map<String, String>> detailData = new ArrayList<>();
        if (!params.containsKey("detailData")) {
            detailData = (List<Map<String, String>>) params.get("detailData");
        }
        String type = "";
        if (params.containsKey("type")) {
            type = String.valueOf(params.get("type"));
        }
        String sfzx = "";
        if (params.containsKey("sfzx")) {
            sfzx = String.valueOf(params.get("sfzx"));
        }
        String userid = "";
        if (params.containsKey("userid")) {
            userid = String.valueOf(params.get("userid"));
        }

        //修改对应的勾稽类型
        updateState(detailData, type);
        if ("1".equals(sfzx)) {
            log.info("进入归档操作！");
            updateByIRId(detailData, userid);

            for (Map<String, String> temp : detailData) {
                //应收单      应收单为空的情况
                String[] ying_sdh = FinanceCheckedAction.deleteArrayNull(temp.get("ying_sd").split(","));
                //发票号码
                String fa_phm = temp.get("fa_psqd");
                String sql = "select id from uf_fa_pxxk where is_delete = 0 and tenant_key = 't6il1ypj4w' and id='" + fa_phm + "'";
                Map<String, Object> map = dataSqlService.eBuilderFromSqlOne(sql, SourceType.LOGIC);
                fa_phm = String.valueOf(map.get("id"));
                for (String s : ying_sdh) {
                    //防止多个应收单 对单个发票
                    updateInyslb(s, fa_phm, userid);
                }
            }
        }
        Map<String, Object> result = new HashMap<>(2);
        result.put("code", 200);
        result.put("msg", "操作成功");
        log.info("{}  返回结果：{}", method,JSON.toJSONString(result));
        return WeaResult.success(result);
    }

    /**
     * 应收单对应发票
     * ying_sdh   应收单号
     * fa_phm    发票号码
     */
    public void updateInyslb(String ying_sdh, String fa_phm, String userid) {
        log.info("开始执行新增修改操作updateInyslb");
        String ysTableId = iDataBaseService.getBaseValue("uf_ying_slb", "objId");

        String sql = "select fa_pbh from uf_ying_slb where is_delete = 0 and tenant_key = 't6il1ypj4w' and id=" + ying_sdh;
        List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        for (Map<String, Object> rs : dataList){
            List<String> list = new ArrayList<>();
            //对空字符做过滤
            String[] fa_pbh = FinanceCheckedAction.deleteArrayNull(String.valueOf(rs.get("fa_pbh")).split(","));
            for (int i = 0; i < fa_pbh.length; i++) {
                if (fa_pbh[i].equals(fa_phm)) {
                    fa_pbh[i] = fa_pbh[fa_pbh.length - 1];
                    fa_pbh = Arrays.copyOf(fa_pbh, fa_pbh.length - 1);
                    list.add(Arrays.toString(fa_pbh));
                }
            }
            log.info("打印删除后的元素：" + String.join(",", list));

            List<Map<String, Object>> dataList3 = new ArrayList<>();
            Map<Object, Object> data = new HashMap<>(2);
            data.put("fa_pbh", String.join(",", list));
            data.put("id", ying_sdh);
            Map<String, Object> dataS = new HashMap<>(1);
            dataS.put("mainTable", data);
            dataList3.add(dataS);

            Map<String, Object> mainTableField = new HashMap<>(1);
            List<String> mainTableFields = new ArrayList<>();
            mainTableFields.add("id");
            mainTableField.put("mainTable", mainTableFields);

            EbFormDataReq builder = new EbFormDataReq.Builder()
                    .userId(userid)
                    .objId(ysTableId)
                    .needAdd("true")
                    .updateType("updatePolicy")
                    .updateField(mainTableField)
                    .datas(dataList3)
                    .build();
            EbFormDataResultVo ebFormDataResultVo = EBuilderUtil.updateFormDataV2(builder, iOpenPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());
            log.info("调用updateByIRId更新数据返回结果：{}", JSON.toJSONString(ebFormDataResultVo));

        }
    }

    public void updateByIRId(List<Map<String, String>> detailData, String userid) {
        log.info("反勾稽进入方法updateByIRId：" + JSON.toJSONString(detailData));
        String fpGjTableId = iDataBaseService.getBaseValue("uf_fa_pgjb", "objId");
        try {
            StringBuilder invoiceId = new StringBuilder();
            StringBuilder ysId = new StringBuilder();

            for (int i = 0; i < detailData.size(); i++) {
                Map<String, String> temp = detailData.get(i);
                invoiceId.append(temp.get("fa_psqd"));
                ysId.append(temp.get("ying_sd"));
                if (i < detailData.size() - 1) {
                    invoiceId.append(",");
                    ysId.append(",");
                }
            }
            log.info("ysId===>{}", ysId);
            log.info("invoiceId===>{}", invoiceId);

            if (ysId.length() > 0) {
                iGetInvoiceInformationService.statisticYsInfo(ysId.toString(), userid);
                List<Map<String, Object>> dataList3 = new ArrayList<>();
                for (String ysIdStr : ysId.toString().split(",")) {
                    Map<String, Object> data = new HashMap<>(2);
                    data.put("ying_synch", 1);
                    data.put("ying_sd", ysIdStr);
                    Map<String, Object> dataS = new HashMap<>(1);
                    dataS.put("mainTable", data);
                    dataList3.add(dataS);
                }

                Map<String, Object> mainTableField = new HashMap<>(1);
                List<String> mainTableFields = new ArrayList<>();
                mainTableFields.add("ying_sd");
                mainTableField.put("mainTable", mainTableFields);

                EbFormDataReq builder2 = new EbFormDataReq.Builder()
                        .userId(userid)
                        .objId(fpGjTableId)
                        .needAdd("true")
                        .updateType("updatePolicy")
                        .updateField(mainTableField)
                        .datas(dataList3)
                        .build();
                EbFormDataResultVo ebFormDataResultVo2 = EBuilderUtil.updateFormDataV2(builder2, iOpenPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());
                log.info("调用updateByIRId更新数据返回结果：{}", JSON.toJSONString(ebFormDataResultVo2));

            }

            if (invoiceId.length() > 0) {
                iGetInvoiceInformationService.statisticInvoiceInfo(invoiceId.toString(), userid);
                List<Map<String, Object>> dataList2 = new ArrayList<>();
                for (String invoiceIdStr : invoiceId.toString().split(",")) {
                    Map<String, Object> data = new HashMap<>(2);
                    data.put("ying_synch", 1);
                    data.put("fa_psqd", invoiceIdStr);
                    Map<String, Object> dataS = new HashMap<>(1);
                    dataS.put("mainTable", data);
                    dataList2.add(dataS);
                }

                Map<String, Object> mainTableField = new HashMap<>(1);
                List<String> mainTableFields = new ArrayList<>();
                mainTableFields.add("fa_psqd");
                mainTableField.put("mainTable", mainTableFields);
                EbFormDataReq builder = new EbFormDataReq.Builder()
                        .userId(userid)
                        .objId(fpGjTableId)
                        .needAdd("true")
                        .updateType("updatePolicy")
                        .updateField(mainTableField)
                        .datas(dataList2)
                        .build();
                EbFormDataResultVo ebFormDataResultVo = EBuilderUtil.updateFormDataV2(builder, iOpenPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());
                log.info("调用updateByIRId更新数据返回结果：{}", JSON.toJSONString(ebFormDataResultVo));
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.info("更新发票、应收单出错!  错误信息: " + e.getMessage());
        }
    }


    public void updateState(List<Map<String, String>> listMap, String type) {
        log.info("反勾稽进入方法updateState：" + JSON.toJSONString(listMap));
        String fpGjTableId = iDataBaseService.getBaseValue("uf_fa_pgjb", "objId");
        List<Map<String, Object>> dataList = new ArrayList<>();
        for (Map<String, String> temp : listMap) {
            String id = temp.get("gouji_id");
            Map<String, Object> data = new HashMap<>(3);
            data.put("fa_gdlx", type.equals("1") ? "2" : "0");
            data.put("ying_synch", "1");
            data.put("id", id);
            Map<String, Object> dataS = new HashMap<>(1);
            dataS.put("mainTable", dataS);
            dataList.add(dataS);
        }

        log.info("反勾稽调用{}  更新数据：{}", method, JSON.toJSONString(dataList));

        if (!dataList.isEmpty()) {
            Map<String, Object> mainTableField = new HashMap<>(1);
            List<String> mainTableFields = new ArrayList<>();
            mainTableFields.add("id");
            mainTableField.put("mainTable", mainTableFields);
            EbFormDataReq builder = new EbFormDataReq.Builder()
                    .userId("1")
                    .objId(fpGjTableId)
                    .needAdd("true")
                    .updateType("updatePolicy")
                    .updateField(mainTableField)
                    .datas(dataList)
                    .build();
            EbFormDataResultVo ebFormDataResultVo = EBuilderUtil.updateFormDataV2(builder, iOpenPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());
            log.info("调用{}  更新数据返回结果：{}", method, JSON.toJSONString(ebFormDataResultVo));
        }
    }
}
