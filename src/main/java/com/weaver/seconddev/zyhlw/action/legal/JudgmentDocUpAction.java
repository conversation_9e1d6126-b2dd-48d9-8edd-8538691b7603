package com.weaver.seconddev.zyhlw.action.legal;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface;
import com.weaver.seconddev.zyhlw.domain.request.eb.EbFormDataReq;
import com.weaver.seconddev.zyhlw.domain.response.form.EbFormDataResultVo;
import com.weaver.seconddev.zyhlw.domain.response.form.vo.DataJson;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.ebuilder.EBuilderUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 法律案件更新附件操作
 *
 * @date 2025-01-14
 */
@Slf4j
@Service("cmicJudgmentDocUpAction")
public class JudgmentDocUpAction implements EsbServerlessRpcRemoteInterface {

    private final String methodName = JudgmentDocUpAction.class.getSimpleName();
    @Resource
    private IDataSqlService dataSqlService;
    @Resource
    IOpenPlatformService openPlatformService;
    @Resource
    private CmicProperties cmicProperties;

    @Override
    public WeaResult<Map<String, Object>> execute(Map<String, Object> params) {
        log.info("调用'{}'方法,时间'{}'", methodName, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        log.info("调用{}  请求参数：{}", methodName, JSONObject.toJSONString(params));
        try {
            String requestId = params.get("requestId") != null ? params.get("requestId").toString() : "";
            String data = params.get("detail") != null ? params.get("detail").toString() : "";
            String userid = params.get("userid").toString();
            String objId = params.get("objId").toString();
            log.info("调用'{}'方法,requestId:{},data:{}", methodName, requestId, data);
            //data转list
            List<Map<String, Object>> detailList = JSONObject.parseObject(data,
                    new ObjectMapper().getTypeFactory().constructCollectionType(List.class, Map.class));
            List<String> collect = detailList.stream().filter(map -> map.get("id") != null)
                    .map(map -> map.get("id").toString()).collect(Collectors.toList());
            log.info("调用{}  请求参数：{}", methodName, JSONObject.toJSONString(detailList));

            String sql = "select * from uf_an_jglLb where id in (" + String.join(",", collect) + ")";
            List<Map<String, Object>> dataList = new ArrayList<>();
            List<Map<String, Object>> maps = dataSqlService.ebuilderFromSql(sql, 1, 100, SourceType.LOGIC);
            log.info("调用{} 查询案件数据{}", methodName, maps);
            if (maps.isEmpty()) {
                log.error("调用{} 查询案件数据为空", methodName);
                return WeaResult.fail("案件数据为空");
            } else {
                Map<String, Map<String, Object>> detailMap = detailList.stream()
                        .collect(Collectors.toMap(
                                detail -> detail.get("id").toString(),
                                detail -> detail
                        ));
                for (Map<String, Object> map : maps) {
                    String id = map.get("id").toString();
                    String[] split = Optional.ofNullable(map.get("xiang_gfj"))
                            .map(Object::toString)
                            .orElse("")
                            .split(",");
                    List<String> list = Arrays.asList(split);
                    Map<String, Object> detail = detailMap.get(id);
                    if (detail != null) {
                        Object o = detail.get("appendix");
                        if (o != null) {
                            List<String> o1 = JSONObject.parseObject(o.toString(), new ObjectMapper().getTypeFactory().constructArrayType(List.class));
                            Set<String> set = new HashSet<>(o1);
                            set.addAll(list);
                            Map<String, Object> mainTable = new HashMap<>();
                            mainTable.put("xiang_gfj", String.join(",", set));
                            mainTable.put("id", id);
                            Map<String, Object> main = new HashMap<>();
                            main.put("mainTable", mainTable);
                            dataList.add(main);
                        }
                    }
                }
            }
            EbFormDataReq ebFormDataReq = new EbFormDataReq.Builder().objId(objId).userId(userid).datas(dataList).build();
            EbFormDataResultVo ebFormDataResultVo = EBuilderUtil.updateFormDataV2(ebFormDataReq, openPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());
            log.info("调用{}  更新数据返回结果：{}", methodName, JSONObject.toJSONString(ebFormDataResultVo));
            Map<String, Object> resultMap = new HashMap<>();
            DataJson dataJson = ebFormDataResultVo.getData().getDataJson().get(0);
            if (dataJson.getStatus()) {
                resultMap.put("code", 200);
                resultMap.put("msg", "更新成功");
                return WeaResult.success(resultMap);
            } else {
                return WeaResult.fail(dataJson.getMessage());
            }
        } catch (Exception e) {
            log.error("调用{} 查询案件附件数据异常：{};{}", methodName, e.getMessage(), e);
            return WeaResult.fail(e.getMessage());
        }
    }

}
