package com.weaver.seconddev.zyhlw.action.outlay;


import cn.hutool.core.map.MapUtil;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface;
import com.weaver.seconddev.zyhlw.domain.request.eb.EbFormDataReq;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import com.weaver.seconddev.zyhlw.util.ebuilder.EBuilderUtil;
import com.weaver.seconddev.zyhlw.util.enums.DsLogicGroupIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.weaver.seconddev.zyhlw.util.ConvertDataUtils.convertMapKeyToLowerCase;

/**
 * 活动单提交成功更新预算单记录预算剩余金额
 * <p>
 * 原E9项目com.zyhlw.web.ActivitySheetSubmissionAction
 *
 * <AUTHOR>
 * @date 2025年06月26日 14:09
 */
@Service("cmicActivitySheetSubmissionAction")
@Slf4j
@SuppressWarnings("SpellCheckingInspection")
public class ActivitySheetSubmissionAction implements EsbServerlessRpcRemoteInterface {

    private static final String SIMPLE_CLASS_NAME = ActivitySheetSubmissionAction.class.getSimpleName();


    @Resource
    private IDataSqlService dataSqlService;

    @Resource
    private IDataBaseService dataBaseService;

    @Resource
    private CmicProperties cmicProperties;

    @Resource
    private IOpenPlatformService openPlatformService;

    @Override
    public WeaResult<Map<String, Object>> execute(Map<String, Object> params) {
        String userId = MapUtil.getStr(params, "userId");
        if (StringUtils.isEmpty(userId)) {
            return WeaResult.fail("用户id不能为空");
        }
        try {
            //关联预算单号
            String orderNo = MapUtil.getStr(params, "guan_lysd");
            //根据关联预算单号获取预算剩余金额
            Float budgetAmount = getBudgetAmount(orderNo);
            //活动总预算金额
            Float activityAmount = MapUtil.getFloat(params, "yu_szje");

            //判断是否 如活动总预算＞剩余预算金额
            if (activityAmount > budgetAmount) {
                return WeaResult.fail("活动总预算不能大于剩余预算金额，请核实后再提交");
            }

            //预算剩余金额 = 预算剩余金额 + 活动总预算金额
            Float newBudgetAmount = budgetAmount + activityAmount;
            //更新预算单记录预算剩余金额
            List<Map<String, Object>> updateDataList = new ArrayList<>();
            Map<String, Object> updateData = new HashMap<>();
            updateData.put("id", orderNo);
            updateData.put("yu_ssyje", newBudgetAmount);
            updateDataList.add(Collections.singletonMap("mainTable", updateData));

            String tableId = dataBaseService.getBaseValue("uf_jing_fysgl", "objId");
            EbFormDataReq ebFormDataReq = new EbFormDataReq.Builder()
                    .userId(userId)
                    .objId(tableId)
                    .datas(updateDataList)
                    .build();
            //调用更新接口
            EBuilderUtil.updateFormDataV2(ebFormDataReq, openPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());

            log.info("{}:更新预算单记录预算剩余金额成功", SIMPLE_CLASS_NAME);

            return WeaResult.success(Collections.singletonMap("success", true));
        } catch (Exception e) {
            log.error("{}: 执行异常", SIMPLE_CLASS_NAME, e);
            return WeaResult.fail("系统异常：" + e.getMessage());
        }
    }

    /**
     * 获取预算单剩余金额
     *
     * @param orderNo 预算单号
     * @return 预算单剩余金额
     * <AUTHOR>
     * @date 2025年06月26日 13:50
     */
    private Float getBudgetAmount(String orderNo) {
        String sql = "select yu_ssyje from uf_jing_fysgl where id = ?";
        Map<String, Object> data = convertMapKeyToLowerCase(
                dataSqlService.executeCommonSqlOne(sql, SourceType.LOGIC,
                        DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId(), Collections.singletonList(orderNo)));
        return MapUtil.getFloat(data, "yu_ssyje");

    }
}
