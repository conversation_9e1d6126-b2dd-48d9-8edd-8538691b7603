package com.weaver.seconddev.zyhlw.action.park;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.util.constant.WorkingSpaceConstants;
import com.weaver.seconddev.zyhlw.util.park.WorkingSpaceUtil;

import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 员工工位申请使用、退还、调整、续期流程
 * <p>
 * type
 * <p>
 * 1 : 超过到期时间就把工位释放
 * <p>
 * 原E9项目Action com.zyhlw.web.area6.Area6GongWeiAction
 *
 * <AUTHOR>
 * @date 2025年04月29日 17:27
 */
@Service("cmicArea6GongWeiAction")
@Slf4j
public class Area6GongWeiAction implements EsbServerlessRpcRemoteInterface {
    private final String method = "调用" + Area6GongWeiAction.class.getSimpleName() + ".execute()-->";

    @Resource
    private IDataSqlService dataSqlService;

    @Resource
    private IDataBaseService dataBaseService;

    @Resource
    private WorkingSpaceUtil workingSpaceUtil;

    @Override
    public WeaResult<Map<String, Object>> execute(Map<String, Object> params) {
        log.info("{} 请求参数：{}", method, JSON.toJSONString(params));
        Map<String, Object> result = new HashMap<>();
        Integer type = MapUtil.getInt(params, "type");
        String userId = MapUtil.getStr(params, "userId");
        if (type == 1) {
            // 超过到期时间就把工位释放
            releaseStation(userId);
        } else {
            result.put("code", 1);
            result.put("msg", "类型错误");
        }
        return WeaResult.success(result);
    }

    private void releaseStation(String userId) {
        // 到期时间小于当前时间且工位状态为正在使用
        String sql = "select * from uf_gong_wxx where dao_qsj < to_char(sysdate,'YYYY-mm-dd') and gong_wzt = 4";
        List<Map<String, Object>> datas = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        List<Map<String, Object>> pepoleUpdateDataList = new ArrayList<>();
        List<Map<String, Object>> stationUpdateDataList = new ArrayList<>();
        if (!datas.isEmpty()) {
            datas.forEach(data -> {
                // 更新人员信息表
                Map<String, Object> pepoleMainTable = new HashMap<>();
                Map<String, Object> pepoleUpdateData = new HashMap<>();
                pepoleUpdateData.put("id", MapUtil.getStr(data, "shi_yr"));
                pepoleUpdateData.put("shi_fsqgw", "1");
                pepoleUpdateData.put("gong_wsqbm", "");
                pepoleMainTable.put("mainTable", pepoleUpdateData);
                pepoleUpdateDataList.add(pepoleMainTable);

                // 更新工位信息表
                Map<String, Object> stationMainTable = new HashMap<>();
                Map<String, Object> stationUpdateData = new HashMap<>();
                stationUpdateData.put("id", MapUtil.getStr(data, "id"));
                stationUpdateData.put("gong_wzt", "2");
                stationUpdateData.put("shi_yr", "");
                stationUpdateData.put("lian_xfs", "");
                stationUpdateData.put("suo_sdw", "");
                stationUpdateData.put("dao_qsj", "");
                stationUpdateData.put("ren_ylx", "");
                stationMainTable.put("mainTable", stationUpdateData);
                stationUpdateDataList.add(stationMainTable);

            });

            if (!pepoleUpdateDataList.isEmpty()) {
                // 获取ObjId
                String hrmInfoTableObjId = dataBaseService.getBaseValue(WorkingSpaceConstants.HRM_INFO_TABLE, "objId");
                log.info("获取到的人员信息表ObjId: {}", hrmInfoTableObjId);

                workingSpaceUtil.batchProcessData(pepoleUpdateDataList, userId, hrmInfoTableObjId, "释放人员工位数据",
                        null);
            }

            if (!stationUpdateDataList.isEmpty()) {
                // 获取ObjId
                String seatInfoTableObjId = dataBaseService.getBaseValue(WorkingSpaceConstants.SEAT_INFO_TABLE,
                        "objId");
                log.info("获取到的工位信息表ObjId: {}", seatInfoTableObjId);

                workingSpaceUtil.batchProcessData(stationUpdateDataList, userId, seatInfoTableObjId, "释放工位数据",
                        null);
            }

        }
    }
}
