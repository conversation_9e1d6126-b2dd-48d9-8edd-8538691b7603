package com.weaver.seconddev.zyhlw.action.park;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface;
import com.weaver.seconddev.zyhlw.domain.request.eb.EbFormDataReq;
import com.weaver.seconddev.zyhlw.domain.response.form.EbFormDataResultVo;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.SmsGongUtil;
import com.weaver.seconddev.zyhlw.util.ebuilder.EBuilderUtil;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.weaver.seconddev.zyhlw.util.SmsGongUtil.*;

/**
 * 工位管理手动生成短信操作
 * <p/>
 * 原E9代码 com.zyhlw.web.sms.SmsGongSendAction
 *
 * <AUTHOR>
 * @date 2025年05月20日 16:30
 */
@Service("cmicSmsGongSendAction")
@Slf4j
@SuppressWarnings("SpellCheckingInspection")
public class SmsGongSendAction implements EsbServerlessRpcRemoteInterface {
    /**
     * 手动发送明细表名
     */
    private static final String HAND_SEND_DETAIL_TABLE_NAME = "uf_gong_handsend_dt1";

    /**
     * 发送状态：未发送
     */
    private static final String SEND_STATUS_NOT_SENT = "0";

    /**
     * 发送类型：手动发送
     */
    private static final String SEND_TYPE_MANUAL = "0";

    /**
     * 安全级别：员工级别
     */
    private static final int SECURITY_LEVEL_EMPLOYEE = 50;
    /**
     * 安全级别：部门级别
     */
    private static final int SECURITY_LEVEL_DEPARTMENT = 80;

    @Resource
    private IDataSqlService dataSqlService;

    @Resource
    private IDataBaseService dataBaseService;

    @Resource
    private CmicProperties cmicProperties;

    @Resource
    private IOpenPlatformService openPlatformService;

    @Override
    public WeaResult<Map<String, Object>> execute(Map<String, Object> params) {
        log.info("【工位管理手动发送短信按钮操作】开始执行,执行方法:SmsGongSendAction.execute");

        // 参数校验
        Long sendId = MapUtil.getLong(params, "hand_send_id");
        if (sendId == null) {
            return WeaResult.fail("发送短信id不能为空");
        }
        Long userId = MapUtil.getLong(params, "userId");
        if (userId == null) {
            return WeaResult.fail("用户id不能为空");
        }

        try {
            // 获取短信表单ID
            String smsTableObjId = dataBaseService.getBaseValue(SmsGongUtil.SMS_TABLE_NAME, "objId");

            // 查询需要发送短信的数据
            List<Map<String, Object>> dataList = querySendData(sendId);
            if (CollectionUtils.isEmpty(dataList)) {
                return WeaResult.fail("未查询到发送短信数据, id:" + sendId);
            }

            // 转换数据，将Map的key转为小写
            List<Map<String, Object>> lowerCaseDataList = convertKeysToLowerCase(dataList);

            // 组装短信数据
            List<Map<String, Object>> saveDataList = assembleSmsDataList(lowerCaseDataList, smsTableObjId, sendId);

            // 保存短信数据并更新发送状态
            return saveSmsDataAndUpdateStatus(userId, sendId, smsTableObjId, saveDataList);
        } catch (Exception e) {
            log.error("【工位管理手动发送短信按钮操作】执行异常", e);
            return WeaResult.fail("发送短信失败：" + e.getMessage());
        }
    }

    /**
     * 查询需要发送短信的数据
     *
     * @param sendId 发送ID
     * @return 短信数据列表
     */
    private List<Map<String, Object>> querySendData(Long sendId) {
        String querySql = " select\n" +
                "  dt.loginid,\n" +
                "  dt.jsrxm,\n" +
                "  dt.sjhm,\n" +
                "  dt.fslx,\n" +
                "  dt.fszt,\n" +
                "  dt.location,\n" +
                "  dt.id,\n" +
                "  sms.content,\n" +
                "  sms.creator,\n" +
                "  dt.seclevel\n" +
                "from\n" +
                "  " + HAND_SEND_DETAIL_TABLE_NAME + " dt\n" +
                "  inner join uf_gong_handsend sms on dt.form_data_id = sms.id\n" +
                "where\n" +
                "  dt.fszt = ? and sms.id = ?";

        List<String> params = new ArrayList<>();
        params.add(SEND_STATUS_NOT_SENT);
        params.add(String.valueOf(sendId));

        return dataSqlService.eBuilderFromSqlAll(querySql, SourceType.LOGIC, params);
    }

    /**
     * 将Map的key转为小写
     *
     * @param dataList 原始数据列表
     * @return 转换后的数据列表
     */
    private List<Map<String, Object>> convertKeysToLowerCase(List<Map<String, Object>> dataList) {
        return dataList.stream()
                .map(data -> data.entrySet().stream()
                        .collect(Collectors.toMap(
                                entry -> entry.getKey().toLowerCase(),
                                Map.Entry::getValue,
                                (v1, v2) -> v1, // 如果有重复键，保留第一个值
                                HashMap::new // 使用HashMap作为结果容器
                        )))
                .collect(Collectors.toList());
    }

    /**
     * 组装短信数据列表
     *
     * @param dataList      原始数据列表
     * @param smsTableObjId 短信表单ID
     * @return 组装后的短信数据列表
     */
    private List<Map<String, Object>> assembleSmsDataList(List<Map<String, Object>> dataList, String smsTableObjId, Long sendId) {
        List<Map<String, Object>> saveDataList = new ArrayList<>();

        dataList.forEach(data -> {
            // 提取数据
            String loginId = MapUtil.getStr(data, "loginid");
            String jsrxm = MapUtil.getStr(data, "jsrxm");
            String sjhm = MapUtil.getStr(data, "sjhm");
            String fslx = MapUtil.getStr(data, "fslx");
            String location = MapUtil.getStr(data, "location");
            String content = MapUtil.getStr(data, "content");
            String creator = MapUtil.getStr(data, "creator");
            String dataId = MapUtil.getStr(data, "id");

            // 安全级别处理
            String secLevel = MapUtil.getStr(data, "seclevel", "10");
            int secLevelValue = Integer.parseInt(StringUtils.isBlank(secLevel) ? "10" : secLevel);

            // 生成短信内容和链接URL
            String[] contentAndUrl = generateContentAndUrl(fslx, content, jsrxm, location, secLevelValue,
                    sendId);
            String smsContent = contentAndUrl[0];
            String linkUrl = contentAndUrl[1];

            // 创建短信数据项
            Map<String, Object> dataMap = createSmsDataItem(
                    smsTableObjId, creator, dataId, loginId, sjhm, jsrxm,
                    location, smsContent, linkUrl, secLevel);

            saveDataList.add(dataMap);
        });

        return saveDataList;
    }

    /**
     * 生成短信内容和链接URL
     *
     * @param sendType      发送类型
     * @param content       原始内容
     * @param receiverName  接收人姓名
     * @param location      位置
     * @param secLevelValue 安全级别值
     * @param sendId        发送ID
     * @return 包含内容和链接的数组，索引0为内容，索引1为URL
     */
    private String[] generateContentAndUrl(String sendType, String content, String receiverName,
            String location, int secLevelValue, Long sendId) {
        String smsContent;
        String linkUrl;

        if (SEND_TYPE_MANUAL.equals(sendType)) {
            // 手动发送处理
            smsContent = content.trim()
                    .replace("{username}", receiverName)
                    .replace("{location}", location);
            // 再次替换可能剩余的模板标记
            smsContent = smsContent.trim()
                    .replace("{username}", "")
                    .replace("{location}", "");

            // 根据安全级别选择不同的链接
            if (secLevelValue < SECURITY_LEVEL_EMPLOYEE) {
                linkUrl = "/spa/mobile/job/jobhanddetail.jsp?smsid=" + sendId; // 员工h5页面地址
            } else if (secLevelValue < SECURITY_LEVEL_DEPARTMENT) {
                linkUrl = "/spa/mobile/job/jobhanddetail2.jsp?smsid=" + sendId; // 部门h5页面地址
            } else {
                linkUrl = "/spa/mobile/job/jobhanddetail3.jsp?smsid=" + sendId; // 公司h5页面地址
            }
        } else {
            // 系统发送处理
            smsContent = receiverName + "您好，工位系统已为您在新大楼安排了办公宝座，工位号：";
            linkUrl = "/spa/mobile/job/jobdetail.jsp?smsid=" + sendId;
        }

        return new String[] { smsContent, linkUrl };
    }

    /**
     * 创建短信数据项
     */
    private Map<String, Object> createSmsDataItem(String smsTableObjId, String creator, String dataId,
            String loginId, String sjhm, String jsrxm, String location,
            String smsContent, String linkUrl, String secLevel) {
        Map<String, Object> employeeInfoMap = dataBaseService.getEmployeeInfo(creator);

        Map<String, Object> dataMap = new HashMap<>();
        LocalDateTime createTime = LocalDateTime.now();
        Map<String, Object> dataItem = new HashMap<>();

        // 表单基本信息
        dataItem.put("formmodeid", smsTableObjId);
        dataItem.put("create_time", createTime.format(DATE_TIME_FORMATTER));// 年月日

        // 订单信息
        dataItem.put("requestid", dataId);
        dataItem.put("orderId", dataId);
        dataItem.put("orderName", "");
        dataItem.put("creator", employeeInfoMap.get("login_value".toUpperCase()));
        dataItem.put("createTime", createTime.format(DATE_TIME_FORMATTER));

        // 接收人信息
        dataItem.put("receiver", loginId); // 接收人账号
        dataItem.put("receivermobile", sjhm);// 接收人帐号手机
        dataItem.put("receivername", jsrxm);// 接收人姓名
        dataItem.put("enterTime", createTime.format(DATE_TIME_FORMATTER));

        // 短信内容
        dataItem.put("description", smsContent);
        dataItem.put("location", location);

        // 短信配置
        String uuid = IdUtil.fastSimpleUUID();
        dataItem.put("linkUrl", linkUrl); // 访问地址
        dataItem.put("isSend", SmsGongUtil.SMS_IS_SEND_NO);// 是否发送
        dataItem.put("sendType", SmsGongUtil.SMS_SEND_TYPE_SMS);// 发送类型
        dataItem.put("smsId", uuid);// 消息id
        dataItem.put("smsType", "0");// 消息类型
        dataItem.put("seclevel", secLevel);
        dataItem.put("cardcontent", "");// 卡片信息

        dataMap.put("mainTable", dataItem);
        return dataMap;
    }

    /**
     * 保存短信数据并更新发送状态
     *
     * @param userId        用户ID
     * @param sendId        发送ID
     * @param smsTableObjId 短信表单ID
     * @param saveDataList  要保存的短信数据列表
     * @return 操作结果
     */
    private WeaResult<Map<String, Object>> saveSmsDataAndUpdateStatus(Long userId, Long sendId,
            String smsTableObjId,
            List<Map<String, Object>> saveDataList) {
        Map<String, Object> result = new HashMap<>();

        if (CollectionUtils.isEmpty(saveDataList)) {
            return WeaResult.fail("没有可发送的短信数据");
        }

        try {
            // 1. 保存短信数据
            WeaResult<Map<String, Object>> saveResult = SmsGongUtil.saveSmsData(
                    cmicProperties.getOpenPlatformUrl(),
                    userId,
                    smsTableObjId,
                    saveDataList,
                    openPlatformService.getAccessToken());

            // 2. 更新发送状态
            updateSendStatus(userId, sendId, saveResult.isStatus());

            log.info("【工位管理手动发送短信按钮操作】执行成功");

            result.put("code", saveResult.getCode());
            result.put("msg", saveResult.getMsg());
            return WeaResult.success(result);
        } catch (Exception e) {
            log.error("【工位管理手动发送短信按钮操作】保存数据失败", e);
            return WeaResult.fail("发送失败: " + e.getMessage());
        }
    }

    /**
     * 更新发送状态
     *
     * @param userId 用户ID
     * @param sendId 发送ID
     */
    private void updateSendStatus(Long userId, Long sendId, Boolean status) {
        try {
            // 获取手动发送明细表的objId
            String objId = dataBaseService.getBaseValue("uf_gong_handsend", "objId");

            // 构建更新数据
            List<Map<String, Object>> datas = new ArrayList<>();
            Map<String, Object> data = new HashMap<>();
            data.put("id", sendId); // 更新状态为已发送
            data.put("fszt", status ? 1 : 2); // 判断是否发送成功，1：发送成功，2：发送失败

            Map<String, Object> mainTable = new HashMap<>();
            mainTable.put("detail1", data);
            datas.add(mainTable);

            EbFormDataReq req = new EbFormDataReq.Builder()
                    .userId(userId.toString())
                    .objId(objId)
                    .datas(datas)
                    .build();

            // 发送请求
            EbFormDataResultVo updateResult = EBuilderUtil.updateFormDataV2(
                    req,
                    openPlatformService.getAccessToken(),
                    cmicProperties.getOpenPlatformUrl());

            // 检查更新结果
            if (updateResult == null || updateResult.getData() == null ||
                    updateResult.getData().getDataJson() == null ||
                    updateResult.getData().getDataJson().isEmpty() ||
                    !updateResult.getData().getDataJson().get(0).getStatus()) {
                String errorMsg = updateResult != null && updateResult.getData() != null &&
                        updateResult.getData().getDataJson() != null &&
                        !updateResult.getData().getDataJson().isEmpty()
                                ? updateResult.getData().getDataJson().get(0).getMessage()
                                : "更新发送状态失败";
                log.warn("【工位管理手动发送短信按钮操作】更新发送状态失败: {}", errorMsg);
            }
        } catch (Exception e) {
            log.error("【工位管理手动发送短信按钮操作】更新发送状态失败", e);
        }
    }
}
