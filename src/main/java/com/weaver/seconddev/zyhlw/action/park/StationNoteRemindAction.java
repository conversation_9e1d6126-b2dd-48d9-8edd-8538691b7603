package com.weaver.seconddev.zyhlw.action.park;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.service.impl.export.converter.Converter;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.constant.WorkingSpaceConstants;
import com.weaver.seconddev.zyhlw.util.converter.ConverterUtil;
import com.weaver.seconddev.zyhlw.util.ebuilder.EBuilderUtil;
import com.weaver.seconddev.zyhlw.util.enums.DsLogicGroupIdEnum;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

import static com.weaver.seconddev.zyhlw.util.SmsGongUtil.*;

/**
 * 工位员工工位申请使用、退还、调整、续期流程 归档后触发 插入短信提醒表
 * <p>
 * 原E9项目Action com.zyhlw.web.StationNoteRemindAction
 *
 * <AUTHOR>
 * @date 2025年04月29日 17:21
 */
@Service("cmicStationNoteRemindAction")
@Slf4j
@SuppressWarnings("SpellCheckingInspection")
public class StationNoteRemindAction implements EsbServerlessRpcRemoteInterface {
    private final String method = "调用" + StationNoteRemindAction.class.getSimpleName() + ".execute()-->";

    @Resource
    private IOpenPlatformService openPlatformService;

    @Resource
    private IDataSqlService dataSqlService;

    @Resource
    private ConverterUtil converterUtil;

    @Resource
    private IDataBaseService dataBaseService;

    @Resource
    private CmicProperties cmicProperties;

    private static final int SUCCESS_CODE = 0;
    private static final String SUCCESS_MESSAGE = "插入成功";
    private static final String DETAIL_PREFIX = "detail_";

    @Override
    public WeaResult<Map<String, Object>> execute(Map<String, Object> params) {
        log.info("{} 请求参数：{}", method, JSON.toJSONString(params));
        log.info("工位员工工位申请使用、退还、调整、续期流程  归档后触发");

        try {
            // 提取基本参数
            BasicParamsDTO basicParams = extractBasicParams(params);

            // 获取申请人信息
            Map<String, Object> employeeInfoMap = dataBaseService
                    .getEmployeeInfo(basicParams.getApplicantId().toString());
            if (MapUtil.isEmpty(employeeInfoMap)) {
                log.warn("获取到的申请账号信息为空! shen_qr:{}", basicParams.getApplicantId());
                return WeaResult.success(new HashMap<>());
            }

            // 处理操作类型
            if (basicParams.getOperatorType() == null
                    || OperatorType.WORK_STATION_RETURN.equals(basicParams.getOperatorType())) {
                return WeaResult.success(new HashMap<>());
            }

            // 处理明细数据
            processDetailData(params, basicParams, employeeInfoMap);

            // 返回成功结果
            Map<String, Object> result = new HashMap<>();
            result.put("code", SUCCESS_CODE);
            result.put("msg", SUCCESS_MESSAGE);
            return WeaResult.success(result);
        } catch (Exception e) {
            log.error("执行异常：{}", e.getMessage(), e);
            return WeaResult.fail("系统异常：" + e.getMessage());
        }
    }

    /**
     * 提取基本参数
     *
     * @param params 请求参数
     * @return 基本参数DTO
     */
    private BasicParamsDTO extractBasicParams(Map<String, Object> params) {
        return BasicParamsDTO.builder()
                .requestId(MapUtil.getStr(params, "requestId"))
                .requestName(MapUtil.getStr(params, "requestName"))
                .operator(MapUtil.getStr(params, "operator"))
                .userId(MapUtil.getLong(params, "userId"))
                .applicantId(MapUtil.getLong(params, "shen_qr"))
                .createDate(MapUtil.getStr(params, "shen_qrq"))
                .operatorType(OperatorType.getOperatorType(MapUtil.getInt(params, "cao_zlx")))
                .build();
    }

    /**
     * 处理明细数据
     *
     * @param params          请求参数
     * @param basicParams     基本参数
     * @param employeeInfoMap 员工信息
     */
    private void processDetailData(Map<String, Object> params, BasicParamsDTO basicParams,
            Map<String, Object> employeeInfoMap) {
        // 获取明细表数据
        String detailKey = DETAIL_PREFIX + (basicParams.getOperatorType().getCode() + 1);
        Object detailValue = params.get(detailKey);

        // 处理空值或空字符串的情况
        if (detailValue == null || "".equals(detailValue) || "[]".equals(detailValue)) {
            log.warn("明细表数据为空: {}", detailKey);
            return;
        }

        List<Map<String, Object>> detailDataList;
        try {
            // 如果是字符串，尝试解析JSON
            if (detailValue instanceof String) {
                // 使用FastJSON直接解析为List<Map<String, Object>>
                detailDataList = JSON.parseObject((String) detailValue,
                        new com.alibaba.fastjson.TypeReference<List<Map<String, Object>>>() {
                        });
            } else {
                // 使用Hutool转换，但增加异常处理
                detailDataList = MapUtil.get(params, detailKey, new TypeReference<List<Map<String, Object>>>() {
                });
            }

            if (detailDataList == null || detailDataList.isEmpty()) {
                log.warn("明细表数据为空数组: {}", detailKey);
                return;
            }
        } catch (Exception e) {
            log.error("解析明细表数据异常: {}, 值: {}", e.getMessage(), detailValue, e);
            return;
        }

        log.info("detailDataList: {}", JSON.toJSONString(detailDataList));

        // 处理每条明细数据
        detailDataList.forEach(detailMap -> {
            // 创建工位明细DTO
            StationDetailDTO detailDTO = StationDetailDTO.builder()
                    .usedList(new ArrayList<>())
                    .receiverList(new ArrayList<>())
                    .receiverMobileList(new ArrayList<>())
                    .receiverNameList(new ArrayList<>())
                    .gongWbhList(new ArrayList<>())
                    .zuoHList(new ArrayList<>())
                    .banGlList(new ArrayList<>())
                    .louCList(new ArrayList<>())
                    .quYList(new ArrayList<>())
                    .build();

            // 根据操作类型处理数据
            processOperatorTypeData(basicParams.getOperatorType(), detailMap, detailDTO);

            // 获取用户和工位信息
            fetchUserAndStationInfo(detailDTO);

            // 获取ObjId
            String smsTableObjId = dataBaseService.getBaseValue(SMS_TABLE_NAME, "objId");

            // 组装短信数据
            SmsDataDTO smsDataDTO = assembleSmsDataDTO(basicParams, employeeInfoMap, detailDTO, smsTableObjId);

            // 组装存入表单的数据
            List<Map<String, Object>> dataList = assembleSmsData(smsDataDTO);

            // 插入表单
            if (!CollectionUtils.isEmpty(dataList)) {
                EBuilderUtil.saveFormDataV2(cmicProperties.getOpenPlatformUrl(),
                        basicParams.getUserId(), smsTableObjId, dataList, openPlatformService.getAccessToken());
            }
        });
    }

    /**
     * 组装短信数据DTO
     */
    private SmsDataDTO assembleSmsDataDTO(BasicParamsDTO basicParams, Map<String, Object> employeeInfoMap,
            StationDetailDTO detailDTO, String smsTableObjId) {
        return SmsDataDTO.builder()
                .smsTableObjId(smsTableObjId)
                .requestId(basicParams.getRequestId())
                .requestName(basicParams.getRequestName())
                .operator(basicParams.getOperator())
                .employeeInfoMap(employeeInfoMap)
                .createDate(basicParams.getCreateDate())
                .usedList(detailDTO.getUsedList())
                .receiverList(detailDTO.getReceiverList())
                .receiverMobileList(detailDTO.getReceiverMobileList())
                .receiverNameList(detailDTO.getReceiverNameList())
                .gongWbhList(detailDTO.getGongWbhList())
                .zuoHList(detailDTO.getZuoHList())
                .banGlList(detailDTO.getBanGlList())
                .louCList(detailDTO.getLouCList())
                .quYList(detailDTO.getQuYList())
                .operatorType(basicParams.getOperatorType())
                .build();
    }

    /**
     * 获取用户和工位信息
     */
    private void fetchUserAndStationInfo(StationDetailDTO detailDTO) {
        // 从人员信息表中获取工位使用者的手机号码
        detailDTO.getUsedList().forEach(gongWsyzId -> {
            String sql = "select * from " + WorkingSpaceConstants.HRM_INFO_TABLE + " where id = ?";
            List<String> sqlParams = new ArrayList<>();
            sqlParams.add(gongWsyzId);
            Map<String, Object> data = dataSqlService.executeCommonSqlOne(
                    sql, SourceType.LOGIC, DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId(), sqlParams);
            detailDTO.getReceiverList().add(MapUtil.getStr(data, "ZHANG_H"));
            detailDTO.getReceiverMobileList().add(MapUtil.getStr(data, "SHOU_J"));
            detailDTO.getReceiverNameList().add(MapUtil.getStr(data, "XING_M"));
        });

        // 从工位信息表中获取工位座号
        detailDTO.getGongWbhList().forEach(gongWbhId -> {
            String sql = "select * from " + WorkingSpaceConstants.SEAT_INFO_TABLE + " where id = ?";
            List<String> sqlParams = new ArrayList<>();
            sqlParams.add(gongWbhId);
            Map<String, Object> data = dataSqlService.executeCommonSqlOne(
                    sql, SourceType.LOGIC, DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId(), sqlParams);
            detailDTO.getZuoHList().add(MapUtil.getStr(data, "ZUO_H"));
        });
    }

    /**
     * 根据操作类型处理数据
     */
    private void processOperatorTypeData(OperatorType operatorType, Map<String, Object> detailMap,
            StationDetailDTO detailDTO) {
        switch (operatorType) {
            case WORK_STATION_USE:
                processWorkStationUse(detailMap, detailDTO);
                break;
            case WORK_STATION_CHANGE:
                processWorkStationChange(detailMap, detailDTO);
                break;
            case WORK_STATION_SWAP:
                processWorkStationSwap(detailMap, detailDTO);
                break;
            case WORK_STATION_RENEWAL:
                processWorkStationRenewal(detailMap, detailDTO);
                break;
            default:
                break;
        }
    }

    /**
     * 处理工位使用场景
     */
    private void processWorkStationUse(Map<String, Object> detailMap, StationDetailDTO detailDTO) {
        detailDTO.getBanGlList().add(processSelectValue("ban_gl", MapUtil.getStr(detailMap, "ban_gl")));
        detailDTO.getLouCList().add(processSelectValue("bei_z", MapUtil.getStr(detailMap, "lou_c")));
        detailDTO.getQuYList().add(processSelectValue("qu_y", MapUtil.getStr(detailMap, "qu_y")));
        detailDTO.getGongWbhList().add(MapUtil.getStr(detailMap, "gong_wbh"));// 工位编号
        detailDTO.getUsedList().add(MapUtil.getStr(detailMap, "gong_wsyz"));// 工位使用者
    }

    /**
     * 处理工位变更场景
     */
    private void processWorkStationChange(Map<String, Object> detailMap, StationDetailDTO detailDTO) {
        detailDTO.getBanGlList().add(processSelectValue("ban_gl", MapUtil.getStr(detailMap, "ban_gl1")));
        detailDTO.getLouCList().add(processSelectValue("bei_z", MapUtil.getStr(detailMap, "lou_c1")));
        detailDTO.getQuYList().add(processSelectValue("qu_y", MapUtil.getStr(detailMap, "qu_y1")));
        detailDTO.getGongWbhList().add(MapUtil.getStr(detailMap, "xin_gwbh"));// 新工位
        detailDTO.getUsedList().add(MapUtil.getStr(detailMap, "gong_wsyz"));// 工位使用者
    }

    /**
     * 处理工位调换场景
     */
    private void processWorkStationSwap(Map<String, Object> detailMap, StationDetailDTO detailDTO) {
        detailDTO.getBanGlList().add(processSelectValue("ban_gl", MapUtil.getStr(detailMap, "ban_gl11")));
        detailDTO.getLouCList().add(processSelectValue("bei_z", MapUtil.getStr(detailMap, "lou_c11")));
        detailDTO.getQuYList().add(processSelectValue("qu_y", MapUtil.getStr(detailMap, "qu_y11")));
        detailDTO.getGongWbhList().add(MapUtil.getStr(detailMap, "gong_wbh11"));// 工位编号1-1

        detailDTO.getBanGlList().add(processSelectValue("ban_gl", MapUtil.getStr(detailMap, "ban_gl22")));
        detailDTO.getLouCList().add(processSelectValue("bei_z", MapUtil.getStr(detailMap, "lou_c22")));
        detailDTO.getQuYList().add(processSelectValue("qu_y", MapUtil.getStr(detailMap, "qu_y22")));
        detailDTO.getGongWbhList().add(MapUtil.getStr(detailMap, "gong_wbh22"));// 工位编号2-2

        detailDTO.getUsedList().add(MapUtil.getStr(detailMap, "diao_hz11"));// 工位调换者1-1
        detailDTO.getUsedList().add(MapUtil.getStr(detailMap, "diao_hz22"));// 工位调换者2-2
    }

    /**
     * 处理工位续期场景
     */
    private void processWorkStationRenewal(Map<String, Object> detailMap, StationDetailDTO detailDTO) {
        detailDTO.getUsedList().add(MapUtil.getStr(detailMap, "gong_wsyzhzhb"));// //工位使用者(合作伙伴) 工位续期明细表
        detailDTO.getBanGlList().add(processSelectValue("ban_gl", MapUtil.getStr(detailMap, "ban_gl")));
        detailDTO.getLouCList().add(processSelectValue("bei_z", MapUtil.getStr(detailMap, "lou_c")));
        detailDTO.getQuYList().add(processSelectValue("qu_y", MapUtil.getStr(detailMap, "qu_y")));
        detailDTO.getGongWbhList().add(MapUtil.getStr(detailMap, "xu_qgwbh"));// 续期工位编号
    }

    /**
     * 组装存入uf_gong_sms表单的数据
     */
    private List<Map<String, Object>> assembleSmsData(SmsDataDTO smsDataDTO) {
        List<Map<String, Object>> dataList = new ArrayList<>();

        if (OperatorType.WORK_STATION_SWAP.equals(smsDataDTO.getOperatorType())) {
            for (int i = 0; i < 2; i++) {
                Map<String, Object> dataMap = new HashMap<>();
                Map<String, Object> dataItem = assembleDataItem(smsDataDTO, i);
                dataMap.put("mainTable", dataItem);
                dataList.add(dataMap);
            }
        } else {
            Map<String, Object> dataMap = new HashMap<>();
            Map<String, Object> dataItem = assembleDataItem(smsDataDTO, 0);
            dataMap.put("mainTable", dataItem);
            dataList.add(dataMap);
        }
        return dataList;
    }

    private Map<String, Object> assembleDataItem(SmsDataDTO smsDataDTO, int i) {
        LocalDateTime createTime = LocalDateTime.now();
        Map<String, Object> dataItem = new HashMap<>();
        dataItem.put("formmodeid", smsDataDTO.getSmsTableObjId());
        dataItem.put("create_time", createTime.format(DATE_TIME_FORMATTER));// 年月日
        dataItem.put("requestid", smsDataDTO.getRequestId());
        dataItem.put("orderId", smsDataDTO.getRequestId());
        dataItem.put("orderName", smsDataDTO.getRequestName());
        dataItem.put("creator", smsDataDTO.getEmployeeInfoMap().get("login_value".toUpperCase()));
        dataItem.put("createTime", smsDataDTO.getCreateDate());
        dataItem.put("receiver", smsDataDTO.getReceiverList().get(i)); // 接收人账号
        dataItem.put("receivermobile", smsDataDTO.getReceiverMobileList().get(i));// 接收人帐号手机
        dataItem.put("receivername", smsDataDTO.getReceiverNameList().get(i));// 接收人姓名
        dataItem.put("enterTime", createTime.format(DATE_TIME_FORMATTER));
        dataItem.put("description", smsDataDTO.getReceiverNameList().get(i) + "您好，工位系统已为您在新大楼安排了办公宝座，工位号：");
        dataItem.put("location",
                smsDataDTO.getBanGlList().get(i) + smsDataDTO.getLouCList().get(i) + smsDataDTO.getQuYList().get(i)
                        + "(" + smsDataDTO.getZuoHList().get(i) + "号)");

        String uuid = IdUtil.fastSimpleUUID();
        dataItem.put("linkUrl", "/spa/mobile/job/jobdetail.jsp?smsid=" + uuid); // 访问地址
        dataItem.put("isSend", SMS_IS_SEND_NO);// 是否发送
        dataItem.put("sendType", SMS_SEND_TYPE_SMS);// 发送类型
        dataItem.put("smsId", uuid);// 消息id
        dataItem.put("smsType", uuid);// 消息类型

        String secLevel = MapUtil.getStr(smsDataDTO.getEmployeeInfoMap(), "sec_level".toUpperCase());
        if (StringUtils.isEmpty(secLevel)) {
            secLevel = DEFAULT_SEC_LEVEL; // 安全级别，如果为空，则默认为10
        }
        dataItem.put("secLevel", secLevel);
        dataItem.put("cardcontent", "");// 卡片信息

        return dataItem;
    }

    /***
     * 处理选择框的值
     *
     * @param fieldName   选择框字段名
     * @param selectValue 选择框的值
     * @return 选择框的名称
     */
    private String processSelectValue(String fieldName, String selectValue) {
        Converter converter = converterUtil.getConverter(WorkingSpaceConstants.SEAT_INFO_TABLE, fieldName);
        return converter.convert(selectValue);
    }

    /**
     * 操作类型 0工位使用 1工位退还 2工位变更 3工位调换 4工位续期
     *
     * <AUTHOR>
     * @date 2025/5/15 13:46
     */
    @Getter
    public enum OperatorType {

        WORK_STATION_USE(0, "工位使用"),
        WORK_STATION_RETURN(1, "工位退还"),
        WORK_STATION_CHANGE(2, "工位变更"),
        WORK_STATION_SWAP(3, "工位调换"),
        WORK_STATION_RENEWAL(4, "工位续期");

        private final int code;
        private final String msg;

        OperatorType(int code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public static OperatorType getOperatorType(int code) {
            for (OperatorType value : OperatorType.values()) {
                if (value.getCode() == code) {
                    return value;
                }
            }
            return null;
        }
    }

    /**
     * 基本参数DTO
     */
    @Data
    @Builder
    private static class BasicParamsDTO {
        private String requestId; // 工单请求ID
        private String requestName; // 工单标题
        private String operator; // 操作人
        private Long userId; // 当前用户Id
        private Long applicantId; // 申请人Id
        private String createDate; // 工单创建日期
        private OperatorType operatorType; // 操作类型
    }

    /**
     * 短信数据DTO
     */
    @Data
    @Builder
    private static class SmsDataDTO {
        private String smsTableObjId; // 短信表单对象ID
        private String requestId; // 工单请求ID
        private String requestName; // 工单标题
        private String operator; // 操作人
        private Map<String, Object> employeeInfoMap; // 员工信息
        private String createDate; // 工单创建日期
        private List<String> usedList; // 工位使用者
        private List<String> receiverList; // 接收人账号
        private List<String> receiverMobileList; // 接收人帐号手机
        private List<String> receiverNameList; // 接收人姓名
        private List<String> gongWbhList; // 工位编号
        private List<String> zuoHList; // 工位座号
        private List<String> banGlList; // 办公楼
        private List<String> louCList; // 楼层
        private List<String> quYList; // 区域
        private OperatorType operatorType; // 操作类型
    }

    /**
     * 工位明细数据DTO
     */
    @Data
    @Builder
    private static class StationDetailDTO {
        private List<String> usedList; // 工位使用者
        private List<String> receiverList; // 接收人账号
        private List<String> receiverMobileList; // 接收人帐号手机
        private List<String> receiverNameList; // 接收人姓名
        private List<String> gongWbhList; // 工位编号
        private List<String> zuoHList; // 工位座号
        private List<String> banGlList; // 办公楼
        private List<String> louCList; // 楼层
        private List<String> quYList; // 区域
    }
}
