package com.weaver.seconddev.zyhlw.action.supervise;

import com.alibaba.fastjson.JSON;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface;
import com.weaver.openapi.pojo.flow.params.vo.FlowDataDetail;
import com.weaver.openapi.pojo.flow.params.vo.FlowFormData;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.workflow.WfcUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;



/**
 * <AUTHOR>
 */
@Slf4j
@Service("cmicFirstTopicEstablishmentAction")
public class FirstTopicEstablishmentAction implements EsbServerlessRpcRemoteInterface {
    private final String method = "调用" + FirstTopicEstablishmentAction.class.getSimpleName() + ".execute()-->";
    @Resource
    IDataSqlService dataSqlService;

    @Resource
    IOpenPlatformService iOpenPlatformService;

    @Resource
    CmicProperties cmicProperties;

    @Override
    public WeaResult<Map<String, Object>> execute(Map<String, Object> params) {
        log.info("{}  请求参数：{}", method, JSON.toJSONString(params));
        try {
        if (params.containsKey("xie_bdw") && params.containsKey("du_brwmc") && params.containsKey("mainId")) {
            String cooperateDepartment = String.valueOf(params.get("xie_bdw"));
            String taskName = String.valueOf(params.get("du_brwmc"));
            String userId = String.valueOf(params.get("userId"));
            String requestId = String.valueOf(params.get("requestId"));
            String workflowId = String.valueOf(params.get("workflowId"));

            if (cooperateDepartment.trim().isEmpty()) {
                return WeaResult.fail("xie_bdw为空！");
            }

            String[] departments = cooperateDepartment.split(",");

            List<FlowDataDetail> dataDetails = new ArrayList<>();
            for (String department : departments) {
                String format = String.format("%s（协办）-%s", taskName, getDeptName(department));

                FlowDataDetail map2 = new FlowDataDetail();
                map2.setContent(department);
                map2.setDataIndex(3L);
                map2.setDataKey("xie_bbm");
                dataDetails.add(map2);

                FlowDataDetail map3 = new FlowDataDetail();
                map3.setContent(format);
                map3.setDataIndex(3L);
                map3.setDataKey("xie_bbt");
                dataDetails.add(map3);
            }

            //插入第一议题明细3
            FlowFormData formData = new FlowFormData();
            formData.setDataDetails(dataDetails);
            WfcUtil.saveFormData(iOpenPlatformService.getAccessToken(), Long.parseLong(userId), requestId, workflowId, formData, cmicProperties.getOpenPlatformUrl());


        }
        Map<String, Object> result = new HashMap<>(2);
        result.put("code", 200);
        result.put("msg", "操作成功");
        log.info("{}  返回结果：{}", "调用FinanceCheckedAction.execute()", JSON.toJSONString(result));
        return WeaResult.success(result);
        }catch (Exception e){
            log.error("{}  系统内部错误：{}", "调用FinanceCheckedAction.execute()", e.getMessage());
            return WeaResult.fail("系统内部错误：" + e.getMessage());
        }
    }

    private String getDeptName(String departmentId) {
        String sql = "SELECT name FROM eteams.department WHERE delete_type = 0 AND tenant_key = 't6il1ypj4w' and STATUS = 1 and id = " + departmentId;
        Map<String, Object> map = dataSqlService.eBuilderFromSqlOne(sql, SourceType.LOGIC);
        return String.valueOf(map.get("name"));
    }
}
