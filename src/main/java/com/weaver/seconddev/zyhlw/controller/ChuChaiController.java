package com.weaver.seconddev.zyhlw.controller;

import com.weaver.common.authority.annotation.WeaPermission;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.seconddev.zyhlw.service.IChuChaiService;
import com.weaver.teams.security.context.UserContext;
import com.weaver.teams.security.user.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping({"/api/secondev/zyhlw/chuChai"})
public class ChuChaiController {

    String simpleName = ChuChaiController.class.getSimpleName();
    @Resource
    IChuChaiService iChuChaiService;

    @WeaPermission(publicPermission = true)
    @GetMapping("/holiday")
    WeaResult<List<Map<String,Object>>> holiday(){
        log.info("调用"+simpleName+".holiday()-->进入查询节假日接口");
        // 获取当前用户
        User currentUser = UserContext.getCurrentUser();

        return WeaResult.success(iChuChaiService.holiday(currentUser.getTenantKey(),currentUser.getEmployeeId()));
    }
}


