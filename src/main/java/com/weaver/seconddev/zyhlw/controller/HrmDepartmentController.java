package com.weaver.seconddev.zyhlw.controller;

import com.weaver.common.authority.annotation.WeaPermission;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.seconddev.zyhlw.domain.hrm.GetDepartmentNameByStaffIdRespVO;
import com.weaver.seconddev.zyhlw.service.IHrmDepartmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * hrm 部门管理
 *
 * <AUTHOR>
 * @date 2025年04月29日 15:52
 */
@Slf4j
@RestController
@RequestMapping({"/api/secondev/zyhlw/hrmDepartment"})
public class HrmDepartmentController {

    @Resource
    private IHrmDepartmentService hrmDepartmentService;

    /***
     * 根据id数组查询人员部门科室名称及id
     * 原e9接口: /zyhlw/web/common/HrmDepartmentApi/getDepartmentNameByStaffId
     *
     * <AUTHOR>
     * @date 2025/4/29 17:07
     * @param ids 人员id组，多个以逗号分隔
     * @return com.weaver.common.base.entity.result.WeaResult<com.weaver.seconddev.zyhlw.domain.hrm.GetDepartmentNameByStaffIdRespVO>
     */
    @GetMapping("/getDepartmentNameByStaffId")
    @WeaPermission(publicPermission = true)
    public WeaResult<GetDepartmentNameByStaffIdRespVO> getDepartmentNameByStaffId(@RequestParam("ids") String ids) {
        return WeaResult.success(hrmDepartmentService.getDepartmentNameByStaffId(ids));
    }
}
