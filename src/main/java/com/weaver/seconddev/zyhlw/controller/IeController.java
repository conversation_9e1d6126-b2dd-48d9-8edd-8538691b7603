package com.weaver.seconddev.zyhlw.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.weaver.ebuilder.common.util.StringUtil;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.service.ITokenService;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import com.weaver.seconddev.zyhlw.util.http.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.CookieStore;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.cookie.Cookie;
import org.apache.http.impl.client.DefaultHttpClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * Ie控制器
 */
@Slf4j
@RestController
@RequestMapping({"/papi/secondev/zyhlw/ie"})
public class IeController {
    String simpleName = IeController.class.getSimpleName();
    @Resource
    ITokenService iTokenService;
    @Resource
    IDataBaseService iDataBaseService;
    /**
     * OA的IE控制接口
     * @param response
     * @param token  使用portalToken
     * @param toUrl 需要跳转的链接
     */
    @GetMapping("/oa")
    void oa(HttpServletResponse response, @RequestParam String token,@RequestParam String toUrl){
        String method = String.format("调用%s.oa(%s,%s)-->",simpleName,token,toUrl);
        iTokenService.init();
        log.info("{}进入",method);
        String validateTokenInfoStr = iTokenService.validatePortalToken(token, "1");
        log.info("{}校验票据结果：{}",method,validateTokenInfoStr);
        JSONObject validateTokenInfoJson = JSON.parseObject(validateTokenInfoStr);
        String portalUserId = validateTokenInfoJson.getString("portalUserId");
        try {
//            String url = "http://10.17.25.2/sso.nsf/getSSOUrl";
//            String params  = "OpenAgent&sysaccount=portalsysuser&syspasswd=portalsyspass&userid="+portalUserId;
            String url = StringUtils.null2String(iDataBaseService.getBaseDataValue("SSO_TO_OA", "OA系统配置"), "http://oa2.cmic.chinamobile.com/sso.nsf/getSSOUrl?OpenAgent&sysaccount=portalsysuser&syspasswd=portalsyspass");
            String domainName = StringUtil.vString(iDataBaseService.getBaseDataValue("PARENT_DOMAIN_NAME", "OA系统配置"), "cmic.chinamobile.com");
            url = url+"&userid="+portalUserId;
            String requestURL = HttpUtils.sendGet(url, null);
            log.info("{}OA返回的数据：{}",method,requestURL);
            DefaultHttpClient httpclient = new DefaultHttpClient();
            HttpGet httpGet = new HttpGet(requestURL);
            // 执行get请求
            httpclient.execute(httpGet);
            CookieStore cookieStore = httpclient.getCookieStore();
            if(cookieStore != null){
                List<Cookie> cookies = cookieStore.getCookies();
                if(cookies != null){
                    log.info("{}返回的cookie数据：{}",method,JSON.toJSONString(cookies));
                    javax.servlet.http.Cookie targetCookie = null;
                    for(org.apache.http.cookie.Cookie cookie : cookies){
                        if(cookie!=null){
                            log.info("{}cookie名称：{}，cookie值：{}",method,cookie.getName(),cookie.getValue());
                            targetCookie = new javax.servlet.http.Cookie(cookie.getName(),cookie.getValue());
                            targetCookie.setPath("/");
                            targetCookie.setDomain(domainName);//cmic.chinamobile.com
                            targetCookie.setMaxAge(10740);
                            log.info("{}写入的Cookie：{}",method,JSON.toJSONString(targetCookie));
                            response.addCookie(targetCookie);
                        }
                    }
                }
            }
            try {
                Thread.sleep(1000); // 暂停 1 秒
                response.sendRedirect(toUrl);
            } catch (InterruptedException e) {
               log.error("{}线程暂停异常",method,e);
            }

        } catch (Exception e) {
            log.error("{}OA跳转异常",method,e);
        }
    }

    /**
     * 通用IE控制接口
     * @param response
     * @param token
     * @param toUrl
     */
    void universal(HttpServletResponse response,@RequestParam String token,@RequestParam String toUrl){

    }
}
