package com.weaver.seconddev.zyhlw.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.weaver.seconddev.zyhlw.service.ISsoService;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import com.weaver.seconddev.zyhlw.util.sso.SSOTokenUtil;
import com.weaver.seconddev.zyhlw.util.sso.ValidateTokenComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2025-01-13
 */
@Slf4j
@RestController
@RequestMapping("/papi/secondev/zyhlw/mobileSSO")
public class MobileSsoController {
    @Resource
    private ISsoService issoService;
    @Resource
    SSOTokenUtil ssotokenUtil;

    private final String simpleName = SsoController.class.getSimpleName();

    /**
     * 通过移动端安全网关地址登录，安全网关认证通过以后进行模拟认证登录
     *
     * @param response 响应
     */
    @GetMapping("/mobileSsoLogin")
    void mobileSsoLogin(HttpServletRequest request, HttpServletResponse response) {
        String methdodName = "调用" + simpleName + ".mobileSsoLogin()";
        String mobile = "";  // 手机号

        String from = StringUtils.null2String(request.getParameter("from"));
        String paramKey = "token";
        if ("gatewayApp".equals(from)) {
            paramKey = "sso_token";
        }
        String token = StringUtils.null2String(request.getParameter(paramKey));
        log.info("========== 短信中间页开始认证/跳转 ==========");
        log.info("请求参数 from: " + from + ", token: " + token);
        if (!"".equals(token)) {
            if ("gatewayApp".equals(from)) {
                //如果为安全网关认证
                try {

                    String mobileInfo = ssotokenUtil.gatewayAppToken(token, "2", "gatewayApp");
                    JSONObject jsonObject = JSON.parseObject(mobileInfo);
                    log.info("OpenWorkflowGateway安全网关响应日志：" + jsonObject.toJSONString());
                    if (Integer.valueOf(jsonObject.get("code").toString()) == 0) {
                        mobile = StringUtils.null2String(jsonObject.get("mobile").toString());
                    }
                } catch (Exception e) {
                    log.error("验证安全网关token失败: " + e + "@" + e.getMessage());
                }
            } else {
                try {
                    mobile = ValidateTokenComponent.getMobileOfValidateToken(token, "5");
                } catch (Exception e) {
                    log.error("验证短信小程序token失败: " + e + "@" + e.getMessage());
                }
            }
        } else {
//            // 看起来这里是去sim页面做了个中转，再回到OpenDataCenter.jsp中
//            // 然后OpenDataCenter.jsp里面又解析了a2p票据，拿出对应的工单id，去详情页
//            // 不确定是否有什么地方用到了这个方法，暂且保留
//            String apptoken = CreateappToken.doPostParam();
//            //测试线内网：http://10.1.58.30:8080/CMIC_OA_TEST/simAuth/index?jumpUrl=${jumpUrl}&appToken=${appToken}
//            //测试线公网：https://117.136.240.16/CMIC_OA_TEST/simAuth/index?jumpUrl=${jumpUrl}&appToken=${appToken}
//            //生产线：    https://hfx.net/CMIC_OA/simAuth/index?appToken=${appToken}&jumpUrl=${jumpUrl}
//            String requestid = request.getParameter("requestid");
//            String jumpUrl = StringUtils.null2String(new BaseBean().getPropValue("Cmic", "jumpUrl"), "https://portal.hfx.net/spa/OpenWorkflow.jsp");
//            jumpUrl = jumpUrl + "?requestid=" + requestid;
//            jumpUrl = URLEncoder.encode(jumpUrl, "UTF-8");
//            // 将这条链接作为参数给了sim认证跳转
//            String simUrl = StringUtils.vString(new BaseBean().getPropValue("Cmic", "simUrl"), "https://portal.hfx.net/CMIC_OA/simAuth/index?jumpUrl=");
//            String redirectUrl = simUrl + jumpUrl + "&appToken=" + apptoken;
//            response.sendRedirect(redirectUrl);
//            return;
        }
        log.info("票据解析mobile:" + mobile);

//        if (mobile != null) {
//            // 模拟登陆
//            Map<String, Object> params = new HashMap<String, Object>();
//            params.put("accountType", "mobile");
//            params.put("loginType", SessionUtil.getLoginType(request));
//            params.put("principalName", mobile);
//            params.put("customSQL", "");
//            String userId = SessionUtil.getUserIdByRule(params);
//            User user_new = null;
//            if (!"".equals(userId)) {
//                String requestid = request.getParameter("requestid");
//
//                User user = (User) request.getSession(true).getAttribute("weaver_user@bean");
//                String istest = Util.null2String((String) request.getSession(true).getAttribute("istest"));
//
//                Calendar today = Calendar.getInstance();
//                String currentdate = Util.add0(today.get(Calendar.YEAR), 4) + "-" + Util.add0(today.get(Calendar.MONTH) + 1, 2) + "-" + Util.add0(today.get(Calendar.DAY_OF_MONTH), 2);
//                if (user == null || user != null && !"1".equals(istest) && !user.getMobile().toLowerCase().equals(mobile)) {
//                    //2,构造User
//                    SessionUtil.createSession(userId, request, response);
//                } else {
//                    user_new = user;
//                    user_new.setLastlogindate(currentdate);
//                }
//
//                //获取域名地址，流程工单跳转逻辑
//
//                String domainUrl;
//                String control = zyhlwUtil.getBaseDataValue("移动端域名强制控制开关", "门户管理");
//                if ("1".equals(control)) {
//                    domainUrl = StringUtil.vString(zyhlwUtil.getBaseDataValue("移动端域名地址", "门户管理"), "https://cmic.hfx.net");
//                } else {
//                    String domain = request.getServerName();
//                    if (StringUtil.isNotNull(domain) && StringUtil.contains(domain, "app.cmic.site")) { //安全网关正式环境
//                        //domainUrl ="https://app.cmic.site:10441";
//                        domainUrl = StringUtil.vString(zyhlwUtil.getBaseDataValue("安全网关域名地址", "门户管理"), "https://app.cmic.site:10441");
//                    } else {
//                        domainUrl = StringUtil.vString(zyhlwUtil.getBaseDataValue("移动端域名地址", "门户管理"), "https://cmic.hfx.net");
//                    }
//                }
//                logger.info("域名地址：" + domainUrl);
//                // 移动端EC平台工单处理完以后，跳转到统一待办列表页面，而不是EC待办页面  moidfy by dengks
//                String goUrl = domainUrl + "/spa/workflow/static4mobileform/index.html#/req?requestid=" + requestid;
//                String returnUrl = domainUrl + "/mobilemode/mobile/view.html?appid=1001";
//                goUrl = goUrl + "&returnUrl=" + URLEncoder.encode(returnUrl);
//                logger.info("流程待办通知，跳转地址为：" + goUrl);
//                response.sendRedirect(goUrl);
//            } else {
//                log.error("accountValue:" + mobile + " is not found in OA!!! please check the account!!!");
//                response.sendRedirect("/wui/index.html");
//            }
//            log.info("========== 短信中间页认证/跳转结束 ==========");
//        }
    }



}

