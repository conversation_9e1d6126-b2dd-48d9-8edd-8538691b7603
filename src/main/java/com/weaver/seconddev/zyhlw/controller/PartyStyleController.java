package com.weaver.seconddev.zyhlw.controller;

import com.weaver.common.authority.annotation.WeaPermission;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.seconddev.zyhlw.domain.portalworkflow.AddCollectionDTO;
import com.weaver.seconddev.zyhlw.domain.portalworkflow.NewWorkFlowDTO;
import com.weaver.seconddev.zyhlw.service.IPartyStyleService;
import com.weaver.seconddev.zyhlw.service.IPortalWorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR> by herry on 2025-02-12
 * Update date:
 * Time: 11:52
 * Project: ecology
 * Package: com.weaver.seconddev.zyhlw.controller
 * Command:
 * <p>
 * Status：Using online
 * <p>
 * Please note:
 * Must be checked once every time you submit a configuration file is correct!
 * Data is priceless! Accidentally deleted the consequences!
 */
@Slf4j
@RestController
@RequestMapping("/api/secondev/zyhlw/partyStyle")
public class PartyStyleController {

    @Resource
    IPartyStyleService partyStyleService;

    /**
     * fxlyByClause
     * <p>
     * 根据部门and领域进行查询。
     * ps：原e9接口 /api/zyhlw/web/dangf/fxlyByClause
     * <p>
     *
     * @param year      年份
     * @param quarterly 季度
     * @param branch    部门
     * @param area      领域
     * @return 成功失败
     * <AUTHOR>
     * @time 2025年01月22 11:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/fxlyByClause")
    public WeaResult<Object> fxlyByClause(@RequestParam("year") String year, @RequestParam("quarterly") String quarterly,
                                          @RequestParam(value = "branch", required = false) String branch,
                                          @RequestParam(value = "area", required = false) String area) {
        return partyStyleService.fxlyByClause(year, quarterly, branch, area);
    }

    /**
     * fxly2
     * <p>
     * 根据时间进行查找(需求变更，之前是关联开始时间和结束时间，这个是只关联结束时间)。
     * ps：原e9接口 /api/zyhlw/web/dangf/fxly2
     * <p>
     *
     * @param year      年份
     * @param quarterly 季度
     * @param branch    部门
     * @param area      领域
     * @return 成功失败
     * <AUTHOR>
     * @time 2025年01月22 11:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/fxly2")
    public WeaResult<Object> fxly2(@RequestParam("year") String year, @RequestParam("quarterly") String quarterly,
                                   @RequestParam(value = "branch", required = false) String branch,
                                   @RequestParam(value = "area", required = false) String area) {
        return partyStyleService.fxly2(year, quarterly, branch, area);
    }

    /**
     * allFxly
     * <p>
     * 查询全部风险领域。
     * ps：原e9接口 /api/zyhlw/web/dangf/allFxly
     * <p>
     *
     * @return 成功失败
     * <AUTHOR>
     * @time 2025年01月22 11:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/allFxly")
    public WeaResult<Object> allFxly() {
        return partyStyleService.allFxly();
    }

    /**
     * other
     * <p>
     * 查询新增、自查、变更等风险流程表。
     * ps：原e9接口 /api/zyhlw/web/dangf/other
     *
     * <p>
     *
     * @param year      年份
     * @param quarterly 季度
     * @param tableName 表名（新增，自查，变更）
     * @return 成功失败
     * <AUTHOR>
     * @time 2025年01月22 11:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/other")
    public WeaResult<Object> other(@RequestParam("year") String year, @RequestParam("quarterly") String quarterly, @RequestParam("tableName") String tableName) {
        if(StringUtils.isBlank(tableName)) {
            return WeaResult.fail("请输入正确的tableName");
        }
        return partyStyleService.other(year, quarterly, tableName);
    }

    /**
     * allDept
     * <p>
     * 查询全部部门。
     * ps：原e9接口 /api/zyhlw/web/dangf/allDept
     *
     * <p>
     *
     * @return 成功失败
     * <AUTHOR>
     * @time 2025年01月22 11:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/allDept")
    public WeaResult<Object> allDept() {
        return partyStyleService.allDept();
    }

    /**
     * getRiskDomain
     * <p>
     * 获取风险信息库-风险领域明细列表
     * ps：原e9接口 /api/zyhlw/web/RiskPointApi/getRiskDomain
     *
     * <p>
     *
     * @return 成功失败
     * <AUTHOR>
     * @time 2025年02月21 11:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/getRiskDomain")
    public WeaResult<Object> getRiskDomain() {
        return partyStyleService.getRiskDomain();
    }

    /**
     * getRiskDepartment
     * <p>
     * 获取风险信息库--风险部门明细列表
     * ps：原e9接口 /api/zyhlw/web/RiskPointApi/getRiskDepartment
     *
     * <p>
     *
     * @return 成功失败
     * <AUTHOR>
     * @time 2025年02月21 11:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/getRiskDepartment")
    public WeaResult<Object> getRiskDepartment() {
        return partyStyleService.getRiskDepartment();
    }

    /**
     * RiskStatisticalStatement
     * <p>
     * 开始执行党风廉政统计报表页面数据接口
     * ps：原e9接口 /api/zyhlw/web/RiskStatisticalStatementApi/RiskStatisticalStatement
     *
     * <p>
     *
     * @return 成功失败
     * <AUTHOR>
     * @time 2025年02月21 11:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/RiskStatisticalStatement")
    public WeaResult<Object> RiskStatisticalStatement() {
        return partyStyleService.RiskStatisticalStatement();
    }

    /**
     * HuoQuRiskTongJi
     * <p>
     * 风险统计
     * ps：原e9接口 /api/zyhlw/web/RiskPointApi/HuoQuRiskTongJi
     *
     * <p>
     *
     * @param lx    lx
     * @param sj    sj
     * @param dType dType
     * @return 成功失败
     * <AUTHOR>
     * @time 2025年02月21 11:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/HuoQuRiskTongJi")
    public WeaResult<Object> HuoQuRiskTongJi(@RequestParam("lx") String lx, @RequestParam("sj") String sj, @RequestParam("dType") String dType) {
        return partyStyleService.HuoQuRiskTongJi(lx, sj, dType);
    }

    /**
     * ResponsiblePersonStatistics
     * <p>
     * 责任人统计
     * ps：原e9接口 /api/zyhlw/web/risk/ResponsiblePersonStatisticsApi/ResponsiblePersonStatistics
     *
     * <p>
     *
     * @param dep    dep
     * @return 成功失败
     * <AUTHOR>
     * @time 2025年02月24 14:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/ResponsiblePersonStatistics")
    public WeaResult<Object> ResponsiblePersonStatistics(@RequestParam("dep") String dep) {
        return partyStyleService.ResponsiblePersonStatistics(dep);
    }

    /**
     * fxlyBydp
     * <p>
     * 根据部门进行风险领域查找
     * ps：原e9接口 /api/zyhlw/web/dangf/fxlyBydp
     *
     * <p>
     *
     * @param branch    branch
     * @return 成功失败
     * <AUTHOR>
     * @time 2025年02月24 14:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/fxlyBydp")
    public WeaResult<Object> fxlyBydp(@RequestParam("branch") String branch) {
        return partyStyleService.fxlyBydp(branch);
    }

    /**
     * selectDeptByLy
     * <p>
     * 根据领域id查询部门
     * ps：原e9接口 /api/zyhlw/web/dangf/selectDeptByLy
     *
     * <p>
     *
     * @param lyid    lyid
     * @return 成功失败
     * <AUTHOR>
     * @time 2025年02月24 14:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/selectDeptByLy")
    public WeaResult<Object> selectDeptByLy(@RequestParam("lyid") String lyid) {
        return partyStyleService.selectDeptByLy(lyid);
    }


}
