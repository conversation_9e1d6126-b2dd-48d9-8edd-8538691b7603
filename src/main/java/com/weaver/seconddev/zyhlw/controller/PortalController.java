package com.weaver.seconddev.zyhlw.controller;

import com.alibaba.fastjson.JSONObject;
import com.weaver.common.authority.annotation.WeaPermission;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.ebuilder.common.util.StringUtil;
import com.weaver.seconddev.zyhlw.domain.portal.PortalMenuDTO;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.service.IPortalService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import com.weaver.seconddev.zyhlw.util.sso.SSOTokenUtil;
import com.weaver.teams.domain.department.SimpleDepartment;
import com.weaver.teams.domain.user.SimpleEmployee;
import com.weaver.teams.security.context.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> on 20251118
 */
@Slf4j
@RestController
@RequestMapping("/api/secondev/zyhlw/portal")
public class PortalController {
    private final String simpleName = PortalController.class.getSimpleName();
    private String tickenKey = "ticken";

    @Resource
    IPortalService portalService;
    @Resource
    IDataBaseService dataBaseService;

    @Resource
    CmicProperties cmicProperties;

    @Resource
    SSOTokenUtil ssotokenUtil;

    /**
     * 获取系统中心中的系统列表
     *
     * @return
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/getDatas")
    public WeaResult<List<Map<String, Object>>> getDatas(HttpServletRequest request, HttpServletResponse response) {
        try {
            // 获取当前用户
            SimpleEmployee currentUser = UserContext.getCurrentUser();
            if (currentUser == null) {
                return WeaResult.fail("获取当前用户currentUser信息失败");
            }
            //是否集团应用
            String groupAppFlag = StringUtils.null2String(request.getParameter("groupAppFlag"), "0");
            List<Map<String, Object>> systemList = portalService.getSystemList(currentUser, groupAppFlag);
            log.info("调用PortalController.getSystemList  获取的currentUser信息：{},获取systemList信息：{}", currentUser, systemList);
            return WeaResult.success(systemList);
        } catch (Exception e) {
            log.error("调用PortalController.getSystemList()  执行异常", e);
            return WeaResult.fail("请求异常");
        }

    }

    /**
     * 获取当前用户的票据信息
     *
     * @param request
     * @param response
     * @return
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/token")
    public Map<String, Object> token(HttpServletRequest request, HttpServletResponse response) {
        Map<String, Object> resultMap = new HashMap<>();
        try {

            // 获取当前用户
            SimpleEmployee currentUser = UserContext.getCurrentUser();
            if (currentUser == null) {

                resultMap.put("code", 202);
                resultMap.put("msg", "获取当前用户currentUser信息失败");
            }
            log.info("调用PortalController.token方法，获取currentUser信息：" + JSONObject.toJSONString(currentUser));


            String methodName = String.format("调用%s.token", simpleName);
            String type = StringUtils.null2String(request.getParameter("type"));
            String loginid_temp = StringUtils.null2String(request.getParameter("loginid"));

            String loginId = currentUser.getLoginid();

            // todo 测试帐号
            if (!"".equals(loginid_temp)) {
                loginId = loginid_temp;
            }

            log.info("调用PortalController.token方法，获取type：{},loginId:{}", type, loginId);


//        if (type.equals("smap")) {
//            String appid = Util.null2String(request.getParameter("appid"));
//           SSOTokenUtil.getSmap(appid, resultMap, user.getLoginid());
//        }
            if (type.equals("ticken")) {
                ssotokenUtil.getTicken(loginId, resultMap);
            } else if (type.equals("portalToken")) {
                // 因portalToken认证（wsdl接口，旧接口）和portalToken2（rest 接口，新接口）规则逻辑都是一样，统一改为新接口调用
                ssotokenUtil.getPortalToken2(loginId, resultMap);

                //ssotokenUtil.getPortalToken(loginId, resultMap);
            } else if (type.equals("portalToken2")) {
                ssotokenUtil.getPortalToken2(loginId, resultMap);
            } else if (type.equals("gateway")) {
                String appid = StringUtils.null2String(request.getParameter("appid"));
                ssotokenUtil.getGateway(appid, loginId, resultMap);
            } else if (type.equals("ticken2")) {
                //账号  例如：zhengqihui
                String loginid = StringUtils.null2String(request.getParameter("loginid"));
                //目标系统ClientId
                String targetClientId = StringUtils.null2String(request.getParameter("targetClientId"));
                //发起OA MOA
                String clientIdType = StringUtils.null2String(request.getParameter("clientIdType"));
                ssotokenUtil.getTicken2(loginid, resultMap, targetClientId, clientIdType);
            } else if (type.equals("e10Token")) {
                ssotokenUtil.getE10Token(loginId, resultMap);
            } else if (type.equals("e9Token")) {
                // E9 认证也采用portalToken2 生成票据
                ssotokenUtil.getPortalToken2(loginId, resultMap);
            } else {
                resultMap.put("code", 201);
                resultMap.put("msg", "不在token类型范围内");
                resultMap.put("ticken", "");
            }
            return resultMap;
        } catch (Exception ex) {
            //throw new RuntimeException(ex);
            log.error("调用PortalController.token  执行异常", ex);
            String errorMsg = "请求异常,失败原因：" + ex.getMessage();

            resultMap.put("code", 202);
            resultMap.put("msg", errorMsg);
        }
        return resultMap;

    }

    /**
     * 获取系统分类列表，只获取有效的应用
     *
     * @param request
     * @param response
     * @return
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/getList")
    public WeaResult<List<Map<String, Object>>> getList(HttpServletRequest request, HttpServletResponse response) {
        try {
            SimpleEmployee currentUser = UserContext.getCurrentUser();
            if (currentUser == null) {
                return WeaResult.fail("获取当前用户currentUser信息失败");
            }

            List<Map<String, Object>> catagoryList = dataBaseService.getSystemCatagoryList();
            log.info("开始调用PortalController.getList，获取的catagoryList{}", catagoryList);
            return WeaResult.success(catagoryList);
        } catch (Exception ex) {
            log.error("调用PortalController.getList  执行异常", ex);
            return WeaResult.fail("请求异常,失败原因：" + ex.getMessage());
        }
    }


    /**
     * 获取系统分类列表，只获取有效的应用
     *
     * @param request
     * @param response
     * @return
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/getChecked")
    public WeaResult<List<Map<String, Object>>> getChecked(HttpServletRequest request, HttpServletResponse response) {
        try {
            SimpleEmployee currentUser = UserContext.getCurrentUser();
            if (currentUser == null) {
                return WeaResult.fail("获取当前用户currentUser信息失败");
            }

            Long employeeId = currentUser.getEmployeeId();
            log.info("开始调用PortalController.getChecked，获取的employeeId{}", employeeId);
            List<Map<String, Object>> catagoryList = dataBaseService.getSystemCollectList(employeeId);
            log.info("开始调用PortalController.getChecked，获取的catagoryList{}", catagoryList);
            return WeaResult.success(catagoryList);
        } catch (Exception ex) {
            log.error("调用PortalController.getList  执行异常", ex);
            return WeaResult.fail("请求异常,失败原因：" + ex.getMessage());
        }
    }


    /**
     * 获取当前登录的用户信息
     *
     * @param request
     * @param response
     * @return
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/getUserInfo")
    public WeaResult<Map<String, String>> getUserInfo(HttpServletRequest request, HttpServletResponse response) {
        Map<String, String> userMap = new HashMap<>();
        try {
            if (cmicProperties != null) {
                log.info("获取的cmicProperties：{}", JSONObject.toJSONString(cmicProperties));
            }
            SimpleEmployee currentUser = UserContext.getCurrentUser();
            log.info("调用getUserInfo方法，获取的currentUser：{}", JSONObject.toJSONString(currentUser));
            if (currentUser == null) {
                return WeaResult.fail("获取当前用户currentUser信息失败");
            } else {
                userMap.put("loginid", currentUser.getLoginid());
                userMap.put("mobile", currentUser.getMobile());
                log.info("调用getUserInfo方法，获取loginid:{},mobile:{}", currentUser.getLoginid(), currentUser.getMobile());
                String depid = "";
                SimpleDepartment simpleDepartment = currentUser.getDepartment();
                if (simpleDepartment != null) {
                    log.info("调用getUserInfo方法，获取的simpleDepartment：{}", JSONObject.toJSONString(simpleDepartment));
                    depid = simpleDepartment.getId().toString();
                }
                userMap.put("depid", depid);
                userMap.put("userid", String.valueOf(currentUser.getEmployeeId()));
                userMap.put("email", currentUser.getEmail());
                userMap.put("username", currentUser.getUsername());
                log.info("调用getUserInfo方法，获取的userMap：{}", JSONObject.toJSONString(userMap));
                return WeaResult.success(userMap);
            }
        } catch (Exception ex) {
            log.info("调用PortalController.getUserInfo  执行异常", ex);
            return WeaResult.fail("请求异常,失败原因：" + ex.getMessage());
        }
    }


    /**
     * 获取当前登录的用户信息
     *
     * @param request
     * @param response
     * @return
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/checkIp")
    public WeaResult<Map<String, Object>> checkIp(HttpServletRequest request, HttpServletResponse response) {
        Map<String, Object> resultMap = new HashMap<>();
        String userIp = request.getHeader("x-forwarded-for");
        if (userIp.isEmpty() || "unknown".equalsIgnoreCase(userIp)) {
            userIp = request.getHeader("Proxy-Client-IP");
        }
        if (userIp.isEmpty() || "unknown".equalsIgnoreCase(userIp)) {
            userIp = request.getHeader("WL-Proxy-Client-IP");
        }
        if (userIp.isEmpty() || "unknown".equalsIgnoreCase(userIp)) {
            userIp = request.getRemoteAddr();
        }
        log.info("开始调用PortalController.checkIp，获取的userIp{}", userIp);
        Boolean checkIp = dataBaseService.checkIp(userIp);
        log.info("开始调用PortalController.checkIp，获取的checkIp{}", checkIp);
        resultMap.put("isRight", checkIp);
        return WeaResult.success(resultMap);
    }

    /**
     * 获取系统分类列表，只获取有效的应用
     *
     * @param request
     * @param response
     * @return
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/getMenuList")
    public WeaResult<List<Map<String, Object>>> getMenuList(HttpServletRequest request, HttpServletResponse response) {
        try {
            SimpleEmployee currentUser = UserContext.getCurrentUser();
            if (currentUser == null) {
                return WeaResult.fail("获取当前用户currentUser信息失败");
            }
            Long employeeId = currentUser.getEmployeeId();
            List<Map<String, Object>> menuConfigList = dataBaseService.getMenuConfigList(employeeId);
            return WeaResult.success(menuConfigList);
        } catch (Exception ex) {
            log.info("调用PortalController.getMenuList  执行异常", ex);
            return WeaResult.fail("请求异常,失败原因：" + ex.getMessage());
        }
    }

    /**
     * update
     * <p>
     * 前端传入json格式的body;{"add":"[4]","delete":"[1,2,3]"}
     * 前端思路如下：
     * 用户add操作时，直接添加到add队列；用户删除操作时，查询add队列中是否存在，若存在则删除，否则添加到delete队列中；
     * 至于add为什么不查询delete队列，因为先删除后加入（相当于修改，因为时间字段影响排序）。所有队列均不能重复添加。
     * 原思路再一个用户多处同时登录且同时进行修改时有影响，现改为，加入之前若存在则修改，若超过8个则加入失败。。
     * ps：注释是e9搬过来的，非本人设计。原e9接口 /zyhlw/web/menu/update
     * <p>
     *
     * @param portalMenuDTO 入参
     * @return 成功失败
     * <AUTHOR>
     * @time 2025年01月22 11:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @PostMapping(value = "/update")
    public WeaResult<Object> update(@RequestBody PortalMenuDTO portalMenuDTO) {
        return portalService.update(portalMenuDTO);
    }

    /**
     * addlog
     * <p>
     * 添加日志接口。
     * ps：原e9接口 /zyhlw/web/LogYingYongData/addlog
     * <p>
     *
     * @param danId     传过来的ID
     * @param danName   应用名称
     * @param userAgent userAgent
     * @return 成功失败
     * <AUTHOR>
     * @time 2025年01月22 11:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/addlog")
    public WeaResult<Object> addlog(@RequestParam("danId") String danId, @RequestParam("danName") String danName, @RequestParam("userAgent") String userAgent) {
        return portalService.addlog(danId, danName, userAgent);
    }

    /**
     * updateChecked
     * <p>
     *
     * @param type    1.流程中心-应用内流程 2.系统中心-只显示集团
     * @param checked 0.取消选中 1.选中
     * @return 勾选状态
     * @description 获取用的流程中心与系统中心的勾选状态
     * ps：e9原接口 /zyhlw/web/portal/NewWorkflowApi/updateChecked
     * <p>
     * <AUTHOR>
     * @time 2025年01月23 11:22:11
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @GetMapping("/updateChecked")
    public WeaResult<Object> updateChecked(@RequestParam("type") String type, @RequestParam("checked") String checked) {
        return portalService.updateChecked(type, checked);
    }


    /**
     * 获取系统中心中的系统列表
     *
     * @return
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/getSystemInfoBySystemCode")
    public WeaResult<Map<String, Object>> getSystemInfoBySystemCode(HttpServletRequest request, HttpServletResponse response) {
        try {
            // 获取当前用户
            SimpleEmployee currentUser = UserContext.getCurrentUser();
            if (currentUser == null) {
                return WeaResult.fail("获取当前用户currentUser信息失败");
            }
            //系统编码
            String systemCode = StringUtils.null2String(request.getParameter("systemCode"), "");
            if(systemCode.equals(""))
            {
                return WeaResult.fail("传过来的系统编号不能为空！");
            }
            Map<String, Object> resultMap = portalService.getSystemInfoBySystemCode(systemCode);
            log.info("调用PortalController.getSystemInfoBySystemCode 获取resultMap信息：{}", resultMap);
            return WeaResult.success(resultMap);
        } catch (Exception e) {
            log.error("调用PortalController.getSystemInfoBySystemCode()  执行异常", e);
            return WeaResult.fail("请求异常");
        }

    }


}
