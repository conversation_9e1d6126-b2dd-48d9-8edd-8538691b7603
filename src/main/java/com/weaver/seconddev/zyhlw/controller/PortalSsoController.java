package com.weaver.seconddev.zyhlw.controller;

import com.alibaba.fastjson.JSONObject;
import com.weaver.common.authority.annotation.WeaPermission;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.ebuilder.common.util.StringUtil;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.service.IPortalService;
import com.weaver.seconddev.zyhlw.service.ISsoService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import com.weaver.seconddev.zyhlw.util.sso.OATokenUtil;
import com.weaver.seconddev.zyhlw.util.sso.SSOTokenUtil;
import com.weaver.teams.domain.user.SimpleEmployee;
import com.weaver.teams.security.context.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> on 20251118
 */
@Slf4j
@RestController
@RequestMapping("/api/secondev/zyhlw/portalSSO")
public class PortalSsoController {

    @Resource
    private ISsoService issoService;
    @Resource
    SSOTokenUtil ssotokenUtil;
    @Resource
    IDataBaseService iDataBaseService;
    @Resource
    IPortalService portalService;

    @Resource
    CmicProperties cmicProperties;
    @Resource
    OATokenUtil oaTokenUtil;

    /**
     * 门户首页中访问集团统邮,type:1 (合理化建议),5(集团总经理信箱),4(员工服务建议),3(互联网公司总经理信箱),2(工会主席信箱帐号)
     *
     * @param request  请求
     * @param response 响应
     */
    @WeaPermission(publicPermission = true)
    @GetMapping("/getMailLoginInfo")
    public WeaResult<Map<String, Object>> getMailLoginInfo(HttpServletRequest request, HttpServletResponse response) throws IOException {
        Map<String, Object> resultMap = new HashMap<>();
        String type = StringUtils.null2String(request.getParameter("type"), "");

        SimpleEmployee currentUser = UserContext.getCurrentUser();
        if (currentUser == null) {
            return WeaResult.fail("获取当前用户currentUser信息失败！");
        }

        log.info("1、调用PortalSsoController.getMailLoginInfo方法，获取currentUser信息：" + JSONObject.toJSONString(currentUser));

        String getPortalLoginInfoId = StringUtils.null2String(currentUser.getLoginid());
        log.info("2、调用PortalSsoController.getMailLoginInfo方法，当前用户的getPortalLoginInfoId:" + getPortalLoginInfoId);


        // String email_sso_url = StringUtil.vString(new BaseBean().getPropValue("Cmic", "email_sso_url"),"http://mail.cmic.chinamobile.com/webmail/login/smapsso.do");
        String email_sso_url = StringUtil.vString(iDataBaseService.getBaseDataValue("工会主席信箱接口地址", "门户首页"), "http://mail.cmic.chinamobile.com/webmail/login/smapsso.do");
        //从配置表中读取集团总经理邮件接口地址
        String email_groupceo_url = iDataBaseService.getBaseDataValue("集团总经理邮件接口地址", "门户首页");
        if (email_groupceo_url.equals("")) {
            email_groupceo_url = "http://cloudoa.hq.cmcc/cmoa-webapp1/cmoaCeoMailbox/toIndexPage?outside_visit_flag=true&menuCode=ZCXXSY";
        }
        ssotokenUtil.getTicken(getPortalLoginInfoId, resultMap);
        log.info("3、调用PortalSsoController中ssotokenUtil.getTicken方法，当前用户的map:" + JSONObject.toJSONString(resultMap));
        String code = resultMap.getOrDefault("code", "").toString();
        //生成对应的ticket 票据
        String ticket = resultMap.getOrDefault("ticken", "").toString();
        if (!code.equals("200")) {
            return WeaResult.fail("获取当前用户ticket失败！");
        }


        log.info("3、获取当前用户encode前的ticket:" + ticket);
        ticket = java.net.URLEncoder.encode(ticket, "UTF-8");
        log.info("3.1、获取当前用户的ticket:" + ticket);
        if (type.equals("5")) {
            //集团总经理信箱
            String toGroupCEOEmail = iDataBaseService.getBaseDataValue("集团总经理信箱", "门户管理");
            if (toGroupCEOEmail.equals("")) {
                toGroupCEOEmail = "<EMAIL>";
            }
            String sendEmailUri = "";
            if (email_groupceo_url.contains("toIndexPage?")) {
                sendEmailUri = email_groupceo_url + "&tourl=" + toGroupCEOEmail + "&ticket=";
            } else {
                sendEmailUri = email_groupceo_url + "?tourl=" + toGroupCEOEmail + "&ticket=";
            }
            log.info("3.2、转换sendEmailUri的URL地址：" + sendEmailUri);

            // response.sendRedirect(sendEmailUri);
            resultMap.put("code", 200);
            resultMap.put("msg", "获取portalToken成功");
            resultMap.put("url", sendEmailUri);
            resultMap.put("ticken",ticket);
        } else if (type.equals("4")) {
            //员工服务建议
            String toEmpolyEmail = iDataBaseService.getBaseDataValue("员工服务建议", "门户管理");
            if (toEmpolyEmail.equals("")) {
                toEmpolyEmail = "toEmployeeEmail";
            }
            String sendEmailUri = email_sso_url + "?lt=WEB&openModule=write&cc=&bcc=&recipient=" + toEmpolyEmail+"&ticket=";
            log.info("3.3、type值：" + type + "，转换sendEmailUri的URL地址：" + sendEmailUri);

            // response.sendRedirect(sendEmailUri);
            resultMap.put("code", 200);
            resultMap.put("msg", "获取portalToken成功");
            resultMap.put("url", sendEmailUri);
            resultMap.put("ticken",ticket);

        } else if (type.equals("3")) {
            //互联网公司总经理信箱
            String toCEOEmail = iDataBaseService.getBaseDataValue("互联网公司总经理信箱", "门户管理");
            if (toCEOEmail.equals("")) {
                toCEOEmail = "ceo";
            }
            String sendEmailUri = email_sso_url + "?lt=WEB&openModule=write&cc=&bcc=&recipient=" + toCEOEmail+"&ticket=";
            log.info("3.2、type值：" + type + "，转换sendEmailUri的URL地址：" + sendEmailUri);
            //sendEmailUri=java.net.URLEncoder.encode(sendEmailUri);
            // response.sendRedirect(sendEmailUri);
            resultMap.put("code", 200);
            resultMap.put("msg", "获取portalToken成功");
            resultMap.put("url", sendEmailUri);
            resultMap.put("ticken",ticket);
        } else if (type.equals("2")) {
            //工会主席信箱帐号
            String unionChairManLoginId = iDataBaseService.getBaseDataValue("工会主席信箱帐号", "门户管理");
            if (unionChairManLoginId.equals("")) {
                unionChairManLoginId = "ghzx";
            }
            String sendEmailUri = email_sso_url + "?lt=WEB&openModule=write&cc=&bcc=&recipient=" + unionChairManLoginId+"&ticket=";
            log.info("3.1、type值：" + type + "，转换sendEmailUri的URL地址：" + sendEmailUri);
            //sendEmailUri=java.net.URLEncoder.encode(sendEmailUri);
            // response.sendRedirect(sendEmailUri);

            resultMap.put("code", 200);
            resultMap.put("msg", "获取portalToken成功");
            resultMap.put("url", sendEmailUri);
            resultMap.put("ticken",ticket);
        } else if (type.equals("1")) {
            //合理化建议
            String toCEOEmail = iDataBaseService.getBaseDataValue("合理化建议", "门户管理");
            if (toCEOEmail.equals("")) {
                toCEOEmail = "ghgzyx-zy";
            }

            String sendEmailUri = email_sso_url + "?lt=WEB&openModule=write&cc=&bcc=&recipient=" + toCEOEmail+"&ticket=";
            log.info("3.1、type值：" + type + "，转换sendEmailUri的URL地址：" + sendEmailUri);
            //sendEmailUri=java.net.URLEncoder.encode(sendEmailUri);
            resultMap.put("code", 200);
            resultMap.put("msg", "获取portalToken成功");
            resultMap.put("url", sendEmailUri);
            resultMap.put("ticken",ticket);

        } else {
            return WeaResult.fail("传入的type值不正确！");

        }
        return WeaResult.success(resultMap);
    }


    /**
     * 门户首页中点击九宫格中的应用，如果为IE打开则IE中间页面中，带上portalToken 生成的票据，访问中间页面后再拼凑票据进行模拟登录
     * 如果为chrome 则直接返回拼凑票据后的访问地址,在前端页面中直接跳转
     *
     * @param request  请求
     * @param response 响应
     */
    @WeaPermission(publicPermission = true)
    @GetMapping("/getPortalLoginInfo")
    public WeaResult<Map<String, Object>> getPortalLoginInfo(HttpServletRequest request, HttpServletResponse response) throws IOException {
        Map<String, Object> resultMap = new HashMap<>();
        // 读取系统中间页访问地址
        String systemMiddleUrl = iDataBaseService.getBaseDataValue("E10系统IE中间页面URL", "门户管理");
        String e9MiddleUrl = iDataBaseService.getBaseDataValue("E9单点中间页面URL", "门户管理");
        String systemCode = StringUtils.null2String(request.getParameter("systemCode"), "");
        String domainName = StringUtil.vString(iDataBaseService.getBaseDataValue("PARENT_DOMAIN_NAME", "OA系统配置"), "cmic.chinamobile.com");

        if ("".equals(systemCode)) {
            return WeaResult.fail("传过来的系统编号不能为空！");
        }

        SimpleEmployee currentUser = UserContext.getCurrentUser();
        if (currentUser == null) {
            return WeaResult.fail("获取当前用户currentUser信息失败！");
        }

        Map<String, Object> systemInfoMap = portalService.getSystemInfoBySystemCode(systemCode);
        if (systemInfoMap.isEmpty()) {
            return WeaResult.fail("获取当前系统信息失败！");
        }

        log.info("1、调用PortalSsoController.getPortalLoginInfo，获取currentUser信息：" + JSONObject.toJSONString(currentUser));

        String portalUserId = StringUtils.null2String(currentUser.getLoginid());
        log.info("2、调用PortalSsoController.getPortalLoginInfo，当前用户的portalUserId:" + portalUserId);
        // 0:Chrome,1:IE
        String liu_lqlx = systemInfoMap.getOrDefault("liu_lqlx", "").toString();
        // 系统访问地址
        String xi_tdz = systemInfoMap.getOrDefault("xi_tdz", "").toString();

        // 认证方式，0：portalToken，1：smap，2：smap，2：ticken，3：portalToken，4：gateway，5：portalToken2，6：e10Token，7：e9Token
        String ren_zfsh = systemInfoMap.getOrDefault("ren_zfsh", "").toString();
        //  认证方式编码
        String tokenType = ssotokenUtil.getTokenType(ren_zfsh);

        if ("H01".equals(systemCode)) {
            //如果OA系统，统一用potalToken生成票据，带上checktype=3
//            String token = ssotokenUtil.createUserToken(portalUserId);
//            log.info("3、调用PortalSsoController.getPortalLoginInfo方法，当前用户的token:" + JSONObject.toJSONString(token));
//            String childToken = "";
//            Map<String, Object> accessMap = net.sf.json.JSONObject.fromObject(token);
//            if (accessMap.size() >= 3) {
//                childToken = StringUtils.null2String(accessMap.get("accessToken"));
//
//            }
//            if ("".equals(childToken)) {
//                return WeaResult.fail("获取当前用户票据信息失败！");
//            }
//            resultMap.put("code", 200);
//            resultMap.put("isIE", liu_lqlx);
//            resultMap.put("msg", "获取portalToken成功");
//            if (!systemMiddleUrl.endsWith("portaltoken=")) {
//                systemMiddleUrl = systemMiddleUrl + "?checktype=3&systemCode=H01&portaltoken=";
//            }


            // 组合成完整的URL
//            StringBuilder sbUrl = new StringBuilder();
//            sbUrl.append(cmicProperties.getEcUrl());
//            sbUrl.append("/papi/secondev/zyhlw/sso/OALogin?portaltoken=");
//            resultMap.put("ticken", childToken);
            // OA系统的跳转地址
            //resultMap.put("url", sbUrl);


            String oaRequestURL = oaTokenUtil.setToOaIndexPageSSOCookieNew(portalUserId, domainName, response);
            resultMap.put("ticken", "");
            if (StringUtils.isEmpty(oaRequestURL)) {
                resultMap.put("code", 2004);
                resultMap.put("isIE", liu_lqlx);
                resultMap.put("msg", "通过用户ID获取OA系统访问地址失败，单点失败！");
                resultMap.put("url", "");

            } else {

                // response.sendRedirect(oaRequestURL);
                resultMap.put("code", 200);
                resultMap.put("isIE", liu_lqlx);
                resultMap.put("msg", "成功获取OA系统地址！");
                resultMap.put("url", oaRequestURL);

            }


        } else {
            // 如果为chrome浏览器，直接拼接访问地址
            if (tokenType.equals("ticken")) {
                ssotokenUtil.getTicken(portalUserId, resultMap);
                resultMap.put("isIE", liu_lqlx);
                resultMap.put("url", xi_tdz);
            } else if (tokenType.equals("portalToken")) {
                // 因portalToken认证（wsdl接口，旧接口）和portalToken2（rest 接口，新接口）规则逻辑都是一样，统一改为新接口调用
                ssotokenUtil.getPortalToken2(portalUserId, resultMap);
                resultMap.put("isIE", liu_lqlx);
                resultMap.put("url", xi_tdz);

            } else if (tokenType.equals("portalToken2")) {
                ssotokenUtil.getPortalToken2(portalUserId, resultMap);
                resultMap.put("url", xi_tdz);
                resultMap.put("isIE", liu_lqlx);
            } else if (tokenType.equals("e10Token")) {
                ssotokenUtil.getE10Token(portalUserId, resultMap);
                resultMap.put("url", xi_tdz);
                resultMap.put("isIE", liu_lqlx);
            } else if (tokenType.equals("e9Token")) {
                // E9 认证也采用portalToken2 生成票据
                ssotokenUtil.getPortalToken2(portalUserId, resultMap);
                String newUrl=xi_tdz;
                if (!"".equals(e9MiddleUrl)) {
                    try {
                        newUrl = e9MiddleUrl + "?goUrl=" + URLEncoder.encode(xi_tdz, "UTF-8") + "&portaltoken=";
                    } catch (UnsupportedEncodingException e) {
                        throw new RuntimeException(e);
                    }
                    resultMap.put("url", newUrl);
                    resultMap.put("isIE", liu_lqlx);
                } else {
                    resultMap.put("url", "");
                    resultMap.put("isIE", liu_lqlx);
                }
            } else {
                resultMap.put("code", 201);
                resultMap.put("msg", "不在token类型范围内");
                resultMap.put("ticken", "");
                resultMap.put("url", "");
                resultMap.put("isIE", liu_lqlx);
            }
        }


        return WeaResult.success(resultMap);


    }



}
