package com.weaver.seconddev.zyhlw.controller;

import com.weaver.common.authority.annotation.WeaPermission;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.seconddev.zyhlw.domain.portalworkflow.AddCollectionDTO;
import com.weaver.seconddev.zyhlw.domain.portalworkflow.NewWorkFlowDTO;
import com.weaver.seconddev.zyhlw.service.IPortalWorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR> by herry on 2025-02-12
 * Update date:
 * Time: 11:52
 * Project: ecology
 * Package: com.weaver.seconddev.zyhlw.controller
 * Command:
 * <p>
 * Status：Using online
 * <p>
 * Please note:
 * Must be checked once every time you submit a configuration file is correct!
 * Data is priceless! Accidentally deleted the consequences!
 */
@Slf4j
@RestController
@RequestMapping("/api/secondev/zyhlw/portalWorkflow")
public class PortalWorkflowController {

    @Resource
    IPortalWorkflowService portalWorkflowService;

    /**
     * getDatas
     * <p>
     * 获取新建流程数据
     * ps：原e9接口 /api/zyhlw/web/portal/NewWorkflowApi/getDatas
     * <p>
     *
     * @param newWorkFlowDTO 入参
     * @return 流程数据
     * <AUTHOR>
     * @time 2025年01月22 11:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @PostMapping(value = "/getNewWorkflowDataList")
    public WeaResult<Object> getNewWorkflowDataList(@RequestBody NewWorkFlowDTO newWorkFlowDTO) {
        return portalWorkflowService.getNewWorkflowDataList(newWorkFlowDTO);
    }

    /**
     * getCollection
     * <p>
     * 获取新建流程我的收藏
     * ps：原e9接口 /api/zyhlw/web/portal/NewWorkflowApi/getCollection
     * <p>
     *
     * @param newWorkFlowDTO 入参
     * @return 流程数据
     * <AUTHOR>
     * @time 2025年01月22 11:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @PostMapping(value = "/getCollection")
    public WeaResult<Object> getCollection(@RequestBody NewWorkFlowDTO newWorkFlowDTO) {
        return portalWorkflowService.getCollection(newWorkFlowDTO);
    }

    /**
     * addCollection
     * <p>
     * 新建流程添加收藏
     * ps：原e9接口 /api/zyhlw/web/portal/NewWorkflowApi/addCollection
     * <p>
     *
     * @param addCollectionDTO 入参
     * @return 流程数据
     * <AUTHOR>
     * @time 2025年01月22 11:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @PostMapping(value = "/addCollection")
    public WeaResult<Object> addCollection(@RequestBody AddCollectionDTO addCollectionDTO) {
        return portalWorkflowService.addCollection(addCollectionDTO);
    }

    /**
     * cancelCollection
     * <p>
     * 新建流程取消收藏
     * ps：原e9接口 /api/zyhlw/web/portal/NewWorkflowApi/cancelCollection
     * <p>
     *
     * @param addCollectionDTO 入参
     * @return 流程数据
     * <AUTHOR>
     * @time 2025年01月22 11:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @PostMapping(value = "/cancelCollection")
    public WeaResult<Object> cancelCollection(@RequestBody AddCollectionDTO addCollectionDTO) {
        return portalWorkflowService.cancelCollection(addCollectionDTO);
    }

    /**
     * getMyWorkflowRequestList
     * <p>
     * 查询我发起的流程列表。
     * <p>
     *
     * @return 成功失败
     * <AUTHOR>
     * @time 2025年01月22 11:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/getMyWorkflowRequestList")
    public WeaResult<Object> getMyWorkflowRequestList() {
        return portalWorkflowService.getMyWorkflowRequestList();
    }

    /**
     * getMyWorkflowRequestList
     * <p>
     * 前台可发起工作流程列表。
     * ps：原e9接口 /api/workflow/createreq/wfinfo
     * <p>
     *
     * @return 成功失败
     * <AUTHOR>
     * @time 2025年01月22 11:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/getCreateWorkflowList")
    public WeaResult<Object> getCreateWorkflowList() {
        return portalWorkflowService.getCreateWorkflowList();
    }

    /**
     * getOptions
     * <p>
     * 查询我发起的流程列表。
     * ps：原e9接口 /zyhlw/web/portal/NewWorkflowApi/getOptions
     * <p>
     *
     * @return 成功失败
     * <AUTHOR>
     * @time 2025年01月22 11:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/getOptions")
    public WeaResult<Object> getOptions() {
        return portalWorkflowService.getOptions();
    }


}
