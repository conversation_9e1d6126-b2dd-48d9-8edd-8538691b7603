package com.weaver.seconddev.zyhlw.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.weaver.ebuilder.common.util.StringUtil;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.service.IPortalService;
import com.weaver.seconddev.zyhlw.service.ISsoService;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import com.weaver.seconddev.zyhlw.util.sso.OATokenUtil;
import com.weaver.seconddev.zyhlw.util.sso.SSOTokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/papi/secondev/zyhlw/sso")
public class SsoController {
    @Resource
    private ISsoService issoService;
    @Resource
    SSOTokenUtil ssotokenUtil;
    @Resource
    IDataBaseService iDataBaseService;
    @Resource
    OATokenUtil oaTokenUtil;
    @Resource
    IPortalService portalService;


    private final String simpleName = SsoController.class.getSimpleName();

    /**
     * 云桌面单点
     *
     * @param response 响应
     * @param token    票据
     */
    @GetMapping("/dcLogin")
    void dcLogin(HttpServletResponse response, @RequestParam String token) {
        String methdodName = "调用" + simpleName + ".dcLogin()";
        Map<String, Object> map = issoService.dcLogin(token);
        Integer code = (Integer) map.get("code");
        response.setCharacterEncoding("UTF-8");

        if (code == 0) {
            String url = (String) map.get("url");
            try {
                response.sendRedirect(url);

            } catch (IOException e) {
                log.error("{} 重定向异常！", methdodName, e);
            }
        } else {
            try {
                response.setContentType("application/json; charset=utf-8");
                response.getWriter().print(JSON.toJSONString(map));
            } catch (IOException e) {
                log.error("{} 返回异常！", methdodName, e);
            }
        }
    }

    /**
     * sim快捷认证
     *
     * @param response 响应
     * @param phone    手机号
     */
    @GetMapping("/simLogin")
    void simLogin(HttpServletResponse response, @RequestParam String phone) {
        String methdodName = "调用" + simpleName + ".dcLogin()";
        Map<String, Object> map = issoService.simLogin(phone);
        Integer code = (Integer) map.get("code");
        response.setCharacterEncoding("UTF-8");

        if (code == 0) {
            String url = (String) map.get("url");
            try {
                log.info("{} 跳转成功", methdodName);
                response.sendRedirect(url);
            } catch (IOException e) {
                log.error("{} 重定向异常！", methdodName, e);
            }
        } else {
            try {
                response.setContentType("application/json; charset=utf-8");
                response.getWriter().print(JSON.toJSONString(map));
            } catch (IOException e) {
                log.error("{} 返回异常！", methdodName, e);
            }
        }
    }


    /**
     * 安全网关登录认证
     *
     * @param request  请求
     * @param response 响应
     */
    @GetMapping("/gateWayLogin")
    void gateWayLogin(HttpServletRequest request, HttpServletResponse response) {
        String methdodName = "调用" + simpleName + ".gateWayLogin()";
        // 手机号码
        String phone = "";
        String requestid = "";
        Map<String, Object> map = new HashMap<>();
        response.setCharacterEncoding("UTF-8");
        try {
            // 需要在前端获取token，并传给后台
            String token = StringUtils.null2String(request.getParameter("sso_token"));
            if (token == null || token.equals("")) {
                log.error("{} 请求sso_token参数为空！", methdodName);
                return;
            }
            requestid = request.getParameter("requestid");
            if (requestid == null || requestid.equals("")) {
                log.error("{} 请求requestid参数为空！", methdodName);
                return;
            }

            String mobileInfo = ssotokenUtil.gatewayAppToken(token, "2", "gatewayApp");
            JSONObject jsonObject = JSON.parseObject(mobileInfo);
            log.info("OpenWorkflowGateway安全网关响应日志：" + jsonObject.toJSONString());
            if (Integer.valueOf(jsonObject.get("code").toString()) == 0) {
                phone = StringUtils.null2String(jsonObject.get("mobile").toString());
            }
        } catch (Exception e) {
            log.error("验证安全网关token失败: " + e + "@" + e.getMessage());
        }

        if (phone.equals("")) {
            try {
                map.put("code", 2004);
                map.put("msg", "验证安全网关token失败,获取的手机号码为空！");
                response.setContentType("application/json; charset=utf-8");
                response.getWriter().print(JSON.toJSONString(map));
            } catch (IOException e) {
                log.error("{} 返回异常！", methdodName, e);
            }

        } else {

            // 正式环境中的域名工单地址：https://gylgw.cmic.site/mobile/workflow/flowpage/view/
            String domainOrderUrl = StringUtil.vString(iDataBaseService.getBaseDataValue("e10安全网关域名工单地址", "门户管理"), "https://gysxt.cmic.site/mobile/workflow/flowpage/view/");

            // e10安全网关域名工单地址,比如：https://gylgw.cmic.site//mobile/workflow/flowpage/view/1098661408937091075，其中参数是动态拼凑上去的
            if (!domainOrderUrl.endsWith("/")) {
                domainOrderUrl = domainOrderUrl + "/";
            }
            String goUrl = domainOrderUrl + requestid;
            log.info("流程工单详情页面跳转地址为：" + goUrl);

            map = issoService.mobileLogin(phone, goUrl);
            Integer code = (Integer) map.get("code");
            log.info("调用gateWayLogin方法，获取的map：" + JSONObject.toJSONString(map));
            if (code == 0) {
                String url = (String) map.get("url");
                try {
                    log.info("{} 跳转成功", methdodName);
                    response.sendRedirect(url);
                } catch (IOException e) {
                    log.error("{} 重定向异常！", methdodName, e);
                }
            } else {
                try {
                    response.setContentType("application/json; charset=utf-8");
                    response.getWriter().print(JSON.toJSONString(map));
                } catch (IOException e) {
                    log.error("{} 返回异常！", methdodName, e);
                }
            }
        }
    }

    /**
     * 在E10 中点击OA应用，用IE打开新页面，跳转到中间页面中，获取OA系统传地来的portalToken生成票据，解释票据获取当前登录名，访问OA系统设置cookie信息并返回设置后的OA访问地址
     *
     * @param request  请求
     * @param response 响应
     */
    @GetMapping("/OALogin")
    Map<String,Object>  OALogin(HttpServletRequest request, HttpServletResponse response) throws IOException {

        String domainName = StringUtil.vString(iDataBaseService.getBaseDataValue("PARENT_DOMAIN_NAME", "OA系统配置"), "cmic.chinamobile.com");
        String methodName = "调用" + simpleName + ".OALogin()";
        Map<String, Object> resultMap = new HashMap<>();

        // 需要在前端获取OA系统生成的token，并传给后台
        String token = StringUtils.null2String(request.getParameter("portaltoken"));
        log.info("调用ssotokenUtil.validateToken方法,获取传过来的token：" + token);
        if (token == null || token.equals("")) {
            log.error("{} 请求token参数为空！", methodName);
            resultMap.put("code", 2004);
            resultMap.put("msg", "请求token参数为空！");
            resultMap.put("url", "");


        } else {
            // 对生成的票据进行校验
            String userToken = ssotokenUtil.validateToken(token, "1");

            log.info("调用ssotokenUtil.validateToken方法，返回userToken：" + userToken);
            Map<String, Object> loginFeedback = new HashMap<String, Object>();
            if (userToken.length() > 2) {
                loginFeedback = JSONObject.parseObject(userToken);
            } else {
                loginFeedback = new HashMap<String, Object>();
            }

            if ("0".equals(StringUtils.null2String(loginFeedback.get("code"))) || "103000".equals(StringUtils.null2String(loginFeedback.get("code")))) {
                // 成功
                String mobile = StringUtils.null2String(loginFeedback.get("mobile"));
                String portalUserId = StringUtils.null2String(loginFeedback.get("portalUserId"));
                log.info("该用户token验证通过:portalUserId=" + portalUserId + ",mobile=" + mobile);
                // 成功解释生成的票据并返回
                if (StringUtils.isNotEmpty(portalUserId)) {
                    String oaRequestURL = oaTokenUtil.setToOaIndexPageSSOCookieNew(portalUserId, domainName, response);
                    if (StringUtils.isEmpty(oaRequestURL)) {
                        resultMap.put("code", 2004);
                        resultMap.put("msg", "通过用户ID获取OA系统访问地址失败，单点失败！");
                        resultMap.put("url", "");

                    } else {

                       // response.sendRedirect(oaRequestURL);
                        resultMap.put("code", 200);
                        resultMap.put("msg", "成功获取OA系统地址！");
                        resultMap.put("url", oaRequestURL);

                    }
                }

            } else {

                resultMap.put("code", 2004);
                resultMap.put("msg", "该用户账号不存在，单点失败！");
                resultMap.put("url", "");


            }
        }
        return resultMap;

    }


//    /**
//     * 在E10个人工作台中，如果为IE打开时，会在e10中间页面中先通过portalToken生在票据，再访问IE打开的页面，调用该方法，
//     * 在方法中判断是否为OA系统，如果为OA系统则按OA系统单点的规则先设置cookie，再跳转到OA系统首页，如果为其他系统则先生成票据，再跳转到e10系统首页
//     *
//     * @param request  请求
//     * @param response 响应
//     */
//    @GetMapping("/portalLogin")
//    void portalLogin(HttpServletRequest request, HttpServletResponse response) throws IOException {
//        String url = StringUtil.vString(iDataBaseService.getBaseDataValue("SSO_TO_OA_HOME_PAGE_URL", "OA系统配置"), "https://oa.cmic.chinamobile.com/gmccoa/index.nsf");
//        String domainName = StringUtil.vString(iDataBaseService.getBaseDataValue("PARENT_DOMAIN_NAME", "OA系统配置"), "cmic.chinamobile.com");
//
//        String systemCode = StringUtils.null2String(request.getParameter("systemCode"), "");
//        String e9MiddleUrl = iDataBaseService.getBaseDataValue("E9单点中间页面URL", "门户管理");
//
//        Map<String, Object> resultMap = new HashMap<>();
//        //获取传过来的portaltoken
//        String accessToken = StringUtils.null2String(request.getParameter("portaltoken"));
//        log.info("1、调用SsoController.portalLogin方法，当前用户的accessToken:" + accessToken + ",systemCode:" + systemCode);
//        if ("".equals(accessToken)) {
//            resultMap.put("code", 2004);
//            resultMap.put("msg", "请求token参数为空！");
//            response.setContentType("application/json; charset=utf-8");
//            response.getWriter().print(JSON.toJSONString(resultMap));
//
//        } else {
//            // 解释用户当前用户信息
//            String userToken = ssotokenUtil.validateToken(accessToken, "1");
//            log.info("2、调用ssotokenUtil.validateToken方法，返回userToken：" + userToken);
//            Map<String, Object> loginFeedback = new HashMap<String, Object>();
//            if (userToken.length() > 2) {
//                loginFeedback = JSONObject.parseObject(userToken);
//            } else {
//                loginFeedback = new HashMap<String, Object>();
//            }
//
//            if ("0".equals(StringUtils.null2String(loginFeedback.get("code"))) || "103000".equals(StringUtils.null2String(loginFeedback.get("code")))) {
//                // 成功
//                String mobile = StringUtils.null2String(loginFeedback.get("mobile"));
//                String portalUserId = StringUtils.null2String(loginFeedback.get("portalUserId"));
//
//                Map<String, Object> systemInfoMap = portalService.getSystemInfoBySystemCode(systemCode);
//                log.info("3、调用SsoController.portalLogin方法，获取当前系统信息systemInfoMap：:" + JSONObject.toJSONString(systemInfoMap));
//                if (systemInfoMap.isEmpty()) {
//                    resultMap.put("code", 2004);
//                    resultMap.put("msg", "获取当前系统信息失败！");
//                    response.setContentType("application/json; charset=utf-8");
//                    response.getWriter().print(JSON.toJSONString(resultMap));
//                } else {
//                    // 0:Chrome,1:IE
//                    String liu_lqlx = systemInfoMap.getOrDefault("liu_lqlx", "").toString();
//                    // 系统访问地址
//                    String xi_tdz = systemInfoMap.getOrDefault("xi_tdz", "").toString();
//
//                    // 认证方式，0：portalToken，1：smap，2：smap，2：ticken，3：portalToken，4：gateway，5：portalToken2，6：e10Token，7：e9Token
//                    String ren_zfsh = systemInfoMap.getOrDefault("ren_zfsh", "").toString();
//                    //  认证方式编码
//                    String tokenType = ssotokenUtil.getTokenType(ren_zfsh);
//                    // 新生成的票据
//                    String newUserToken = "";
//                    //  访问地址+新生成的票据
//                    String newUrl = "";
//                    if (tokenType.equals("ticken")) {
//                        ssotokenUtil.getTicken(portalUserId, resultMap);
//                        newUserToken = StringUtils.null2String(resultMap.get("ticken").toString());
//                        newUrl = xi_tdz + newUserToken;
//                        response.sendRedirect(newUrl);
//
//                    } else if (tokenType.equals("portalToken")) {
//                        // 因portalToken认证（wsdl接口，旧接口）和portalToken2（rest 接口，新接口）规则逻辑都是一样，统一改为新接口调用
//                        ssotokenUtil.getPortalToken2(portalUserId, resultMap);
//                        newUserToken = StringUtils.null2String(resultMap.get("ticken").toString());
//                        newUrl = xi_tdz + newUserToken;
//                        response.sendRedirect(newUrl);
//
//
//                    } else if (tokenType.equals("portalToken2")) {
//                        ssotokenUtil.getPortalToken2(portalUserId, resultMap);
//                        newUserToken = StringUtils.null2String(resultMap.get("ticken").toString());
//                        newUrl = xi_tdz + newUserToken;
//                        response.sendRedirect(newUrl);
//                    } else if (tokenType.equals("e9Token")) {
//                        // E9 认证也采用portalToken2 生成票据
//                        ssotokenUtil.getPortalToken2(portalUserId, resultMap);
//                        if (!"".equals(e9MiddleUrl)) {
//                            try {
//                                xi_tdz = e9MiddleUrl + "?goUrl=" + URLEncoder.encode(xi_tdz, "UTF-8") + "&portaltoken=";
//                                newUserToken = StringUtils.null2String(resultMap.get("ticken").toString());
//                                newUrl = xi_tdz + newUserToken;
//                                response.sendRedirect(newUrl);
//                            } catch (UnsupportedEncodingException e) {
//                                throw new RuntimeException(e);
//
//                            }
//
//                        } else {
//                            resultMap.put("code", 2004);
//                            resultMap.put("msg", "该应用为E9应用，未配置E9 中间页面地址！");
//                            response.setContentType("application/json; charset=utf-8");
//                            response.getWriter().print(JSON.toJSONString(resultMap));
//                        }
//                    } else {
//
//
//                        resultMap.put("code", 2004);
//                        resultMap.put("msg", "该应用不在token类型范围内！");
//                        response.setContentType("application/json; charset=utf-8");
//                        response.getWriter().print(JSON.toJSONString(resultMap));
//                    }
//
//                }
//
//            } else {
//                resultMap.put("code", 2004);
//                resultMap.put("msg", "用户登录失败，系统单点失败！");
//                response.setContentType("application/json; charset=utf-8");
//                response.getWriter().print(JSON.toJSONString(resultMap));
//
//            }
//        }
//    }
//

}
