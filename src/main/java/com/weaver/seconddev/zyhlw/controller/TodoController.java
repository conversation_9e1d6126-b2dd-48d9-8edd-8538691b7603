package com.weaver.seconddev.zyhlw.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.weaver.common.authority.annotation.WeaPermission;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.common.form.formapi.FormAPIService;
import com.weaver.common.form.metadata.Form;
import com.weaver.common.form.metadata.field.FormField;
import com.weaver.common.form.metadata.field.FormFieldService;
import com.weaver.common.hrm.dto.emp.HrmEmployeIdFilterMeta;
import com.weaver.common.hrm.service.HrmCommonEmployeeService;
import com.weaver.ebuilder.form.client.entity.data.*;
import com.weaver.ebuilder.form.client.entity.field.ModuleField;
import com.weaver.ebuilder.form.client.entity.obj.Obj;
import com.weaver.ebuilder.form.client.service.data.RemoteSimpleDataService;
import com.weaver.ebuilder.form.client.service.emobile.IEtFormDatasetService;
import com.weaver.framework.rpc.annotation.RpcReference;
import com.weaver.framework.rpc.context.impl.TenantRpcContext;
import com.weaver.seconddev.zyhlw.domain.request.todo.TodoGrayRequest;
import com.weaver.seconddev.zyhlw.domain.request.todo.TodoRequest;
import com.weaver.seconddev.zyhlw.domain.response.todo.FlowListResultResponse;
import com.weaver.seconddev.zyhlw.domain.response.todo.TodoResponse;
import com.weaver.seconddev.zyhlw.domain.response.todo.TodoTabResponse;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.service.ITodoService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.teams.core.orm.mybatis.Page;
import com.weaver.teams.domain.comment.Comment;
import com.weaver.teams.domain.user.SimpleEmployee;
import com.weaver.teams.security.context.UserContext;
import com.weaver.teams.security.user.User;
import com.weaver.workflow.common.entity.core.flow.RequestBaseInfoEntity;
import com.weaver.workflow.core.api.rest.flow.WfcRequestCommonRest;
import com.weaver.workflow.core.api.rest.publicapi.WfcGetRequestDataRest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping({"/api/secondev/zyhlw/todoController"})
public class TodoController {
    @Resource
    private ITodoService iTodoService;
    @Resource
    private IOpenPlatformService iOpenPlatformService;

   private String simpleName = TodoController.class.getSimpleName();
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/getTodo")
    WeaResult<List<TodoResponse>> getTodo(@RequestBody TodoRequest todoRequest){
        List<TodoResponse> todoResponses = new ArrayList<>();
        log.info("调用TodoController.getTodo 入参：{}", JSON.toJSONString(todoRequest));
        return WeaResult.success(todoResponses,"请求成功");
    }

    /**
     * 查询待办数量接口
     * @return
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/getTotal")
    WeaResult<List<TodoTabResponse>> getTotal(@RequestBody TodoRequest todoRequest){
        List<TodoTabResponse> todoTotalResponses = new ArrayList<>();
        log.info("调用TodoController.getTotal 入参：{}",JSON.toJSONString(todoRequest));
        iTodoService.getTab(todoRequest);
        return WeaResult.success(todoTotalResponses,"请求成功");
    }

    /**
     * 置灰接口
     * @param todoGrayRequest
     * @return
     */
    @WeaPermission(publicPermission = true)
    @PostMapping(value = "/updateGray")
    WeaResult<Boolean> updateGray(@RequestBody TodoGrayRequest todoGrayRequest){
        log.info("调用TodoController.updateGray 入参：{}", JSONObject.toJSONString(todoGrayRequest));
        iTodoService.updateGray(todoGrayRequest);
        return WeaResult.success(true,"请求成功");
    }
//    @RpcReference
//    WfcGetRequestDataRest wfcGetRequestDataRest;
//    @RpcReference(group = "workflow")
//    WfcRequestCommonRest wfcRequestCommonRest;
//    @GetMapping(value = "/test2")
//    WeaResult<Page<Comment>> test2(){
//      String methodName=  "调用"+simpleName+".test2()";
//      log.info("{} 进入",methodName);
//        try {
//            Page<Comment> flowComment = wfcRequestCommonRest.getFlowComment(1034455934314782722L, 110001700000001879L, 1, 100);
//            log.info("{} 获取流程意见：{}",methodName,JSON.toJSONString(flowComment));
//          return WeaResult.success(flowComment);
//      }catch (Exception e){
//          log.error("{} 请求异常：",methodName,e);
//        return  WeaResult.fail("请求异常");
//      }
//
//    }
//
//    @GetMapping(value = "/test3")
//    WeaResult<List<RequestBaseInfoEntity>> test3(){
//        String methodName=  "调用"+simpleName+".test3()";
//        log.info("{} 进入",methodName);
//        List<Long> requestIds = new ArrayList<>();
//        requestIds.add(110504370006368831L);
//        requestIds.add(110504370006370845L);
//        SimpleEmployee simpleEmployee = new SimpleEmployee();
//        simpleEmployee.setId(110001700000001879L);
//        List<RequestBaseInfoEntity> requestBaseInfoEntities = wfcGetRequestDataRest.queryByRequestIds(requestIds, simpleEmployee);
//        log.info("{} 请求完成");
//        for (RequestBaseInfoEntity requestBaseInfoEntity : requestBaseInfoEntities) {
//            log.info("{} 批量根据流程Id获取流程基本信息：{}",methodName,JSON.toJSONString(requestBaseInfoEntity));
//        }
//        return WeaResult.success(requestBaseInfoEntities);
//    }
//    @Resource
//    HrmCommonEmployeeService hrmCommonEmployeeService;
//    @Resource
//    CmicProperties cmicProperties;
//    @GetMapping(value = "/test4")
//    WeaResult<List<SimpleEmployee>> test4(){
//        String methodName=  "调用"+simpleName+".test4()";
//        log.info("{} 进入",methodName);
//        List<Long> ids = new ArrayList<>();
//        ids.add(110001700000000530L);
//        ids.add(110001700000001879L);
//        HrmEmployeIdFilterMeta filterMeta = new HrmEmployeIdFilterMeta();
//        filterMeta.setCorvertExtraOrg(false);
//        filterMeta.setIncludeDelete(false);
//        filterMeta.setIncludeDifTenantKey(false);
//        try {
//            List<SimpleEmployee> employeeByIdsFilterMeta = hrmCommonEmployeeService.getEmployeeByIdsFilterMeta(ids, cmicProperties.getHostTenantKey(), filterMeta);
//            return WeaResult.success(employeeByIdsFilterMeta);
//        }catch (Exception e){
//            log.error("{} 请求异常：",methodName,e);
//            return  WeaResult.fail("请求异常");
//        }
//    }
//    @Resource
//    FormAPIService formAPIService;
//    @Resource
//    FormFieldService formFieldService;
//    @RpcReference(group = "ebuilderform")
//    IEtFormDatasetService iEtFormDatasetService;
//    @RpcReference(group = "ebuilderform")
//    private RemoteSimpleDataService remoteSimpleDataService;
//    @GetMapping(value = "/test5")
//    WeaResult<String> test5(){
//        String methodName=  "调用"+simpleName+".test5()";
//        log.info("{} 进入",methodName);
//
//            String tenantKey = TenantRpcContext.getTenantKey();
//            log.info("{} 获取到的租户key：{}",methodName,tenantKey);
//        // 获取当前用户
//        User currentUser = UserContext.getCurrentUser();
//        // 用户employeeId
//         Long employeeId = currentUser.getEmployeeId();
//        // 租户key
//       currentUser.getTenantKey();//租户
//        try {
//            SimpleEmployee simpleEmployee = new SimpleEmployee();
//            simpleEmployee.setId(employeeId);
//            simpleEmployee.setTenantKey(currentUser.getTenantKey());
//            log.info("{} 请求用户数据：{}",methodName,JSON.toJSONString(simpleEmployee));
//            List<Form> forms = formAPIService.listFormByNameAndTenantKey(null, simpleEmployee);
//            log.info("{} listFormByNameAndTenantKey：{}",methodName,JSON.toJSONString(forms));
//        }catch (Exception e){
//            log.error("{} formAPIService异常",methodName,e);
//        }
//        try {
//            List<Obj> tables = iEtFormDatasetService.getTables("982811074357518336");
//            log.info("{} 获取当前应用下表单：{}",methodName,JSON.toJSONString(tables));
//        }catch (Exception e){
//            log.error("{} 获取当前应用下表单",methodName,e);
//        }
//        try {
//            List<ModuleField> fields = iEtFormDatasetService.getFields(null, 1037452275351003136L, "1", true);
//            for (ModuleField field : fields) {
//                log.info("{} 获取表单字段1：{}",methodName,JSON.toJSONString(field));
//            }
//        }catch (Exception e){
//            log.error("{} 获取表单字段1异常",methodName,e);
//        }
//        try {
//            EBDataChangeReqDto ebDataChangeReqDto = new EBDataChangeReqDto();// 构建基础参数; objId 表单id, operator 操作人, tenantKey 租户
//            ebDataChangeReqDto.setHeader(new EBDataReqHeader("1037452275351003136", employeeId+"", currentUser.getTenantKey()));
//            List<EBDataReqDto> datas = new ArrayList<>();
//            // 若数据写入后就要从前台看到数据或者立即操作本次写入的数据, 需要调整后置处理为同步 权限处理完成再返回
//            EBDataReqOperation operation = new EBDataReqOperation();
//            operation.setAsyncPostProcess(false);
//            ebDataChangeReqDto.setOperation(operation);
//
//
//            EBDataReqDto ebDataReqDto = new EBDataReqDto();
//            List<EBDataReqDetailDto> mainData1 =new ArrayList<>();
//            // 单行文本
//            mainData1.add(new EBDataReqDetailDto("1038464719443697665", "1034455934314782722"));
//            mainData1.add(new EBDataReqDetailDto("1040005822328315904", employeeId+""));
//            ebDataReqDto.setMainDatas(mainData1);
//
//            datas.add(ebDataReqDto);
//
//
//
//            ebDataChangeReqDto.setDatas(datas);
//            EBDataChangeResult ebDataChangeResult = remoteSimpleDataService.saveFormData(ebDataChangeReqDto);
//            log.info("{} 新增数据返回：{}",methodName,JSON.toJSONString(ebDataChangeResult));
//        }catch (Exception e){
//            log.info("{} 新增数据异常",methodName,e);
//        }
//
//        return WeaResult.success("请求成功");
//
//    }
}
