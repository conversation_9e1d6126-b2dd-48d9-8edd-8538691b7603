package com.weaver.seconddev.zyhlw.controller;

import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.seconddev.zyhlw.domain.request.todo.TodoRemindRequest;
import com.weaver.seconddev.zyhlw.domain.response.todo.TodoRemindResponse;
import com.weaver.seconddev.zyhlw.domain.response.todo.TodoResponse;
import com.weaver.seconddev.zyhlw.service.ITodoRemindService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> by herry on 2025-02-07.
 * Update date:
 * Time: 11:52
 * Project: ecology
 * Package: com.weaver.seconddev.zyhlw.service.impl
 * Command:
 * <p>
 * Status：Using online
 * <p>
 * Please note:
 * Must be checked once every time you submit a configuration file is correct!
 * Data is priceless! Accidentally deleted the consequences!
 */
@Slf4j
@RestController
@RequestMapping({"/sapi/secondev/zyhlw/todoRemind"})
public class TodoRemindController {
    @Resource
    private ITodoRemindService iTodoRemindService;

    /**
     * queryTodo
     * <p>
     * 根据用户loginId查询代办记录 。
     * <p>
     * <AUTHOR>
     * @time 2025年02月10 10:04:04
     * @since 1.0
     */
    @PostMapping(value = "/queryTodoAllList")
    public WeaResult<List<TodoRemindResponse>> queryTodoAllList(){
        return iTodoRemindService.queryTodoAllList();
    }

//    /**
//     * queryTodo
//     * <p>
//     * 查询所有用户代办记录 。
//     * <p>
//     * <AUTHOR>
//     * @time 2025年02月10 10:04:04
//     * @since 1.0
//     */
//    @PostMapping(value = "/queryTodoAll")
//    public WeaResult<List<TodoRemindResponse>> queryTodoAll(){
//        return iTodoRemindService.queryTodoAll();
//    }

}
