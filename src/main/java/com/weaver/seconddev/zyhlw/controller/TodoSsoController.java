package com.weaver.seconddev.zyhlw.controller;

import com.alibaba.fastjson.JSON;
import com.weaver.seconddev.zyhlw.domain.request.todo.TodoSsoRequest;
import com.weaver.seconddev.zyhlw.service.ITodoSsoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping({"/papi/secondev/zyhlw/todosso"})
public class TodoSsoController {

    String simpleName = TodoSsoController.class.getSimpleName();
    @Resource
    ITodoSsoService iTodoSsoService;

    @GetMapping("/sso")
    public void  sso(HttpServletResponse response,TodoSsoRequest param){
        String method = "调用"+simpleName+".sso()-->";
        log.info("{}请求入参：{}",method, JSON.toJSONString(param));
        if (param.getAuthType() == null) {
            param.setAuthType("ticket");
//            param.setSysCode("CMIC_GYL");
        }

        Map<String, Object> map = iTodoSsoService.sso(param);
        Integer code = (Integer) map.get("code");
        response.setCharacterEncoding("UTF-8");

        if (code == 0) {
            String url = (String) map.get("url");
            try {
                response.sendRedirect(url);

            } catch (IOException e) {
                log.error("{} 重定向异常！", method, e);
            }
        } else {
            try {
                response.setContentType("application/json; charset=utf-8");
                response.getWriter().print(JSON.toJSONString(map));
            } catch (IOException e) {
                log.error("{} 返回异常！", method, e);
            }
        }
    }
}
