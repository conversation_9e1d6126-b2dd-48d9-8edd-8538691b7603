package com.weaver.seconddev.zyhlw.controller;

import com.weaver.common.authority.annotation.WeaPermission;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.openapi.pojo.basicserver.res.vo.AccountVo;
import com.weaver.seconddev.zyhlw.domain.request.token.TokenRequest;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.service.ITokenService;
import com.weaver.teams.security.context.UserContext;
import com.weaver.teams.security.user.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping({"/api/secondev/zyhlw/token"})
public class TokenController {
    @Resource
    ITokenService iTokenService;
    @Resource
    IOpenPlatformService iOpenPlatformService;
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/v1/{type}")
    WeaResult<String> getToken(@PathVariable String type, @ModelAttribute TokenRequest request){
        // 获取当前用户
        User currentUser = UserContext.getCurrentUser();
        // 用户employeeId
        Long employeeId = currentUser.getEmployeeId();

        AccountVo account = iOpenPlatformService.findAccount(employeeId);
        String token = "";
        if ("ticket".equals(type)) {
            // 统一用户ticket
            token = iTokenService.generateTicket(account.getLoginName());
            if (token == null || "".equals(token)) {
                return WeaResult.fail("获取统一用户ticket失败");
            }else{
                return WeaResult.success(token,"获取统一用户ticket成功");
            }

        } else if ("portalToken".equals(type)) {
            // 获取portalToken
            token = iTokenService.generatePortalToken(account.getLoginName());
            if (token == null || "".equals(token)) {
                return WeaResult.fail("获取PortalToken失败");
            }else {
                return WeaResult.success(token,"获取PortalToken成功");
            }
        } else {
            return WeaResult.fail("票据类型不存在");
        }
    }
}
