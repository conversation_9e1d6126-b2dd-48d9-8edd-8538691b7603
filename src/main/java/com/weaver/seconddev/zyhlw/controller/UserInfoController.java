package com.weaver.seconddev.zyhlw.controller;


import com.weaver.common.authority.annotation.WeaPermission;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.seconddev.zyhlw.service.IUserInfoService;
import com.weaver.teams.domain.user.SimpleEmployee;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping({"/api/zyhlw/web/user"})
public class UserInfoController {

    @Resource
    private IUserInfoService userInfoService;

    /**
     * 查询当前登录用户信息
     * @return 用户信息
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/getUserInfo")
    public WeaResult<SimpleEmployee> getUserInfo() {
        return userInfoService.getUserInfo();
    }
}

