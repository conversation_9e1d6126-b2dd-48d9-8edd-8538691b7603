package com.weaver.seconddev.zyhlw.controller;

import com.weaver.common.authority.annotation.WeaPermission;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.seconddev.zyhlw.domain.response.todo.TodoTabResponse;
import com.weaver.seconddev.zyhlw.service.ISynHrService;
import com.weaver.seconddev.zyhlw.service.ITodoService;
import com.weaver.seconddev.zyhlw.service.IWorkflowTodoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 流程-待办流程控制
 *
 * <AUTHOR>
 * @Date 14:54 2024/7/1
 * @Param
 * @return
 **/
@Slf4j
@RestController
@RequestMapping({"/api/secondev/zyhlw/worlflowTodoController"})
public class WorkflowTodoController {
    @Resource
    private ITodoService iTodoService;
    @Resource
    private ISynHrService iSynHrService;
    @Resource
    private IWorkflowTodoService iWorkflowTodoService;
//
//    /**
//     * 查询流程待办接口
//     *
//     * <AUTHOR>
//     * @Date 14:56 2024/7/1
//     * @Param [todoRequest]
//     **/
//    @WeaPermission(publicPermission = true)
//    @GetMapping(value = "/getTodoListData")
//    WeaResult<List<TodoResponse>> getTodoListData(@RequestBody TodoRequest todoRequest){
//        List<TodoResponse> todoResponses = new ArrayList<>();
//        log.info("调用WorlflowTodoController.getTodoListData 入参：{}"+ JSON.toJSONString(todoRequest));
//        return WeaResult.success(todoResponses,"请求成功");
//    }
//
//    /**
//     * 查询流程待办数量接口
//     * @return
//     */
//    @WeaPermission(publicPermission = true)
//    @GetMapping(value = "/getWorkflowTodoTotal")
//    WeaResult<List<TodoTabResponse>> getWorkflowTodoTotal(@RequestBody TodoRequest todoRequest){
//        List<TodoTabResponse> todoTotalResponses = new ArrayList<>();
//        log.info("调用TodoController.getTotal 入参：{}",JSON.toJSONString(todoRequest));
//        iTodoService.getTab(todoRequest);
//        return WeaResult.success(todoTotalResponses,"请求成功");
//    }

    /**
     * 同步待办数据接口
     * @return
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/syncWorkflowTodoData")
    WeaResult<List<TodoTabResponse>> syncWorkflowTodoData() {
        List<TodoTabResponse> todoTotalResponses = new ArrayList<>();
        iWorkflowTodoService.syncWorkflowTodoJt();
        return WeaResult.success(todoTotalResponses,"请求成功");
    }

    /**
     * 稽核待办数据接口
     * @return
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/checkWorkflowTodoData")
    WeaResult<List<TodoTabResponse>> checkWorkflowTodoData() {
        List<TodoTabResponse> todoTotalResponses = new ArrayList<>();
        iWorkflowTodoService.checkWorkflowTodoData();
        return WeaResult.success(todoTotalResponses,"请求成功");
    }


}
