package com.weaver.seconddev.zyhlw.controller.appeal;

import com.weaver.common.authority.annotation.WeaPermission;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.seconddev.zyhlw.domain.appeal.AppealHelpDTO;
import com.weaver.seconddev.zyhlw.domain.appeal.AppealListDTO;
import com.weaver.seconddev.zyhlw.domain.appeal.AppealListRespVO;
import com.weaver.seconddev.zyhlw.domain.appeal.AppealTypeListRespVO;
import com.weaver.seconddev.zyhlw.service.IAppealCenterService;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 诉求中心相关api
 * <p/>
 * 原E9接口: com.api.zyhlw.web.AppealCenterApi
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@RestController
@Slf4j
@RequestMapping("/api/secondev/zyhlw/appeal/center")
public class AppealCenterController {

    @Resource
    private IAppealCenterService appealCenterService;

    /**
     * 获取诉求分类
     *
     * @param id 分类ID
     * @return 诉求分类列表
     * <AUTHOR>
     * @date 2025-05-22 11:01:00
     */
    @GetMapping("/getTypeList")
    @WeaPermission(publicPermission = true)
    public WeaResult<List<AppealTypeListRespVO>> getTypeList(@RequestParam(value = "id", required = false) String id) {
        try {
            return WeaResult.success(appealCenterService.getTypeList(id));
        } catch (Exception e) {
            log.error("获取诉求分类失败", e);
            return WeaResult.fail("获取诉求分类失败: " + e.getMessage());
        }
    }

    /**
     * 获取诉求列表
     *
     * @param appealListDTO 诉求列表请求参数
     * @return 诉求列表
     * <AUTHOR>
     * @date 2025-05-22 11:01:00
     */
    @PostMapping("/getAppealList")
    @WeaPermission(publicPermission = true)
    public WeaResult<AppealListRespVO> getAppealList(@RequestBody AppealListDTO appealListDTO) {
        try {
            return WeaResult.success(appealCenterService.getAppealList(appealListDTO));
        } catch (Exception e) {
            log.error("获取诉求列表失败", e);
            return WeaResult.fail("获取诉求列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取诉求详情
     *
     * @param no 编码
     * @return 诉求详情
     * <AUTHOR>
     * @date 2025-05-22 11:01:00
     */
    @GetMapping("/getAppealDetail")
    @WeaPermission(publicPermission = true)
    public WeaResult<Map<String, Object>> getAppealDetail(@RequestParam("bian_h") String no) {
        if (StringUtils.isBlank(no)) {
            return WeaResult.fail("编码不能为空");
        }
        return WeaResult.success(appealCenterService.getAppealDetail(no));
    }

    /**
     * 插入没帮助问题记录
     *
     * @param appealHelpDTO 没帮助问题记录请求参数
     * @return 是否成功
     * <AUTHOR>
     * @date 2025-05-22 11:01:00
     */
    @PostMapping("/addAppealNoHelp")
    @WeaPermission(publicPermission = true)
    public WeaResult<Boolean> addAppealNoHelp(@RequestBody @Valid AppealHelpDTO appealHelpDTO) {
        try {
            return WeaResult.success(appealCenterService.addAppealNoHelp(appealHelpDTO));
        } catch (Exception e) {
            log.error("插入没帮助问题记录失败", e);
            return WeaResult.fail("插入没帮助问题记录失败: " + e.getMessage());
        }
    }

    /**
     * 有帮助数+1
     *
     * @param appealHelpDTO 请求体
     * @return 是否成功
     * <AUTHOR>
     * @date 2025-05-22 11:01:00
     */
    @PostMapping("/addAppealHelp")
    @WeaPermission(publicPermission = true)
    public WeaResult<Boolean> addAppealHelp(@RequestBody @Valid AppealHelpDTO appealHelpDTO) {
        try {
            return WeaResult.success(appealCenterService.addAppealHelp(appealHelpDTO));
        } catch (Exception e) {
            log.error("有帮助数+1失败", e);
            return WeaResult.fail("有帮助数+1接口调用失败: " + e.getMessage());
        }
    }

    /**
     * 获取热门诉求列表
     */
    @GetMapping("/getHotAppealList")
    @WeaPermission(publicPermission = true)
    public WeaResult<AppealListRespVO> getHotAppealList(
            @RequestParam(value = "currentPage", required = false, defaultValue = "1") Integer currentPage,
            @RequestParam(value = "pageSize", required = false, defaultValue = "20") Integer pageSize) {
        try {
            return WeaResult.success(appealCenterService.getHotAppealList(currentPage, pageSize));
        } catch (Exception e) {
            log.error("获取热门诉求列表失败", e);
            return WeaResult.fail("获取热门诉求列表失败: " + e.getMessage());
        }
    }

    /**
     * 设置热门
     *
     * @param ids 诉求ID列表，多个ID用逗号分隔
     * @param hot 是否热门 0否 1是
     * @return 是否成功
     * <AUTHOR>
     * @date 2025-05-22 11:01:00
     */
    @GetMapping("/setHot")
    @WeaPermission(publicPermission = true)
    public WeaResult<Boolean> setHot(@RequestParam("ids") String ids, @RequestParam("hot") String hot) {
        try {
            return WeaResult.success(appealCenterService.setHot(ids, hot));
        } catch (Exception e) {
            log.error("设置热门失败", e);
            return WeaResult.fail("设置热门失败: " + e.getMessage());
        }
    }
}
