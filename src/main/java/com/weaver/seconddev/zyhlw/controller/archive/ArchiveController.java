package com.weaver.seconddev.zyhlw.controller.archive;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.weaver.common.authority.annotation.WeaPermission;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.seconddev.zyhlw.service.IArchiveService;

import lombok.extern.slf4j.Slf4j;

/***
 * 拼车功能api
 * <p/>
 * 原E9接口: com.api.zyhlw.web.pinche.PingCheApi
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
@Slf4j
@RestController
@RequestMapping("/api/secondev/zyhlw/archive")
@WeaPermission(publicPermission = true)
public class ArchiveController {

    @Resource
    private IArchiveService archiveService;

    /**
     * 短信列表
     * 
     * 原E9接口: com.api.zyhlw.web.dangan.GetDASmsApi.list
     * 
     * @param orderId
     * @param pageNo
     * @param pageSize
     * @return
     * 
     * <AUTHOR>
     * @date 2025-06-04
     */
    @GetMapping("/sms/list")
    public WeaResult<String> smsList(@RequestParam(value = "orderid") @NotNull(message = "订单号不能为空") String orderId,
            @RequestParam(value = "pageNo", required = false, defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize) {
        try {
            return WeaResult.success(JSON.toJSONString(archiveService.smsList(orderId, pageNo, pageSize)));
        } catch (Exception e) {
            log.error("短信列表失败", e);
            return WeaResult.fail("获取短信列表失败, " + e.getMessage());
        }
    }

}
