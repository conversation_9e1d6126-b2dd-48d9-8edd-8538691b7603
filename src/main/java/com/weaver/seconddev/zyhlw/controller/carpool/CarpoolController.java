package com.weaver.seconddev.zyhlw.controller.carpool;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.weaver.common.authority.annotation.WeaPermission;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.seconddev.zyhlw.service.ICarpoolService;

import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/***
 * 拼车功能api
 * <p/>
 * 原E9接口: com.api.zyhlw.web.pinche.PingCheApi
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
@Slf4j
@RestController
@RequestMapping("/api/secondev/zyhlw/carpool")
@WeaPermission(publicPermission = true)
public class CarpoolController {

    @Resource
    private ICarpoolService carpoolService;

    /**
     * 点击日志
     * 
     * @param billId 账单id
     * @return 是否成功
     * 
     * <AUTHOR>
     * @date 2025-05-29 10:00:00
     */
    @GetMapping("clickLog")
    public WeaResult<Boolean> clickLog(@RequestParam("billid") String billId) {
        return WeaResult.success(carpoolService.clickLog(billId));
    }

}
