package com.weaver.seconddev.zyhlw.controller.formdata;


import com.alibaba.fastjson.JSONObject;
import com.weaver.common.authority.annotation.WeaPermission;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.seconddev.zyhlw.domain.formdata.FormDataListReqDTO;
import com.weaver.seconddev.zyhlw.service.impl.FormDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 表单数据相关操作接口
 *
 * <AUTHOR>
 * @date 2025年05月06日 15:13
 */
@Slf4j
@RestController
@RequestMapping({"/api/secondev/zyhlw/formData"})
public class FormDataController {

    @Resource
    private FormDataService formDataService;


    /**
     * 获取表单数据列表接口
     * 原e9接口：/zyhlw/web/JMDataApi/FormDataApi/getList2
     *
     * @param reqDTO 请求入参
     * @return com.weaver.common.base.entity.result.WeaResult<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @date 2025/5/6 15:22
     */
    @PostMapping("/list")
    @WeaPermission(publicPermission = true)
    public WeaResult<JSONObject> getFormDataList(@RequestBody @Valid FormDataListReqDTO reqDTO) {
        return formDataService.getFormDataList(reqDTO);
    }

}
