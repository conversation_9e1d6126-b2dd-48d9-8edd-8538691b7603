package com.weaver.seconddev.zyhlw.controller.income;

import com.weaver.common.authority.annotation.WeaPermission;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.seconddev.zyhlw.service.income.IFinanceCheckedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping({"/api/secondev/zyhlw/controller/FinanceChecked"})
public class FinanceCheckedController {
    @Resource
    IFinanceCheckedService iFinanceCheckedService;

    /**
     * 判断是否有在途的红冲或者作废的发票
     *
     * @param hong_cfpje 红冲或者作废的发票号码
     * @return
     */
    @WeaPermission(publicPermission = true)
    @GetMapping("/addFlow")
    public WeaResult<Object> addFlow(@RequestParam("hong_cfpje") String hong_cfpje) {
        Map<String, String> resultMap = iFinanceCheckedService.addFlow(hong_cfpje);
        return WeaResult.success(resultMap);

    }

    /**
     * 1、当一个发票同时勾稽多个应收单，系统判断这张发票的勾稽金额汇总数情况;
     * 1)小于或等于发票未勾稽金额，系统不做预警;
     * 2)大于发票未勾稽金额，系统弹窗提示:勾稽金额已超过发票金额;
     * 2、当一张应收单同时勾稽多张发票时，系统判断这张应收单的勾稽金额汇总数情况;应小于或等于应收单的未开票金额。
     * 1)小于或等于未开票金额，系统不做预警;
     * 2)大于未开票金额，系统弹窗提示:勾稽发票金额已超过未开票金额;
     *
     * @param arr 传过来的josn 数据
     * @return
     */
    @WeaPermission(publicPermission = true)
    @GetMapping("/newSelverify")
    public WeaResult<Object> newSelverify(@RequestParam("arr") String arr) {
        Map<String, Object> resultMap = iFinanceCheckedService.newSelverify(arr);
        return WeaResult.success(resultMap);

    }

    /**
     * 判断相关勾稽记录是否存在
     *
     * @param gouji_id 勾稽记录ID
     * @return
     */
    @WeaPermission(publicPermission = true)
    @GetMapping("/SelFlow")
    public WeaResult<Object> SelFlow(@RequestParam("gouji_id") String gouji_id) {
        Map<String, String> resultMap = iFinanceCheckedService.selFlow(gouji_id);
        return WeaResult.success(resultMap);

    }

    /**
     * 获取发票最大Id值
     *
     * @return
     */
    @WeaPermission(publicPermission = true)
    @GetMapping("/GetMaxId")
    public WeaResult<Object> GetMaxId() {
        Integer maxId = iFinanceCheckedService.GetMaxId();
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("code", "1");
        resultMap.put("id", maxId.toString());
        return WeaResult.success(resultMap);
    }

    /**
     * 通过ID判断合同欠费规则中的数据中的是否已发起设置欠费规则流程
     *
     * @return
     */
    @WeaPermission(publicPermission = true)
    @GetMapping("/GetEnterpriseOverduePaymentRules")
    public WeaResult<Object> GetEnterpriseOverduePaymentRules(@RequestParam("ids") String ids) {
        Map<String, String> result = iFinanceCheckedService.GetEnterpriseOverduePaymentRulesMap(ids);
        return WeaResult.success(result);
    }


}
