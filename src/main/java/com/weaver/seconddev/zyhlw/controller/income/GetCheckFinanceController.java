package com.weaver.seconddev.zyhlw.controller.income;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.weaver.common.authority.annotation.WeaPermission;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.seconddev.zyhlw.service.impl.invoice.GetInvoiceInformationImpl;
import com.weaver.seconddev.zyhlw.service.income.IGetCheckFinanceService;
import com.weaver.seconddev.zyhlw.service.income.IIncomeService;
import com.weaver.seconddev.zyhlw.service.income.IInvoiceStatisticService;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 发票模块功能
 * @date 2025/2/27 17:38
 */
@Slf4j
@RestController
@RequestMapping({"/api/secondev/zyhlw/controller/GetCheckFinance"})
public class GetCheckFinanceController {
    public static final String S = "{}开始执行";
    private final String simpleName = GetCheckFinanceController.class.getSimpleName();

    @Resource
    public IGetCheckFinanceService iGetCheckFinanceService;

    @Resource
    public IInvoiceStatisticService iInvoiceStatisticService;

    /**
     * 该接口用于校验财务系统的的数据，可以根据验证类型来决定
     * ids  对应的id 1,2,3,4
     * faphms 发票号码  “f”,"d"
     * checkType
     * 1、校验是否同一个合同（根据应收ID）
     * 2、校验应收单勾稽状态是否已勾稽（根据应收ID）
     * 3、校验发票状态是否正常（根据发票ID）
     * 4、校验勾稽记录是否反勾稽（根据勾稽记录明细ID）
     * 5、验证勾稽记录数据是否正常(根据发票ID)
     * 6、验证勾稽记录数据是否正常(根据应收ID)
     * 7、验证勾稽记录数据是否正常(勾稽记录ID)
     * 8、验证发票号码是否唯一(发票号码)
     * 9、验证红冲金额不能大于发票的已勾稽金额（JSON ）原发票ID[{faId:1,jine:9.0},{faId:1,jine:9.0}]
     * 10、验证开票金额大于应收报账单未开票的总金额（JSON）
     * <p>
     * 12,验证发票反勾稽是否有重复勾稽
     * 13,执行校验勾稽记录是否反勾稽方法
     * 14,验证 红冲可用金额是否大于等于0
     */
    @WeaPermission(publicPermission = true)
    @GetMapping("/getCheck")
    public WeaResult<Object> getCheck(HttpServletRequest request) {
        String method = String.format("1、调用%s.getCheck-->", simpleName);
        log.info(S, method);
        Map<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        String ids = StringUtils.null2String(request.getParameter("ids"));
        String checkType = StringUtils.null2String(request.getParameter("checkType"));
        try {
            log.info("2、获取到的参数：  ids 【" + ids + "】 checkType 【" + checkType + "】");
            resultMap.put("msg", "校验通过");
            resultMap.put("code", 1);
            switch (checkType) {
                case "1":
                    //校验是否同一个合同（根据应收id）
                    iGetCheckFinanceService.checkHeTong(ids, resultMap);
                    break;
                case "2":
                    //校验应收单勾稽状态是否已勾稽
                    iGetCheckFinanceService.checkGJZT(ids, resultMap);
                    break;
                case "3":
                    //校验发票状态是否正常
                    iGetCheckFinanceService.checkFPZT(ids, resultMap);
                    break;
                case "4":
                    //校验勾稽记录是否有勾稽金额
                    iGetCheckFinanceService.checkGJJU(ids, resultMap);
                    break;
                case "5":
                    //验证勾稽记录数据是否正常(根据发票ID)
                    iGetCheckFinanceService.checkGJJLFP(ids, resultMap);
                    break;
                case "6":
                    //验证勾稽记录数据是否正常(应收ID)
                    iGetCheckFinanceService.checkGJJLYS(ids, resultMap);
                    break;
                case "7":
                    //验证勾稽记录数据是否正常(勾稽记录ID)
                    iGetCheckFinanceService.checkGJJLID(ids, resultMap);
                    break;
                case "8":
                    //验证发票号码是否唯一(发票号码)
                    String faphms = StringUtils.null2String(request.getParameter("faphms"));
                    iGetCheckFinanceService.checkFPHM(faphms, resultMap);
                    break;
                case "9":
                    //验证红冲金额不能大于发票的已勾稽金额（JSON ）原发票ID[{faId:1,jine:9.0},{faId:1,jine:9.0}]
                    String parm = StringUtils.null2String(request.getParameter("parm"));
                    iGetCheckFinanceService.checkHCJE(parm, resultMap);
                    break;
                case "10":
                    //验证开票金额大于应收报账单未开票的总金额（JSON）
                    String parm_10 = StringUtils.null2String(request.getParameter("parm"));
                    iGetCheckFinanceService.checkKPJE(parm_10, resultMap);
                    break;
                case "12":
                    //验证发票反勾稽是否有重复勾稽
                    iGetCheckFinanceService.checkGJJLFP2(ids, resultMap);
                    break;
                case "13":
                    //验证发票是否被反勾稽
                    iGetCheckFinanceService.checkGJJU2(ids, resultMap);
                    break;
                case "14":
                    //验证 红冲可用金额是否大于等于0
                    iGetCheckFinanceService.checkGJJLFP3(ids, resultMap);
                    break;
                case "15":
                    //验证发票是否被反勾稽
                    iGetCheckFinanceService.checkdate(ids, list);
                    break;
                case "16":
                    //发票的未勾稽金额为0，请重新选择
                    iGetCheckFinanceService.checkfpwgjje(ids, resultMap);
                    break;
                case "17":
                    //客户编号是否存在
                    iGetCheckFinanceService.ChenKhbhSel(ids, resultMap);
                    break;
                case "18":
                    //判断对应发票下面是否存在未归档的工单（1：批准，2：提交）
                    iGetCheckFinanceService.ChenOrderOnWayByFPBH(ids, resultMap);
                    break;
                default:
                    resultMap.put("msg", "没有校验类型");
                    resultMap.put("code", 0);
                    break;
            }
            if (checkType.equals("15")) {
                return WeaResult.success(list);
            }

        } catch (Exception ex) {
            String msg = String.format("getCheck校验失败，错误信息：%s", ex.getMessage());
            log.error("getCheck校验失败，错误信息：" + msg);
            return WeaResult.fail(msg);

        }
        // String method = String.format("1、{}调用%s.getCheck-->", simpleName);
        log.info("2、调用{}.getCheck,返回resultMap：{}", method, JSONObject.toJSONString(resultMap));
        return WeaResult.success(resultMap);

    }


    /**
     * 更新勾稽记录的同步状态
     * @param gjId
     * @return
     */
    @WeaPermission(publicPermission = true)
    @GetMapping("/updateByGjId")
    public WeaResult<Object> updateByGjId(@RequestParam("gjId") String gjId) {
        String method = String.format("1、调用%s.updateByGjId-->", simpleName);
        log.info(S, method);
        Map<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        try {
            resultMap = iInvoiceStatisticService.updateByGjId(gjId);
        } catch (Exception ex) {
            resultMap.put("isSuccess", 0);
            resultMap.put("msg", "数据更新失败，原因：" + ex.getMessage());

        }
        // String method = String.format("1、{}调用%s.getCheck-->", simpleName);
        log.info("2、调用{}.getCheck,返回resultMap：{}", method, JSONObject.toJSONString(resultMap));
        return WeaResult.success(resultMap);

    }


}
