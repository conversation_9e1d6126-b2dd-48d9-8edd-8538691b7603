package com.weaver.seconddev.zyhlw.controller.invoice;

import com.weaver.common.authority.annotation.WeaPermission;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.seconddev.zyhlw.service.IGetInvoiceInformationService;
import com.weaver.seconddev.zyhlw.service.impl.invoice.GetInvoiceInformationImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: 发票模块功能
 * @date 2025/2/19 17:38
 */
@Slf4j
@RestController
@RequestMapping({"/api/secondev/zyhlw/controller/invoice"})

public class GetInvoiceInformationController {
    public static final String S = "{}开始执行";
    private final String simpleName = GetInvoiceInformationController.class.getSimpleName();

    @Resource
    IGetInvoiceInformationService iGetInvoiceInformationService;


    /**
     * 根据IDS查询发票信息。
     *
     * @param ids 以逗号分隔的发票ID字符串
     * @return 包含发票信息的结果对象
     */
    @WeaPermission(publicPermission = true)
    @GetMapping("/getInvoiceListByIds/{ids}")
    public WeaResult<Object> getInvoiceListByIds(@PathVariable("ids") String ids) {
        String method = String.format("调用%s.getInvoiceListByIds-->", simpleName);
        log.info(S, method);
        return iGetInvoiceInformationService.getInvoiceListByIds(ids);
    }


    /**
     * 根据IDS查询发票饭勾稽信息。
     *
     * @param ids 以逗号分隔的发票ID字符串
     * @return 包含发票信息的结果对象
     */
    @WeaPermission(publicPermission = true)
    @GetMapping("/getReverseCheckOfInvoicesByIds/{ids}")
    public WeaResult<Object> getReverseCheckOfInvoicesByIds(@PathVariable("ids") String ids) {
        String method = String.format("调用%s.getReverseCheckOfInvoicesByIds-->", simpleName);
        log.info(S, method);
        return iGetInvoiceInformationService.getReverseCheckOfInvoicesByIds(ids);
    }


    /**
     * 根据IDS列表获取应收单信息。
     *
     * @param ids 以逗号分隔的应收单ID字符串
     * @return 包含应收信息的结果对象
     */
    @WeaPermission(publicPermission = true)
    @GetMapping("/getReceivablesInformationByIds/{ids}")
    public WeaResult<Object> getReceivablesInformationByIds(@PathVariable("ids") String ids) {
        String method = String.format("调用%s.getReceivablesInformationByIds-->", simpleName);
        log.info(S, method);
        return iGetInvoiceInformationService.getReceivablesInformationByIds(ids);
    }


    /**
     * 根据发票号码查询发票红冲、作废信息
     *
     * @param invoiceNumber 以逗号分隔的发票ID字符串
     * @return 包含发票信息的结果对象
     */
    @WeaPermission(publicPermission = true)
    @GetMapping("/getInvoiceCancelInformationByInvoiceNumber/{invoiceNumber}")
    public WeaResult<Object> getInvoiceCancelInformationByInvoiceNumber(@PathVariable("invoiceNumber") String invoiceNumber) {
        String method = String.format("调用%s.getInvoiceCancelInformationByInvoiceNumber-->", simpleName);
        log.info(S, method);
        return iGetInvoiceInformationService.getInvoiceCancelInformationByInvoiceNumber(invoiceNumber);
    }

    /**
     * 根据ID查询发票反勾稽信息
     *
     * @param ids 以逗号分隔的发票ID字符串
     * @return 包含发票信息的结果对象
     */
    @WeaPermission(publicPermission = true)
    @GetMapping("/getInvoiceCancelInformationByIds/{ids}")
    public WeaResult<Object> getInvoiceCancelInformationByIds(@PathVariable("ids") String ids) {
        String method = String.format("调用%s.getInvoiceCancelInformationByIds-->", simpleName);
        log.info(S, method);
        return iGetInvoiceInformationService.getInvoiceCancelInformationByIds(ids);
    }


}
