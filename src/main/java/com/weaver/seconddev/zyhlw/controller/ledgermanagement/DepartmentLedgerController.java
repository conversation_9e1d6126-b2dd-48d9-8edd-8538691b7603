package com.weaver.seconddev.zyhlw.controller.ledgermanagement;

import com.weaver.common.authority.annotation.WeaPermission;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.seconddev.zyhlw.service.IDepartmentLedgerService;
import com.weaver.seconddev.zyhlw.service.IResponsiblePersonLedgerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <h1>责任部门台账</h1>
 *
 * <p>Description: </p>
 *
 * <p>Note: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@RestController
@RequestMapping("/api/secondev/zyhlw/departmentLedger")
public class DepartmentLedgerController {

    @Resource
    IDepartmentLedgerService departmentLedgerService;

    /**
     * getManagementSum
     * <p>
     * 获取管理部门数量
     * ps：原e9接口 /api/zyhlw/web/ResponsibleDepartmentLedgerApi/getManagementSum
     * <p>
     *
     * @param condition1 风险领域
     * @param condition2 风险等级
     * @return 成功失败
     * <AUTHOR>
     * @time 2025年01月25 11:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/getManagementSum")
    public WeaResult<Object> getManagementSum(@RequestParam("condition1") String condition1, @RequestParam("condition2") String condition2) {
        return departmentLedgerService.getManagementSum(condition1, condition2);
    }

    /**
     * getExecutiveArmSum
     * <p>
     * 获取执行部门数量
     * ps：原e9接口 /api/zyhlw/web/ResponsibleDepartmentLedgerApi/getExecutiveArmSum
     * <p>
     *
     * @param condition1 风险领域
     * @param condition2 风险等级
     * @return 成功失败
     * <AUTHOR>
     * @time 2025年01月25 11:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/getExecutiveArmSum")
    public WeaResult<Object> getExecutiveArmSum(@RequestParam("condition1") String condition1, @RequestParam("condition2") String condition2) {
        return departmentLedgerService.getExecutiveArmSum(condition1, condition2);
    }

    /**
     * ResponsibleDepartmentLedger
     * <p>
     * 责任部门台账页面数据接口
     * ps：原e9接口 /api/zyhlw/web/ResponsibleDepartmentLedgerApi/ResponsibleDepartmentLedger
     * <p>
     *
     * @param condition1 风险领域
     * @param condition2 风险等级
     * @param condition3 部门条件
     * @return 成功失败
     * <AUTHOR>
     * @time 2025年01月25 11:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/ResponsibleDepartmentLedger")
    public WeaResult<Object> ResponsibleDepartmentLedger(@RequestParam("condition1") String condition1, @RequestParam("condition2") String condition2, @RequestParam("condition3") String condition3) {
        return departmentLedgerService.ResponsibleDepartmentLedger(condition1, condition2, condition3);
    }
}
