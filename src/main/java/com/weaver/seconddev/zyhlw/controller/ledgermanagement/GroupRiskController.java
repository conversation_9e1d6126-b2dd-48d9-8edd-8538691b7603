package com.weaver.seconddev.zyhlw.controller.ledgermanagement;

import com.weaver.common.authority.annotation.WeaPermission;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.seconddev.zyhlw.service.IGroupRiskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <h1>风险统计Controller</h1>
 *
 * <p>Description: </p>
 *
 * <p>Note: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@RestController
@RequestMapping("/api/secondev/zyhlw/groupRisk")
public class GroupRiskController {

    @Resource
    private IGroupRiskService groupRiskService;
    /**
     * execute
     * <p>
     * 风险统计执行 execute
     * ps：原e9接口 /api/zyhlw/web/GroupRiskApi1/execute
     * <p>
     *
     * @return 成功失败
     * <AUTHOR>
     * @time 2025年03月03 11:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/execute")
    public WeaResult<Object> execute() {
        return groupRiskService.execute();
    }
}
