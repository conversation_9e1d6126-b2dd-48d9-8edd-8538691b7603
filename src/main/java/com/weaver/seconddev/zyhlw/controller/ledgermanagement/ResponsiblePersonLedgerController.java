package com.weaver.seconddev.zyhlw.controller.ledgermanagement;

import com.weaver.common.authority.annotation.WeaPermission;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.seconddev.zyhlw.service.IResponsiblePersonLedgerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * <h1>责任人台账</h1>
 *
 * <p>Description: </p>
 *
 * <p>Note: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@RestController
@RequestMapping("/api/secondev/zyhlw/responsiblePersonLedger")
public class ResponsiblePersonLedgerController {
    @Resource
    IResponsiblePersonLedgerService responsiblePersonLedgerService;

    /**
     * HuoQuStandingBook
     * <p>
     * 获取责任人台账统计（饼图）。
     * ps：原e9接口 /api/zyhlw/web/StandingBookApi/HuoQuStandingBook
     * <p>
     *
     * @param ly      ly
     * @param dj      dj
     * @param gettype gettype
     * @return 成功失败
     * <AUTHOR>
     * @time 2025年01月22 11:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/HuoQuStandingBook")
    public WeaResult<Object> HuoQuStandingBook(@RequestParam("ly") String ly, @RequestParam("dj") String dj,
                                               @RequestParam("gettype") String gettype) {
        return responsiblePersonLedgerService.HuoQuStandingBook(ly, dj, gettype);
    }

    /**
     * downloadAsyncExportFile
     * <p>
     * 下载同步文件导出接口
     * ps：原e9接口 /api/zyhlw/web/ConfigrableExcelExportApi/downloadAsyncExportFile
     * <p>
     *
     * @param key key
     * <AUTHOR>
     * @time 2025年01月22 11:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @PostMapping(value = "/downloadAsyncExportFile")
    public void downloadAsyncExportFile(@RequestParam("key") String key, HttpServletResponse response) {
        responsiblePersonLedgerService.downloadAsyncExportFile(key, response);
    }

    /**
     * queryAsyncExportProgress
     * <p>
     * Excel导出结果查询接口
     * ps：原e9接口 /api/zyhlw/web/ConfigrableExcelExportApi/queryAsyncExportProgress
     * <p>
     *
     * @param key key
     * <AUTHOR>
     * @time 2025年01月22 11:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/queryAsyncExportProgress")
    public WeaResult<Object> queryAsyncExportProgress(@RequestParam("key") String key) {
        return responsiblePersonLedgerService.queryAsyncExportProgress(key);
    }

    /**
     * asyncExport
     * <p>
     * Excel导出接口
     * ps：原e9接口 /api/zyhlw/web/ConfigrableExcelExportApi/asyncExport
     * <p>
     *
     * @param templateId templateId
     * @param type       type
     * @param idList     idList
     * <AUTHOR>
     * @time 2025年01月22 11:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @PostMapping(value = "/asyncExport")
    public WeaResult<Object> asyncExport(@RequestParam("templateId") String templateId, @RequestParam("type") String type,
                                         @RequestParam("idList") String idList, HttpServletResponse response) {
        return responsiblePersonLedgerService.asyncExport(templateId, type, idList, response);
    }

    /**
     * export
     * <p>
     * export导出接口
     * ps：原e9接口 /api/zyhlw/web/ConfigrableExcelExportApi/export
     * <p>
     *
     * @param templateId templateId
     * @param type       type
     * @param idList     idList
     * <AUTHOR>
     * @time 2025年01月22 11:32:35
     * @since 1.0
     */
    @WeaPermission(publicPermission = true)
    @PostMapping(value = "/export")
    public void export(@RequestParam("templateId") String templateId, @RequestParam("type") String type,
                                         @RequestParam("idList") String idList, HttpServletResponse response) {
        responsiblePersonLedgerService.export(templateId, type, idList, response);
    }

}
