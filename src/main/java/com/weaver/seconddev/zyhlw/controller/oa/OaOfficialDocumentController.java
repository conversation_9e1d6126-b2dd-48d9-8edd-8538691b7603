package com.weaver.seconddev.zyhlw.controller.oa;

import com.alibaba.fastjson.JSONObject;
import com.weaver.common.authority.annotation.WeaPermission;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.seconddev.zyhlw.domain.oa.OaAnnouncementInfo;
import com.weaver.seconddev.zyhlw.service.OaSystemOfficialService;
import io.swagger.annotations.ApiModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 接收OA公文推送接口
 *
 * @date 2025-01-20
 */
@ApiModel("接收OA公文推送接口")
@Slf4j
@RestController
@RequestMapping(value = "/papi/secondev/zyhlw/oa/OaOfficialDocumentController")
public class OaOfficialDocumentController {


    @Resource
    OaSystemOfficialService oaSystemOfficialService;

    @PostMapping(value = "/getOfficialDocumentList")
    public WeaResult<String> getOfficialDocumentList(@RequestBody OaAnnouncementInfo oaAnnouncementInfo) {
        Map<String, Object> officialDocumentList = oaSystemOfficialService.getOfficialDocumentList(oaAnnouncementInfo);
        log.info("officialDocumentList:{}", officialDocumentList);
        int code = Integer.parseInt(JSONObject.toJSONString(officialDocumentList.get("code")));
        String msg = (String) officialDocumentList.get("msg");
        log.info("code:{},msg:{}", code, msg);
        log.info("code == 200:{}", code == 200);
        if (code == 200) {
            return WeaResult.success(msg);
        } else {
            return WeaResult.fail(1, msg);
        }
    }


}
