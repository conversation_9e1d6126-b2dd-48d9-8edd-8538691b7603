package com.weaver.seconddev.zyhlw.controller.park;


import com.weaver.common.authority.annotation.WeaPermission;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.seconddev.zyhlw.service.IStationValidationService;
import com.weaver.teams.domain.user.SimpleEmployee;
import com.weaver.teams.security.context.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 园区行政服务管理系统 - 工位管理
 *
 * <AUTHOR>
 * @date 2025年04月30日 15:58
 */
@Slf4j
@RestController
@RequestMapping("/api/secondev/zyhlw/park/stationValidation")
public class StationValidationController {

    @Resource
    private IStationValidationService validationService;

    /**
     * 判断选择人员与当前人员是否为同部门人员
     * 原e9接口: /zyhlw/web/station/StationValidationApi/isSameDept
     *
     * @return com.weaver.common.base.entity.result.WeaResult<java.lang.Boolean>
     * <AUTHOR>
     * @date 2025/4/30 16:03
     */

    @GetMapping("/isSameDept")
    @WeaPermission(publicPermission = true)
    public WeaResult<Boolean> isSameDept(@RequestParam("departmentId") Long departmentId) {
        // 获取当前用户
        SimpleEmployee currentUser = UserContext.getCurrentUser();
        if (currentUser == null) {
            return WeaResult.fail("获取当前用户currentUser信息失败");
        }
        return WeaResult.success(validationService.isSameDept(currentUser.getUid(), departmentId));
    }

    /**
     * 根据id获取工位信息
     * 原e9接口: /zyhlw/web/station/StationValidationApi/getStationInfoByIds
     *
     * @return com.weaver.common.base.entity.result.WeaResult<java.util.List < java.util.Map < java.lang.String, java.lang.String>>>
     * <AUTHOR>
     * @date 2025/4/30 16:03
     */
    @GetMapping("/getStationInfoByIds")
    @WeaPermission(publicPermission = true)
    public WeaResult<List<Map<String, String>>> getStationInfoByIds(@RequestParam("ids") String ids) {
        return WeaResult.success(validationService.getStationInfoByIds(ids));
    }

    /**
     * 根据工位编号获取工位信息
     * 原e9接口: /zyhlw/web/station/StationValidationApi/getStationInfoByStationNum
     *
     * @return com.weaver.common.base.entity.result.WeaResult<java.util.List < java.util.Map < java.lang.String, java.lang.String>>>
     * <AUTHOR>
     * @date 2025/4/30 17:40
     */
    @GetMapping("/getStationInfoByStationNum")
    @WeaPermission(publicPermission = true)
    public WeaResult<List<Map<String, String>>> getStationInfoByStationNum(@RequestParam("?") String stationNums) {
        try {
            log.info("根据StationNum查询工位信息...");
            List<Map<String, String>> data = validationService.getStationInfoByStationNums(stationNums);
            return WeaResult.success(data);
        } catch (Exception e) {
            log.error("执行异常：{}", e.getMessage(), e);
            return WeaResult.fail("系统异常：" + e.getMessage());
        }
    }

    /**
     * 根据人员id获取人员信息
     * 原e9接口: /zyhlw/web/station/StationValidationApi/getPeopleInfoById
     *
     * @return com.weaver.common.base.entity.result.WeaResult<java.util.List < java.util.Map < java.lang.String, java.lang.String>>>
     * <AUTHOR>
     * @date 2025/4/30 17:45
     */
    @GetMapping("/getPeopleInfoById")
    @WeaPermission(publicPermission = true)
    public WeaResult<List<Map<String, String>>> getPeopleInfoById(@RequestParam("ids") String ids) {
        try {
            log.info("根据人员ID查询人员信息...");
            List<Map<String, String>> data = validationService.getPeopleInfoByIds(ids);
            return WeaResult.success(data);
        } catch (Exception e) {
            log.error("执行异常：{}", e.getMessage(), e);
            return WeaResult.fail("系统异常：" + e.getMessage());
        }
    }

}
