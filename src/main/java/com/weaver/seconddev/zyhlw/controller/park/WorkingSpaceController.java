package com.weaver.seconddev.zyhlw.controller.park;

import com.weaver.common.authority.annotation.WeaPermission;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.datasource.utils.rest.CommonRestService;
import com.weaver.seconddev.zyhlw.domain.park.*;
import com.weaver.seconddev.zyhlw.service.IWorkingSpaceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/***
 * 园区行政服务管理系统 - 工位管理
 *
 * <AUTHOR>
 * @date 2025-04-25 15:26:00
 */
@Slf4j
@RestController
@RequestMapping("/api/secondev/zyhlw/park/workingSpace")
public class WorkingSpaceController {

    @Resource
    private IWorkingSpaceService workingSpaceService;

    @Resource
    private CommonRestService commonRestService;

    /***
     * 工位管理 -工位平面图接口、 首页背景图调整 - 获取用户权限
     * 原e9接口: /zyhlw/web/taster/SeatInfoApi/getUserAuth
     *
     * <AUTHOR>
     * @date 2025/4/25 15:58
     * @param userId 用户id
     * @param roleId 角色id
     * @return com.weaver.common.base.entity.result.WeaResult<com.weaver.seconddev.zyhlw.domain.park.GetUserAuthRespVO>
     */
    @GetMapping("/getUserAuth")
    @WeaPermission(publicPermission = true)
    public WeaResult<GetUserAuthRespVO> getUserAuth(@RequestParam("userid") String userId,
                                                    @RequestParam("roleid") String roleId) {
        log.info("-------查看是否是管理员--------");
        log.info("用户id:{}", userId);
        return WeaResult.success(
                GetUserAuthRespVO.builder()
                        .isAdmin(workingSpaceService.checkIsAdmin(userId, roleId))
                        .build());
    }

    /**
     * 工位信息同步
     * 原e9接口: /zyhlw/web/taster/SeatInfoApi/syncStationPeopleInfo
     *
     * @return com.weaver.common.base.entity.result.WeaResult<com.weaver.seconddev.zyhlw.domain.park.SyncStationPeopleInfoRespVO>
     * <AUTHOR>
     * @date 2025/4/25 17:02
     */
    @GetMapping("/syncStationPeopleInfo")
    @WeaPermission(publicPermission = true)
    public WeaResult<SyncStationPeopleInfoRespVO> syncStationPeopleInfo(@RequestParam(value = "async", required = false) Boolean async) {
        return WeaResult.success(workingSpaceService.syncStationPeopleInfo(async));
    }

    /**
     * 获取工位信息列表
     * 原e9接口: /zyhlw/web/taster/SeatInfoApi/getSeats
     *
     * @param building 楼宇
     * @param floor    楼层
     * @param state    状态
     * @param hrmType  人员类型
     * @return com.weaver.common.base.entity.result.WeaResult<com.weaver.seconddev.zyhlw.domain.park.GetSeatsRespVO>
     * <AUTHOR>
     * @date 2025/4/27 17:45
     */
    @GetMapping("/getSeats")
    @WeaPermission(publicPermission = true)
    public WeaResult<GetSeatsRespVO> getSeats(@RequestParam(value = "building", required = false) String building,
                                              @RequestParam(value = "floor", required = false) String floor,
                                              @RequestParam(value = "state", required = false) String state,
                                              @RequestParam(value = "hrmType", required = false) String hrmType) {
        log.info("-------获取工位信息列表--------");
        log.info("楼宇:{}, 楼层:{}, 状态:{}, 人员类型:{}", building, floor, state, hrmType);
        return WeaResult.success(workingSpaceService.getSeats(building, floor, state, hrmType));
    }

    /**
     * 获取工位参数配置表
     * 原e9接口: /zyhlw/web/taster/SeatInfoApi/getPlanInfo
     *
     * @param floorId 楼层ID
     * @return com.weaver.common.base.entity.result.WeaResult<com.weaver.seconddev.zyhlw.domain.park.GetPlanInfoRespVO>
     * <AUTHOR>
     * @date 2025/4/28 10:45
     */
    @GetMapping("/getPlanInfo")
    @WeaPermission(publicPermission = true)
    public WeaResult<GetPlanInfoRespVO> getPlanInfo(@RequestParam(value = "floorId", required = false) String floorId) {
        log.info("-------获取工位参数配置表--------");
        log.info("楼层ID:{}", floorId);
        return WeaResult.success(workingSpaceService.getPlanInfo(floorId));
    }

    /**
     * 获取工位楼层信息
     * 原e9接口: /zyhlw/web/taster/SeatInfoApi/getFloorInfo
     *
     * @param building 楼宇
     * @param floor    楼层
     * @return com.weaver.common.base.entity.result.WeaResult<com.weaver.seconddev.zyhlw.domain.park.GetFloorInfoRespVO>
     * <AUTHOR>
     * @date 2025/4/28 11:30
     */
    @GetMapping("/getFloorInfo")
    @WeaPermission(publicPermission = true)
    public WeaResult<GetFloorInfoRespVO> getFloorInfo(
            @RequestParam(value = "building", required = false) String building,
            @RequestParam(value = "floor", required = false) String floor) {
        log.info("-------获取工位楼层信息-------- \n, 楼宇:{}, 楼层:{}", building, floor);
        return WeaResult.success(workingSpaceService.getFloorInfo(building, floor));
    }

    /**
     * 获取领导办公室信息
     * 原e9接口: /zyhlw/web/taster/SeatInfoApi/getLeaderSeats
     *
     * @param building 楼宇
     * @param floor    楼层
     * @return com.weaver.common.base.entity.result.WeaResult<com.weaver.seconddev.zyhlw.domain.park.GetLeaderSeatsRespVO>
     * <AUTHOR>
     * @date 2025/4/28 15:30
     */
    @GetMapping("/getLeaderSeats")
    @WeaPermission(publicPermission = true)
    public WeaResult<GetLeaderSeatsRespVO> getLeaderSeats(
            @RequestParam(value = "building", required = false) String building,
            @RequestParam(value = "floor", required = false) String floor) {
        log.info("-------获取领导办公室信息-------- \n, 楼宇:{}, 楼层:{}", building, floor);
        return WeaResult.success(workingSpaceService.getLeaderSeats(building, floor));
    }

    /**
     * 获取会议室信息
     * 原e9接口: /zyhlw/web/taster/SeatInfoApi/getMeetingInformation
     *
     * @param building         楼宇
     * @param floor            楼层
     * @param id               会议室ID
     * @param meetingRoomsName 会议室名称
     * @return com.weaver.common.base.entity.result.WeaResult<com.weaver.seconddev.zyhlw.domain.park.GetMeetingInformationRespVO>
     * <AUTHOR>
     * @date 2025/4/29 10:40
     */
    @GetMapping("/getMeetingInformation")
    @WeaPermission(publicPermission = true)
    public WeaResult<GetMeetingInformationRespVO> getMeetingInformation(
            @RequestParam(value = "building", required = false) String building,
            @RequestParam(value = "floor", required = false) String floor,
            @RequestParam(value = "id", required = false) String id,
            @RequestParam(value = "meetingRoomsName", required = false) String meetingRoomsName) {
        log.info("-------获取会议室信息--------");
        log.info("楼宇:{}, 楼层:{}, 会议室ID:{}, 会议室名称:{}", building, floor, id, meetingRoomsName);
        return WeaResult.success(workingSpaceService.getMeetingInformation(building, floor, id, meetingRoomsName));
    }
}
