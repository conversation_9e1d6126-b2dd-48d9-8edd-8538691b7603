package com.weaver.seconddev.zyhlw.controller.partystyle;

import com.weaver.common.authority.annotation.WeaPermission;
import com.weaver.file.ud.common.WeaApiResult;
import com.weaver.seconddev.zyhlw.service.IPortalService;
import com.weaver.seconddev.zyhlw.service.StatisticalFormService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 主动发现问题统计分析
 *
 * @date 2025-05-26
 */
@RestController
@RequestMapping("/api/secondev/zyhlw/partystyle/StatisticalFormController")
@Slf4j
public class StatisticalFormController {

    @Resource
    IPortalService portalService;

    @Resource
    private StatisticalFormService statisticalFormService;

    /**
     * 获取数据总览
     *
     * @param year    年份
     * @param quarter 季度
     * @param dep     部门
     * @return 数据总览
     */
    @WeaPermission(publicPermission = true)
    @GetMapping("/getDataOverview")
    public WeaApiResult<List<Map<String, Object>>> getDataOverview(@RequestParam(required = false, defaultValue = "") String year,
                                                                   @RequestParam(required = false, defaultValue = "") String quarter,
                                                                   @RequestParam(required = false, defaultValue = "") String dep) {
        log.info("获取数据总览");
        List<Map<String, Object>> dataOverview = statisticalFormService.getDataOverview(year, quarter, dep);
        return WeaApiResult.success(dataOverview);
    }

    /**
     * 获取分类统计
     *
     * @param year    年份
     * @param quarter 季度
     * @param dep     部门
     * @return 分类统计
     */
    @WeaPermission(publicPermission = true)
    @GetMapping("/getClassifiedStatistic")
    public WeaApiResult<Map<String, Object>> getClassifiedStatistic(@RequestParam(required = false, defaultValue = "") String year,
                                                                    @RequestParam(required = false, defaultValue = "") String quarter,
                                                                    @RequestParam(required = false, defaultValue = "") String dep) {
        log.info("获取分类统计");
        Map<String, Object> classifiedStatistic = statisticalFormService.getClassifiedStatistic(year, quarter, dep);
        return WeaApiResult.success(classifiedStatistic);
    }

    /**
     * 获取问题来源
     *
     * @return 问题来源
     */
    @WeaPermission(publicPermission = true)
    @GetMapping("/getProblemSources")
    public WeaApiResult<List<Map<String, Object>>> getProblemSources() {
        log.info("获取问题来源");
        List<Map<String, Object>> problemSources = statisticalFormService.getProblemSources();
        return WeaApiResult.success(problemSources);
    }

    /**
     * 获取部门分组
     *
     * @param year    年份
     * @param quarter 季度
     * @param dep     部门
     * @param source  问题来源
     * @return 部门分组
     */
    @WeaPermission(publicPermission = true)
    @GetMapping("/getDepartmentalGrouping")
    public WeaApiResult<Map<String, Object>> getDepartmentalGrouping(@RequestParam(required = false, defaultValue = "") String year,
                                                                     @RequestParam(required = false, defaultValue = "") String quarter,
                                                                     @RequestParam(required = false, defaultValue = "") String dep,
                                                                     @RequestParam(required = false, defaultValue = "") String source) {
        log.info("获取部门分组");
        Map<String, Object> departmentalGrouping = statisticalFormService.getDepartmentalGrouping(year, quarter, dep, source);
        return WeaApiResult.success(departmentalGrouping);
    }

    /**
     * 获取部门占比统计
     *
     * @param year    年份
     * @param quarter 季度
     * @param dep     部门
     * @return 部门占比统计
     */
    @WeaPermission(publicPermission = true)
    @GetMapping("/getDepartmentalProportionStatistics")
    public WeaApiResult<List<Map<String, String>>> getDepartmentalProportionStatistics(@RequestParam(required = false, defaultValue = "") String year,
                                                                                       @RequestParam(required = false, defaultValue = "") String quarter,
                                                                                       @RequestParam(required = false, defaultValue = "") String dep) {
        log.info("获取部门占比统计");
        List<Map<String, String>> departmentalProportionStatistics = statisticalFormService.getDepartmentalProportionStatistics(year, quarter, dep);
        return WeaApiResult.success(departmentalProportionStatistics);
    }

    /**
     * 获取下级部门
     *
     * @param dep 部门
     * @return 下级部门 及本部门id
     */
    @WeaPermission(publicPermission = true)
    @GetMapping("/getSubordinateDepartment2")
    public WeaApiResult<String> getSubordinateDepartment2(@RequestParam String dep) {
        log.info("获取下级部门");
        List<String> departIdList = portalService.getDepartIdList(dep);
        return WeaApiResult.success(String.join(",", departIdList));
    }
}
