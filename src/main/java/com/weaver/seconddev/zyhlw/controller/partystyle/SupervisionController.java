package com.weaver.seconddev.zyhlw.controller.partystyle;

import com.weaver.common.authority.annotation.WeaPermission;
import com.weaver.file.ud.common.WeaApiResult;
import com.weaver.seconddev.zyhlw.service.SupervisionService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 监督计划统计图表接口
 *
 * @date 2025-05-21
 */
@RestController
@RequestMapping("/api/secondev/zyhlw/partystyle/SupervisionController")
public class SupervisionController {

    @Resource
    private SupervisionService supervisionService;


    @WeaPermission(publicPermission = true)
    @GetMapping("/getBlockChart")
    public WeaApiResult<List<Map<String, String>>> getBlockChart(@RequestParam(value = "year", required = false, defaultValue = "") String year,
                                                                 @RequestParam(value = "dep", required = false, defaultValue = "") String dep) {

        return WeaApiResult.success(supervisionService.getBlockChart(year, dep));
    }

    //监督计划方块图
    @WeaPermission(publicPermission = true)
    @GetMapping("/getBlockChart2")
    public WeaApiResult<List<Map<String, String>>> getBlockChart2(@RequestParam(value = "year", required = false, defaultValue = "") String year,
                                                                  @RequestParam(value = "dep", required = false, defaultValue = "") String dep) {
        return WeaApiResult.success(supervisionService.getBlockChart2(year, dep));
    }

    //获取监督计划类型
    @WeaPermission(publicPermission = true)
    @GetMapping("/getType")
    public WeaApiResult<List<Map<String, Object>>> getType() {
        return WeaApiResult.success(supervisionService.getType());
    }

    //监督计划饼状图数据
    @WeaPermission(publicPermission = true)
    @GetMapping("/getPieChart")
    public WeaApiResult<List<Map<String, Object>>> getPieChart(@RequestParam(value = "year", required = false, defaultValue = "") String year,
                                                               @RequestParam(value = "dep", required = false, defaultValue = "") String dep,
                                                               @RequestParam(value = "supervisionType", required = false, defaultValue = "") String supervisionType) {
        return WeaApiResult.success(supervisionService.getPieChart(year, dep, supervisionType));
    }

    //监督计划柱状图数据
    @WeaPermission(publicPermission = true)
    @GetMapping("/getColumnarChart")
    public WeaApiResult<List<Map<String, Object>>> getColumnarChart(@RequestParam(value = "year", required = false, defaultValue = "") String year,
                                                                    @RequestParam(value = "dep", required = false, defaultValue = "") String dep,
                                                                    @RequestParam(value = "supervisionType", required = false, defaultValue = "") String supervisionType) {
        return WeaApiResult.success(supervisionService.getColumnarChart(year, dep, supervisionType));
    }

    //监督计划数线图数据
    @WeaPermission(publicPermission = true)
    @GetMapping("/getNumericalDiagram")
    public WeaApiResult<List<Map<String, Object>>> getNumericalDiagram(@RequestParam(value = "year", required = false, defaultValue = "") String year,
                                                                       @RequestParam(value = "dep", required = false, defaultValue = "") String dep,
                                                                       @RequestParam(value = "supervisionType", required = false, defaultValue = "") String supervisionType) {
        return WeaApiResult.success(supervisionService.getNumericalDiagram(year, dep, supervisionType));
    }

    //根据id查询计划任务数据
    @WeaPermission(publicPermission = true)
    @GetMapping("/getProjectById")
    public WeaApiResult<Map<String, Object>> getProjectById(@RequestParam(value = "projectId") String projectId) {
        return WeaApiResult.success(supervisionService.getProjectById(projectId));
    }
}
