package com.weaver.seconddev.zyhlw.controller.partystyle;

import com.weaver.common.authority.annotation.WeaPermission;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.seconddev.zyhlw.service.SupervisionTemplateService;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 监督模板问题统计API接口
 *
 * @date 2025-02-24
 */
@RestController
@RequestMapping("/api/secondev/zyhlw/partystyle/SupervisionTemplateController")
public class SupervisionTemplateController {

    @Resource
    private SupervisionTemplateService supervisionTemplateService;

    /**
     * 返回监督模版问题列表 场景点总数、列表总数、管控点总数、领域数
     *
     * @return 返回String的键值对
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/supervisionTemplateStatistics")
    public WeaResult<Map<String, Object>> supervisionTemplateStatistics(@RequestParam(value = "year", required = false, defaultValue = "") String year,
                                                                        @RequestParam(value = "quarter", required = false, defaultValue = "") String quarter,
                                                                        @RequestParam(value = "dep", required = false, defaultValue = "") String dep) {
        Map<String, Object> stringObjectMap = supervisionTemplateService.supervisionTemplateStatistics(year, quarter, dep);
        return WeaResult.success(stringObjectMap);
    }

    /**
     * 查询监督模版问题统计
     *
     * @param year    年份
     * @param quarter 季度
     * @param type    类型
     * @param dep     部门
     * @return 返回String的键值对
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/moduleProportion")
    public WeaResult<Map<String, Object>> moduleProportion(@RequestParam(value = "dep", required = false, defaultValue = "") String dep,
                                                           @RequestParam(value = "quarter", required = false, defaultValue = "") String quarter,
                                                           @RequestParam(value = "year", required = false, defaultValue = "") String year,
                                                           @RequestParam(value = "type", required = false, defaultValue = "") String type) {
        Map<String, Object> stringObjectMap = supervisionTemplateService.moduleProportion(dep, quarter, year, type);
        return WeaResult.success(stringObjectMap);
    }

    /**
     * 获取场景点更改
     *
     * @param year    年份
     * @param quarter 季度
     * @param dep     部门
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/getScenePointChanges")
    public WeaResult<Map<String, Object>> getScenePointChanges(@RequestParam(value = "year", required = false, defaultValue = "") String year,
                                                               @RequestParam(value = "quarter", required = false, defaultValue = "") String quarter,
                                                               @RequestParam(value = "dep", required = false, defaultValue = "") String dep) {
        Map<String, Object> stringObjectMap = supervisionTemplateService.getScenePointChanges(year, quarter, dep);
        return WeaResult.success(stringObjectMap);
    }

    /**
     * 监督模板查询列表筛选条件
     */
    @WeaPermission(publicPermission = true)
    @PostMapping(value = "/supervisionTemplateCondition")
    public WeaResult<String> supervisionTemplateCondition(@RequestBody Map<String, Object> params) {
        //部门
        String guiSbm = StringUtils.null2String(params.get("gui_sbm"));
        //开始时间
        String startTime = StringUtils.null2String(params.get("start"));
        //结束时间
        String endTime = StringUtils.null2String(params.get("end"));
        //所属领域
        String suoSly = StringUtils.null2String(params.get("suo_sly"));
        //问题模块
        String wenTmk = StringUtils.null2String(params.get("wen_tmk"));
        //管控点
        String controlPoints = StringUtils.null2String(params.get("controlPoints"));
        //场景点
        String scenePoints = StringUtils.null2String(params.get("scenePoints"));
        //场景点变化类型:新增0/更新1
        String kind = StringUtils.null2String(params.get("kind1"));
        String sqlCondition = supervisionTemplateService.supervisionTemplateCondition(guiSbm, startTime, endTime, suoSly, wenTmk, controlPoints, scenePoints, kind);
        return WeaResult.success(sqlCondition);
    }
}
