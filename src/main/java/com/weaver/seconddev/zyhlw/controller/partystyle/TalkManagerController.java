package com.weaver.seconddev.zyhlw.controller.partystyle;

import com.weaver.common.authority.annotation.WeaPermission;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.seconddev.zyhlw.service.IPortalService;
import com.weaver.seconddev.zyhlw.service.TalkManagerService;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import io.swagger.annotations.ApiModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 党风廉政谈话管理接口
 *
 * @date 2025-02-18
 */
@ApiModel("党风廉政谈话管理接口")
@Validated
@RestController
@Slf4j
@RequestMapping("/api/secondev/zyhlw/partystyle/TalkManagerController")
public class TalkManagerController {

    private static final String simpleName = TalkManagerController.class.getSimpleName();

    @Resource
    private TalkManagerService talkManagerService;
    @Resource
    private IPortalService portalService;

    /**
     * 获取谈话统计报表信息
     *
     * @param year    年份
     * @param quarter 季度
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/getCarTrackApi")
    public WeaResult<List<Map<String, Object>>> getCarTrackApi(@RequestParam(value = "year", required = false, defaultValue = "")
                                                               String year,
                                                               @RequestParam(value = "quarter", required = false, defaultValue = "")
                                                               String quarter) {
        log.info("调用{}.getTalkManagerList()-->进入查询talkManagerList接口", simpleName);
        log.info("year:{},quarter:{}", year, quarter);
        List<Map<String, Object>> talkManagerList = talkManagerService.getCarTrackApi(year, quarter);
        return WeaResult.success(talkManagerList);
    }

    /**
     * 获取统计报表数据总览
     *
     * @param year         年份
     * @param quarter      季度
     * @param departmentId 部门id
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/getCountInfo2")
    public WeaResult<List<Map<String, Object>>> getCountInfo2(@RequestParam(value = "year", required = false, defaultValue = "")
                                                              String year,
                                                              @RequestParam(value = "quarter", required = false, defaultValue = "")
                                                              String quarter,
                                                              @RequestParam(value = "departmentId", required = false, defaultValue = "")
                                                              String departmentId) {
        log.info("调用{}.getCountInfo2()-->进入查询getCountInfo2接口", simpleName);
        log.info("year:{},quarter:{},departmentId:{}", year, quarter, departmentId);
        List<Map<String, Object>> talkManagerList = talkManagerService.getCountInfo2(year, quarter, departmentId);
        return WeaResult.success(talkManagerList);
    }

    /**
     * 获取谈话管理饼状数据统计
     *
     * @param year         年份
     * @param quarter      季度
     * @param departmentId 部门
     * @param type         类型
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/getCountInfo3")
    public WeaResult<List<Map<String, Object>>> getCountInfo3(@RequestParam(value = "year", required = false, defaultValue = "")
                                                              String year,
                                                              @RequestParam(value = "quarter", required = false, defaultValue = "")
                                                              String quarter,
                                                              @RequestParam(value = "departmentId", required = false, defaultValue = "")
                                                              String departmentId,
                                                              @RequestParam(value = "type", required = false, defaultValue = "")
                                                              String type) {
        log.info("调用{}.getCountInfo3()-->进入查询getCountInfo3接口", simpleName);
        log.info("year:{},quarter:{},departmentId:{},type:{}", year, quarter, departmentId, type);
        List<Map<String, Object>> talkManagerList = talkManagerService.getCountInfo3(year, quarter, departmentId, type);
        return WeaResult.success(talkManagerList);
    }

    /**
     * 谈话对象部门数据统计
     *
     * @param year         年份
     * @param quarter      季度
     * @param departmentId 部门
     * @param shi_szl      实施种类
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/getCountInfo5")
    public WeaResult<Map<String, Object>> getCountInfo5(@RequestParam(value = "year", required = false, defaultValue = "")
                                                        String year,
                                                        @RequestParam(value = "quarter", required = false, defaultValue = "")
                                                        String quarter,
                                                        @RequestParam(value = "departmentId", required = false, defaultValue = "")
                                                        String departmentId,
                                                        @RequestParam(value = "shi_szl", required = false, defaultValue = "")
                                                        String shi_szl) {
        log.info("调用{}.getCountInfo5()-->进入查询getCountInfo5接口", simpleName);
        Map<String, Object> talkManagerList = talkManagerService.getCountInfo5(year, quarter, departmentId, shi_szl);
        return WeaResult.success(talkManagerList);
    }

    /**
     * 获取谈话条件
     *
     * @param params
     * @return
     */
    @WeaPermission(publicPermission = true)
    @PostMapping(value = "/conversationCondition")
    public WeaResult<String> conversationCondition(@RequestBody Map<String, Object> params) {
        //实施种类
        String shi_szl = StringUtils.null2String(params.get("shi_szl"));
        //开始时间
        String startTime = StringUtils.null2String(params.get("startTime"));
        //结束时间
        String endTime = StringUtils.null2String(params.get("endTime"));
        //实施对象部门
        String department = StringUtils.null2String(params.get("department"));
        //实施形式
        String shi_sxs = StringUtils.null2String(params.get("shi_sxs"));
        //是否包含实施形式 0 包含 1 不包含
        String shapeType = StringUtils.null2String(params.get("shapeType"));
        log.info("调用{}.conversationCondition()-->进入查询conversationCondition接口", simpleName);
        log.info("shi_szl:{},startTime:{},endTime:{},department:{},shi_sxs:{},shapeType:{}", shi_szl, startTime, endTime, department, shi_sxs, shapeType);
        String condition = talkManagerService.conversationCondition(shi_szl, startTime, endTime, department, shi_sxs, shapeType);
        return WeaResult.success(condition);
    }

    /**
     * 获取所有下级部门
     *
     * @param depId 部门id
     * @return 所有下级部门id字符串
     */
    @WeaPermission(publicPermission = true)
    @GetMapping(value = "/getChildDepartListById")
    public WeaResult<String> getChildDepartListById(@RequestParam(value = "depId", required = false, defaultValue = "") String depId) {
        log.info("调用{}.getChildDepartListById({})-->进入查询getChildDepartListById接口", simpleName, depId);
        List<String> departIdList = portalService.getDepartIdList(depId);
        log.info("{} 根据ID查询所有下级部门 depId:{} ", simpleName, departIdList);
        return WeaResult.success(String.join(",", departIdList));
    }
}
