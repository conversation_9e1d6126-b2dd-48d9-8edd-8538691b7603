package com.weaver.seconddev.zyhlw.controller;

import com.weaver.common.base.entity.result.WeaResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/sapi/secondev/zyhlw/sso")
public class test {
    @GetMapping(value = "/test1")
    WeaResult<String> test1(){
        return WeaResult.success("请求成功");
    }
}
