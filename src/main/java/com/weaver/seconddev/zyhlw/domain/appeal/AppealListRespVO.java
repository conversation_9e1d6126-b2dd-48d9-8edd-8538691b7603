package com.weaver.seconddev.zyhlw.domain.appeal;

import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppealListRespVO {

    /**
     * 总条数
     */
    private Integer total;

    /**
     * 当前页
     */
    private Integer currentPage;

    /**
     * 每页条数
     */
    private Integer pageSize;

    /**
     * 数据
     */
    private List<Map<String, Object>> data;

}
