package com.weaver.seconddev.zyhlw.domain.appeal;

import java.util.List;

import lombok.Builder;
import lombok.Data;

/**
 * 诉求分类响应VO
 * 
 * <AUTHOR>
 * @date 2025-05-22 11:04:00
 */
@Data
@Builder
public class AppealTypeListRespVO {
    /**
     * 类型ID
     */
    private String id;
    /**
     * 类型名称
     */
    private String name;

    /**
     * 子类型列表
     */
    private List<AppealTypeListRespVO> children;
}
