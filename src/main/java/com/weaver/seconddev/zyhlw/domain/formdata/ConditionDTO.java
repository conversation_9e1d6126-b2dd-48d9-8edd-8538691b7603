package com.weaver.seconddev.zyhlw.domain.formdata;

import com.weaver.seconddev.zyhlw.util.constant.FormSqlQueryType;
import lombok.Data;

@Data
public class ConditionDTO implements java.io.Serializable {
    /**
     * 字段名
     */
    private String fieldname;

    /**
     * 查询值
     */
    private String value;

    /**
     * 查询值 {@link FormSqlQueryType}
     */
    private String queryType;

    /**
     * 区间查询-开始
     */
    private String startValue;

    /**
     * 区间查询-结束
     */
    private String endValue;
}