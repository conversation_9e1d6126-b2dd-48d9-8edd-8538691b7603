package com.weaver.seconddev.zyhlw.domain.formdata;


import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年05月06日 15:21
 */
@Data
public class FormDataListReqDTO implements Serializable {
    /**
     * 表单id
     */
    @NotNull(message = "表单id不能为空")
    private String formId;

    /**
     * 明细表名
     */
    private String detail;

    /**
     * 查询字段 多个用逗号分隔
     */
    private String queryFields;

    /**
     * 查询条件
     */
    List<ConditionDTO> conditions;

}
