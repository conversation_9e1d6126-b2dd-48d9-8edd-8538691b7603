package com.weaver.seconddev.zyhlw.domain.formdata;


import lombok.Data;

import java.io.Serializable;

/**
 * {"ID":"100003684028953156","FORM_ID":"100003664028953156","TABLE_NAME":"uf_car","TENANT_KEY":"tld0nhuikk","CREATE_TIME":"2024-12-14 03:34:21","UPDATE_TIME":"2024-12-14 00:43:15","OPERATOR":"1077628696546025486","CREATOR":"","DELETE_TYPE":"0","TABLE_TYPE":"0","FORM_NAME":"","OTHER_PROPERTIES":"","SYSTEM_TABLE":"0"}
 *
 * <AUTHOR>
 * @date 2025年05月09日 12:03
 */
@Data
public class FormTableDTO implements Serializable {
    private Long id;

    private Long formId;

    private String tableName;

    private String tenantKey;

    private String createTime;

    private String updateTime;

    private String operator;

    private String creator;

    private Boolean deleteType;

    private Integer tableType;

    private String formName;

    private String otherProperties;

    private String systemTable;
}
