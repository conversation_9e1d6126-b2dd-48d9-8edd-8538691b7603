package com.weaver.seconddev.zyhlw.domain.hrm;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 根据人员ID查询部门科室信息响应VO
 *
 * <AUTHOR>
 * @date 2025年04月30日 10:30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetDepartmentNameByStaffIdRespVO implements Serializable {
    /**
     * 人员部门数据列表
     * 包含：id 人员id、lastname 人员名称、departmentId 部门id、departmentName 部门名称、
     * sectionsName 科室名称(父级部门名称)、sectionsId 科室id(父级部门id)
     */
    private List<Map<String, String>> data;

    /**
     * 状态码
     */
    private int code;

    /**
     * 消息
     */
    private String msg;
}
