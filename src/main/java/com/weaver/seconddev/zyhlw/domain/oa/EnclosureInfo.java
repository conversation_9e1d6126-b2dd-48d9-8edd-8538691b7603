package com.weaver.seconddev.zyhlw.domain.oa;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * OA系统附件信息
 *
 * @date 2025-01-20
 */
@Data
@ApiModel("OA系统附件信息")
public class EnclosureInfo {
    @ApiModelProperty("附件名字")
    private String name;
    @ApiModelProperty("附件下载路径")
    private String path;
    @ApiModelProperty("附件类型")
    private String type;
}
