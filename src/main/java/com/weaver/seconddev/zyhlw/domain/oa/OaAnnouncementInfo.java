package com.weaver.seconddev.zyhlw.domain.oa;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * OA公文接收类
 *
 * @date 2025-01-20
 */
@Data
@ApiModel("OA公文接收类")
public class OaAnnouncementInfo {
    @ApiModelProperty("公文标题")
    private String title;
    @ApiModelProperty("公文编号")
    private String postNo;
    @ApiModelProperty("公文发起时间")
    private String startTime;
    @ApiModelProperty("发起人")
    private String initiator;
    @ApiModelProperty("公告发起单位")
    private String departName;
    @ApiModelProperty("结束公示日期")
    private String endTime;
    @ApiModelProperty("附件信息")
    private List<EnclosureInfo> enclosureInfos;
}
