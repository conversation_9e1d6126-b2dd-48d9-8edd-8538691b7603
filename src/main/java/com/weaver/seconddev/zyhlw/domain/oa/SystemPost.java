package com.weaver.seconddev.zyhlw.domain.oa;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * OA系统推送信息
 *
 * @date 2025-02-10
 */
@ApiModel("OA系统推送信息")
@Data
public class SystemPost {
    @ApiModelProperty("公文标题")
    private String title;
    @ApiModelProperty("公文编号")
    private String postNo;
    @ApiModelProperty("公文发起时间")
    private String startTime;
    @ApiModelProperty("审批信息")
    private List<ApprovalInfo> approvalInfos;
    @ApiModelProperty("附件信息")
    private List<EnclosureInfo> enclosureInfos;
}
