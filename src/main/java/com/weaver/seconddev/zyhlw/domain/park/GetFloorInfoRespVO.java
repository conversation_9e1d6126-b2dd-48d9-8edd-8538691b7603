package com.weaver.seconddev.zyhlw.domain.park;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 获取工位楼层信息响应VO
 *
 * <AUTHOR>
 * @date 2025/4/28 11:15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetFloorInfoRespVO {
    /**
     * 工位楼层数据
     */
    private List<Map<String, Object>> data;
    
    /**
     * 选项数据
     */
    private Map<String, List<Map<String, Object>>> options;
}
