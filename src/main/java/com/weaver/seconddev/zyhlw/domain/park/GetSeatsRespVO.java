package com.weaver.seconddev.zyhlw.domain.park;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 工位信息响应VO
 *
 * <AUTHOR>
 * @date 2025年04月27日 17:30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetSeatsRespVO implements Serializable {
    /**
     * 工位数据列表
     */
    private List<Map<String, Object>> data;
    
    /**
     * 选项数据
     */
    private Map<String, List<Map<String, Object>>> options;
}
