package com.weaver.seconddev.zyhlw.domain.portal;

import lombok.Data;

@Data
public class DepartmentModel {
    /**
     * 上级组织（分部/部门
     */
    String parent;

    /**
     * 编号
     */
    String code;

    /**
     * 组织key
     */
    String tenant_key;

    /**
     * 是否隐藏
     */
    Boolean invisible;
    /**
     * 组织类型
     */
    String type;
    /**
     * 是否有子组织
     */
    String has_sub;
    /**
     * 组织id
     */
    String id;
    /**
     * 多维id
     */
    String virtualid;
    /**
     * 排序
     */
    Integer disporder;
    /**
     * 所属分部id
     */
    String subcompanyid;
    /**
     * 层级
     */
    Integer datarank;
    /**
     * 是否删除
     */
    Boolean is_delete;
    /**
     * 组织名称
     */
    String name;
    /**
     * 全称
     */
    String fullname;
    /**
     * 组织状态(1 启用；0 停用)
     */
    Boolean status;
}
