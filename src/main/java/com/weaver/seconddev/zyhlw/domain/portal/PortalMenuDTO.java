package com.weaver.seconddev.zyhlw.domain.portal;

import com.alibaba.fastjson.JSONArray;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <h1>PortalMenuDTO</h1>
 *
 * <p>Description: menu update方法入参实体</p>
 *
 * <p>Note: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class PortalMenuDTO implements Serializable {
    private JSONArray add;
    private JSONArray delete;
    @NotNull(message = "排序不能为空")
    private Boolean isSort;
}
