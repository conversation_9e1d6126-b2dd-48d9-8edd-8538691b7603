package com.weaver.seconddev.zyhlw.domain.portalworkflow;

import lombok.Data;

import java.io.Serializable;

/**
 * <h1>NewWorkFlowDTO</h1>
 *
 * <p>Description: menu update方法入参实体</p>
 *
 * <p>Note: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class NewWorkFlowDTO implements Serializable {
    private String id;
    private String workflowName;
    private String workflowId;
    private String workflowType;
    private String workflowOneClassify;
    private String workflowTwoClassify;
    private String isShow;
    private String system;
    private String isMobileShow;
}
