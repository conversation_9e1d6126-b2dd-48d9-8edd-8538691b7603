package com.weaver.seconddev.zyhlw.domain.request.eb;

import com.alibaba.fastjson.JSON;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> by herry on 2025-01-02.
 * Update date:
 * Time: 15:21
 * Project: ecology
 * Package: com.zyhlw.e10.req.eb
 * Command:
 * <p>
 * Status：Using online
 *  "operationinfo": {
 *  "operateType": {
 *      },
 *  "fieldNoFindIgnore": "true",
 *  "needAdd": "true",
 *  "doAction": "false",
 *  "updateType": "updatePolicy",
 *  "updateField": {
 *      "mainTable": [
 *          "sjid"
 *          ]
 *      }
 *  }
 * <p>
 * Please note:
 * Must be checked once every time you submit a configuration file is correct!
 * Data is priceless! Accidentally deleted the consequences!
 */
public class EbFormDataReq {
    /**
     * 操作人id
     **/
    private final String userId;
    /**
     * 表单id
     **/
    private final String objId;
    /**
     * 数据集合
     * [{"mainTable":{},"detail1":[{}]}]
     **/
    private final List<Map<String, Object>> datas;
    /**
     * 传入key:detail+明细表顺序,value:cover 将明细操作转为覆盖操作
     * {"detail1","cover"}
     **/
    private final Map<String, String> operateType;
    /**
     * 传入字段不存在是否忽略(默认忽略; n/false不忽略)
     **/
    private final String fieldNoFindIgnore;
    /**
     * 更新数据不存在是否需要新增数据(默认不新增; y/true新增)
     **/
    private final String needAdd;
    /**
     * 执行当前操作数据对应action动作(默认不执行; y/true执行)
     **/
    private final String doAction;
    /**
     * 更新方式(只针对主表:ids数据id更新; updatePolicy字段策略更新. 传入为空或者错误时默认ids更新)
     **/
    private final String updateType;
    /**
     * 更新字段, updatePolicy字段策略更新字段配置, 传入要更新的字段名,updateType为updatePolicy必填
     * {mainTable:["id"],"detail1":["dxwbdt1"]}
     **/
    private final Map<String, Object> updateField;

    // 私有构造函数，防止外部直接实例化
    private EbFormDataReq(Builder builder) {
        this.userId = builder.userId;
        this.objId = builder.objId;
        this.datas = builder.datas;
        this.operateType = builder.operateType;
        this.fieldNoFindIgnore = builder.fieldNoFindIgnore;
        this.needAdd = builder.needAdd;
        this.doAction = builder.doAction;
        this.updateType = builder.updateType;
        this.updateField = builder.updateField;
    }

    // Getter 方法
    public String getUserId() {
        return userId;
    }

    public String getObjId() {
        return objId;
    }

    public List<Map<String, Object>> getDatas() {
        return datas;
    }

    public Map<String, String> getOperateType() {
        return operateType;
    }

    public String getFieldNoFindIgnore() {
        return fieldNoFindIgnore;
    }

    public String getNeedAdd() {
        return needAdd;
    }

    public String getDoAction() {
        return doAction;
    }

    public String getUpdateType() {
        return updateType;
    }

    public Map<String, Object> getUpdateField() {
        return updateField;
    }

    // 内部静态构建器类
    public static class Builder {
        private String userId;
        private String objId;
        private List<Map<String, Object>> datas;
        private Map<String, String> operateType = new HashMap<>();
        private String fieldNoFindIgnore = "true"; // 默认值
        private String needAdd = "false"; // 默认值
        private String doAction = "false"; // 默认值
        private String updateType = "ids"; // 默认值
        private Map<String, Object> updateField = new HashMap<>(); // 默认值

        public Builder userId(String userId) {
            this.userId = userId;
            return this;
        }

        public Builder objId(String objId) {
            this.objId = objId;
            return this;
        }

        public Builder datas(List<Map<String, Object>> datas) {
            this.datas = datas;
            return this;
        }

        public Builder operateType(Map<String, String> operateType) {
            this.operateType = operateType;
            return this;
        }

        public Builder fieldNoFindIgnore(String fieldNoFindIgnore) {
            this.fieldNoFindIgnore = fieldNoFindIgnore;
            return this;
        }

        public Builder needAdd(String needAdd) {
            this.needAdd = needAdd;
            return this;
        }

        public Builder doAction(String doAction) {
            this.doAction = doAction;
            return this;
        }

        public Builder updateType(String updateType) {
            this.updateType = updateType;
            return this;
        }

        public Builder updateField(Map<String, Object> updateField) {
            this.updateField = updateField;
            return this;
        }

        public EbFormDataReq build() {
            if (userId == null) {
                throw new IllegalArgumentException("userId is required");
            }
            if (objId == null) {
                throw new IllegalArgumentException("objId is required");
            }
            return new EbFormDataReq(this);
        }
    }

    // 示例方法，用于构建和使用 EbFormDataBuilder
    public static void main(String[] args) {
        // 示例调用，传递部分参数
        EbFormDataReq builder = new Builder()
                .userId("123L")
                .objId("obj123")
                .fieldNoFindIgnore("false")
                .doAction("true")
                .build();

        System.out.println("userId: " + builder.getUserId());
        System.out.println("objId: " + builder.getObjId());
        System.out.println("fieldNoFindIgnore: " + builder.getFieldNoFindIgnore());
        System.out.println("doAction: " + builder.getDoAction());

        // 示例调用，不传递参数（使用默认值）
        EbFormDataReq defaultBuilder = new Builder()
                .userId("456L")
                .objId("obj456")
                .build();

        System.out.println("userId: " + defaultBuilder.getUserId());
        System.out.println("objId: " + defaultBuilder.getObjId());
        System.out.println("fieldNoFindIgnore: " + defaultBuilder.getFieldNoFindIgnore());
        System.out.println("doAction: " + defaultBuilder.getDoAction());
        // 打印builder
        System.out.println(JSON.toJSONString(builder));
    }
}
