package com.weaver.seconddev.zyhlw.domain.request.todo;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 统一待办业务稽核接口
 * <AUTHOR>
 */
@Data
@Builder
public class TodoAuditRequest implements Serializable {

    /** 事项类型 */
    private String itemType;

    /** 查询开始时间 */
    private String startTime;

    /** 查询结束时间 */
    private String endTime;

    /** 用户唯一标识，互联网公司内部唯一账号标识账号*/
    private String portalUserId;

    /** 推送待办时使用的用户名，可以是互联网公司账号名，也可以是员工编码*/
    private String receiverUserId;

    /** 查询分页，默认1 */
    private Integer pageNumber;

    /** 每次查询数量，默认500 */
    private Integer pageSize;

}
