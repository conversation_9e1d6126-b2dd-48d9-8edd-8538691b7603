package com.weaver.seconddev.zyhlw.domain.request.todo;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 待办置灰请求参数
 * employType参数说明
 * 传 1 的时候会调用旧的待办服务接口，todoType 参数必填
 * 传 2 的时候会调用新的待办服务解耦，todoType 为空
 */


public class TodoGrayRequest implements Serializable {
    /**
     * 登录名
     */
    private String loginId;

    /**
     *  待办唯一标识
     */
    private String uniqueId;
    /**
     *  业务系统名称
     */
    private String todoType;
    /**
     * 使用类型 1 调用旧待办服务接口  2 调用新待办服务接口
     */
    private Integer employType;


    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("loginId",getLoginId())
                .append("uniqueId",getUniqueId())
                .append("todoType",getTodoType())
                .append("employType",getEmployType())
                .toString();
    }

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public Integer getEmployType() {
        return employType;
    }

    public void setEmployType(Integer employType) {
        this.employType = employType;
    }

    public String getTodoType() {
        return todoType;
    }

    public void setTodoType(String todoType) {
        this.todoType = todoType;
    }

    public String getUniqueId() {
        return uniqueId;
    }

    public void setUniqueId(String uniqueId) {
        this.uniqueId = uniqueId;
    }
}
