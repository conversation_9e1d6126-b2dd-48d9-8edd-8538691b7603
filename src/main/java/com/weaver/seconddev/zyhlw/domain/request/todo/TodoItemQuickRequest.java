package com.weaver.seconddev.zyhlw.domain.request.todo;

import lombok.Data;

import java.io.Serializable;

/**
 * 待办接口推送请求参数（快捷）
 * <AUTHOR>
 */
@Data
public class TodoItemQuickRequest implements Serializable {

    /** 业务系统事项的唯一标识（必填）*/
    private String originalId;

    /** 推送待办系统的系统编码*/
    private String systemCode;

    /** 事项当前处理人账号（必填） */
    private String receiverUserId;

    /** 待办唯一记录id，如果业务系统可以通过此id直接删除或修改对应的事项类型。不需要上三项的属性值 （必填）*/
    private String recordId;

    /** 行为类型.
     *  itemType:变更事项类型和url地址.
     *  del:删除事项
     *  gray:待办置灰操作
     *  （非必填）
     **/
    private String actionType;

    /** actionType为itemType时可填，Web访问Url地址，若无修改可不用传 （非必填）*/
    private String webUrl;

    /** actionType为itemType时可填，移动端访问Url地址。若无修改可不用传 （必填）*/
    private String mobileUrl;

    /** actionType为itemType时必填。事项类型，1、待办、2、待阅，3、已办、4、已阅，6、草稿 （非必填）*/
    private String itemType;

    /** actionType类型为gray,该值为必填 （非必填）*/
    private String hasGray;

}
