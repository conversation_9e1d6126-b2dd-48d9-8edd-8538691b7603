package com.weaver.seconddev.zyhlw.domain.request.todo;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 待办接口推送请求参数
 * <AUTHOR>
 */
@Data
@Builder
public class TodoItemRequest implements Serializable {

    /** 事项标题（必填）*/
    private String itemTitle;

    /** 事项状态、0删除，1有效（必填）*/
    private String itemStatus;

    /** 业务系统事项的唯一标识，同一用户在同一流程实例内流程id一致，可使用实例ID（必填） */
    private String originalId;

    /** 推送待办系统的系统编码 （必填）*/
    private String systemCode;

    /** 推送待办系统的系统名称 （非必填）*/
    private String systemName;

    /** 待办的认证类型，1、portal认证，2、smap，其它待定 （必填）*/
    private String authType;

    /** 事项创建时间，必填，格式yyyy-MM-dd HH:mm:ss （必填）*/
    private String createTime;

    /** 事项发起人的账号 （必填）*/
    private String creatorUserId;
    /** 发起人ID 非接口需要 */
    private Long creator;
    /** 事项发起人的中文名称 （非必填）*/
    private String creatorCnName;

    /** 事项发起人账号类型，默认 1 portal,1、员工编号，2、集团编号，9 其它类型 （必填）*/
    private Integer creatorUserIdType;

    /** 上一流程节点处理人账号 （非必填）*/
    private String prevUserId;

    /** 上一流程节点处理人名称 （非必填）*/
    private String prevCnName;
    /** 上一处理人 非接口必要 */
    private Long prev;

    /** 事项当前处理人账号 （必填）*/
    private String receiverUserId;

    /** 事项当前处理人名称 （必填）*/
    private String receiverCnName;
    /** 事项当前处理人账号类型，默认 1 portal,1、员工编号，2、集团编号，9 其它类型 （必填）*/
    private String receiverUserIdType;
    /** 当前处理人 非接口需要 */
    private Long receiver;
    /** Web访问Url地址 （必填）*/
    private String webUrl;

    /** 移动端访问Url地址。若无移动端访问地址可为空值 （非必填）*/
    private String mobileUrl;

    /** 事项类型，1、待办、2、待阅，3、已办、4、已阅，5、我的创建，6、草稿 （必填）*/
    private Integer itemType;

    /** 紧急程度 （非必填）*/
    private String emergencyDegree;

    /** 实例ID，待办流程实例ID （必填）*/
    private String instId;

    /** 节点id，待办流程节点ID （非必填）*/
    private String actId;
    /** 非接口需要 */
    private String requestId;
}
