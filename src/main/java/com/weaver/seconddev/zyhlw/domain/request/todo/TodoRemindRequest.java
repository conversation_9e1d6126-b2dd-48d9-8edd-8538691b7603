package com.weaver.seconddev.zyhlw.domain.request.todo;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 待办接口请求参数
 */
@Getter
@Setter
public class TodoRemindRequest implements Serializable {
    /**
     * 用户ID list
     */
    private List<Long> userIds;

    /**
     * 当前页
     */
    @NotNull(message = "当前页不能为空")
    private Integer pageNo;
    /**
     * 每页显示数量 默认100，传-1 查询全部
     */
    @NotNull(message = "每页数不能为空")
    private Integer pageSize;
}
