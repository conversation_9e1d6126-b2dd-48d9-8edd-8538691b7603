package com.weaver.seconddev.zyhlw.domain.request.todo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 待办接口请求参数
 */
@Getter
@Setter
public class TodoRequest implements Serializable {
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 分类编码
     */
    private  String code;
    /**
     * 标题
     */
    private String title;
    /**
     * 当前页
     */
    private String pageNo;
    /**
     * 每页显示数量
     */
    private String pageSize;
}
