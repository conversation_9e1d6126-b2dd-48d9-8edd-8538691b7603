package com.weaver.seconddev.zyhlw.domain.request.todo;


import lombok.Data;
import lombok.Setter;
import lombok.Value;

import java.io.Serializable;
@Data
public class TodoSsoRequest implements Serializable {
    /**
     * token auth的认证token接口，使用token属性传参
     * portalToken auth的认证portalToken接口，使用portalToken属性传参
     * ticket  统一用户ticket的认证，使用ticket属性传参，如果ticket属性为空会获取token，默认
     */
    String authType;

    String token;

    String portalToken;

    String ticket;
    /**
     * 待办的地址，需要url编码
     */
    String tourl;
    /**
     * 系统标识
     */
    String sysCode;

    String flushPortalUrl;

    String uniqueId;

    String itemId;

    String appId;
}
