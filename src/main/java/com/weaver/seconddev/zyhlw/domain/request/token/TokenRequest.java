package com.weaver.seconddev.zyhlw.domain.request.token;

import java.io.Serializable;

public class TokenRequest implements Serializable {
    private String appid;

    private String envType;

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getEnvType() {
        return envType;
    }

    public void setEnvType(String envType) {
        this.envType = envType;
    }
}
