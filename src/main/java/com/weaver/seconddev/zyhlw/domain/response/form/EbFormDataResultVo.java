package com.weaver.seconddev.zyhlw.domain.response.form;

//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//


import com.weaver.openapi.pojo.MessageResult;
import com.weaver.seconddev.zyhlw.domain.response.form.vo.FormDataVo;

public class EbFormDataResultVo extends MessageResult {
    private String errcode;
    private String errmsg;
    private FormDataVo data;

    public EbFormDataResultVo() {
    }

    public FormDataVo getData() {
        return data;
    }

    public void setData(FormDataVo data) {
        this.data = data;
    }

    public String getErrcode() {
        return errcode;
    }

    public void setErrcode(String errcode) {
        this.errcode = errcode;
    }

    public String getErrmsg() {
        return errmsg;
    }

    public void setErrmsg(String errmsg) {
        this.errmsg = errmsg;
    }
}

