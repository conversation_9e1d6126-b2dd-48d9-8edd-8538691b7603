package com.weaver.seconddev.zyhlw.domain.response.form.vo;

import java.util.List;

/**
 * <AUTHOR> by herry on 2024-12-26.
 * Update date:
 * Time: 14:39
 * Project: ecology
 * Package: com.zyhlw.e10.res.form.vo
 * Command:
 * <p>
 * Status：Using online
 * <p>
 * Please note:
 * Must be checked once every time you submit a configuration file is correct!
 * Data is priceless! Accidentally deleted the consequences!
 */
public class DataJson {
    private List<String> dataIds;
    private Boolean status;
    private String message;
    private List<Datas> datas;

    public List<Datas> getDatas() {
        return datas;
    }

    public void setDatas(List<Datas> datas) {
        this.datas = datas;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<String> getDataIds() {
        return dataIds;
    }

    public void setDataIds(List<String> dataIds) {
        this.dataIds = dataIds;
    }

    public Boolean getStatus() {
        return status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }
}
