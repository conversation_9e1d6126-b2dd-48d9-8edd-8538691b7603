package com.weaver.seconddev.zyhlw.domain.response.todo;


import com.weaver.workflow.common.constant.pathdef.pathnode.WfpNodeType;
import lombok.Data;

import java.io.Serializable;

@Data
public class FlowLastOperatorResponse implements Serializable {
    private String lastOperateDateTime;

    private long lastOperator =-1L;

    private String lastOperatorName;
    private long lastOperatorIdentityId;
    private int lastOperatorType;


    private long lastFeedBackOperator = -1L;

    private String lastFeedBackOperatorName;
    private long lastFeedBackUserIdentityId;

    private int lastFeedBackOperatorType;

    protected long lastNodeId;
    protected WfpNodeType lastNodeType;
}
