package com.weaver.seconddev.zyhlw.domain.response.todo;

import com.weaver.openapi.pojo.flow.res.vo.FlowList;
import com.weaver.openapi.pojo.flow.res.vo.FlowSign;
import com.weaver.workflow.common.entity.core.flow.RequestBaseInfoEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class FlowListResultResponse implements Serializable {
    /**
     * 待办数据
     */
    private FlowList flow;
    /**
     * 签字意见
     */
    private List<FlowSign> signList;
    /**
     * 待办最后操作信息
     */
    FlowLastOperatorResponse lastOperator;
}
