package com.weaver.seconddev.zyhlw.domain.response.todo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TodoAuditResponse implements Serializable {
    /**
     *  状态码，成功返回0
     */
    private Integer code;
    /**
     *  结果描述
     */
    private String message;
    /**
     * 结果
     */
    private List<Item> itemList;

    @Data
    public static class Item implements Serializable{
        /**
         *  事项标题
         */
        private String itemTitle;
        /**
         *  事项状态、0删除，1有效
         */
        private String itemStatus;
        /**
         *  业务系统事项的唯一标识，
         * {originalId}
         */
        private String originalId;
        /**
         *  推送待办系统的系统编码
         */
        private String systemCode;
        /**
         *  推送待办系统的系统名称
         */
        private String systemName;
        /**
         *  待办的认证类型，1、portal认证，2、smap，其它待定
         */
        private String authType;
        /**
         *  事项创建时间，必填，格式yyyy-MM-dd HH:mm:ss
         */
        private String createTime;
        /**
         *  事项发起人的账号
         */
        private String creatorUserId;
        /**
         *  事项发起人的中文名称
         */
        private String creatorCnName;
        /**
         *  事项发起人账号类型，默认 1 portal,1、员工编号，2、集团编号，9 其它类型
         */
        private Integer creatorUserIdType;
        /**
         *  上一流程节点处理人账号
         */
        private String prevUserId;
        /**
         *  上一流程节点处理人名称
         */
        private String prevCnName;
        /**
         *  事项当前处理人账号
         */
        private String receiverUserId;
        /**
         *  事项当前处理人名称
         */
        private String receiverCnName;
        /**
         *  事项当前处理人账号类型，默认 1 portal,1、员工编号，2、集团编号，9 其它类型
         */
        private String receiverUserIdType;
        /**
         *  Web访问Url地址
         */
        private String webUrl;
        /**
         *  移动端访问Url地址。若无移动端访问地址可为空值
         */
        private String mobileUrl;
        /**
         *  事项类型，1、待办、2、待阅，3、已办、4、已阅，6、草稿
         */
        private Integer itemType;

    }
}
