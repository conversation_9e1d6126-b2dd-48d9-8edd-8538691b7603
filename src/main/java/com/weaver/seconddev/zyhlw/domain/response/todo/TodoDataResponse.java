package com.weaver.seconddev.zyhlw.domain.response.todo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
@Getter
@Setter
public class TodoDataResponse implements Serializable {
    /**
     *  待办标题
     */
    private String title;

    private String titleHtml;
    /**
     * 待办PC端链接
     */
    private String pcUrl;
    /**
     * id
     */
    private String id;
    /**
     * 认证类型
     */
    private String authType;
    /**
     * appid 集团待办存在值
     */
    private String itemAppId;
    /**
     * 接收人
     */
    private String lastUser;
    private String lastuserHtml;
    /**
     * 置灰状态: Y N
     */
    private String isGray;
    /**
     * 系统编码
     */
    private String systemCode;

    /**
     *  接收时间
     * */
    private String formatDate;
    private String formatDataHtml;
    /**
     *  系统名称
     */
    private String systemName;
    private String systemNameHtml;
    /**
     * 系统简称
     */
    private String systemAbbreviation;
    private String systemAbbreviationHtml;
    /**
     * 唯一ID
     */
    private String uniqueId;
    /**
     * 服务版本
     */
    private String serviceVersion;
    /**
     * 浏览器类型
     */
    private String browserType;
    /**
     * 最后接收时间
     */
    private String lastUpdateTime;
    /**
     * 创建时间
     */
    private  String createTime;
    /**
     * 创建人
     */
    private String createName;
    /**
     * 票据参数名
     */
    private String billParamName;
}
