package com.weaver.seconddev.zyhlw.domain.response.todo;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 待办推送集团响应实体
 * <AUTHOR>
 */
@Data
public class TodoItemResponse implements Serializable {

    private Integer code;

    private String msg;
    /** 非批量返回 */
    private Item item;
    /** 批量返回 */
    private List<Item> itemList;
    /**
     * 如：{
     *  "itemTitle": "不能为null",
     *  "authType": "不能为null"
     *  }
     */
    private Object data;

    @Data
    static class Item implements Serializable {
        /** 记录ID，每次更新都会产生新的记录id */
        private String recordId;
        /** 业务系统原始id，用于业务系统校对数据 */
        private String originalId;
        /** 变更类型，update 更新，insert 新增 */
        private String changgeType;
        /** Portal统一待办此待办的唯一标识id */
        private String uniqueId;
        /** 若返回空值，代表Portal内没有对应的用户 */
        private String receiverPortalUserId;
    }
}




