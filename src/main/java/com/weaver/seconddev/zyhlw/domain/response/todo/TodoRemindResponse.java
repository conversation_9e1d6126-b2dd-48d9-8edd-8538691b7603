package com.weaver.seconddev.zyhlw.domain.response.todo;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@Builder
public class TodoRemindResponse implements Serializable {

    /**
     * 操作表流程节点id
     */
    private String sendToNodeId;
    /**
     * 发送模板类型 dataType_pt_tx 通用提醒、CMIC_M_TX专用提醒
     */
    private String sendTempType;
    /**
     * LASTNAME 最后操作者电话/发送人电话（上一节点）
     */
    private String sendBusLastUserMobile;
    /**
     * 待处理人id
     */
    private String sendUserId;
    /**
     * requestname 请求名称
     */
    private String sendBusName;
    /**
     * 原始地址安全网关from=gatewayApp、||chr(38)短小a2p
     * 测试环境： https://outside-project.hfx.net/spa/OpenWorkflowGateway.jsp?requestid=
     * 正式环境： https://cmic.hfx.net/spa/OpenWorkflowGateway.jsp?requestid=
     */
    private String sendUrl;
    /**
     *
     */
    private String sendIsChatbotSend;
    /**
     * REQUESTLEVEL 请求级别 0正常,1重要,2紧急
     */
    private String sendBusLevel;
    /**
     * LASTNAME 最后操作者名/发送人名（上一节点）
     */
    private String sendBusLastUserName;
    /**
     * CREATER 流程创建人姓名
     */
    private String sendBusCreateUserName;
    /**
     * WORKFLOWID 工作流id
     */
    private String sendBusFlowId;
    /**
     * CREATER 流程创建人id
     *
     */
    private String sendBusCreateUserId;
    /**
     * LASTOPERATOR 最后操作者id/发送人id（上一节点）
     */
    private String sendBusLastUserId;
    /**
     * REQUESTID 请求id
     */
    private String sendBusId;
    /**
     * 接收人-手机号码
     */
    private String sendToUserMobile;
    /**
     * CURRENTNODEID 当前节点id
     */
    private String sendBusFlowNode;
    /**
     * 接收人-姓名
     */
    private String sendToUserName;
    /**
     *
     */
    private String sendIsSend;
    /**
     * 待办标题
     */
    private String titleName;
    /**
     * 发送失败次数
     */
    private String sendErrorCt;
    /**
     *
     */
    private String sendBusCreateUserMobile;
    /**
     * 操作表唯一ID
     */
    private String curNumberId;
    /**
     * 是否需要转换安全网关地址 默认0
     */
    private String sendGatewayShortUrl;

}
