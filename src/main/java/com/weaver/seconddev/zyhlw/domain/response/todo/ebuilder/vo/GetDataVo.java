package com.weaver.seconddev.zyhlw.domain.response.todo.ebuilder.vo;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
public class GetDataVo {
    @Setter
    @Getter
    private Map<String, Object> mainTable;
    private Map<String, List<Map<String, Object>>> details = new HashMap<>();

    // Use @JsonAnySetter to capture any additional fields
    @JsonAnySetter
    public void setDetails(String key, List<Map<String, Object>> value) {
        this.details.put(key, value);
    }

    // Use @JsonAnyGetter to provide access to the captured fields
    @JsonAnyGetter
    public Map<String, List<Map<String, Object>>> getDetails() {
        return details;
    }

    public static void main(String[] args) throws Exception {
        String json = "{\n" +
                "    \"mainTable\": {\n" +
                "        \"dxwb\": {\n" +
                "            \"fieldId\": 899353095309271040,\n" +
                "            \"fieldType\": \"Text\",\n" +
                "            \"fieldValue\": \"123123\"\n" +
                "        },\n" +
                "        \"dxk\": [\n" +
                "            {\n" +
                "                \"fieldId\": 899353095309271042,\n" +
                "                \"fieldType\": \"RadioBox\",\n" +
                "                \"fieldOptionId\": \"11\",\n" +
                "                \"fieldOptionName\": \"a\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"sj\": {\n" +
                "            \"fieldId\": 899353095309271044,\n" +
                "            \"fieldType\": \"TimeComponent\",\n" +
                "            \"fieldValue\": \"\"\n" +
                "        },\n" +
                "        \"sz\": {\n" +
                "            \"fieldId\": 899353095309271041,\n" +
                "            \"fieldType\": \"NumberComponent\",\n" +
                "            \"fieldValue\": \"111\"\n" +
                "        },\n" +
                "        \"id\": {\n" +
                "            \"fieldId\": 5,\n" +
                "            \"fieldType\": \"String\",\n" +
                "            \"fieldValue\": \"938283417605275652\",\n" +
                "            \"dataIndex\": 2,\n" +
                "            \"subFormId\": 899353146808803329\n" +
                "        },\n" +
                "        \"ryxz\": [\n" +
                "            {\n" +
                "                \"fieldId\": 899353146808803328,\n" +
                "                \"fieldType\": \"Employee\",\n" +
                "                \"fieldOptionId\": \"51128494848280496591\",\n" +
                "                \"fieldOptionName\": \"\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"rq\": {\n" +
                "            \"fieldId\": 899353095309271043,\n" +
                "            \"fieldType\": \"DateComponent\",\n" +
                "            \"fieldValue\": \"2023-11-03 00:00:00\"\n" +
                "        }\n" +
                "    },\n" +
                "    \"detail1\": [\n" +
                "        {\n" +
                "            \"dxwbdt1\": {\n" +
                "                \"fieldId\": 899353146808803330,\n" +
                "                \"fieldType\": \"Text\",\n" +
                "                \"fieldValue\": \"111\",\n" +
                "                \"dataIndex\": 1,\n" +
                "                \"subFormId\": 899353146808803329\n" +
                "            }\n" +
                "        },\n" +
                "        {\n" +
                "            \"dxwbdt1\": {\n" +
                "                \"fieldId\": 899353146808803330,\n" +
                "                \"fieldType\": \"Text\",\n" +
                "                \"fieldValue\": \"222\",\n" +
                "                \"dataIndex\": 2,\n" +
                "                \"subFormId\": 899353146808803329\n" +
                "            }\n" +
                "        }\n" +
                "    ]\n" +
                "}";

        ObjectMapper mapper = new ObjectMapper();
        GetDataVo entity = mapper.readValue(json, GetDataVo.class);

        System.out.println("MainTable: " + JSON.toJSONString(entity.getMainTable()));
        for (Map.Entry<String, List<Map<String, Object>>> entry : entity.getDetails().entrySet()) {
            System.out.println("MainTable: " + JSON.toJSONString(entry.getValue()));

            // You can further process each DetailItem if needed
        }
    }
}