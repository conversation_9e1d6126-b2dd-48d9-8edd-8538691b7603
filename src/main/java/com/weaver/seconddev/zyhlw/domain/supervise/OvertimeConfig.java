package com.weaver.seconddev.zyhlw.domain.supervise;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class OvertimeConfig {

    /**
     * 流程名称
     */
    private String wfName;
    /**
     * 流程id
     */
    private String workflowId;
    /**
     * 流程表名
     */
    private String wfTableName;
    /**
     * 发送类型
     */
    private String sendType;
    /**
     * 预警最大数量
     */
    private String warnMaxNum;
    /**
     * 预警天数
     */
    private String afterDayNum;
    /**
     * 预警天数2
     */
    private String afterDayNum2;
    /**
     * 选择节点id
     */
    private String selectNodeId;
}
