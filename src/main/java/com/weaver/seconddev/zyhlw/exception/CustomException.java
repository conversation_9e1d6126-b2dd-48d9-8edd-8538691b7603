package com.weaver.seconddev.zyhlw.exception;


/**
 * <AUTHOR>
 * @Description 自定义异常通用类
 * @Date 2024-08-01 10:35
 */
// 自定义异常类 InvalidUserInputException
public class CustomException extends RuntimeException {

    // 可以根据需要添加其他方法或逻辑
    private final int errorCode;

    public CustomException() {
        // 调用父类的无参构造函数
        super();
        this.errorCode = 500;
    }

    // 定义一个无参构造函数
    public CustomException(int errorCode) {
        // 调用父类的无参构造函数
        super();
        this.errorCode = errorCode;
    }

    // 定义一个带有详细信息的构造函数
    public CustomException(String message, int errorCode) {
        // 调用父类的带有详细信息的构造函数
        super(message);
        this.errorCode = errorCode;
    }

    // 定义一个带有详细信息和原因的构造函数
    public CustomException(String message, Throwable cause, int errorCode) {
        // 调用父类的带有详细信息和原因的构造函数
        super(message, cause);
        this.errorCode = errorCode;
    }

    // 定义一个带有原因的构造函数
    public CustomException(Throwable cause) {
        // 调用父类的带有原因的构造函数
        super(cause);
        this.errorCode = 500;
    }



    public int getErrorCode() {
        return errorCode;
    }
}