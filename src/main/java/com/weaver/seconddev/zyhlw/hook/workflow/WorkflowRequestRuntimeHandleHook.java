package com.weaver.seconddev.zyhlw.hook.workflow;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.loom.api.annotation.RegHook;
import com.weaver.loom.context.register.AbstractRegHookEvent;
import com.weaver.openapi.pojo.basicserver.res.vo.AccountVo;
import com.weaver.openapi.pojo.flow.res.FlowListResultVo;
import com.weaver.openapi.pojo.flow.res.vo.*;
import com.weaver.openapi.pojo.user.res.vo.UserInfoResult;
import com.weaver.seconddev.zyhlw.domain.request.todo.TodoItemRequest;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.service.IPortalWaitService;
import com.weaver.seconddev.zyhlw.service.IWorkflowTodoService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.SerializationUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 流程实例钩子
 * 在创建开始，创建结束，流程流转开始，流程流转数据计算完成，流程流转结束后 可以通过二开对流程流转以及创建进行自定义逻辑处理；并且可以阻断业务；
 * eventType：
 * CREATE_START        创建流程开始时
 * CREATE_END          创建流程结束时
 * FLOW_START          流程流转开始时，此时流程信息已经保存好了；
 * CAL_FLOW_INFO_END   流程流转过程中，计算出口、节点、操作者完成时
 * FLOW_END            流程流转结束时
 * SAVE_START          流程保存开始时；FLOW_START前也会触发；
 * SAVE_END            流程保存结束时；FLOW_START前也会触发；
 *
 * <AUTHOR>
 * @Return 返回一个map，status必填，true或者false，false代表阻断，当为false，msg要精确填写
 * @Date 10:41 2024/7/2
 **/
@Slf4j
@RegHook(module = "workflow", event = "wfcRequestOperateEvent", order = 1)
public class WorkflowRequestRuntimeHandleHook extends AbstractRegHookEvent {
    @Resource
    private transient IOpenPlatformService iOpenPlatformService;
    @Resource
    private transient IWorkflowTodoService workflowTodoService;
    @Resource
    private transient IPortalWaitService iPortalWaitService;
    @Resource
    private IDataSqlService dataSqlService;
    @Resource
    CmicProperties cmicProperties;
    private static final String USER_ID = "userId";
    private String simpleName = WorkflowRequestRuntimeHandleHook.class.getSimpleName();



    @Override
    public Object handle(Object[] args) throws Exception {
        String methodName = simpleName+".handle()";
        Map<String, Object> result = new HashMap<>(2);
        List<String> eventTypes = Collections.singletonList("FLOW_END");
        // 参数1：eventType
        String eventType = (String) args[0];

        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(args[1]));
        JSONObject wfpPath = jsonObject.getJSONObject("wfpPath");
        JSONObject baseInfo = wfpPath.getJSONObject("baseInfo");
        String tenantKey = baseInfo.getString("tenantKey");
        if (tenantKey == null || "".equals(tenantKey)) {
            log.warn("{} 获取租户代码为空",methodName);
            result.put("status", true);
            result.put("msg", "");
            // 返回结果
            return result;
        }
        log.info("{} 监听流程钩子，eventType：{}", methodName,eventType);
        // 查询对应的租户有没有进行配置
//        String sql = "select * from uf_todo_push_tenant_config where is_delete=0 and tenant_code='"+tenantKey+"'";
//        List<Map<String, Object>> tenantConfigList = dataSqlService.ebuilderFromSql(sql, 1, 100, SourceType.LOGIC);
        if (1 > 0) {
//            log.info("{} 查询到租户配置信息",methodName);
//            Map<String, Object> tenantConfig = tenantConfigList.get(0);
            Map<String, Object> tenantConfig = new HashMap<>();
//            log.info("{} 租户代码：{}",methodName,tenantConfig.get("tenant_code").toString());
//            log.info("{} 企业ID：{}",methodName,tenantConfig.get("corp_id").toString());
//            log.info("{} 应用appkey：{}",methodName,tenantConfig.get("app_key").toString());
//            log.info("{} 应用appsecret：{}",methodName,tenantConfig.get("app_secret").toString());

            // 监听流程创建结束、流程流转结束时触发
            if (eventTypes.contains(eventType)) {

                log.info("{} args[1]: {}", methodName,JSON.toJSONString(args[1]));
                ScheduledExecutorService executorService = Executors.newScheduledThreadPool(1);
                // 使用延时任务，2秒后执行
                executorService.schedule(() -> handleWorkflow4(args,tenantConfig), 2, TimeUnit.SECONDS);
            }
            result.put("status", true);
            result.put("msg", "");
            // 返回结果
            return result;
        }else {
            log.info("{} 该租户没有配置推送：{}",methodName,tenantKey);
            result.put("status", true);
            result.put("msg", "");
            // 返回结果
            return result;
        }
    }
    private void  handleWorkflow4(Object[] args, Map<String, Object> tenantConfig){
        String methodName = "调用"+simpleName+".handleWorkflow4()";
        // 参数2：流程相关参数
        Map<?, ?> paramMap = (Map<?, ?>) args[1];
        long requestId = Long.parseLong(paramMap.get("requestId").toString());
        Long userIdOperate = Long.parseLong(paramMap.get(USER_ID).toString());
        Map<?, ?> requestBaseInfo = (Map<?, ?>) paramMap.get("requestBaseInfo");
        String requestName = requestBaseInfo.get("requestName").toString();
        Map<?, ?> currentNode = (Map<?, ?>) paramMap.get("currentNode");
        Map<?,?> rejectConfig = (Map<?, ?>) currentNode.get("rejectConfig");
        // 当前节点id
        String currentNodeId = currentNode.get("id").toString();
        // 默认节点路径
        String defaultNodeIdStr = rejectConfig.get("defaultNodeIdStr").toString();
        String[] defaultNodeIdArr = defaultNodeIdStr.split(",");
        // 当前节点类型  START=发起  APPROVE=批准
        String currentNodeType = currentNode.get("nodeType").toString();
        // 节点id
        String lastNodeId = requestBaseInfo.get("lastNodeId").toString();

        String lastOperator = requestBaseInfo.get("lastOperator").toString();
        // 开始节点
        String startNodeId = requestBaseInfo.get("startNodeId").toString();
        // 创建时间
        String createDateTime = requestBaseInfo.get("createDateTime").toString().replace("T"," ");
        LocalDateTime createLocalDateTime = DateUtils.parseString(createDateTime, DateUtils.yyyy_MM_dd_HH_mm_ss);
        // 最后操作时间
        String lastOperateDateTime = requestBaseInfo.get("lastOperateDateTime").toString().replace("T"," ");
        LocalDateTime lastOperateLocalDateTime = DateUtils.parseString(lastOperateDateTime, DateUtils.yyyy_MM_dd_HH_mm_ss);
        // 创建人ID
        String creator = requestBaseInfo.get("creator").toString();
        // 创建人名
        String creatorLastName = requestBaseInfo.get("creatorLastName").toString();

        String flowStatus  = requestBaseInfo.get("flowStatus").toString();
        // 流程ID
        String workflowId = requestBaseInfo.get("workflowId").toString();
        String systemName = tenantConfig.get("system_name").toString();
        String systemCode = tenantConfig.get("system_code").toString();

        // 流程信息
//        WorkFlowRequestInfo workflowRequest = iOpenPlatformService.getWorkflowRequest(userIdOperate, String.valueOf(requestId));
//        log.info("{} 查询流程信息：workflowBaseInfo：{}",methodName,JSON.toJSONString(workflowRequest.getWorkflowBaseInfo()));
//        log.info("{} 查询流程信息：workflowRequestLogs：{}",methodName,JSON.toJSONString(workflowRequest.getWorkflowRequestLogs()));
//        // 推送数据
//        List<TodoItemRequest> todoItemRequestList = new ArrayList<>();
//        //  推送记录数据
//        List<Map<String,Object>> recordList = new ArrayList<>();
//        // 取最后的一条日志
//        WorkFlowRequestLog requestLog=   workflowRequest.getWorkflowRequestLogs().size() > 0 ? workflowRequest.getWorkflowRequestLogs().get(workflowRequest.getWorkflowRequestLogs().size()-1):  new WorkFlowRequestLog();
//        LocalDateTime receiveDateTime = DateUtils.parseString(requestLog.getReceiveDateTime(), DateUtils.yyyy_MM_dd_HH_mm_ss);
//        LocalDateTime operateDatetime = DateUtils.parseString(requestLog.getOperateDatetime(),DateUtils.yyyy_MM_dd_HH_mm_ss);
//        AccountVo userAccount = iOpenPlatformService.findAccount(requestLog.getUserId());
//        // 发起节点
//        if ("0".equals(lastNodeId) && "START".equals(currentNodeType)) {
//            // 用创建人账号调用查询创建数据接口
//            FlowListResultVo myWorkflowRequestList = iOpenPlatformService.getMyWorkflowRequestList(Long.valueOf(creator), 1, 10, null, workflowId, new ArrayList<>());
//            log.info("{} 查询我的创建数据：{}",methodName,JSON.toJSONString(myWorkflowRequestList));
//            for (FlowList item : myWorkflowRequestList.getData()) {
//                log.info("{} 创建数据的RequestId：{}，Requestid：{}",methodName,item.getRequestId(),item.getRequestid());
//                if (item.getRequestid().equals(String.valueOf(requestId))) {
//                    // 查询创建人账号
//                    AccountVo creatorAccount = iOpenPlatformService.findAccount(Long.parseLong(item.getCreatorId()));
//                    String originalId= String.format("my-%s-%s",requestId,workflowId);
////                    UserInfoResult creatorUser = iOpenPlatformService.getUser(Long.parseLong(item.getCreatorId()));
//                    TodoItemRequest todoItemRequest = TodoItemRequest.builder()
//                            .systemName(systemName)
//                            .systemCode(systemCode)
//                            .itemTitle(requestName)
//                            .itemStatus("1")
//                            .authType("1")
//                            .creatorUserIdType(1)
//                            .receiverUserIdType("1")
//                            .itemType(5)
//                            .webUrl(replaceUrl(tenantConfig.get("web_url").toString(),String.valueOf(requestId)))
//                            .mobileUrl(replaceUrl(tenantConfig.get("mobile_url").toString(),String.valueOf(requestId)))
//                            .createTime(item.getCreateTime())
//                            .creatorUserId(creatorAccount.getLoginName())
//                            .creatorCnName(item.getCreatorName())
//                            .creator(Long.valueOf(item.getCreatorId()))
//                            .prevCnName(item.getCreatorName())
//                            .prevUserId(creatorAccount.getLoginName())
//                            .prev(Long.valueOf(item.getCreatorId()))
//                            .receiverCnName(item.getCreatorName())
//                            .receiverUserId(creatorAccount.getLoginName())
//                            .receiver(Long.valueOf(item.getCreatorId()))
//                            .originalId(originalId)
//                            .instId(originalId)
//                            .requestId(String.valueOf(requestId))
//                            .build();
//                    iPortalWaitService.pushTodoData(todoItemRequest);
//                    break;
//                }
//            }
//            // 用创建人账号调用查询已办数据接口
//            FlowListResultVo handledWorkflowRequestList = iOpenPlatformService.getHandledWorkflowRequestList(Long.valueOf(creator), 1, 10, null, workflowId, new ArrayList<>());
//            log.info("{} 查询已办数据：{}",methodName,JSON.toJSONString(handledWorkflowRequestList));
//            for (FlowList item : handledWorkflowRequestList.getData()) {
//                if (item.getRequestid().equals(String.valueOf(requestId))) {
//                    // 查询创建人账号
//                    AccountVo creatorAccount = iOpenPlatformService.findAccount(Long.parseLong(item.getCreatorId()));
//                    String originalId= String.format("%s-%s",requestId,workflowId);
////                    UserInfoResult creatorUser = iOpenPlatformService.getUser(Long.parseLong(item.getCreatorId()));
//                    TodoItemRequest todoItemRequest = TodoItemRequest.builder()
//                            .systemName(systemName)
//                            .systemCode(systemCode)
//                            .itemTitle(requestName)
//                            .itemStatus("1")
//                            .authType("1")
//                            .creatorUserIdType(1)
//                            .receiverUserIdType("1")
//                            .itemType(3)
//                            .webUrl(replaceUrl(tenantConfig.get("web_url").toString(),String.valueOf(requestId)))
//                            .mobileUrl(replaceUrl(tenantConfig.get("mobile_url").toString(),String.valueOf(requestId)))
//                            .createTime(item.getCreateTime())
//                            .creatorUserId(creatorAccount.getLoginName())
//                            .creatorCnName(item.getCreatorName())
//                            .creator(Long.valueOf(item.getCreatorId()))
//                            .prevCnName(item.getCreatorName())
//                            .prevUserId(creatorAccount.getLoginName())
//                            .prev(Long.valueOf(item.getCreatorId()))
//                            .receiverCnName(item.getCreatorName())
//                            .receiverUserId(creatorAccount.getLoginName())
//                            .receiver(Long.valueOf(item.getCreatorId()))
//                            .originalId(originalId)
//                            .instId(originalId)
//                            .requestId(String.valueOf(requestId))
//                            .build();
//                    iPortalWaitService.pushTodoData(todoItemRequest);
//                    break;
//                }
//            }
//            // 用一下节点接收人查询
//            String[] receivedPersonIds = requestLog.getReceivedPersonIds().split(",");
//            for (String receivedPersonId : receivedPersonIds) {
//                FlowListResultVo toDoWorkflowRequestList = iOpenPlatformService.getToDoWorkflowRequestList(Long.valueOf(receivedPersonId), 1, 10, null, workflowId, new ArrayList<>());
//                log.info("{} 查询待办数据：{}",methodName,JSON.toJSONString(toDoWorkflowRequestList));
//                for (FlowList item : toDoWorkflowRequestList.getData()) {
//                    if (item.getRequestid().equals(String.valueOf(requestId))) {
//                        // 查询创建人账号
//                        AccountVo creatorAccount = iOpenPlatformService.findAccount(Long.parseLong(item.getCreatorId()));
//                        AccountVo receiveAccount = iOpenPlatformService.findAccount(Long.parseLong(receivedPersonId));
//                        UserInfoResult receivedUser = iOpenPlatformService.getUser(Long.parseLong(receivedPersonId));
//                        String originalId= String.format("%s-%s",requestId,workflowId);
////                    UserInfoResult creatorUser = iOpenPlatformService.getUser(Long.parseLong(item.getCreatorId()));
//                        TodoItemRequest todoItemRequest = TodoItemRequest.builder()
//                                .systemName(systemName)
//                                .systemCode(systemCode)
//                                .itemTitle(requestName)
//                                .itemStatus("1")
//                                .authType("1")
//                                .creatorUserIdType(1)
//                                .receiverUserIdType("1")
//                                .itemType(1)
//                                .webUrl(replaceUrl(tenantConfig.get("web_url").toString(),String.valueOf(requestId)))
//                                .mobileUrl(replaceUrl(tenantConfig.get("mobile_url").toString(),String.valueOf(requestId)))
//                                .createTime(item.getCreateTime())
//                                .creatorUserId(creatorAccount.getLoginName())
//                                .creatorCnName(item.getCreatorName())
//                                .creator(Long.valueOf(item.getCreatorId()))
//                                .prevCnName(item.getCreatorName())
//                                .prevUserId(creatorAccount.getLoginName())
//                                .prev(Long.valueOf(item.getCreatorId()))
//                                .receiverCnName(receivedUser.getUsername())
//                                .receiverUserId(receiveAccount.getLoginName())
//                                .receiver(Long.parseLong(receivedPersonId))
//                                .originalId(originalId)
//                                .instId(originalId)
//                                .requestId(String.valueOf(requestId))
//                                .build();
//                        iPortalWaitService.pushTodoData(todoItemRequest);
//                        break;
//                    }
//                }
//
//            }
//        }

    }
    private void handleWorkflow3(Object[] args, Map<String, Object> tenantConfig){
        String methodName = "调用"+simpleName+".handleWorkflow3()";
        // 参数2：流程相关参数
        Map<?, ?> paramMap = (Map<?, ?>) args[1];
        long requestId = Long.parseLong(paramMap.get("requestId").toString());
        Long userIdOperate = Long.parseLong(paramMap.get(USER_ID).toString());
        Map<?, ?> requestBaseInfo = (Map<?, ?>) paramMap.get("requestBaseInfo");
        String requestName = requestBaseInfo.get("requestName").toString();
        Map<?, ?> currentNode = (Map<?, ?>) paramMap.get("currentNode");
        Map<?,?> rejectConfig = (Map<?, ?>) currentNode.get("rejectConfig");
        // 当前节点id
        String currentNodeId = currentNode.get("id").toString();
        // 默认节点路径
        String defaultNodeIdStr = rejectConfig.get("defaultNodeIdStr").toString();
        String[] defaultNodeIdArr = defaultNodeIdStr.split(",");
        // 当前节点类型  START=发起  APPROVE=批准
        String currentNodeType = currentNode.get("nodeType").toString();
        // 节点id
        String lastNodeId = requestBaseInfo.get("lastNodeId").toString();

        String lastOperator = requestBaseInfo.get("lastOperator").toString();
        // 开始节点
        String startNodeId = requestBaseInfo.get("startNodeId").toString();
        // 创建时间
        String createDateTime = requestBaseInfo.get("createDateTime").toString().replace("T"," ");
        LocalDateTime createLocalDateTime = DateUtils.parseString(createDateTime, DateUtils.yyyy_MM_dd_HH_mm_ss);
        // 最后操作时间
        String lastOperateDateTime = requestBaseInfo.get("lastOperateDateTime").toString().replace("T"," ");
        LocalDateTime lastOperateLocalDateTime = DateUtils.parseString(lastOperateDateTime, DateUtils.yyyy_MM_dd_HH_mm_ss);
        // 创建人ID
        String creator = requestBaseInfo.get("creator").toString();
        AccountVo creatorAccount = iOpenPlatformService.findAccount(Long.parseLong(creator));
        // 创建人名
        String creatorLastName = requestBaseInfo.get("creatorLastName").toString();

        String flowStatus  = requestBaseInfo.get("flowStatus").toString();
        // 流程ID
        String workflowId = requestBaseInfo.get("workflowId").toString();
        String systemName = tenantConfig.get("system_name").toString();
        String systemCode = tenantConfig.get("system_code").toString();

        // 流程信息
        WorkFlowRequestInfo workflowRequest = iOpenPlatformService.getWorkflowRequest(userIdOperate, String.valueOf(requestId));
        log.info("{} 查询流程信息：workflowBaseInfo：{}",methodName,JSON.toJSONString(workflowRequest.getWorkflowBaseInfo()));
        log.info("{} 查询流程信息：workflowRequestLogs：{}",methodName,JSON.toJSONString(workflowRequest.getWorkflowRequestLogs()));
        // 推送数据
        List<TodoItemRequest> todoItemRequestList = new ArrayList<>();
        //  推送记录数据
        List<Map<String,Object>> recordList = new ArrayList<>();
        // 取最后的一条日志
        WorkFlowRequestLog requestLog=   workflowRequest.getWorkflowRequestLogs().size() > 0 ? workflowRequest.getWorkflowRequestLogs().get(workflowRequest.getWorkflowRequestLogs().size()-1):  new WorkFlowRequestLog();
        LocalDateTime receiveDateTime = DateUtils.parseString(requestLog.getReceiveDateTime(), DateUtils.yyyy_MM_dd_HH_mm_ss);
        LocalDateTime operateDatetime = DateUtils.parseString(requestLog.getOperateDatetime(),DateUtils.yyyy_MM_dd_HH_mm_ss);
        AccountVo userAccount = iOpenPlatformService.findAccount(requestLog.getUserId());
        // 发起节点
        if ("0".equals(lastNodeId) && "START".equals(currentNodeType)) {

            //接收时间-请求ID-流程ID-当前节点ID
            String originalId =  String.format("%s-%s",requestId,workflowId);
            // 我的创建数据
            TodoItemRequest todoItemRequest = TodoItemRequest.builder().systemName(systemName).systemCode(systemCode).itemTitle(requestName).itemStatus("1").authType("1").creatorUserIdType(1).receiverUserIdType("1").build();
            todoItemRequest.setWebUrl(replaceUrl(tenantConfig.get("web_url").toString(),String.valueOf(requestId)));
            todoItemRequest.setMobileUrl(replaceUrl(tenantConfig.get("mobile_url").toString(),String.valueOf(requestId)));
            todoItemRequest.setOriginalId("my-"+originalId);
            todoItemRequest.setCreateTime(DateUtils.formatTime(createLocalDateTime, DateUtils.yyyy_MM_dd_HH_mm_ss));
            todoItemRequest.setCreatorUserId(creatorAccount.getLoginName());
            todoItemRequest.setCreatorCnName(creatorLastName);
            todoItemRequest.setPrevCnName(creatorLastName);
            todoItemRequest.setPrevUserId(creatorAccount.getLoginName());
            todoItemRequest.setReceiverUserId(creatorAccount.getLoginName());
            todoItemRequest.setReceiverCnName(creatorLastName);
            todoItemRequest.setItemType(5);
            todoItemRequest.setInstId("my-"+originalId);
            todoItemRequest.setActId(currentNodeId);
            todoItemRequestList.add(todoItemRequest);
            Map<String,Object> myResordMap = new HashMap<>();
            myResordMap.put("related_processes",requestId);
            myResordMap.put("receiver_user",creator);
            myResordMap.put("todo_type",5);
            myResordMap.put("prev_user",creator);
            myResordMap.put("push_parameter",JSON.toJSONString(todoItemRequest));
            myResordMap.put("original_id","my"+originalId);
            recordList.add(myResordMap);
            // 已办数据
            TodoItemRequest alreadyDone = SerializationUtils.clone(todoItemRequest);
            alreadyDone.setOriginalId(originalId);
            alreadyDone.setItemType(3);
            alreadyDone.setInstId(originalId);
            todoItemRequestList.add(alreadyDone);
            Map<String,Object> alreadyResordMap = new HashMap<>();
            alreadyResordMap.putAll(myResordMap);
            alreadyResordMap.put("todo_type",3);
            alreadyResordMap.put("original_id",originalId);
            alreadyResordMap.put("push_parameter",JSON.toJSONString(alreadyDone));
            recordList.add(alreadyResordMap);

            String[] receivedPersonIds = requestLog.getReceivedPersonIds().split(",");
            for (String receivedPersonId : receivedPersonIds) {
                // 待办数据
                TodoItemRequest todoData = SerializationUtils.clone(alreadyDone);
                todoData.setItemType(1);
                String todoOriginalId = String.format("%s-%s",requestId,workflowId);
                todoData.setOriginalId(todoOriginalId);
                todoData.setInstId(todoOriginalId);
                AccountVo receivedPersonAccount = iOpenPlatformService.findAccount(Long.parseLong(receivedPersonId));
                UserInfoResult receivedPrtsonUser = iOpenPlatformService.getUser(Long.parseLong(receivedPersonId));
                todoData.setReceiverUserId(receivedPersonAccount.getLoginName());
                todoData.setReceiverCnName(receivedPrtsonUser.getUsername());
                todoItemRequestList.add(todoData);
                Map<String,Object> todoResordMap = new HashMap<>();
                todoResordMap.putAll(myResordMap);
                todoResordMap.put("todo_type",1);
                todoResordMap.put("original_id",todoOriginalId);
                todoResordMap.put("push_parameter",JSON.toJSONString(todoData));
                todoResordMap.put("receiver_user",receivedPersonId);
                recordList.add(todoResordMap);
            }


        }
        // 需要产生已办的情况
        log.info("{} flowStatus：{}",methodName,flowStatus);
        log.info("{} lastNodeId：{}",methodName,lastNodeId);
        log.info("{} lastNodeId：{}",methodName,currentNodeType);
        if (("APPROVAL".equals(flowStatus) && !"0".equals(lastNodeId) && !"START".equals(currentNodeType)) || ("NORMAL_ARCHIVE".equals(flowStatus))) {
            long lastFeedBackOperator = Long.parseLong(requestBaseInfo.get("lastFeedBackOperator").toString());
            AccountVo lastFeedBackOperatorAccount = iOpenPlatformService.findAccount(lastFeedBackOperator);
            UserInfoResult lastFeedBackOperatorUser = iOpenPlatformService.getUser(lastFeedBackOperator);
            log.info("{} 签字意见：{}",methodName,JSON.toJSONString(requestLog));
            String yiabngOriginalId = String.format("%s-%s",requestId,workflowId);
            log.info("{} 已办唯一标识：{}",methodName,yiabngOriginalId);
            TodoItemRequest todoItemRequest = TodoItemRequest.builder().systemName(systemName).systemCode(systemCode).itemTitle(requestName).itemStatus("1").authType("1").creatorUserIdType(1).receiverUserIdType("1").build();
            todoItemRequest.setWebUrl(replaceUrl(tenantConfig.get("web_url").toString(),String.valueOf(requestId)));
            todoItemRequest.setMobileUrl(replaceUrl(tenantConfig.get("mobile_url").toString(),String.valueOf(requestId)));
            todoItemRequest.setOriginalId(yiabngOriginalId);
            todoItemRequest.setCreateTime(DateUtils.formatTime(createLocalDateTime, DateUtils.yyyy_MM_dd_HH_mm_ss));
            todoItemRequest.setCreatorUserId(creatorAccount.getLoginName());
            todoItemRequest.setCreatorCnName(creatorLastName);
            todoItemRequest.setPrevCnName(lastFeedBackOperatorUser.getUsername());
            todoItemRequest.setPrevUserId(lastFeedBackOperatorAccount.getLoginName());
            todoItemRequest.setReceiverUserId(userAccount.getLoginName());
            todoItemRequest.setReceiverCnName(requestLog.getUserName());
            todoItemRequest.setItemType(3);
            todoItemRequest.setInstId(yiabngOriginalId);
            todoItemRequest.setActId(currentNodeId);
            todoItemRequestList.add(todoItemRequest);
            Map<String,Object> todoResordMap = new HashMap<>();
            todoResordMap.put("related_processes",requestId);
            todoResordMap.put("receiver_user",requestLog.getUserId());
            todoResordMap.put("todo_type",3);
            todoResordMap.put("prev_user",lastFeedBackOperator);
            todoResordMap.put("push_parameter",JSON.toJSONString(todoItemRequest));
            todoResordMap.put("original_id",yiabngOriginalId);
            recordList.add(todoResordMap);
        }
        if (!"NORMAL_ARCHIVE".equals(flowStatus) && !"0".equals(lastNodeId) && !"START".equals(currentNodeType)) {

            //操作时间-请求ID-流程ID-下一节点ID
            String todoOriginalId = String.format("%s-%s",requestId,workflowId);
            TodoItemRequest todoItemRequest = TodoItemRequest.builder().systemName(systemName).systemCode(systemCode).itemTitle(requestName).itemStatus("1").authType("1").creatorUserIdType(1).receiverUserIdType("1").build();
            todoItemRequest.setWebUrl(replaceUrl(tenantConfig.get("web_url").toString(),String.valueOf(requestId)));
            todoItemRequest.setMobileUrl(replaceUrl(tenantConfig.get("mobile_url").toString(),String.valueOf(requestId)));
            todoItemRequest.setOriginalId(todoOriginalId);
            todoItemRequest.setCreateTime(DateUtils.formatTime(createLocalDateTime, DateUtils.yyyy_MM_dd_HH_mm_ss));
            todoItemRequest.setCreatorUserId(creatorAccount.getLoginName());
            todoItemRequest.setCreatorCnName(creatorLastName);

            String[] receivedPersonIds = requestLog.getReceivedPersonIds().split(",");
            todoItemRequest.setItemType(1);
            todoItemRequest.setInstId(todoOriginalId);
            todoItemRequest.setActId(currentNodeId);
            for (String receivedPersonId : receivedPersonIds) {
                AccountVo receivedPersonAccount = iOpenPlatformService.findAccount(Long.parseLong(receivedPersonId));
                UserInfoResult receivedPrtsonUser = iOpenPlatformService.getUser(Long.parseLong(receivedPersonId));
                todoItemRequest.setReceiverUserId(receivedPersonAccount.getLoginName());
                todoItemRequest.setReceiverCnName(receivedPrtsonUser.getUsername());
                todoItemRequest.setPrevCnName(requestLog.getUserName());
                todoItemRequest.setPrevUserId(userAccount.getLoginName());
                todoItemRequestList.add(todoItemRequest);
                Map<String,Object> todoResordMap = new HashMap<>();
                todoResordMap.put("related_processes",requestId);
                todoResordMap.put("receiver_user",receivedPersonId);
                todoResordMap.put("todo_type",1);
                todoResordMap.put("prev_user",requestLog.getUserId());
                todoResordMap.put("push_parameter",JSON.toJSONString(todoItemRequest));
                todoResordMap.put("original_id",todoOriginalId);
                recordList.add(todoResordMap);
            }
        }
        iPortalWaitService.pushTodoDataToJtBatch(todoItemRequestList,recordList,null);

    }
    private void  handleWorkflow2(Object[] args, Map<String, Object> tenantConfig){
        String methodName = "调用"+simpleName+".handleWorkflow2()";
        // 参数2：流程相关参数
        Map<?, ?> paramMap = (Map<?, ?>) args[1];
        long requestId = Long.parseLong(paramMap.get("requestId").toString());
        Long userIdOperate = Long.parseLong(paramMap.get(USER_ID).toString());
        Map<?, ?> requestBaseInfo = (Map<?, ?>) paramMap.get("requestBaseInfo");
        String requestName = requestBaseInfo.get("requestName").toString();
        Map<?, ?> currentNode = (Map<?, ?>) paramMap.get("currentNode");
        Map<?,?> rejectConfig = (Map<?, ?>) currentNode.get("rejectConfig");
        // 当前节点id
        String currentNodeId = currentNode.get("id").toString();
        // 默认节点路径
        String defaultNodeIdStr = rejectConfig.get("defaultNodeIdStr").toString();
        String[] defaultNodeIdArr = defaultNodeIdStr.split(",");
        // 当前节点类型  START=发起  APPROVE=批准
        String currentNodeType = currentNode.get("nodeType").toString();
        // 节点id
        String lastNodeId = requestBaseInfo.get("lastNodeId").toString();

        String lastOperator = requestBaseInfo.get("lastOperator").toString();
        // 开始节点
        String startNodeId = requestBaseInfo.get("startNodeId").toString();
        // 创建时间
        String createDateTime = requestBaseInfo.get("createDateTime").toString().replace("T"," ");
        LocalDateTime createLocalDateTime = DateUtils.parseString(createDateTime, DateUtils.yyyy_MM_dd_HH_mm_ss);
        // 最后操作时间
        String lastOperateDateTime = requestBaseInfo.get("lastOperateDateTime").toString().replace("T"," ");
        LocalDateTime lastOperateLocalDateTime = DateUtils.parseString(lastOperateDateTime, DateUtils.yyyy_MM_dd_HH_mm_ss);
        // 创建人ID
        String creator = requestBaseInfo.get("creator").toString();
        AccountVo creatorAccount = iOpenPlatformService.findAccount(Long.parseLong(creator));
        // 创建人名
        String creatorLastName = requestBaseInfo.get("creatorLastName").toString();

        String flowStatus  = requestBaseInfo.get("flowStatus").toString();
        // 流程ID
        String workflowId = requestBaseInfo.get("workflowId").toString();
        String systemName = tenantConfig.get("system_name").toString();
        String systemCode = tenantConfig.get("system_code").toString();
        long lastFeedBackOperator = Long.parseLong(requestBaseInfo.get("lastFeedBackOperator").toString());
        AccountVo lastFeedBackOperatorAccount = iOpenPlatformService.findAccount(lastFeedBackOperator);
        UserInfoResult lastFeedBackOperatorUser = iOpenPlatformService.getUser(lastFeedBackOperator);
        // 流程信息
        WorkFlowRequestInfo workflowRequest = iOpenPlatformService.getWorkflowRequest(userIdOperate, String.valueOf(requestId));
        log.info("{} 查询流程信息：workflowBaseInfo：{}",methodName,JSON.toJSONString(workflowRequest.getWorkflowBaseInfo()));
        log.info("{} 查询流程信息：workflowRequestLogs：{}",methodName,JSON.toJSONString(workflowRequest.getWorkflowRequestLogs()));
        // 推送数据
        List<TodoItemRequest> todoItemRequestList = new ArrayList<>();
        //  推送记录数据
        List<Map<String,Object>> recordList = new ArrayList<>();
        // 取最后的一条日志
        WorkFlowRequestLog requestLog=   workflowRequest.getWorkflowRequestLogs().size() > 0 ? workflowRequest.getWorkflowRequestLogs().get(workflowRequest.getWorkflowRequestLogs().size()-1):  new WorkFlowRequestLog();
        LocalDateTime receiveDateTime = DateUtils.parseString(requestLog.getReceiveDateTime(), DateUtils.yyyy_MM_dd_HH_mm_ss);
        LocalDateTime operateDatetime = DateUtils.parseString(requestLog.getOperateDatetime(),DateUtils.yyyy_MM_dd_HH_mm_ss);
        // 发起节点
        if ("0".equals(lastNodeId) && "START".equals(currentNodeType)) {

            //接收时间-请求ID-流程ID-当前节点ID
            String originalId =  String.format("%s-%s-%s-%s",DateUtils.formatTime(receiveDateTime, DateUtils.yyyyMMddHHmmss),requestId,workflowId,currentNodeId);
            // 我的创建数据
            TodoItemRequest todoItemRequest = TodoItemRequest.builder().systemName(systemName).systemCode(systemCode).itemTitle(requestName).itemStatus("1").authType("1").creatorUserIdType(1).receiverUserIdType("1").build();
            todoItemRequest.setWebUrl(replaceUrl(tenantConfig.get("web_url").toString(),String.valueOf(requestId)));
            todoItemRequest.setMobileUrl(replaceUrl(tenantConfig.get("mobile_url").toString(),String.valueOf(requestId)));
            todoItemRequest.setOriginalId("my-"+originalId);
            todoItemRequest.setCreateTime(DateUtils.formatTime(createLocalDateTime, DateUtils.yyyy_MM_dd_HH_mm_ss));
            todoItemRequest.setCreatorUserId(creatorAccount.getLoginName());
            todoItemRequest.setCreatorCnName(creatorLastName);
            todoItemRequest.setPrevCnName(creatorLastName);
            todoItemRequest.setPrevUserId(creatorAccount.getLoginName());
            todoItemRequest.setReceiverUserId(creatorAccount.getLoginName());
            todoItemRequest.setReceiverCnName(creatorLastName);
            todoItemRequest.setItemType(5);
            todoItemRequest.setInstId("my-"+originalId);
            todoItemRequest.setActId(currentNodeId);
            todoItemRequestList.add(todoItemRequest);
            Map<String,Object> myResordMap = new HashMap<>();
            myResordMap.put("related_processes",requestId);
            myResordMap.put("receiver_user",creator);
            myResordMap.put("todo_type",5);
            myResordMap.put("prev_user",creator);
            myResordMap.put("push_parameter",JSON.toJSONString(todoItemRequest));
            myResordMap.put("original_id","my"+originalId);
            recordList.add(myResordMap);
            // 已办数据
            TodoItemRequest alreadyDone = SerializationUtils.clone(todoItemRequest);
            alreadyDone.setOriginalId(originalId);
            alreadyDone.setItemType(3);
            alreadyDone.setInstId(originalId);
            todoItemRequestList.add(alreadyDone);
            Map<String,Object> alreadyResordMap = new HashMap<>();
            alreadyResordMap.putAll(myResordMap);
            alreadyResordMap.put("todo_type",3);
            alreadyResordMap.put("original_id",originalId);
            alreadyResordMap.put("push_parameter",JSON.toJSONString(alreadyDone));
            recordList.add(alreadyResordMap);
            // 待办数据
            TodoItemRequest todoData = SerializationUtils.clone(alreadyDone);
            todoData.setItemType(1);
            //操作时间-请求ID-流程ID-下一节点ID

            int i = 0;
            for (String s : defaultNodeIdArr) {
                if (currentNodeId.equals(s)) {
                    i++;
                    break;
                }
                i++;
            }
            String nodeId  = defaultNodeIdArr[i];
            log.info("{} 待办对应节点：{}",methodName,nodeId);
            String todoOriginalId = String.format("%s-%s-%s-%s",DateUtils.formatTime(operateDatetime, DateUtils.yyyyMMddHHmmss),requestId,workflowId,nodeId);
            todoData.setOriginalId(todoOriginalId);
            todoData.setActId(nodeId);
            todoData.setInstId(todoOriginalId);
            String[] receivedPersonIds = requestLog.getReceivedPersonIds().split(",");
            for (String receivedPersonId : receivedPersonIds) {
                AccountVo receivedPersonAccount = iOpenPlatformService.findAccount(Long.parseLong(receivedPersonId));
                UserInfoResult receivedPrtsonUser = iOpenPlatformService.getUser(Long.parseLong(receivedPersonId));
                todoData.setReceiverUserId(receivedPersonAccount.getLoginName());
                todoData.setReceiverCnName(receivedPrtsonUser.getUsername());
                todoItemRequestList.add(todoData);
                Map<String,Object> todoResordMap = new HashMap<>();
                todoResordMap.putAll(myResordMap);
                todoResordMap.put("todo_type",1);
                todoResordMap.put("original_id",todoOriginalId);
                todoResordMap.put("push_parameter",JSON.toJSONString(todoData));
                todoResordMap.put("receiver_user",receivedPersonId);
                recordList.add(todoResordMap);
            }


        }
        // 需要产生已办的情况
        log.info("{} flowStatus：{}",methodName,flowStatus);
        log.info("{} lastNodeId：{}",methodName,lastNodeId);
        log.info("{} lastNodeId：{}",methodName,currentNodeType);
        if ("APPROVAL".equals(flowStatus) && !"0".equals(lastNodeId) && !"START".equals(currentNodeType)) {
            log.info("{} 签字意见：{}",methodName,JSON.toJSONString(requestLog));
            String yiabngOriginalId = String.format("%s-%s-%s-%s",DateUtils.formatTime(receiveDateTime, DateUtils.yyyyMMddHHmmss),requestId,workflowId,requestLog.getNodeId());
            log.info("{} 已办唯一标识：",methodName,yiabngOriginalId);
            TodoItemRequest todoItemRequest = TodoItemRequest.builder().systemName(systemName).systemCode(systemCode).itemTitle(requestName).itemStatus("1").authType("1").creatorUserIdType(1).receiverUserIdType("1").build();
            todoItemRequest.setWebUrl(replaceUrl(tenantConfig.get("web_url").toString(),String.valueOf(requestId)));
            todoItemRequest.setMobileUrl(replaceUrl(tenantConfig.get("mobile_url").toString(),String.valueOf(requestId)));
            todoItemRequest.setOriginalId(yiabngOriginalId);
            todoItemRequest.setCreateTime(DateUtils.formatTime(createLocalDateTime, DateUtils.yyyy_MM_dd_HH_mm_ss));
            todoItemRequest.setCreatorUserId(creatorAccount.getLoginName());
            todoItemRequest.setCreatorCnName(creatorLastName);
            todoItemRequest.setPrevCnName(lastFeedBackOperatorUser.getUsername());
            todoItemRequest.setPrevUserId(lastFeedBackOperatorAccount.getLoginName());
            AccountVo userAccount = iOpenPlatformService.findAccount(requestLog.getUserId());
            todoItemRequest.setReceiverUserId(userAccount.getLoginName());
            todoItemRequest.setReceiverCnName(requestLog.getUserName());
            todoItemRequest.setItemType(3);
            todoItemRequest.setInstId(yiabngOriginalId);
            todoItemRequest.setActId(currentNodeId);
            todoItemRequestList.add(todoItemRequest);
            Map<String,Object> todoResordMap = new HashMap<>();
            todoResordMap.put("related_processes",requestId);
            todoResordMap.put("receiver_user",requestLog.getUserId());
            todoResordMap.put("todo_type",3);
            todoResordMap.put("prev_user",lastFeedBackOperator);
            todoResordMap.put("push_parameter",JSON.toJSONString(todoItemRequest));
            todoResordMap.put("original_id",yiabngOriginalId);
            recordList.add(todoResordMap);
        }
        if (!"NORMAL_ARCHIVE".equals(flowStatus) && !"0".equals(lastNodeId) && !"START".equals(currentNodeType)) {
            int i = 0;
            for (String s : defaultNodeIdArr) {
                if (currentNodeId.equals(s)) {
                    i++;
                    break;
                }
                i++;
            }
            String nodeId  = defaultNodeIdArr[i];
            log.info("{} 待办对应节点：{}",methodName,nodeId);
            //操作时间-请求ID-流程ID-下一节点ID
            String todoOriginalId = String.format("%s-%s-%s-%s",DateUtils.formatTime(operateDatetime, DateUtils.yyyyMMddHHmmss),requestId,workflowId,nodeId);
            TodoItemRequest todoItemRequest = TodoItemRequest.builder().systemName(systemName).systemCode(systemCode).itemTitle(requestName).itemStatus("1").authType("1").creatorUserIdType(1).receiverUserIdType("1").build();
            todoItemRequest.setWebUrl(replaceUrl(tenantConfig.get("web_url").toString(),String.valueOf(requestId)));
            todoItemRequest.setMobileUrl(replaceUrl(tenantConfig.get("mobile_url").toString(),String.valueOf(requestId)));
            todoItemRequest.setOriginalId(todoOriginalId);
            todoItemRequest.setCreateTime(DateUtils.formatTime(createLocalDateTime, DateUtils.yyyy_MM_dd_HH_mm_ss));
            todoItemRequest.setCreatorUserId(creatorAccount.getLoginName());
            todoItemRequest.setCreatorCnName(creatorLastName);
            todoItemRequest.setPrevCnName(lastFeedBackOperatorUser.getUsername());
            todoItemRequest.setPrevUserId(lastFeedBackOperatorAccount.getLoginName());
            String[] receivedPersonIds = requestLog.getReceivedPersonIds().split(",");
            todoItemRequest.setItemType(1);
            todoItemRequest.setInstId(todoOriginalId);
            todoItemRequest.setActId(currentNodeId);
            for (String receivedPersonId : receivedPersonIds) {
                AccountVo receivedPersonAccount = iOpenPlatformService.findAccount(Long.parseLong(receivedPersonId));
                UserInfoResult receivedPrtsonUser = iOpenPlatformService.getUser(Long.parseLong(receivedPersonId));
                todoItemRequest.setReceiverUserId(receivedPersonAccount.getLoginName());
                todoItemRequest.setReceiverCnName(receivedPrtsonUser.getUsername());
                todoItemRequestList.add(todoItemRequest);
                Map<String,Object> todoResordMap = new HashMap<>();
                todoResordMap.put("related_processes",requestId);
                todoResordMap.put("receiver_user",receivedPersonId);
                todoResordMap.put("todo_type",1);
                todoResordMap.put("prev_user",lastFeedBackOperator);
                todoResordMap.put("push_parameter",JSON.toJSONString(todoItemRequest));
                todoResordMap.put("original_id",todoOriginalId);
                recordList.add(todoResordMap);
            }



        }
        iPortalWaitService.pushTodoDataToJtBatch(todoItemRequestList,recordList,null);
    }
    private void handleWorkflow(Object[] args, Map<String, Object> tenantConfig) {
        String methodName = "调用"+simpleName+".handleWorkflow()";
        // 参数2：流程相关参数
        Map<?, ?> paramMap = (Map<?, ?>) args[1];
        long requestId = Long.parseLong(paramMap.get("requestId").toString());
        Long userIdOperate = Long.parseLong(paramMap.get(USER_ID).toString());
        Map<?, ?> requestBaseInfo = (Map<?, ?>) paramMap.get("requestBaseInfo");
        String requestName = requestBaseInfo.get("requestName").toString();
        Map<?, ?> currentNode = (Map<?, ?>) paramMap.get("currentNode");
        List<TodoItemRequest> todoItemRequestList = new ArrayList<>();
        // 当前节点id
        String currentNodeId = currentNode.get("id").toString();
        // 当前节点类型  START=发起  APPROVE=批准
        String currentNodeType = currentNode.get("nodeType").toString();
        // 节点id
        String lastNodeId = requestBaseInfo.get("lastNodeId").toString();

        String lastOperator = requestBaseInfo.get("lastOperator").toString();
        // 开始节点
        String startNodeId = requestBaseInfo.get("startNodeId").toString();
        // 创建时间
        String createDateTime = requestBaseInfo.get("createDateTime").toString().replace("T"," ");
        LocalDateTime createLocalDateTime = DateUtils.parseString(createDateTime, DateUtils.yyyy_MM_dd_HH_mm_ss);
        // 最后操作时间
        String lastOperateDateTime = requestBaseInfo.get("lastOperateDateTime").toString().replace("T"," ");
        LocalDateTime lastOperateLocalDateTime = DateUtils.parseString(lastOperateDateTime, DateUtils.yyyy_MM_dd_HH_mm_ss);
        // 创建人ID
        String creator = requestBaseInfo.get("creator").toString();
        // 创建人名
        String creatorLastName = requestBaseInfo.get("creatorLastName").toString();
        // 流程ID
        String workflowId = requestBaseInfo.get("workflowId").toString();
        String systemName = tenantConfig.get("system_name").toString();
        String systemCode = tenantConfig.get("system_code").toString();
        List<Map<String,Object>> recordList = new ArrayList<>();

        try {

            AccountVo creatorAccount = iOpenPlatformService.findAccount(Long.parseLong(creator));
            if (currentNodeType.equals("START")) { // 节点为发起，需要推送我的创建、待办、已办三种状态
                //最后操作时间-请求ID-流程ID-当前节点ID
                String originalId =  String.format("%s-%s-%s-%s",DateUtils.formatTime(lastOperateLocalDateTime, DateUtils.yyyyMMddHHmmss),requestId,workflowId,currentNodeId);
                // 我的创建数据
                TodoItemRequest myCreation = TodoItemRequest.builder().itemTitle(requestName).itemStatus("1").systemCode(systemCode).systemName(systemName).authType("1").receiverUserIdType("1").creatorUserIdType(1).webUrl(replaceUrl(tenantConfig.get("web_url").toString(),String.valueOf(requestId))).mobileUrl(replaceUrl(tenantConfig.get("mobile_url").toString(),String.valueOf(requestId)))
                        .originalId("my-"+originalId)
                        .createTime(DateUtils.formatTime(createLocalDateTime, DateUtils.yyyy_MM_dd_HH_mm_ss))
                        .creatorUserId(creatorAccount.getLoginName())
                        .creatorCnName(creatorLastName)
                        .prevUserId(creatorAccount.getLoginName())
                        .prevCnName(creatorLastName)
                        .receiverUserId(creatorAccount.getLoginName())
                        .receiverCnName(creatorLastName)
                        .itemType(5)
                        .instId(originalId)
                        .actId(currentNodeId)
                        .build();
                 todoItemRequestList.add(myCreation);
                 Map<String,Object> myResordMap = new HashMap<>();
                 myResordMap.put("related_processes",requestId);
                 myResordMap.put("receiver_user",creator);
                 myResordMap.put("todo_type",5);
                 myResordMap.put("prev_user",creator);
                 myResordMap.put("push_parameter",JSON.toJSONString(myCreation));
                 myResordMap.put("original_id","my"+originalId);
                 recordList.add(myResordMap);
                 // 已办数据
                TodoItemRequest alreadyDone  = TodoItemRequest.builder().itemTitle(requestName).itemStatus("1").systemCode(systemCode).systemName(systemName).authType("1").creatorUserIdType(1).receiverUserIdType("1").webUrl(replaceUrl(tenantConfig.get("web_url").toString(),String.valueOf(requestId))).mobileUrl(replaceUrl(tenantConfig.get("mobile_url").toString(),String.valueOf(requestId)))
                        .originalId(originalId)
                         .createTime(DateUtils.formatTime(createLocalDateTime, DateUtils.yyyy_MM_dd_HH_mm_ss))
                         .creatorUserId(creatorAccount.getLoginName())
                         .creatorCnName(creatorLastName)
                         .prevUserId(creatorAccount.getLoginName())
                         .prevCnName(creatorLastName)
                         .receiverUserId(creatorAccount.getLoginName())
                         .receiverCnName(creatorLastName)
                         .itemType(3)
                         .instId(originalId)
                         .actId(currentNodeId)
                         .build();
                todoItemRequestList.add(alreadyDone);
                Map<String,Object> alreadyResordMap = new HashMap<>();
                alreadyResordMap.put("related_processes",requestId);
                alreadyResordMap.put("receiver_user",creator);
                alreadyResordMap.put("todo_type",3);
                alreadyResordMap.put("prev_user",creator);
                alreadyResordMap.put("original_id",originalId);
                alreadyResordMap.put("push_parameter",JSON.toJSONString(alreadyDone));
                recordList.add(alreadyResordMap);
            }

            // 流程信息
            WorkFlowRequestInfo workflowRequest = iOpenPlatformService.getWorkflowRequest(userIdOperate, String.valueOf(requestId));
            log.info("{} 查询流程信息：workflowBaseInfo：{}",methodName,JSON.toJSONString(workflowRequest.getWorkflowBaseInfo()));
            log.info("{} 查询流程信息：workflowRequestLogs：{}",methodName,JSON.toJSONString(workflowRequest.getWorkflowRequestLogs()));

            // 审批类型 --已办数据
            if (currentNodeType.equals("APPROVE")) {
                WorkFlowRequestLog workFlowRequestLog = workflowRequest.getWorkflowRequestLogs().get(workflowRequest.getWorkflowRequestLogs().size() - 1);
                LocalDateTime localDateTime = DateUtils.parseString(workFlowRequestLog.getReceiveDateTime(), DateUtils.yyyy_MM_dd_HH_mm_ss);
                String originalId =  String.format("%s-%s-%s-%s",DateUtils.formatTime(localDateTime, DateUtils.yyyyMMddHHmmss),requestId,workflowId,lastNodeId);
                log.info("{} 已办唯一标识：{}",methodName,originalId);
                AccountVo nextOperatorsAccount = iOpenPlatformService.findAccount(Long.parseLong(lastOperator));
                log.info("{} 操作人：{}",methodName,nextOperatorsAccount.getLoginName());
                UserInfoResult nextOperatorsUser = iOpenPlatformService.getUser(Long.parseLong(lastOperator));
                log.info("{} 操作人2：{}",methodName,nextOperatorsUser.getUsername());
            }

            // 待办数据



            JSONObject nodeOperator = iOpenPlatformService.getNodeOperator(userIdOperate, requestId, false, Long.parseLong(currentNodeId));
            log.info("{} 获取当前节点信息：{}",methodName,nodeOperator.toString());
            JSONObject nodeInfoJson = nodeOperator.getJSONObject(currentNodeId);
            JSONArray operatorUsers = nodeInfoJson.getJSONArray("operatorUsers");

            AccountVo prevUserIdAccount = iOpenPlatformService.findAccount(Long.parseLong(operatorUsers.getJSONObject(0).getString("userId")));
            String prevUserId =prevUserIdAccount.getLoginName();
            String prevCnName = operatorUsers.getJSONObject(0).getString("userName");

            // 获取下一节点操作信息
            List<WorkFlowRequestNextOperator> requestNextOperator = iOpenPlatformService.getRequestNextOperator(userIdOperate, String.valueOf(requestId));
            log.info("{} 获取下一节点操作信息：{}",methodName,JSON.toJSONString(requestNextOperator));





            for (WorkFlowRequestNextOperator workFlowRequestNextOperator : requestNextOperator) {
                List<WorkFlowNextOperator> nextOperators = workFlowRequestNextOperator.getNextOperators();
                for (WorkFlowNextOperator nextOperator : nextOperators) {


                    AccountVo nextOperatorsAccount = iOpenPlatformService.findAccount(nextOperator.getUserId());
                    UserInfoResult nextOperatorsUser = iOpenPlatformService.getUser(nextOperator.getUserId());
                    Integer itemType = 1;

                    //非会签 itemType =1
                    if (nextOperator.getSignOrder().equals("NO_SIGN")) {
                        itemType = 1;
                    }
                    // 会签 itemType =1
                    if (nextOperator.getSignOrder().equals("SIGN")) {
                        itemType = 1;
                    }
                    //依次逐个处理 itemType =1
                    if (nextOperator.getSignOrder().equals("IN_TURN")) {
                        itemType = 1;
                    }
                    // 抄送需提交 itemType = 2
                    if (nextOperator.getSignOrder().equals("CC_SUBMIT")) {
                        itemType = 2;
                    }
                    // 抄送不需提交 itemType = 2
                    if (nextOperator.getSignOrder().equals("CC_NO_SUBMIT")) {
                        itemType = 2;
                    }
                    // 或签
                    if (nextOperator.getSignOrder().equals("OR_SIGN")) {

                    }
                    WorkFlowRequestLog workFlowRequestLog = workflowRequest.getWorkflowRequestLogs().get(workflowRequest.getWorkflowRequestLogs().size() - 1);

                    LocalDateTime localDateTime = DateUtils.parseString(workFlowRequestLog.getOperateDatetime(), DateUtils.yyyy_MM_dd_HH_mm_ss);
                    //操作时间-请求ID-流程ID-当前节点ID
                    String originalId =  String.format("%s-%s-%s-%s",DateUtils.formatTime(localDateTime, DateUtils.yyyyMMddHHmmss),requestId,workflowId,currentNodeId);
                    TodoItemRequest todoItemRequest = TodoItemRequest.builder()
                            .itemTitle(requestName)
                            .itemStatus("1")
                            .originalId(originalId)
                            .systemCode(systemCode)
                            .systemName(systemName)
                            .authType("1")
                            .createTime(DateUtils.formatTime(createLocalDateTime, DateUtils.yyyy_MM_dd_HH_mm_ss))
                            .creatorUserId(creatorAccount.getLoginName())
                            .creatorCnName(creatorLastName)
                            .creatorUserIdType(1)
                            .prevUserId(prevUserId)
                            .prevCnName(prevCnName)
                            .receiverUserId(nextOperatorsAccount.getLoginName())
                            .receiverCnName(nextOperatorsUser.getUsername())
                            .receiverUserIdType("1")
                            .webUrl(replaceUrl(tenantConfig.get("web_url").toString(),String.valueOf(requestId)))
                            .mobileUrl(replaceUrl(tenantConfig.get("mobile_url").toString(),String.valueOf(requestId)))
                            .itemType(itemType)
                            .instId(originalId)
                            .actId(currentNodeId)
                            .build();
                    todoItemRequestList.add(todoItemRequest);
                    Map<String,Object> resordMap = new HashMap<>();
                    resordMap.put("related_processes",requestId);
                    resordMap.put("receiver_user",nextOperator.getUserId());
                    resordMap.put("todo_type",itemType);
                    resordMap.put("prev_user",creator);
                    resordMap.put("push_parameter",JSON.toJSONString(todoItemRequest));
                    resordMap.put("original_id",originalId);
                    recordList.add(resordMap);
                }
            }
            for (TodoItemRequest todoItemRequest : todoItemRequestList) {
                log.info("{} 推送数据：{}",methodName,JSON.toJSONString(todoItemRequest));
            }
            iPortalWaitService.pushTodoDataToJtBatch(todoItemRequestList,recordList,null);
        } catch (Exception e) {
            log.error("{} 推送待办异常", e);
        }
    }

    private String replaceUrl(String url,String requestId){
        if (url.indexOf("${requestId}$") > -1) {
            url = url.replace("${requestId}$",requestId);
        }
        return url;
    }
}
