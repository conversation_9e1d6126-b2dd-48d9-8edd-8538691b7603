package com.weaver.seconddev.zyhlw.manager.ete;

import cn.hutool.core.map.MapUtil;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.seconddev.zyhlw.domain.supervise.OperatorInfo;
import com.weaver.seconddev.zyhlw.domain.supervise.OvertimeConfig;
import com.weaver.seconddev.zyhlw.manager.supervise.SuperviseMessageManager;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import com.weaver.seconddev.zyhlw.util.enums.DsLogicGroupIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.weaver.seconddev.zyhlw.util.ConvertDataUtils.convertListMapKeyToUpperCase;
import static com.weaver.seconddev.zyhlw.util.ConvertDataUtils.convertMapKeyToUpperCase;

/**
 * 端对端督办任务公共管理器
 *
 * <AUTHOR>
 * @date 2025/6/23 14:00
 */
@Slf4j
@Component
public class EndToEndOvertimeManager {

    private static final String DATE_TIME_FORMATTER = "yyyy-MM-dd HH:mm:ss";

    @Resource
    private IDataBaseService dataBaseService;

    @Resource
    private IDataSqlService dataSqlService;

    @Resource
    private SuperviseMessageManager superviseMessageManager;

    @Resource
    private CmicProperties cmicProperties;

    /**
     * 执行端对端督办任务
     *
     * @param params 参数
     * @param taskType 任务类型（协办/主办）
     * @param tableConfigKey 表配置键
     * @param className 类名（用于日志）
     * @return 执行结果
     */
    public WeaResult<Map<String, Object>> executeOvertimeTask(Map<String, Object> params, String taskType, String tableConfigKey, String className) {
        Map<String, Object> resultMap = new HashMap<>();
        log.info("{}: 开始执行端对端督办({})任务超时提醒收集任务...", className, taskType);
        
        Long sendUserId = MapUtil.getLong(params, "userId");
        if (sendUserId == null) {
            return WeaResult.fail(className + ": 用户id不能为空");
        }

        String tableName = dataBaseService.getBaseDataValue(tableConfigKey, "端对端服务责任管理");
        String selectNodeId = "";   //流程要提醒的环节ids
        String afterDayNum = "10";   //提前多少天提醒
        String warnMaxNum = "1";    //最大提醒次次数
        
        OvertimeConfig overtimeConfig = superviseMessageManager.getOvertimeConfigByTableName(tableName);
        if (overtimeConfig != null) {
            selectNodeId = overtimeConfig.getSelectNodeId();
            afterDayNum = overtimeConfig.getAfterDayNum();
            warnMaxNum = overtimeConfig.getWarnMaxNum();
        }

        // 获取流程信息sql
        String findWorkflowInfoSql = getWorkflowInfoSql(tableName, selectNodeId);
        // 获取流程信息
        List<Map<String, Object>> dataResult = convertListMapKeyToUpperCase(
                dataSqlService.executeCommonSqlAll(findWorkflowInfoSql, SourceType.LOGIC,
                        DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId(), Collections.singletonList(afterDayNum))
        );
        
        if (dataResult.isEmpty()) {
            return WeaResult.fail(className + ":未查询到需要督办的数据");
        }

        for (Map<String, Object> data : dataResult) {
            processOvertimeData(data, tableName, warnMaxNum, taskType, className, sendUserId);
        }

        resultMap.put("success", true);
        return WeaResult.success(resultMap);
    }

    /**
     * 处理超时数据
     *
     * @param data 数据
     * @param tableName 表名
     * @param warnMaxNum 最大提醒次数
     * @param taskType 任务类型
     * @param className 类名
     * @param sendUserId 发送用户ID
     */
    private void processOvertimeData(Map<String, Object> data, String tableName, String warnMaxNum, 
                                   String taskType, String className, Long sendUserId) {
        String requestId = MapUtil.getStr(data, "REQUESTID"); // 请求id
        String currentNodeId = MapUtil.getStr(data, "CURRENTNODEID"); // 当前节点id
        String requestName = MapUtil.getStr(data, "REQUESTNAME"); // 请求名称
        String lastOperatorId = MapUtil.getStr(data, "LASTOPERATOR"); // 上一节点操作人
        String creator = MapUtil.getStr(data, "CREATER"); // 工单创建人
        String createDateTime = MapUtil.getStr(data, "CREATEDATETIME"); // 工单创建日期
        String workflowId = MapUtil.getStr(data, "WORKFLOWID"); // 工作流id
        
        log.info("端对端督办({})超时工单-【节点: {} 请求id: {}】", taskType, currentNodeId, requestId);
        log.info("工单信息[请求名称: {} 工单创建时间: {}最后操作人id: {}创建人id: {}]", requestName, createDateTime, lastOperatorId, creator);

        // 获取当前处理人信息
        OperatorInfo operatorInfo = superviseMessageManager.getCurrentOperatorInfo(currentNodeId, requestId);
        String currentNodeOperatorId = operatorInfo.getUserId();
        String currentNodeReceiveDateTime = operatorInfo.getReceiveDateTime();
        log.info("当前节点处理人信息[当前节点处理人id: {} 当前节点接收时间: {}]", currentNodeOperatorId, currentNodeReceiveDateTime);

        // 获取人员信息
        Map<String, Object> currentOperatorMap = dataBaseService.getEmployeeInfo(currentNodeOperatorId);
        Map<String, Object> creatorMap = dataBaseService.getEmployeeInfo(creator);
        Map<String, Object> lastOperatorMap = dataBaseService.getEmployeeInfo(lastOperatorId);
        String creatorLoginValue = MapUtil.getStr(creatorMap, "LOGIN_VALUE"); // 创建人portal账号
        String lastOperatorLoginValue = MapUtil.getStr(lastOperatorMap, "LOGIN_VALUE"); // 上一节点处理人账号
        String receiver = MapUtil.getStr(currentOperatorMap, "LOGIN_VALUE"); // 接收人portal账号
        String receiverMobile = MapUtil.getStr(currentOperatorMap, "MOBILE"); // 接收人手机号

        // 获取督办任务名称和要求完成日期
        String getSuperviseInfoSql = "select yao_qwcrq,trunc(to_date(nvl(shi_jwcrq,yao_qwcrq),'yyyy-mm-dd hh24-mi-ss')-sysdate) as days from " + tableName + " where id = ?";
        Map<String, Object> supervisorMap = convertMapKeyToUpperCase(
                dataSqlService.executeCommonSqlOne(getSuperviseInfoSql, SourceType.LOGIC,
                        DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId(), Collections.singletonList(requestId))
        );
        String deadline = MapUtil.getStr(supervisorMap, "YAO_QWCRQ");
        
        String getTimesSql = "select count(1) as times from UF_SMS_MESSAGE where ORDERID = ? and CURRENTNODEID=? and RECEIVER=?";
        Map<String, Object> timesDataMap = convertMapKeyToUpperCase(
                dataSqlService.executeCommonSqlOne(getTimesSql, SourceType.LOGIC,
                        DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId(), Arrays.asList(requestId, currentNodeId, receiver))
        );
        Integer times = MapUtil.getInt(timesDataMap, "TIMES", 0);
        
        // 提醒次数判断
        if (times <= Integer.parseInt(warnMaxNum)) {
            // 消息模板：督办任务\t\t2020/05/09\n【XXX督办】要求要求于XR年X月X日完成，请您尽快处理
            String smsContent = String.format("【%s】要求于%s完成，截至目前还剩%s天，请您及时进行办理", requestName, deadline, MapUtil.getStr(supervisorMap, "DAYS"));
            log.info("消息内容: [{}]", smsContent);
            
            // 生成消息集合
            if (StringUtils.isNotEmpty(receiver)) {
                String messageType = String.format("服务责任任务到期提醒(%s)", taskType);
                List<Map<String, Object>> saveDataList = superviseMessageManager.buildMessage(
                        requestId, requestName, workflowId, createDateTime, currentNodeReceiveDateTime,
                        currentNodeId, messageType, creatorLoginValue, lastOperatorLoginValue,
                        receiver, receiverMobile, smsContent);
                //保存短信
                superviseMessageManager.saveMessage(saveDataList, requestId, receiver, currentNodeId, requestName, "0", sendUserId);
            } else {
                log.info("{}:接收人为空，不进行保存操作！", className);
            }
        }
    }

    /**
     * 获取流程信息sql
     *
     * @param tableName    任务下达流程表
     * @param selectNodeId 流程要提醒的环节ids
     * @return java.lang.String
     */
    private String getWorkflowInfoSql(String tableName, String selectNodeId) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT workflow_requestbase.requestid AS requestid, workflow_requestbase.requestname AS requestname, workflow_requestbase.lastoperator AS lastoperator, workflow_requestbase.nodeid AS currentnodeid, workflow_requestbase.creater AS creater\n" + "\t, workflow_requestbase.createdatetime AS createdatetime\n" + "\t, workflow_requestbase.workflowid AS workflowid\n" + "FROM (\n" + "\tSELECT wfc_requestbase.*, wfc_currentnode.nodeid\n" + "\tFROM e10_core_business.wfc_requestbase\n" + "\t\tLEFT JOIN e10_core_business.wfc_currentnode ON wfc_requestbase.requestid = wfc_currentnode.requestid\n" + "\tAND wfc_currentnode.delete_type = 0\n" + "\tAND wfc_currentnode.tenant_key = '")
                .append(cmicProperties.getHostTenantKey()).append("' \n")
                .append("\tWHERE wfc_requestbase.delete_type = 0\n")
                .append("\t\tAND wfc_requestbase.tenant_key = '")
                .append(cmicProperties.getHostTenantKey())
                .append("'\n").append(") workflow_requestbase\n")
                .append("WHERE workflow_requestbase.requestid IN (\n")
                .append("\t\tSELECT id \n")
                .append("\t\tFROM ").append(tableName).append(" \n")
                .append("\t\tWHERE trunc(to_date(nvl(shi_jwcrq, yao_qwcrq), 'yyyy-mm-dd hh24-mi-ss') - SYSDATE) <= ?\n")
                .append("\t)\n").append("\tAND workflow_requestbase.delete_type = 0\n")
                .append("\tAND workflow_requestbase.tenant_key = '")
                .append(cmicProperties.getHostTenantKey()).append("'");
        if (StringUtils.isNotBlank(selectNodeId)) {
            sql.append("\n\tAND workflow_requestbase.nodeid IN (").append(selectNodeId).append(")");
        }
        return sql.toString();
    }
}
