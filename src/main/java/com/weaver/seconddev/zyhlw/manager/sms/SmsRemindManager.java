package com.weaver.seconddev.zyhlw.manager.sms;

import cn.hutool.core.util.IdUtil;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.ebuilder.EBuilderUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Component
public class SmsRemindManager {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Resource
    private IDataBaseService dataBaseService;

    @Resource
    private CmicProperties cmicProperties;

    @Resource
    private IOpenPlatformService openPlatformService;

    /**
     * 新短信提醒方法，所有提醒内容都在CommonWarn.jsp中呈现 //TODO CommonWarn.jsp H5页面需要前端配合开发
     * <p>
     * 原E9方法 com.zyhlw.util.sms.SmsRemind.sendCommonSMS
     *
     * @param requestId     // 工单id
     * @param lastOperator  // 发送人
     * @param receiver      // 接收人
     * @param receiverPhone // 接收人手机号
     * @param orderTitle    // 工单标题
     * @param smsContent    // 短信内容
     * @param smsType       // 场景类型
     */
    public void sendCommonSMS(String requestId, String lastOperator, String receiver, String receiverPhone,
                              String orderTitle, String smsContent, String smsType, Long sendUserId) {
        String smsTableObjId = dataBaseService.getBaseValue("uf_datacenter_sms", "objId");
        LocalDateTime now = LocalDateTime.now();
        String smsId = IdUtil.fastSimpleUUID();
        // 短信链接 TODO 需要前端配合开发
        String linkUrl = "/spa/mobile/order/CommonWarn.jsp?smsid=" + smsId;

        List<Map<String, Object>> saveDataList = new ArrayList<>();
        Map<String, Object> dataMap = new HashMap<>();
        Map<String, Object> dataItem = new HashMap<>();

        // 表单基本信息
        dataItem.put("formmodeid", smsTableObjId);// 表单id
        dataItem.put("creator", lastOperator);// 发送人
        dataItem.put("create_time", now.format(DATE_TIME_FORMATTER));// 创建时间
        dataItem.put("ordername", orderTitle); // 工单标题
        dataItem.put("smsconent", smsContent); // 短信内容
        dataItem.put("receiveruserid", receiver); // 接收人
        dataItem.put("issend", 0); // 短小发送
        dataItem.put("createtime", now.format(DATE_TIME_FORMATTER)); // 创建时间
        dataItem.put("linkurl", linkUrl); // 跳转地址
        dataItem.put("orderid", requestId); // 工单id
        dataItem.put("creatoruserid", lastOperator); // 发送人
        dataItem.put("datatype", smsType); // 场景类型 0【通用场景】、1【数据中心】、2【差旅风险提醒】、3【用车取消提醒】、4【用车结束提醒】、5【用车信息提醒】、6【用车确认操作提醒】
        dataItem.put("synstatus", 0);// 同步状态
        dataItem.put("receivermobile", receiverPhone);// 接收人手机号
        dataItem.put("smsId", smsId);// 消息id

        dataMap.put("mainTable", dataItem);
        saveDataList.add(dataMap);

        // 保存短信数据
        EBuilderUtil.saveFormDataV2(
                cmicProperties.getOpenPlatformUrl(),
                sendUserId,
                smsTableObjId,
                saveDataList,
                openPlatformService.getAccessToken());
    }
}
