package com.weaver.seconddev.zyhlw.manager.supervise;

import cn.hutool.core.map.MapUtil;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.seconddev.zyhlw.domain.supervise.OperatorInfo;
import com.weaver.seconddev.zyhlw.domain.supervise.OvertimeConfig;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.ConvertDataUtils;
import com.weaver.seconddev.zyhlw.util.ebuilder.EBuilderUtil;
import com.weaver.seconddev.zyhlw.util.enums.DsLogicGroupIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 督办管理超时提醒配置管理
 * <p>
 * 原e9代码 com.cmic.cronjob.duban.OvertimeMessageUtils
 *
 * <AUTHOR>
 * @date 2025/6/9 10:00
 */
@Component
@Slf4j
public class SuperviseMessageManager {

    @Resource
    private IDataSqlService dataSqlService;

    @Resource
    private IDataBaseService dataBaseService;

    @Resource
    private CmicProperties cmicProperties;

    @Resource
    private IOpenPlatformService openPlatformService;

    private static final String WF_NAME_UPPERCASE = "WF_NAME";
    private static final String WORKFLOW_ID_UPPERCASE = "WORKFLOWID";
    private static final String WF_TABLE_NAME_UPPERCASE = "WF_TABLENAME";
    private static final String SEND_TYPE_UPPERCASE = "SENDTYPE";
    private static final String WARN_MAX_NUM_UPPERCASE = "WARNMAXNUM";
    private static final String AFTER_DAY_NUM_UPPERCASE = "AFTERDAYNUM";
    private static final String SELECT_NODE_ID_UPPERCASE = "SELECTNODEID";

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 获取超时提醒配置
     *
     * @return 超时提醒配置
     * <AUTHOR>
     * @date 2025/6/9 10:00
     */
    private List<OvertimeConfig> getOvertimeConfig() {
        List<OvertimeConfig> overtimeConfigs = new ArrayList<>();
        String sql = "select * from uf_workflow_sms";

        List<Map<String, Object>> dataResult = ConvertDataUtils.convertListMapKeyToUpperCase(
                dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC));
        dataResult.forEach(item -> overtimeConfigs.add(convertOvertimeConfig(item)));
        return overtimeConfigs;
    }

    private OvertimeConfig convertOvertimeConfig(Map<String, Object> item) {
        return OvertimeConfig.builder()
                .wfName(MapUtil.getStr(item, WF_NAME_UPPERCASE))
                .workflowId(MapUtil.getStr(item, WORKFLOW_ID_UPPERCASE))
                .wfTableName(MapUtil.getStr(item, WF_TABLE_NAME_UPPERCASE))
                .sendType(MapUtil.getStr(item, SEND_TYPE_UPPERCASE))
                .warnMaxNum(MapUtil.getStr(item, WARN_MAX_NUM_UPPERCASE))
                .afterDayNum(MapUtil.getStr(item, AFTER_DAY_NUM_UPPERCASE))
                .afterDayNum2(MapUtil.getStr(item, AFTER_DAY_NUM_UPPERCASE))
                .selectNodeId(MapUtil.getStr(item, SELECT_NODE_ID_UPPERCASE))
                .build();
    }

    /**
     * 通过表名称获取超时提醒配置信息
     *
     * @param wfTableName 表名称
     */
    public OvertimeConfig getOvertimeConfigByTableName(String wfTableName) {
        String sql = "select * from uf_workflow_sms where trim(lower(wf_tablename))=?";
        Map<String, Object> dataResult = ConvertDataUtils.convertMapKeyToUpperCase(
                dataSqlService.executeCommonSqlOne(sql, SourceType.LOGIC, DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId(),
                        Collections.singletonList(wfTableName.toLowerCase().trim())));
        if (dataResult.isEmpty()) {
            return null;
        }
        return convertOvertimeConfig(dataResult);
    }

    /**
     * 通过当前节点ID和请求ID获取该工单接收人数组
     *
     * @param currentNodeId 当前环节ID
     * @param requestId     请求ID
     */
    @SuppressWarnings("SpellCheckingInspection")
    public OperatorInfo getCurrentOperatorInfo(String currentNodeId, String requestId) {
        String sql = "SELECT\n" +
                "  *\n" +
                "FROM\n" +
                "  (\n" +
                "    SELECT\n" +
                "      *\n" +
                "    FROM\n" +
                "      e10_core_business.wfc_operate workflow_currentoperator\n" +
                "    WHERE\n" +
                "      workflow_currentoperator.requestid = ?\n" +
                "      AND workflow_currentoperator.nodeid IN ('  " + currentNodeId + " ')\n" +
                "      AND workflow_currentoperator.showorder != -1\n" +
                "      AND workflow_currentoperator.delete_type = 0\n" +
                "      AND workflow_currentoperator.tenant_key = 'tld0nhuikk'\n" +
                "    ORDER BY\n" +
                "      workflow_currentoperator.RECEIVEDATETIME DESC\n" +
                "  )\n" +
                "WHERE\n" +
                "  ROWNUM = 1";
        List<Map<String, Object>> dataResult = ConvertDataUtils.convertListMapKeyToUpperCase(
                dataSqlService.executeCommonSqlAll(sql, SourceType.LOGIC, DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId(),
                        Collections.singletonList(requestId)));
        if (dataResult.isEmpty()) {
            return null;
        }
        return convertOperatorInfo(dataResult.get(0));
    }

    @SuppressWarnings("SpellCheckingInspection")
    private OperatorInfo convertOperatorInfo(Map<String, Object> dataMap) {
        return OperatorInfo.builder()
                .userId(MapUtil.getStr(dataMap, "USERID"))
                .receiveDateTime(MapUtil.getStr(dataMap, "RECEIVEDATETIME"))
                .build();
    }

    /**
     * 构建信息历史记录，默认发送类型 5G消息
     *
     * @param requestId              请求ID
     * @param requestName            请求名称
     * @param workflowId             工作流ID
     * @param createDateTimeStr      创建时间
     * @param receiveDateTimeStr     接收时间
     * @param currentNodeId          当前节点ID
     * @param title                  标题
     * @param creatorLoginValue      创建人登录值
     * @param lastOperatorLoginValue 上一节点操作人登录值
     * @param receiver               接收人
     * @param receiverMobile         接收人手机号
     * @param smsContent             短信内容
     * @return 短信数据列表
     */
    public List<Map<String, Object>> buildMessage(String requestId, String requestName, String workflowId,
                                                  String createDateTimeStr, String receiveDateTimeStr, String currentNodeId,
                                                  String title, String creatorLoginValue, String lastOperatorLoginValue,
                                                  String receiver, String receiverMobile, String smsContent) {
        return buildMessage(requestId, requestName, workflowId, createDateTimeStr, receiveDateTimeStr, currentNodeId,
                title, creatorLoginValue, lastOperatorLoginValue, receiver, receiverMobile, smsContent, "0");
    }

    /**
     * 构建信息历史记录，默认发送类型 5G消息
     *
     * @param requestId              请求ID
     * @param requestName            请求名称
     * @param workflowId             工作流ID
     * @param createDateTimeStr      创建时间
     * @param receiveDateTimeStr     接收时间
     * @param currentNodeId          当前节点ID
     * @param title                  标题
     * @param creatorLoginValue      创建人登录值
     * @param lastOperatorLoginValue 上一节点操作人登录值
     * @param receiver               接收人
     * @param receiverMobile         接收人手机号
     * @param smsContent             短信内容
     * @param sendType               5G消息下发标识
     * @return 短信数据列表
     */
    public List<Map<String, Object>> buildMessage(String requestId, String requestName, String workflowId,
                                                  String createDateTimeStr, String receiveDateTimeStr, String currentNodeId,
                                                  String title, String creatorLoginValue, String lastOperatorLoginValue,
                                                  String receiver, String receiverMobile, String smsContent, String sendType) {
        List<Map<String, Object>> messageDataList = new ArrayList<>();
        Map<String, Object> dataMap = new HashMap<>();
        Map<String, Object> dataItem = new HashMap<>();

        LocalDateTime now = LocalDateTime.now();
        String smsTableObjId = dataBaseService.getBaseValue("uf_sms_message", "objId");

        dataItem.put("requestid", null); // 建模表自带请求id
        dataItem.put("orderid", requestId); // 工单请求id
        dataItem.put("ordername", requestName); // 请求名称
        dataItem.put("creator", creatorLoginValue); // 创建人
        dataItem.put("lastoperator", lastOperatorLoginValue); // 上一节点处理人
        dataItem.put("receiver", receiver); // 接收人portal账号
        dataItem.put("receivermobile", receiverMobile); // 接收人手机号
        dataItem.put("workflowid", workflowId); // 工作流id
        dataItem.put("createtime", createDateTimeStr); // 创建时间
        dataItem.put("receivetime", receiveDateTimeStr); // 接收时间
        dataItem.put("sendtype", 1); // 消息类型
        dataItem.put("entertime", now.format(DATE_TIME_FORMATTER)); // EC入库时间
        dataItem.put("syssource", "EC系统"); // 来源系统名称
        dataItem.put("is_ec", 1); // 是否EC
        dataItem.put("ischatbotsend", sendType); // 5G消息下发标识
        dataItem.put("issend", 0); // 短小下发标识
        dataItem.put("synstatus", 0); // 同步状态
        dataItem.put("currentnodeid", creatorLoginValue); // 当前节点ID
        dataItem.put("smsconent", smsContent); // 短信内容
        dataItem.put("formmodeid", smsTableObjId); // 表单ID
        dataItem.put("create_time", now.format(DATE_TIME_FORMATTER));// 创建时间
        dataItem.put("mark1", title); //MARK1
        dataItem.put("mark2", ""); //MARK2
        dataItem.put("mark3", null); //MARK3


        dataMap.put("mainTable", dataItem);
        messageDataList.add(dataMap);
        return messageDataList;
    }

    /**
     * 保存超时短信信息到表中
     *
     * @param saveDataList  超时短信数据集合
     * @param orderId       工单请求ID
     * @param receiveUser   接收人登录名
     * @param currentNodeId 当前环节ID
     * @param orderName     请求工单ID
     * @param type          类型（0：只提醒一次，1：每天只提醒一次,2:当前环节只提醒一次）
     * @param sendUserId    发送人id
     */
    public void saveMessage(List<Map<String, Object>> saveDataList, String orderId, String receiveUser,
                            String currentNodeId, String orderName, String type, Long sendUserId) {
        String smsTableObjId = dataBaseService.getBaseValue("uf_sms_message", "objId");
        String recipientLog = ",接收人：";
        String linkIdLog = "，环节ID";
        String querySMS;
        List<String> params = new ArrayList<>();
        if (type.equals("0")) {
            querySMS = "select count(1) as sum from uf_sms_message where ORDERID=? and RECEIVER=?";
            params.add(orderId);
            params.add(receiveUser);
        } else if (type.equals("2")) {
            querySMS = "select count(1) as sum from uf_sms_message where ORDERID=? and RECEIVER=? and CURRENTNODEID=? ";
            params.add(orderId);
            params.add(receiveUser);
            params.add(currentNodeId);
        } else {
            querySMS = "select count(1) as sum from uf_sms_message where ORDERID=? and RECEIVER=? and CURRENTNODEID=? and to_char(to_date(entertime,'yyyy-mm-dd hh24-mi-ss'),'yyyymmdd')=to_char(sysdate,'yyyymmdd')";
            params.add(orderId);
            params.add(receiveUser);
            params.add(currentNodeId);
        }
        Map<String, Object> countMap = ConvertDataUtils.convertMapKeyToUpperCase(
                dataSqlService.executeCommonSqlOne(querySMS, SourceType.LOGIC,
                        DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId(), params));
        Integer sum = MapUtil.getInt(countMap, "SUM");
        if (sum == 0) {
            // 保存短信数据
            EBuilderUtil.saveFormDataV2(
                    cmicProperties.getOpenPlatformUrl(),
                    sendUserId,
                    smsTableObjId,
                    saveDataList,
                    openPlatformService.getAccessToken());
            log.info("{}{}{}{}{}，添加成功", orderName, recipientLog, receiveUser, linkIdLog, currentNodeId);
        } else {
            log.info("{}{}{}{}{}，已存在，不进行插入", orderName, recipientLog, receiveUser, linkIdLog, currentNodeId);
        }
    }
}
