package com.weaver.seconddev.zyhlw.service;

import java.util.List;
import java.util.Map;

import com.weaver.seconddev.zyhlw.domain.appeal.AppealHelpDTO;
import com.weaver.seconddev.zyhlw.domain.appeal.AppealListDTO;
import com.weaver.seconddev.zyhlw.domain.appeal.AppealListRespVO;
import com.weaver.seconddev.zyhlw.domain.appeal.AppealTypeListRespVO;

/**
 * 诉求中心服务接口
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
public interface IAppealCenterService {

    /**
     * 获取诉求分类
     * 
     * @param id 分类ID
     * @return 诉求分类列表
     *
     * <AUTHOR>
     * @date 2025-05-22 11:01:00
     */
    List<AppealTypeListRespVO> getTypeList(String id);

    /**
     * 获取诉求列表
     * 
     * @param appealListDTO 诉求列表请求参数
     * @return 诉求列表
     * 
     * <AUTHOR>
     * @throws Exception
     * @date 2025-05-22 11:01:00
     */
    AppealListRespVO getAppealList(AppealListDTO appealListDTO) throws Exception;

    /**
     * 获取诉求详情
     * 
     * @param no 编码
     * @return 诉求详情
     * 
     * <AUTHOR>
     * @date 2025-05-22 11:01:00
     */
    Map<String, Object> getAppealDetail(String no);

    /**
     * 插入没帮助问题记录
     * 
     * @param appealHelpDTO 没帮助问题记录请求参数
     * @return 是否成功
     * 
     * <AUTHOR>
     * @date 2025-05-22 11:01:00
     */
    Boolean addAppealNoHelp(AppealHelpDTO appealHelpDTO);

    /**
     * 有帮助数+1
     * 
     * @param appealHelpDTO 有帮助问题记录请求参数
     * @return 是否成功
     * 
     * <AUTHOR>
     * @date 2025-05-22 11:01:00
     */
    Boolean addAppealHelp(AppealHelpDTO appealHelpDTO);

    /**
     * 获取热门诉求列表
     * 
     * @param currentPage 当前页
     * @param pageSize 每页条数
     * @return 热门诉求列表
     * @throws Exception 
     */
    AppealListRespVO getHotAppealList(Integer currentPage, Integer pageSize) throws Exception;

    /**
     * 设置热门
     * 
     * @param ids 诉求ID列表，多个ID用逗号分隔
     * @param hot 是否热门 0否 1是
     * @return 是否成功
     * 
     * <AUTHOR>
     * @date 2025-05-22 11:01:00
     */
    Boolean setHot(String ids, String hot);
}
