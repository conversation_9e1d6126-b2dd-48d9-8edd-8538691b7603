package com.weaver.seconddev.zyhlw.service;

import net.sf.json.JSONObject;

import java.util.List;
import java.util.Map;

public interface IChuChaiService {

    /**
     * 查询节假日信息
     * @param tenantKey 租户key
     * @param employeeId 用户employeeId
     * @return
     */
    List<Map<String,Object>> holiday(String tenantKey,Long employeeId);

    /**
     *推送行程到商旅100
     * @param params
     * @return
     */
    Map<String,Object> putDataToTrade100(JSONObject params);

    /**
     * 酒店推送
     * @param params
     * @return
     */
    Map<String,Object> hotelSysn(JSONObject params);
}
