package com.weaver.seconddev.zyhlw.service;

import com.alibaba.fastjson.JSONArray;

/**
 * <AUTHOR>
 */
public interface ICmicService {
    /**
     *  SIM快捷认证
     * @param msisdn  手机号
     * @param dataToBeDisplay  弹窗内容
     * @param operatetype 操作类型
     * @return 结果
     * @throws Exception 异常
     */
    String simQuickAuth(String msisdn,String dataToBeDisplay,String operatetype) throws Exception;
    /**
     * 同步人员数据
     * @param startDate  开始时间 yyyy-MM-dd HH:mm:ss
     * @param functionId 执行方法  query_all_emps
     * @param endDate  结束时间 yyyy-MM-dd HH:mm:ss
     * @return 结果
     * @throws Exception 异常
     */
    JSONArray syncEmp(String startDate,String functionId,String endDate) throws Exception;
    /**
     * 同步组织结构
     * @param reqTimestamp  时间戳
     * @param functionId  调用方法 query_all_orgs
     * @param recordDate  记录日期
     * @return 结果
     * @throws Exception 异常
     */
    JSONArray syncOrg(String reqTimestamp, String functionId, String recordDate) throws Exception;
}
