package com.weaver.seconddev.zyhlw.service;

import java.util.List;
import java.util.Map;

public interface IDataBaseService {
    /**
     * 获取配置表中uf_basedata_config中有效的配置数据
     *
     * @return
     */
    List<Map<String, String>> getBaseDataList();

    /**
     * 通过名称和类型名称获取配置表中uf_basedata_config中有效的配置数据中显示值
     *
     * @param selectName 配置项名称
     * @param typeName   类型名称
     * @return
     */
    String getBaseDataValue(String selectName, String typeName);

    /**
     * 通过类型名称查询配置信息
     *
     * @param typeName
     * @return
     */
    List<Map<String, Object>> getBaseDataByTypeName(List<String> typeName);

    /**
     * 通过名称和类型名称获取配置表中uf_basedata中有效的配置数据中显示值
     *
     * @param selectName 配置项名称
     * @param typeName   类型名称
     * @return
     */
    String getBaseValue(String selectName, String typeName);

    /**
     * 通过数据库表名称，获取数据库表结构中配置的对应表单ID
     *
     * @param tableName 数据库表名称
     * @return
     */
    String getTableFormIdValue(String tableName);

    /**
     * 获取系统分类列表
     *
     * @return
     */
    Map<String, String> getSystemCatagoryMap();

    /**
     * 获取系统分类列表，返回List 集合
     *
     * @return
     */
    List<Map<String, Object>> getSystemCatagoryList();

    /**
     * 获取指定用户已收藏的应用列表
     *
     * @param employeeId
     * @return
     */
    List<Map<String, Object>> getSystemCollectList(Long employeeId);

    /**
     * 判断IP地址是否在配置的IP地址范围内
     *
     * @return
     */

    Boolean checkIp(String ipStr);

    /**
     * 获取提定用户收藏的菜单列表
     *
     * @param employeeId
     * @return
     */
    List<Map<String, Object>> getMenuConfigList(Long employeeId);

    /**
     * 根据用户id获取用户信息
     *
     * @param userId 用户id
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @date 2025/5/21 09:53
     */
    Map<String, Object> getEmployeeInfo(String userId);

}
