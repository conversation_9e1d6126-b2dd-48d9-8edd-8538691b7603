package com.weaver.seconddev.zyhlw.service;

import com.weaver.ebuilder.datasource.api.enums.SourceType;

import java.util.List;
import java.util.Map;

/**
 * 查询数据库
 *
 * <AUTHOR>
 */
public interface IDataSqlService {
    /**
     * 查询weaver-ebuilder-form-service分组数据
     *
     * @param sql        分组的唯一标识
     * @param pageNo     页码
     * @param pageSize   页大小
     * @param sourceType 数据源类型(目前仅支持LOGIC和EXTERNAL类型)，如果有参数？则要配置为EXTERNAL，没有参数则配置为LOGIC
     * @return 结果返回records
     */
    List<Map<String, Object>> ebuilderFromSql(String sql, Integer pageNo, Integer pageSize, SourceType sourceType) throws Exception;

    /**
     * 查询weaver-ebuilder-form-service分组数据
     *
     * @param sql        分组的唯一标识
     * @param pageNo     页码
     * @param pageSize   页大小
     * @param sourceType 数据源类型(目前仅支持LOGIC和EXTERNAL类型)
     * @param paramsList 参数集合,可和SQL中的？参数一一对应
     * @return 结果返回records
     */
    List<Map<String, Object>> ebuilderFromSql(String sql, Integer pageNo, Integer pageSize, SourceType sourceType, List<String> paramsList) throws Exception;

    List<Map<String, Object>> workflowFromSql(String sql, Integer pageNo, Integer pageSize, SourceType sourceType, List<String> paramsList) throws Exception;

    /**
     * 查询weaver-ebuilder-form-service分组数据，获取全部数据
     *
     * @param sql        分组的唯一标识
     * @param sourceType 数据源类型(目前仅支持LOGIC和EXTERNAL类型)，如果有参数？则要配置为EXTERNAL，没有参数则配置为LOGIC
     * @return 结果返回records
     */
    List<Map<String, Object>> eBuilderFromSqlAll(String sql, SourceType sourceType);

    /**
     * 查询weaver-ebuilder-form-service分组数据，获取全部数据，支持参数化查询
     *
     * @param sql        分组的唯一标识
     * @param sourceType 数据源类型(目前仅支持LOGIC和EXTERNAL类型)
     * @param paramsList 参数集合,可和SQL中的？参数一一对应
     * @return 结果返回records
     * <AUTHOR>
     * @date 2025/4/27 18:30
     */
    List<Map<String, Object>> eBuilderFromSqlAll(String sql, SourceType sourceType, List<String> paramsList);

    /**
     * 查询weaver-ebuilder-form-service分组数据，获取一条数据
     *
     * @param sql        分组的唯一标识
     * @param sourceType 数据源类型(目前仅支持LOGIC和EXTERNAL类型)，如果有参数？则要配置为EXTERNAL，没有参数则配置为LOGIC
     * @return 结果返回records
     */
    Map<String, Object> eBuilderFromSqlOne(String sql, SourceType sourceType);

    List<Map<String, Object>> workflowFromSqlAll(String sql, SourceType sourceType);

    Map<String, Object> workflowFromSqlOne(String sql, SourceType sourceType);

    /**
     * 通用查询（单个）
     *
     * @param querySql   查询sql
     * @param sourceType 数据源类型
     * @param groupId    分组的唯一标识 (可以不填，但得保证sql查询的表要带上库名)
     * @param paramsList 参数集合
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @date 2025/4/27 15:22
     */
    Map<String, Object> executeCommonSqlOne(String querySql, SourceType sourceType, String groupId, List<String> paramsList);


    /**
     * 通用查询 (全部)
     *
     * @param querySql   查询sql
     * @param sourceType 数据源类型
     * @param groupId    分组的唯一标识 (可以不填，但得保证sql查询的表要带上库名)
     * @param paramsList 参数集合
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @date 2025/4/27 15:22
     */
    List<Map<String, Object>> executeCommonSqlAll(String querySql, SourceType sourceType, String groupId, List<String> paramsList);


    /**
     * 带事务的sql执行
     *
     * @param sql        sql
     * @param sourceType 数据源类型
     * @param groupId    分组的唯一标识 (可以不填，但得保证sql查询的表要带上库名)
     * @param paramsList 预编译sql参数
     * @param transId    事务id
     * @param startTrans 是否开启事务
     * @param commit     是否提交事务
     * @param rollback   是否回滚事务
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @date 2025/4/27 16:49
     */
    Map<String, Object> executeSqlForWithTrans(String sql, SourceType sourceType,
                                               String groupId, List<String> paramsList, String transId,
                                               Boolean startTrans, Boolean commit, Boolean rollback);
}
