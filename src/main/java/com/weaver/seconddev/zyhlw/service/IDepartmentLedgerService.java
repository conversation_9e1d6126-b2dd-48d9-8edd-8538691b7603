package com.weaver.seconddev.zyhlw.service;

import com.weaver.common.base.entity.result.WeaResult;

/**
 * <h1>IDepartmentLedgerService</h1>
 *
 * <p>Description: </p>
 *
 * <p>Note: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public interface IDepartmentLedgerService {


    WeaResult<Object> getManagementSum(String condition1, String condition2);

    WeaResult<Object> getExecutiveArmSum(String condition1, String condition2);

    WeaResult<Object> ResponsibleDepartmentLedger(String condition1, String condition2, String condition3);
}
