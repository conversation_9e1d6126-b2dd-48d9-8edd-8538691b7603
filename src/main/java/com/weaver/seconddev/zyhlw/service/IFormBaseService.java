package com.weaver.seconddev.zyhlw.service;


import com.weaver.common.form.metadata.option.FieldOption;
import com.weaver.seconddev.zyhlw.domain.formdata.FormTableDTO;

import java.util.List;
import java.util.Map;

public interface IFormBaseService {

    List<FieldOption> getFormOptions(String tableName, String dataKey, Integer formType);

    Map<String, String> getFormOptionsValue(String tableName, String dataKey, Integer formType);

    /***
     * 根据表单id获取表名
     *
     * <AUTHOR>
     * @date 2025/5/6 17:07
     * @param formId 表单id
     * @return {@link FormTableDTO}
     */
    FormTableDTO getTableNameByFormId(Long formId);
}
