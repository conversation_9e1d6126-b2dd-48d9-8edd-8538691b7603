package com.weaver.seconddev.zyhlw.service;

import com.weaver.common.base.entity.result.WeaResult;


/**
 * <AUTHOR>
 * @description: 发票模块功能
 * @date 2025/2/19 17:38
 */
public interface IGetInvoiceInformationService {

    /**
     * 根据ID查询发票信息。
     *
     * @param ids 以逗号分隔的发票ID字符串
     * @return 包含发票信息的结果对象
     */
    WeaResult<Object> getInvoiceListByIds(String ids);

    /**
     * 根据ID查询发票反勾稽信息。
     *
     * @param ids 以逗号分隔的发票ID字符串
     * @return 包含发票信息的结果对象
     */
    WeaResult<Object> getReverseCheckOfInvoicesByIds(String ids);

    /**
     * 根据ID查询应收单信息。
     *
     * @param ids 以逗号分隔的发票ID字符串
     * @return 包含发票信息的结果对象
     */
    WeaResult<Object> getReceivablesInformationByIds(String ids);

    /**
     * 根据发票号码查询发票红冲、作废信息
     *
     * @param invoiceNumber 以逗号分隔的发票ID字符串
     * @return 包含发票信息的结果对象
     */
    WeaResult<Object> getInvoiceCancelInformationByInvoiceNumber(String invoiceNumber);

    /**
     * 根据ID查询发票反勾稽信息
     *
     * @param invoiceNumber 以逗号分隔的发票ID字符串
     * @return 包含发票信息的结果对象
     */

    WeaResult<Object> getInvoiceCancelInformationByIds(String invoiceNumber);

    /**
     * 从勾稽列表中获取相关的数据，更新原应收单数据，用逗号分隔
     *
     * @param affectedYsIdArea 本次受影响的应收单id 用逗号分隔
     * @param userid           用户id
     */

    void statisticYsInfo(String affectedYsIdArea, String userid);

    /**
     * 统计本次受影响的发票数据，并更新
     *
     * @param invoiceIdArea 次受影响的发票id，用逗号分隔
     * @param userid 用户id
     */
    void statisticInvoiceInfo(String invoiceIdArea, String userid);
}
