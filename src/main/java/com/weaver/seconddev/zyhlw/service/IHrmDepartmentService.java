package com.weaver.seconddev.zyhlw.service;

import com.weaver.seconddev.zyhlw.domain.hrm.GetDepartmentNameByStaffIdRespVO;
import com.weaver.seconddev.zyhlw.domain.portal.DepartmentModel;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IHrmDepartmentService {

    /**
     * 根据部门id获取该部门id下面提有子部门集合
     */
    List<DepartmentModel> getChildDepartListById(String departId,List<DepartmentModel> allDepartmentList);

    /**
     * 获取组织架构信息（只获取有效的部门）
     */
    List<DepartmentModel> getAllDepartmentList();

    /**
     * 根据id数组查询人员部门科室名称及id
     * @param ids 人员id组，多个以逗号分隔
     * @return 包含人员部门信息的响应对象
     */
    GetDepartmentNameByStaffIdRespVO getDepartmentNameByStaffId(String ids);
}
