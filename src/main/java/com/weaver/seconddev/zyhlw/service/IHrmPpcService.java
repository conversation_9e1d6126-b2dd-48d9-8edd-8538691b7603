package com.weaver.seconddev.zyhlw.service;



import com.weaver.common.hrm.domain.organization.HrmOrgEmpCondition;
import com.weaver.common.hrm.dto.syncdata.HrmSyncDataConfig;
import net.sf.json.JSONObject;

import java.util.List;
import java.util.Map;

public interface IHrmPpcService {

    List<Map<String, Object>> getEmployeeDataAllList(HrmOrgEmpCondition condition, HrmSyncDataConfig dataConfig, Integer current, Integer pageSize);

    Map<String, Object> getEmployeeDataAllMap(HrmOrgEmpCondition condition, HrmSyncDataConfig dataConfig, Integer current, Integer pageSize);

    List<Map<String, Object>> queryEmployeeDataAllList(HrmOrgEmpCondition condition, HrmSyncDataConfig dataConfig);
}
