package com.weaver.seconddev.zyhlw.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.weaver.openapi.pojo.basicserver.res.vo.AccountVo;
import com.weaver.openapi.pojo.flow.res.FlowListResultVo;
import com.weaver.openapi.pojo.flow.res.vo.*;
import com.weaver.openapi.pojo.user.res.vo.EmployeeData;
import com.weaver.openapi.pojo.user.res.vo.UserInfoResult;
import com.weaver.openapi.pojo.user.res.vo.UserResult;
import com.weaver.seconddev.zyhlw.domain.response.todo.FlowListResultResponse;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface IOpenPlatformService {
    /**
     * 获取access_token 通过缓存机制保存，有效或者不存在重新获取并保存缓存
     *
     * @return access_token
     */
    String getAccessToken();

    /**
     * 请求开放平台 部门自定义字段信息接口
     *
     * @param tenantKey 租户Key
     * @return 返回
     */
    JSONArray listDepartmentField(String tenantKey);

    /**
     * 请求开放平台 查询部门接口
     *
     * @param containDisableOrg 是否含停用部门
     * @param containExtend     是否包含自定义
     * @param loadAllChilds     是否含下级
     * @param codeList          部门编号集合
     * @param current           分页-当前页
     * @param pageSize          分页-页大小
     * @param tenantKey         租户Key
     * @return 返回
     */
    JSONObject restfulQueryOrg(Boolean containDisableOrg, Boolean containExtend, Boolean loadAllChilds, List<String> codeList, Integer current, Integer pageSize, String tenantKey);

    /**
     * 请求开放平台 同步部门接口
     *
     * @param dataList  数据集
     * @param tenantKey 租户key
     * @return 返回
     */
    Map<String, Object> restfulSyncDepartment(List<Map<String, Object>> dataList, String tenantKey);

    /**
     * 请求开放平台 封存部门
     *
     * @param ids       部门ID
     * @param tenantKey 租户key
     * @return 返回
     */
    List<Map<String, Object>> restfulDisableDepartment(List<Map<String, Object>> ids, String tenantKey);

    /**
     * 请求开放平台 同步人员
     *
     * @param userList  用户数据
     * @param tenantKey 租户key
     * @return 返回
     */
    String restfulSyncEmployee(List<Map<String, Object>> userList, String tenantKey);

    /**
     * 请求开放平台 同步岗位
     *
     * @param positList 岗位数据
     * @param tenantKey 团队key
     * @return 返回
     */
    String restfulSyncPosition(List<Map<String, String>> positList, String tenantKey);

    /**
     * 请求开放平台 获取本系统令牌
     * 免登本系统-获取本系统令牌，第三方系统免登录本系统场景使用。
     *
     * @param appKey      应用key
     * @param appSecurity 应用密钥
     * @param account     认证数据
     * @param authType    认证类型
     * @return 返回
     */
    String restfilGetLoginToken(String appKey, String appSecurity, String account, String authType);

    /**
     * 请求开放平台 所有流程列表
     *
     * @param userId         用户ID
     * @param requestname    标题
     * @param workflowid     流程ID(多个以,分隔)
     * @param flowStatusList 流程流转的状态;0-草稿,1-审批中,2-已退回,3-正常归档,4-强制结束,5-已作废,6-待提交,7-暂停,8-撤销,-99-已删除(例："flowStatusList":[0,1,2,3,4,5])
     */
    FlowListResultVo getAllToDoWorkflowRequestList(Long userId, String requestname, String workflowid, List<Integer> flowStatusList);

    /**
     * 请求开放平台 所有流程列表 存在分页
     *
     * @param userId         用户ID
     * @param pageNo         页数
     * @param pageSize       单页大小
     * @param requestname    标题
     * @param workflowid     流程ID(多个以,分隔)
     * @param flowStatusList 流程流转的状态;0-草稿,1-审批中,2-已退回,3-正常归档,4-强制结束,5-已作废,6-待提交,7-暂停,8-撤销,-99-已删除(例："flowStatusList":[0,1,2,3,4,5])
     * @return 结果
     */
    FlowListResultVo getAllToDoWorkflowRequestList(Long userId, Integer pageNo, Integer pageSize, String requestname, String workflowid, List<Integer> flowStatusList);

    /**
     * 请求开放平台 所有流程数量
     *
     * @param userId         用户ID
     * @param pageNo         页数
     * @param pageSize       单页大小
     * @param requestname    标题
     * @param workflowid     流程ID(多个以,分隔)
     * @param flowStatusList 流程流转的状态;0-草稿,1-审批中,2-已退回,3-正常归档,4-强制结束,5-已作废,6-待提交,7-暂停,8-撤销,-99-已删除(例："flowStatusList":[0,1,2,3,4,5])
     * @return 结果
     */
    Integer getAllWorkflowRequestCount(Long userId, Integer pageNo, Integer pageSize, String requestname, String workflowid, List<Integer> flowStatusList);

    /**
     * 请求开放平台 待办流程数量
     *
     * @param userId         用户ID
     * @param pageNo         页数
     * @param pageSize       单页大小
     * @param requestname    标题
     * @param workflowid     流程ID(多个以,分隔)
     * @param flowStatusList 流程流转的状态;0-草稿,1-审批中,2-已退回,3-正常归档,4-强制结束,5-已作废,6-待提交,7-暂停,8-撤销,-99-已删除(例："flowStatusList":[0,1,2,3,4,5])
     * @return 结果
     */
    Integer getToDoWorkflowRequestCount(Long userId, Integer pageNo, Integer pageSize, String requestname, String workflowid, List<Integer> flowStatusList);

    /**
     * 请求开放平台 待办流程列表 存在分页
     *
     * @param userId         用户ID
     * @param pageNo         页数
     * @param pageSize       单页大小
     * @param requestname    标题
     * @param workflowid     流程ID(多个以,分隔)
     * @param flowStatusList 流程流转的状态;0-草稿,1-审批中,2-已退回,3-正常归档,4-强制结束,5-已作废,6-待提交,7-暂停,8-撤销,-99-已删除(例："flowStatusList":[0,1,2,3,4,5])
     * @return 结果
     */
    FlowListResultVo getToDoWorkflowRequestList(Long userId, Integer pageNo, Integer pageSize, String requestname, String workflowid, List<Integer> flowStatusList);

    /**
     * 请求开放平台 待办流程列表 循环查询所有数据
     *
     * @param userId         用户ID
     * @param requestname    标题
     * @param workflowid     流程ID(多个以,分隔)
     * @param flowStatusList 流程流转的状态;0-草稿,1-审批中,2-已退回,3-正常归档,4-强制结束,5-已作废,6-待提交,7-暂停,8-撤销,-99-已删除(例："flowStatusList":[0,1,2,3,4,5])
     * @return 结果
     */
    FlowListResultVo getToDoWorkflowRequestList(Long userId, String requestname, String workflowid, List<Integer> flowStatusList);

    /**
     * 待办数据列表 包含签字意见数据
     *
     * @param userId         用户ID
     * @param requestname    标题
     * @param workflowid     流程ID(多个以,分隔)
     * @param flowStatusList 流程流转的状态;0-草稿,1-审批中,2-已退回,3-正常归档,4-强制结束,5-已作废,6-待提交,7-暂停,8-撤销,-99-已删除(例："flowStatusList":[0,1,2,3,4,5])
     * @return 待办数据包含签字意见
     */
    List<FlowListResultResponse> getTodoContainSignList(Long userId, String requestname, String workflowid, List<Integer> flowStatusList);

    /**
     * 待办数据列表 包含最后操作人数据
     *
     * @param userId         用户ID
     * @param requestname    标题
     * @param workflowid     流程ID(多个以,分隔)
     * @param flowStatusList 流程流转的状态;0-草稿,1-审批中,2-已退回,3-正常归档,4-强制结束,5-已作废,6-待提交,7-暂停,8-撤销,-99-已删除(例："flowStatusList":[0,1,2,3,4,5])
     * @return 待办数据包含签字意见
     */
    List<FlowListResultResponse> getTodoContainLastOperatorList(Long userId, String requestname, String workflowid, List<Integer> flowStatusList);

    /**
     * 请求开放平台 已办流程数量
     *
     * @param userId         用户ID
     * @param pageNo         页数
     * @param pageSize       单页大小
     * @param requestname    标题
     * @param workflowid     流程ID(多个以,分隔)
     * @param flowStatusList 流程流转的状态;0-草稿,1-审批中,2-已退回,3-正常归档,4-强制结束,5-已作废,6-待提交,7-暂停,8-撤销,-99-已删除(例："flowStatusList":[0,1,2,3,4,5])
     * @return 结果
     */
    Integer getHandledWorkflowRequestCount(Long userId, Integer pageNo, Integer pageSize, String requestname, String workflowid, List<Integer> flowStatusList);

    /**
     * 请求开放平台 已办流程列表
     *
     * @param userId         用户ID
     * @param pageNo         页数
     * @param pageSize       单页大小
     * @param requestname    标题
     * @param workflowid     流程ID(多个以,分隔)
     * @param flowStatusList 流程流转的状态;0-草稿,1-审批中,2-已退回,3-正常归档,4-强制结束,5-已作废,6-待提交,7-暂停,8-撤销,-99-已删除(例："flowStatusList":[0,1,2,3,4,5])
     * @return 结果
     */
    FlowListResultVo getHandledWorkflowRequestList(Long userId, Integer pageNo, Integer pageSize, String requestname, String workflowid, List<Integer> flowStatusList);

    /**
     * 请求开放平台 已办流程列表 循环查询所有数据
     *
     * @param userId         用户ID
     * @param requestname    标题
     * @param workflowid     流程ID(多个以,分隔)
     * @param flowStatusList 流程流转的状态;0-草稿,1-审批中,2-已退回,3-正常归档,4-强制结束,5-已作废,6-待提交,7-暂停,8-撤销,-99-已删除(例："flowStatusList":[0,1,2,3,4,5])
     * @return 结果
     */
    FlowListResultVo getHandledWorkflowRequestList(Long userId, String requestname, String workflowid, List<Integer> flowStatusList);

    /**
     * 请求开放平台 我发起的流程数量
     *
     * @param userId         用户ID
     * @param pageNo         页数
     * @param pageSize       单页大小
     * @param requestname    标题
     * @param workflowid     流程ID(多个以,分隔)
     * @param flowStatusList 流程流转的状态;0-草稿,1-审批中,2-已退回,3-正常归档,4-强制结束,5-已作废,6-待提交,7-暂停,8-撤销,-99-已删除(例："flowStatusList":[0,1,2,3,4,5])
     * @return 结果
     */
    Integer getMyWorkflowRequestCount(Long userId, Integer pageNo, Integer pageSize, String requestname, String workflowid, List<Integer> flowStatusList);

    /**
     * 请求开放平台 我发起的流程列表
     *
     * @param userId         用户ID
     * @param pageNo         页数
     * @param pageSize       单页大小
     * @param requestname    标题
     * @param workflowid     流程ID(多个以,分隔)
     * @param flowStatusList 流程流转的状态;0-草稿,1-审批中,2-已退回,3-正常归档,4-强制结束,5-已作废,6-待提交,7-暂停,8-撤销,-99-已删除(例："flowStatusList":[0,1,2,3,4,5])
     * @return 结果
     */
    FlowListResultVo getMyWorkflowRequestList(Long userId, Integer pageNo, Integer pageSize, String requestname, String workflowid, List<Integer> flowStatusList);

    /**
     * 请求开放平台 我发起的流程列表 循环查询所有数据
     *
     * @param userId         用户ID
     * @param requestname    标题
     * @param workflowid     流程ID(多个以,分隔)
     * @param flowStatusList 流程流转的状态;0-草稿,1-审批中,2-已退回,3-正常归档,4-强制结束,5-已作废,6-待提交,7-暂停,8-撤销,-99-已删除(例："flowStatusList":[0,1,2,3,4,5])
     * @return 结果
     */
    FlowListResultVo getMyWorkflowRequestList(Long userId, String requestname, String workflowid, List<Integer> flowStatusList);

    /**
     * 请求开放平台 获取节点未操作者
     *
     * @param userId            用户ID 必填
     * @param requestId         流程ID 必填
     * @param userCurrentNodeId 节点ID
     */
    List<NodeOperator> getNodeNotOperator(Long userId, String requestId, Long userCurrentNodeId);

    /**
     * 请求开放平台 获取成员信息
     *
     * @param userId 用户ID
     */
    UserInfoResult getUser(long userId);

    /**
     * 请求开放平台  获取流程基础信息
     *
     * @param userid    用户ID
     * @param requestId 流程ID
     * @return 流程信息
     */
    FlowBasicInfo getRequestInfo(Long userid, String requestId);

    /**
     * 请求开放平台 获取节点操作者
     * 获取节点未操作者，如果传递了节点ID则获取指定节点的未操作者。如果没传递节点ID则获取当前节点操作者（当前节点可能有多个）。
     * 本接口默认只返回提交权限的操作者数据，如果需要获取所有操作者（转发接收人需反馈、抄送接收人许反馈等）则需要onlyOperator传递false.
     *
     * @param userid            用户Id
     * @param requestId         请求ID
     * @param onlyOperator      是否只获取操作者。默认值：true。true：只获取提交权限的操作者。false：获取所有操作者（提交、抄送接收人、转发接收人等
     * @param userCurrentNodeId 节点ID
     * @return {
     * "1022951027002875955": {
     * "nodeId": "1022951027002875955",
     * "nodeName": "1级审批",
     * "operatorUsers": [
     * {
     * "userId": "110001700000001879",
     * "userName": "郑启辉",
     * "mainUserId": "110001700000001879",
     * "departMentId": "110001590000000287",
     * "isRemark": 100
     * },
     * {
     * "userId": "110001700000001877",
     * "userName": "dwliaoqiaofeng",
     * "mainUserId": "110001700000001877",
     * "departMentId": "110001590000000287",
     * "isRemark": 100
     * }
     * ]
     * }
     */
    JSONObject getNodeOperator(Long userid, Long requestId, Boolean onlyOperator, Long userCurrentNodeId);

    /**
     * 请求开放平台 获取流程信息
     *
     * @param userid    用户ID
     * @param requestId 审批数据ID
     */
    WorkFlowRequestInfo getWorkflowRequest(Long userid, String requestId);

    /**
     * 请求开放平台 获取团队人员账号信息
     *
     * @param userid 用户ID
     * @return 账号信息
     */
    AccountVo findAccount(long userid);

    /**
     * 请求开放平台 查询所有人员
     *
     * @return List<UserInfoResult>
     * <AUTHOR>
     * @Date 11:06 2024/7/4
     **/
    List<UserInfoResult> findAllUsersV3();

    /**
     * 请求开放平台  获取流程签字意见
     *
     * @param userid    用户ID
     * @param requestId 审批数据ID
     * @return 结果
     */
    List<FlowSign> getRequestLog(Long userid, String requestId);

    /**
     * 请求开放平台  查询人员
     *
     * @param userid 用户ID
     * @param name   用户名
     * @param email  邮箱
     * @param mobile 手机号
     * @param jobNum 编号（登录名）
     * @return 结果  List<UserResult>
     */
    List<UserResult> findUser(String userid, String name, String email, String mobile, String jobNum);

    /**
     * 人员查询-高级搜索
     *
     * @param jobNumList    工号
     * @param subcompanyIds 分部id
     * @param departmentIds 岗位id
     * @param ids           人员id
     * @param current       分页-当前页
     * @param pageSize      分页-页大小（最大1000）
     * @param nameLikeList  名称模糊查询
     * @param account       账号（支持： 卡片-账号信息中 账号、绑定手机、绑定邮箱、登录名（weaver-hrm-service 1.14后支持））
     */
    List<EmployeeData> queryEmployee(List<String> jobNumList, List<String> subcompanyIds, List<String> departmentIds, List<String> ids, Integer current, Integer pageSize, List<String> nameLikeList, String account);

    /**
     * 获取下一节点操作者信息
     *
     * @param userid    用户ID
     * @param requestId 审批数据ID
     */
    List<WorkFlowRequestNextOperator> getRequestNextOperator(Long userid, String requestId);

    /**
     * 创建审批请求
     *
     * @param userid      用户ID
     * @param requestName 流程名称
     * @param workflowId  工作流ID
     * @param formData    表单数据
     * @param remark      签字意见
     * @param isNextFlow  是否流转到下一节点：0 否，1 是。默认值：0
     * @return 结果
     */
    String createRequest(Long userid, String requestName, String workflowId, String formData, String remark, Integer isNextFlow);

    /**
     * 获取所有数据(分页)
     *
     * @param headers  请求参数{"checkRight": "n","isReturnDetail": "y","objId": 647423567994028033,"recordCount": "y"}
     *                 checkRight是否校验权限(默认校验; n不校验; y校验)非必填
     *                 isReturnDetail是否需要返回明细(默认不返回; y返回; n不返回)非必填
     *                 recordCount是否统计表单数据(默认不统计; y统计; n不统计)非必填
     *                 objId 表单id(必填)
     * @param pageInfo 分页信息 {"pageNo": "9","pageSize": "10"}
     *                 pageNo 当前页码 默认1 非必填
     *                 pageSize 当前分页大小 默认20 非必填
     * @param userId   用户ID 必填
     * @return 数据
     */
    String getAllPaginationData(Map<String, Object> headers, Map<String, Object> pageInfo, Long userId) throws Exception;

    /**
     * 获取所有数据总数(暂不支持筛选)
     *
     * @param userid 用户ID(必填)
     * @param objId  表单id(必填)
     * @param header {"ebbusinessid": "785723653804818434"}
     * @return 数据总数
     */
    Integer getAllDataCount(Long userid, Long objId, Map<String, String> header);

    /**
     * 获取所有下级部门Id
     */
    List<String> getAllSubDepartmentId(Long departmentId);
}
