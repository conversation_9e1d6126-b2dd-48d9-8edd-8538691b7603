package com.weaver.seconddev.zyhlw.service;

import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.seconddev.zyhlw.domain.portalworkflow.AddCollectionDTO;
import com.weaver.seconddev.zyhlw.domain.portalworkflow.NewWorkFlowDTO;

/**
 * <h1>IPortalWorkflowService</h1>
 *
 * <p>Description: </p>
 *
 * <p>Note: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public interface IPartyStyleService {

    WeaResult<Object> fxlyByClause(String year, String quarterly, String branch, String area);

    WeaResult<Object> fxly2(String year, String quarterly, String branch, String area);

    WeaResult<Object> allFxly();

    WeaResult<Object> other(String year, String quarterly, String tableName);

    WeaResult<Object> allDept();

    WeaResult<Object> getRiskDomain();

    WeaResult<Object> getRiskDepartment();

    WeaResult<Object> RiskStatisticalStatement();

    WeaResult<Object> HuoQuRiskTongJi(String lx, String sj, String dType);

    WeaResult<Object> ResponsiblePersonStatistics(String dep);

    WeaResult<Object> fxlyBydp(String branch);

    WeaResult<Object> selectDeptByLy(String lyid);
}
