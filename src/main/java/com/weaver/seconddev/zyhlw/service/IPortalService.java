package com.weaver.seconddev.zyhlw.service;

import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.seconddev.zyhlw.domain.portal.DepartmentModel;
import com.weaver.seconddev.zyhlw.domain.portal.PortalMenuDTO;
import com.weaver.teams.domain.user.SimpleEmployee;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

public interface IPortalService {

    /**
     * 获取指下用户下面应用系统信息
     *
     * @param currentUser  当前用户信息
     * @param groupAppFlag 是否为集团应用，1：是
     * @return
     */
    List<Map<String, Object>> getSystemList(SimpleEmployee currentUser, String groupAppFlag);

    /**
     * 获取该部门和部门下所有子部门科室ID集合
     *
     * @param departId
     * @return
     */
    List<String> getDepartIdList(String departId);

    /**
     * 获取所有应用系统基础信息
     *
     * @return
     */
    List<Map<String, Object>> getAllSystemList(String groupAppFlag);

    String getDepartIdBySubSector(List<DepartmentModel> departments, String departId, Integer dateRank);


    /**
     * update
     * <p>
     * 前端传入json格式的body;{"add":"[4]","delete":"[1,2,3]"}
     * 前端思路如下：
     * 用户add操作时，直接添加到add队列；用户删除操作时，查询add队列中是否存在，若存在则删除，否则添加到delete队列中；
     * 至于add为什么不查询delete队列，因为先删除后加入（相当于修改，因为时间字段影响排序）。所有队列均不能重复添加。
     * 原思路再一个用户多处同时登录且同时进行修改时有影响，现改为，加入之前若存在则修改，若超过8个则加入失败。。
     * ps：注释是e9搬过来的，非本人设计。原e9接口 /zyhlw/web/menu/update
     * <p>
     *
     * @param portalMenuDTO 入参
     * @return 成功失败
     * <AUTHOR>
     * @time 2025年01月22 11:32:35
     * @since 1.0
     */
    WeaResult<Object> update(PortalMenuDTO portalMenuDTO);

    /**
     * addlog
     * <p>
     * 添加日志接口。
     * ps：原e9接口 /zyhlw/web/LogYingYongData/addlog
     * <p>
     *
     * @param danId     传过来的ID
     * @param danName   应用名称
     * @param userAgent userAgent
     * @return 成功失败
     * <AUTHOR>
     * @time 2025年01月22 11:32:35
     * @since 1.0
     */
    WeaResult<Object> addlog(String danId, String danName, String userAgent);

    /**
     * updateChecked
     * <p>
     *
     * @param type    1.流程中心-应用内流程 2.系统中心-只显示集团
     * @param checked 0.取消选中 1.选中
     * @return 勾选状态
     * @description 获取用的流程中心与系统中心的勾选状态
     * ps：e9原接口 /zyhlw/web/portal/NewWorkflowApi/updateChecked
     * <p>
     * <AUTHOR>
     * @time 2025年01月23 11:22:11
     * @since 1.0
     */
    WeaResult<Object> updateChecked(String type, String checked);


    /**
     * 获取指定系统编码的系统名称
     *
     * @param systemCode   系统编码
     * @return
     */
    Map<String, Object> getSystemInfoBySystemCode(String systemCode);

}
