package com.weaver.seconddev.zyhlw.service;

import com.weaver.seconddev.zyhlw.domain.request.todo.TodoItemRequest;
import com.weaver.seconddev.zyhlw.domain.response.todo.TodoAuditResponse;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface IPortalWaitService {

    void pushTodoDataToJtBatch(List<TodoItemRequest> todoItemRequestList, List<Map<String,Object>> recordList, String logMsg);

    /**
     * 推送数据到统一待办
     * @param todoItemRequest
     */
    void pushTodoData(TodoItemRequest todoItemRequest);
    List<TodoAuditResponse.Item> auditUserTodoData(Long userId);


}
