package com.weaver.seconddev.zyhlw.service;

import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.seconddev.zyhlw.domain.portalworkflow.AddCollectionDTO;
import com.weaver.seconddev.zyhlw.domain.portalworkflow.NewWorkFlowDTO;

import java.util.Map;

/**
 * <h1>IPortalWorkflowService</h1>
 *
 * <p>Description: </p>
 *
 * <p>Note: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public interface IPortalWorkflowService {
    WeaResult<Object> getNewWorkflowDataList(NewWorkFlowDTO newWorkFlowDTO);

    WeaResult<Object> getCollection(NewWorkFlowDTO newWorkFlowDTO);

    WeaResult<Object> addCollection(AddCollectionDTO addCollectionDTO);

    WeaResult<Object> cancelCollection(AddCollectionDTO addCollectionDTO);

    WeaResult<Object> getMyWorkflowRequestList();

    WeaResult<Object> getOptions();

    WeaResult<Object> getCreateWorkflowList();
}
