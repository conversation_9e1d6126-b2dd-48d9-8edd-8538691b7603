package com.weaver.seconddev.zyhlw.service;

import com.weaver.common.base.entity.result.WeaResult;

import javax.servlet.http.HttpServletResponse;

/**
 * <h1>IResponsiblePersonLedgerService</h1>
 *
 * <p>Description: </p>
 *
 * <p>Note: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public interface IResponsiblePersonLedgerService {


    WeaResult<Object> HuoQuStandingBook(String ly, String dj, String gettype);

    void downloadAsyncExportFile(String key, HttpServletResponse response);

    WeaResult<Object> queryAsyncExportProgress(String key);

    WeaResult<Object> asyncExport(String templateId, String type, String idList, HttpServletResponse response);

    void export(String templateId, String type, String idList, HttpServletResponse response);
}
