package com.weaver.seconddev.zyhlw.service;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ISsoService {
    /**
     *  云桌面单点
     * @param token
     * @return
     */
    Map<String,Object> dcLogin(String token);

    /**
     * 通过portal Token单点
     * @param token
     * @return
     */
    Map<String,Object> portalTokenLogin(String token);

    /**
     * SIM认证登录
     * @param phone 手机号码
     * @return
     */
    Map<String,Object> simLogin(String phone);

    /**
     * 通过安全网关认证登录后，返回到到指定的页面中
     *
     * @param phone 手机号码
     * @param goUrl 登录成功后返回的Url地址
     * @return
     */
    Map<String, Object> mobileLogin(String phone,String goUrl);

}
