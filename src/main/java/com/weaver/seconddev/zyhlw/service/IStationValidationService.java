package com.weaver.seconddev.zyhlw.service;

import java.util.List;
import java.util.Map;

/**
 * 园区行政服务管理系统 - 工位管理模块服务接口
 *
 * <AUTHOR>
 * @date 2025年04月30日 15:58
 */
public interface IStationValidationService {
    /***
     * 判断选择人员与当前人员是否为同部门人员
     *
     * <AUTHOR>
     * @date 2025/4/30 16:07
     * @param userId  当前用户id
     * @param targetDepartmentId  选择人员部门id
     * @return java.lang.Boolean
     */
    Boolean isSameDept(Long userId, Long targetDepartmentId);

    /**
     * 根据id获取工位信息
     *
     * <AUTHOR>
     * @date 2025/4/30 16:37
     * @param ids 工位id合集
     * @return java.util.List<java.util.Map<java.lang.String, java.lang.String>>
     */
    List<Map<String, String>> getStationInfoByIds(String ids);

    /**
     * 根据工位编号获取工位信息
     *
     * <AUTHOR>
     * @date 2025/4/30 17:30
     * @param stationNums 工位编号合集，以逗号分隔
     * @return java.util.List<java.util.Map<java.lang.String, java.lang.String>>
     */
    List<Map<String, String>> getStationInfoByStationNums(String stationNums);

    /**
     * 根据人员id获取人员信息
     *
     * <AUTHOR>
     * @date 2025/4/30 17:35
     * @param ids 人员id合集，以逗号分隔
     * @return java.util.List<java.util.Map<java.lang.String, java.lang.String>>
     */
    List<Map<String, String>> getPeopleInfoByIds(String ids);
}
