package com.weaver.seconddev.zyhlw.service;

import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.seconddev.zyhlw.domain.request.todo.TodoGrayRequest;
import com.weaver.seconddev.zyhlw.domain.request.todo.TodoRemindRequest;
import com.weaver.seconddev.zyhlw.domain.request.todo.TodoRequest;
import com.weaver.seconddev.zyhlw.domain.response.todo.TodoRemindResponse;
import com.weaver.seconddev.zyhlw.domain.response.todo.TodoResponse;
import com.weaver.seconddev.zyhlw.domain.response.todo.TodoTabResponse;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 */
public interface ITodoRemindService {

    WeaResult<List<TodoRemindResponse>> queryTodoAll();

    WeaResult<List<TodoRemindResponse>> queryTodoAllList();
}
