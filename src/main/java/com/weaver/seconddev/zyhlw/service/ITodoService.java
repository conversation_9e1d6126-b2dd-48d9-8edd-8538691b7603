package com.weaver.seconddev.zyhlw.service;

import com.weaver.seconddev.zyhlw.domain.request.todo.TodoGrayRequest;
import com.weaver.seconddev.zyhlw.domain.request.todo.TodoRequest;
import com.weaver.seconddev.zyhlw.domain.response.todo.TodoResponse;
import com.weaver.seconddev.zyhlw.domain.response.todo.TodoTabResponse;

/**
 * <AUTHOR>
 */
public interface ITodoService {
    /**
     * 获取tab数据
     *
     * @param todoRequest
     * @return
     */
    TodoTabResponse getTab(TodoRequest todoRequest);

    /**
     * 获取待办数据
     *
     * @param todoRequest
     * @return
     */
    TodoResponse getData(TodoRequest todoRequest);

    /**
     * 新待办服务置灰接口
     *
     * @param todoGrayRequest
     * @return
     */
    Boolean updateGray(TodoGrayRequest todoGrayRequest);

    /**
     * 添加和更新对应用户的待办缓存
     * @param userid
     * @return
     */
    Boolean cacheTodo(Long userid);
}
