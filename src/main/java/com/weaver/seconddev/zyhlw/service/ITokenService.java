package com.weaver.seconddev.zyhlw.service;

import java.util.Map;

/**
 * 生成各种票据
 * 验证各种票据
 * 使用前先调用init
 */
public interface ITokenService {

    void init();
    /**
     * 生成统一用户ticket
     * @param portalUserId 登录账号
     * @return
     */
    String generateTicket(String portalUserId);

    /**
     * 生成portalToken
     * @param portalUserId 登录账号
     * @return
     */
    String generatePortalToken(String portalUserId);

    /**
     * 验证portalToken
     * @param token
     * @param source  默认不填为1、pc端，移动端为2
     * @return
     */
    String validatePortalToken(String token,String source);
    /**
     * 生成统一认证票据
     * @param portalUserId 登录账号
     * @return
     */
    String generateToken(String portalUserId);

    /**
     * 验证统一用户ticket
     * @param ticket
     * @return   {"msg":"success","code":0,"mobile":"10016100556","portalUserId":"shingxinng981"}
     */
    String validateTicket(String ticket);
}
