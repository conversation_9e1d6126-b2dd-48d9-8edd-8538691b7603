package com.weaver.seconddev.zyhlw.service;



import com.weaver.teams.domain.user.SimpleEmployee;
import com.weaver.workflow.common.entity.list.api.RequestListConditionApiEntity;
import com.weaver.workflow.common.entity.list.api.publicapi.RequestListInfoPAEntity;

import java.util.List;
import java.util.Map;

public interface IWflPpcService {
    List<RequestListInfoPAEntity> getToDoWorkflowRequestAllList(SimpleEmployee employee, RequestListConditionApiEntity conditionEntity);

    List<RequestListInfoPAEntity> getToDoWorkflowRequestList(SimpleEmployee employee, RequestListConditionApiEntity conditionEntity, int pageNo, int pageSize);
}
