package com.weaver.seconddev.zyhlw.service;


import com.weaver.openapi.pojo.flow.res.vo.FlowList;
import com.weaver.seconddev.zyhlw.domain.request.todo.TodoItemRequest;
import com.weaver.seconddev.zyhlw.domain.response.todo.TodoAuditResponse;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IWorkflowTodoService {
    void syncWorkflowTodoJt();
    void checkWorkflowTodoData();

    List<TodoItemRequest> assembleTodoData(FlowList flowList, Long operatorId);
    List<TodoItemRequest> assembleTodoDataDel(TodoAuditResponse.Item item, Long operatorId);

    void auditWorkflowData(List<Long> userIds, String requestId, String workflowId, String requestName);

}
