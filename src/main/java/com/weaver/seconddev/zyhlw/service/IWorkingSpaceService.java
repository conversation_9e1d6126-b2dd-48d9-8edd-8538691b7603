package com.weaver.seconddev.zyhlw.service;

import com.weaver.seconddev.zyhlw.domain.park.*;

/**
 * 园区行政服务管理系统 - 工位管理模块服务接口
 *
 * <AUTHOR>
 * @date 2025/4/25 15:58
 */
public interface IWorkingSpaceService {
    /**
     * 判断用户是否是管理员
     *
     * @param userId 用户id
     * @param roleId 角色id
     * @return boolean
     * <AUTHOR>
     * @date 2025/4/25 15:58
     */
    Boolean checkIsAdmin(String userId, String roleId);

    /**
     * 工位信息同步
     *
     * @return com.weaver.seconddev.zyhlw.domain.park.SyncStationPeopleInfoRespVO
     * <AUTHOR>
     * @date 2025/4/25 17:03
     */
    SyncStationPeopleInfoRespVO syncStationPeopleInfo(Boolean async);

    /**
     * 获取工位信息列表
     *
     * @param building 楼宇
     * @param floor    楼层
     * @param state    状态
     * @param hrmType  人员类型
     * @return com.weaver.seconddev.zyhlw.domain.park.GetSeatsRespVO
     * <AUTHOR>
     * @date 2025/4/27 17:35
     */
    GetSeatsRespVO getSeats(String building, String floor, String state, String hrmType);

    /**
     * 获取工位参数配置表
     *
     * @param floorId 楼层ID
     * @return com.weaver.seconddev.zyhlw.domain.park.GetPlanInfoRespVO
     * <AUTHOR>
     * @date 2025/4/28 10:35
     */
    GetPlanInfoRespVO getPlanInfo(String floorId);

    /**
     * 获取工位楼层信息
     *
     * @param building 楼宇
     * @param floor    楼层
     * @return com.weaver.seconddev.zyhlw.domain.park.GetFloorInfoRespVO
     * <AUTHOR>
     * @date 2025/4/28 11:20
     */
    GetFloorInfoRespVO getFloorInfo(String building, String floor);

    /**
     * 获取领导办公室信息
     *
     * @param building 楼宇
     * @param floor    楼层
     * @return com.weaver.seconddev.zyhlw.domain.park.GetLeaderSeatsRespVO
     * <AUTHOR>
     * @date 2025/4/28 15:00
     */
    GetLeaderSeatsRespVO getLeaderSeats(String building, String floor);

    /**
     * 获取会议室信息
     *
     * @param building         楼宇
     * @param floor            楼层
     * @param id               会议室ID
     * @param meetingRoomsName 会议室名称
     * @return com.weaver.seconddev.zyhlw.domain.park.GetMeetingInformationRespVO
     * <AUTHOR>
     * @date 2025/4/29 10:30
     */
    GetMeetingInformationRespVO getMeetingInformation(String building, String floor, String id, String meetingRoomsName);
}
