package com.weaver.seconddev.zyhlw.service;

import com.weaver.seconddev.zyhlw.domain.oa.OaAnnouncementInfo;
import com.weaver.seconddev.zyhlw.domain.oa.SystemPost;

import java.util.Map;

/**
 * OA公文推送接口
 *
 * @date 2025-01-20
 */
public interface OaSystemOfficialService {

    /**
     * 接收OA公文（制度发文）信息推送接口
     *
     * @param oaAnnouncementInfo 公文信息
     */
    Map<String, Object> getOfficialDocumentList(OaAnnouncementInfo oaAnnouncementInfo);

    /**
     * 接收OA公文（制度发文）信息推送接口
     *
     * @param systemPost 系统发文
     */
    Map<String, Object> saveSystemPost(SystemPost systemPost);

    String getBaseDataValue(String name, String type);

}
