package com.weaver.seconddev.zyhlw.service;

import java.util.List;
import java.util.Map;

/**
 * @date 2025-05-26
 */
public interface StatisticalFormService {
    List<Map<String, Object>> getDataOverview(String year, String quarter, String departmentId);

    Map<String, Object> getClassifiedStatistic(String year, String quarter, String departmentId);

    List<Map<String, Object>> getProblemSources();

    Map<String, Object> getDepartmentalGrouping(String year, String quarter, String departmentId, String source);

    List<Map<String, String>> getDepartmentalProportionStatistics(String year, String quarter, String departmentId);
}
