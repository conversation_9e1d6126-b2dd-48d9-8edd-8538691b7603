package com.weaver.seconddev.zyhlw.service;

import java.util.List;
import java.util.Map;

/**
 * @date 2025-05-20
 */
public interface SupervisionService {

    //监督计划方块图
    List<Map<String, String>> getBlockChart(String year, String dep);

    //监督计划方块图
    List<Map<String, String>> getBlockChart2(String year, String dep);

    //获取监督计划类型
    List<Map<String, Object>> getType();

    //监督计划饼状图数据
    List<Map<String, Object>> getPieChart(String year, String dep, String supervisionType);

    //监督计划柱状图数据
    List<Map<String, Object>> getColumnarChart(String year, String dep, String supervisionType);

    //监督计划数线图数据
    List<Map<String, Object>> getNumericalDiagram(String year, String dep, String supervisionType);

    //根据id查询计划任务数据
    Map<String, Object> getProjectById(String projectId);
}
