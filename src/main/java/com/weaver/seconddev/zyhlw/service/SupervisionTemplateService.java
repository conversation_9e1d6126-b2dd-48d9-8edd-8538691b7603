package com.weaver.seconddev.zyhlw.service;

import java.util.Map;

/**
 * @date 2025-02-24
 */
public interface SupervisionTemplateService {

    /**
     * 返回监督模版问题列表 场景点总数、列表总数、管控点总数、领域数
     *
     * @param year         年份
     * @param quarter      季度
     * @param departmentId 部门id
     * @return 返回String的键值对
     */
    Map<String, Object> supervisionTemplateStatistics(String year, String quarter, String departmentId);

    /**
     * 查询监督模版问题统计
     *
     * @param dep       部门id
     * @param quarter   季度
     * @param parameter 年份
     * @return 返回map集合
     */
    Map<String, Object> moduleProportion(String dep, String quarter, String parameter, String type);

    /**
     * 获取场景点变化情况
     *
     * @return {"change": "1","newAdd": "1","del": "0"}
     */
    Map<String, Object> getScenePointChanges(String year, String quarter, String departmentId);

    String supervisionTemplateCondition(String guiSbm, String startTime, String endTime, String suoSly, String wenTmk, String controlPoints, String scenePoints, String kind);
}
