package com.weaver.seconddev.zyhlw.service;

import java.util.List;
import java.util.Map;

/**
 * 党风廉政谈话管理
 *
 * @date 2025-02-18
 */
public interface TalkManagerService {

    /**
     * 根据年份、季度查询谈话数据
     *
     * @param year    年份
     * @param quarter 季度
     * @return List<Map < String, Object>>
     */
    List<Map<String, Object>> getCarTrackApi(String year, String quarter);

    /**
     * 根据年份、季度、部门id查询统计数据
     *
     * @param year         年份
     * @param quarter      季度
     * @param departmentId 部门id
     * @return List<Map < String, Object>>
     */
    List<Map<String, Object>> getCountInfo2(String year, String quarter, String departmentId);

    /**
     * 根据年份、季度、部门id、类型查询统计数据
     *
     * @param year         年份
     * @param quarter      季度
     * @param departmentId 部门id
     * @param type         类型
     * @return List<Map < String, Object>>
     */
    List<Map<String, Object>> getCountInfo3(String year, String quarter, String departmentId, String type);

    /**
     * 根据年份、季度、部门id、实施种类查询统计数据(柱状图)
     *
     * @param year         年份
     * @param quarter      季度
     * @param departmentId 部门id
     * @param shi_szl      实施种类
     * @return List<Map < String, Object>>
     */
    Map<String, Object> getCountInfo5(String year, String quarter, String departmentId, String shi_szl);

    String conversationCondition(String shiSzl, String startTime, String endTime, String department, String shiSxs, String shapeType);
}
