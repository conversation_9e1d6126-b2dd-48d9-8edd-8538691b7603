package com.weaver.seconddev.zyhlw.service.impl;


import com.alibaba.fastjson.JSON;
import com.weaver.ebuilder.form.client.service.emobile.IEtFormDatasetService;
import com.weaver.ebuilder.teams.etform.base.query.Condition;
import com.weaver.ebuilder.teams.etform.base.query.Query;
import com.weaver.framework.rpc.annotation.RpcReference;
import com.weaver.openapi.pojo.basicserver.res.vo.AccountVo;
import com.weaver.openapi.pojo.flow.res.vo.FlowSign;
import com.weaver.seconddev.zyhlw.service.IChuChaiService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.IvParameterSpec;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.Charset;
import java.nio.charset.CharsetDecoder;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChuChaiServiceImpl implements IChuChaiService {

    String simpleName = ChuChaiServiceImpl.class.getSimpleName();
    @RpcReference(group = "ebuilderform")
    IEtFormDatasetService iEtFormDatasetService;
    @Autowired
    IOpenPlatformService iOpenPlatformService;




    @Override
    public List<Map<String, Object>> holiday(String tenantKey, Long employeeId) {
        String method = "调用" + simpleName + ".holiday()-->";
        log.info("{}租户：{}，用户：{}", method, tenantKey, employeeId);
//        EsbFormFieldValueQueryDto esbFormFieldValueQueryDto = new EsbFormFieldValueQueryDto();
//        esbFormFieldValueQueryDto.setTenantKey(tenantKey);
//        esbFormFieldValueQueryDto.setEmployeeId(employeeId);
//        esbFormFieldValueQueryDto.setCheckRight(false);
//        esbFormFieldValueQueryDto.setObjId(110002760000028502L);
//        Map<String, List<FieldValue>> formFieldValues = esbDubboService.getFormFieldValues(esbFormFieldValueQueryDto);
//        log.info("{}查询表单数据：{}",method,formFieldValues.entrySet());
//        formFieldValues.forEach((key,values)->{
//            log.info("{}对应key：{}，值：{}",method,key,values);
//        });
        List<Condition> conditions = new ArrayList<>();
        Query query = new Query();
        query.setConditions(conditions);
        query.setPageNo(1);
        query.setPageSize(1000);
        String queryStr = Base64.getEncoder().encodeToString(JSON.toJSONString(query).getBytes());
        Map<String, Object> datas = iEtFormDatasetService.getDatas(110002760000028502L, "", queryStr, null);
        log.info("{}查询到数据：{}", method, JSON.toJSONString(datas));
        return null;
    }

    @Override
    public Map<String,Object> putDataToTrade100(JSONObject params) {
        String method = "调用" + simpleName + ".putDataToTrade100()-->";
        log.info("{}",method);
        JSONArray dt1 = params.getJSONArray("dt1");
        String requestid = params.getString("requestid");
        log.info("{}获取到请求ID：{}", method, requestid);
        String currentUser = params.getString("current_user");
        log.info("{}当前用户：{}", method, currentUser);
        long sysGeographyObjId = Long.valueOf(params.getString("sysGeographyObjId"));
        String sysGeographyFieId = params.getString("sysGeographyFieId");
        String systemId = params.getString("systemId");
        String systemName = params.getString("systemName");
        String slUrl = params.getString("slUrl");
        int countFly = 1;
        int typeMap = -1;
        // 出差行程数据
        JSONArray journeyArr = new JSONArray();
        List<LocalDate> startDateList = new ArrayList<>();
        List<LocalDate> endDateList = new ArrayList<>();
        List<JSONObject> travelUserList = new JSONArray();
        List<String> ids = new ArrayList<>();
        List<Integer> tuispcs = new ArrayList<>();
        for (int i = 0; i < dt1.size(); i++) {
            JSONObject itemJson = dt1.getJSONObject(i);
            int shiFxz = itemJson.getInt("shi_fxz");
            String mx_id = itemJson.getString("mx_id");
            String tuiSpcStr = itemJson.getString("tui_spc");
            log.info("{}推送批次：{}",method,tuiSpcStr);
            if (tuiSpcStr == null || "".equals(tuiSpcStr)) {
                tuispcs.add(0);
            }else{
                tuispcs.add(Integer.valueOf(tuiSpcStr));
            }

            if (shiFxz == 0) {
                LocalDate startDate = LocalDate.parse(itemJson.getString("xing_ckssj"));
                startDateList.add(startDate);
                LocalDate endDate = LocalDate.parse(itemJson.getString("xing_cjssj"));
                endDateList.add(endDate);
                ids.add(mx_id);
            }
        }
        log.info("{}推送批次数据：{}",method,JSON.toJSONString(tuispcs));
        Integer tuispc = Collections.max(tuispcs, new Comparator<Integer>() {
            @Override
            public int compare(Integer o1, Integer o2) {
                return o1.compareTo(o2);
            }
        });
        log.info("{}最大批次数：{}",method,tuispc);
        // 获取最大开始日期
        LocalDate startDate = Collections.max(startDateList, new Comparator<LocalDate>() {
            @Override
            public int compare(LocalDate o1, LocalDate o2) {
                return o1.compareTo(o2);
            }
        });

        // 获取最小结束时间
        LocalDate endDate = Collections.min(endDateList, new Comparator<LocalDate>() {
            @Override
            public int compare(LocalDate o1, LocalDate o2) {
                return o1.compareTo(o2);
            }
        });

        for (int i = 0; i < dt1.size(); i++) {
            JSONObject itemJson = dt1.getJSONObject(i);
            int shiFxz = itemJson.getInt("shi_fxz");
            if (shiFxz == 0) {
                if (countFly == 1) {
                    typeMap = itemJson.getInt("chu_clx");
                }
                int xingClx = itemJson.getInt("xing_clx");
                int jiaoTgj = (itemJson.getInt("jiao_tgj") == 1 || itemJson.getInt("jiao_tgj") == 2) ? itemJson.getInt("jiao_tgj") : 5;
                // 往返程
                if (xingClx == 1) {
                    // 去程
                    JSONObject jour = new JSONObject();
                    jour.put("fromCity", getSysGeography(itemJson.getString("chu_fcsm"),sysGeographyObjId,sysGeographyFieId));
                    jour.put("arrCity", getSysGeography(itemJson.getString("mu_dcsm"),sysGeographyObjId,sysGeographyFieId));
                    jour.put("startDate", startDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
                    jour.put("endDate", endDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
                    jour.put("trafficType", jiaoTgj);
                    jour.put("stepNo", countFly++);
                    journeyArr.add(jour);
                    // 返程
                    JSONObject jour2 = new JSONObject();
                    jour2.put("fromCity", getSysGeography(itemJson.getString("mu_dcsm"),sysGeographyObjId,sysGeographyFieId));
                    jour2.put("arrCity", getSysGeography(itemJson.getString("chu_fcsm"),sysGeographyObjId,sysGeographyFieId));
                    jour2.put("startDate", startDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
                    jour2.put("endDate", endDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
                    jour2.put("trafficType", jiaoTgj);
                    jour2.put("stepNo", countFly++);
                    journeyArr.add(jour2);
                } else {
                    JSONObject jour = new JSONObject();
                    jour.put("fromCity", getSysGeography(itemJson.getString("chu_fcsm"),sysGeographyObjId,sysGeographyFieId));
                    jour.put("arrCity", getSysGeography(itemJson.getString("mu_dcsm"),sysGeographyObjId,sysGeographyFieId));
                    jour.put("startDate", startDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
                    jour.put("endDate", endDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
                    jour.put("trafficType", jiaoTgj);
                    jour.put("stepNo", countFly++);
                    journeyArr.add(jour);
                }
                JSONObject travelUserJson = new JSONObject();
                AccountVo account = iOpenPlatformService.findAccount(Long.valueOf(dt1.getJSONObject(i).getString("chu_cr")));
                travelUserJson.put("userName", account.getLoginName());
                travelUserJson.put("fullName", dt1.getJSONObject(i).getString("chu_crnc"));
                travelUserList.add(travelUserJson);
            } else {
                log.info("{}行程非新增数据：{}", method, itemJson);
            }
        }
        log.info("{} 出差行程数据：{}", method, journeyArr.toString());
        //        Page<Comment> flowComment = wfcRequestCommonRest.getFlowComment(requestid, currentUser, 1, 100);
        List<FlowSign> requestLog = iOpenPlatformService.getRequestLog(Long.valueOf(currentUser), requestid);
//        log.info("{}查询到的签字意见1：{}",method, JSON.toJSONString(flowComment));

        log.info("{}查询到的签字意见2：{}", method, JSON.toJSONString(requestLog));
        JSONArray auditArr = new JSONArray();
        int a = requestLog.size();
        if (requestLog.size() > 14) {
            a = 15;
        }
        for (int i1 = 0; i1 < requestLog.size(); i1++) {

            if (i1 > 14) {
                break;
            }
            FlowSign flowSign = requestLog.get(i1);

            JSONObject audit = new JSONObject();

            String content = flowSign.getContent().replace("_weaverMte_", "").replace("<p>", "").replace("</p>", "").replace("<br />", "").replace("<br>", "").replaceAll("\\<.*?>", "").replace("&quot;", "");
            ;
            try {
                if (content.getBytes("utf-8").length > 200) {
                    String itemcurrRemark = cutString(content, 191, "utf-8");
                    content = itemcurrRemark.substring(0, itemcurrRemark.length() - 1) + "...";
                }
            } catch (UnsupportedEncodingException e) {
                log.error("{}截取审批意见异常", method, e);
            }
            if (content.length() != 0) {
                // 审批结果
                audit.put("auditResult", "1");
                // 审批方式
                audit.put("auditWays", "1");
                // 审批备注
                audit.put("remark", content);
                // 审批步骤
                audit.put("stepNo", (a--));
                AccountVo account = iOpenPlatformService.findAccount(Long.valueOf(flowSign.getCommentor().getEmployeeId()));
                // 审批人账号
                audit.put("auditUserName", account.getLoginName());
                // 审批时间 yyyy-MM-dd HH:mm:ss
                LocalDateTime dateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.valueOf(flowSign.getAddTime())), ZoneId.systemDefault());
                audit.put("auditTime", DateUtils.formatTime(dateTime, DateUtils.yyyy_MM_dd_HH_mm_ss));
                auditArr.add(audit);
            }
        }
        log.info("{}审批意见：{}", method, auditArr.toString());
        Set<String> list2 = new HashSet<>();
        List<JSONObject> travelUserList2 = travelUserList.stream().filter(p -> list2.add(p.getString("userName"))).collect(Collectors.toList());
        AccountVo tidrAccount = iOpenPlatformService.findAccount(Long.valueOf(params.getString("ti_dr")));

        String appId = "";
        String guanAppId = "";
        if (tuispc == 0) {
            tuispc++;
            appId = params.getString("liu_cbh");
        }else{
            tuispc++;
            appId = String.format("%s_%s",params.getString("liu_cbh"),tuispc);
            guanAppId = params.getString("liu_cbh");
        }
        log.info("{}流程编号：{}",method,appId);
        log.info("{}关联流程编号：{}",method,guanAppId);
        // AppInfo节点的map
        JSONObject appInfoMap = new JSONObject();
        //申请单状态 传入编号，编号含义为：0:审批申请、1:正在审批、2:审批通过、3:审批驳回；为空时表示审批通过。
        appInfoMap.put("auditStatus", "2");
        // 出差时间
        appInfoMap.put("startDate", startDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
        // 返回时间
        appInfoMap.put("returnDate", endDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
        // 出差类型
        appInfoMap.put("travelType", typeMap == -1 ? "0" : typeMap + "");
        // 申请人
        appInfoMap.put("appUser", tidrAccount.getLoginName());
        appInfoMap.put("appId", appId);
        appInfoMap.put("travelUserList", travelUserList2);
        appInfoMap.put("jouList", journeyArr);
        appInfoMap.put("auditList", auditArr);
        log.info("{}最终推送的数据：{}", method, appInfoMap.toString());
        String wsdlResponseContent = getWsdlResponseContent(appInfoMap.toString(), guanAppId,systemId,systemName,slUrl);
        Map<String,Object> resultMap = new HashMap<>();
        if (wsdlResponseContent.indexOf("Error") > -1) {
            resultMap.put("code",201);
            resultMap.put("msg",wsdlResponseContent);
            return resultMap;
        } else {
            resultMap.put("code",200);
            resultMap.put("msg","同步成功");
            resultMap.put("data",ids);
            resultMap.put("tuispc",tuispc);
            return resultMap;
        }
    }

    /**
     * 同步酒店申请单
     * @param params
     * @return
     */
    @Override
    public Map<String, Object> hotelSysn(JSONObject params) {
        String method = "调用"+simpleName+".hotelSysn()-->";
        Map<String,Object> resultMap = new HashMap<>();
        if (!params.containsKey("config")) {
            resultMap.put("code",201);
            resultMap.put("msg","未配置config参数");
            return resultMap;
        }
        String configStr = params.getString("config");
        JSONObject config = JSONObject.fromObject(configStr);
        if (!config.containsKey("systemName")) {
            resultMap.put("code",201);
            resultMap.put("msg","未配置系统名称");
            return resultMap;
        }
        if (!config.containsKey("systemId")) {
            resultMap.put("code",201);
            resultMap.put("msg","未配置系统ID");
            return resultMap;
        }
        if (!config.containsKey("companyCode")) {
            resultMap.put("code",201);
            resultMap.put("msg","未配置公司编号");
            return resultMap;
        }
        if (!config.containsKey("des")) {
            resultMap.put("code",201);
            resultMap.put("msg","未配置DES密钥");
            return resultMap;
        }
        if (!config.containsKey("des2")) {
            resultMap.put("code",201);
            resultMap.put("msg","未配置DES2密钥");
            return resultMap;
        }
        if (!config.containsKey("url")) {
            resultMap.put("code",201);
            resultMap.put("msg","未配置URL");
            return resultMap;
        }
        if (!config.containsKey("uid")) {
            resultMap.put("code",201);
            resultMap.put("msg","未配置UID");
            return resultMap;
        }
        String type = params.getString("type");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        LocalDate currentDate = LocalDate.now();
        String startTime = "";
        String endTime = currentDate.format(formatter)+" 23:59:59";
        if ("1".equals(type)) {
            // 全量同步
            LocalDate date = currentDate.minusDays(90);
            startTime = date.format(formatter);
        } else if ("2".equals(type)) {
            // 增量同步
        }
        log.info("{}开始时间：{}，结束时间：{}",method,startTime,endTime);

        return resultMap;
    }

    /**
     * 获取三字码
     *
     * @param id 数据ID
     * @param sysGeographyObjId 三字码表单ID
     * @param sysGeographyFieId 三字码字段ID
     * @return
     */
    private String getSysGeography(String id,Long sysGeographyObjId,String sysGeographyFieId) {
        String method = "调用" + simpleName + ".getSysGeography()-->";
        List<Condition> conditions = new ArrayList<>();
        String resultStr = "";
        Query query = new Query();
        Condition condition = new Condition();
        condition.setConditionId("id");
        condition.setCompareType("eq");
        condition.setConditionValue(id);
        conditions.add(condition);
        query.setConditions(conditions);
        query.setPageNo(1);
        query.setPageSize(1000);
        String queryStr = Base64.getEncoder().encodeToString(JSON.toJSONString(query).getBytes());
        log.info("{}获取到的表单ID：{}",method,sysGeographyObjId);
        Map<String, Object> datas = iEtFormDatasetService.getDatas(sysGeographyObjId, "", queryStr, null);
        log.info("{}查询到数据：{}", method, JSON.toJSONString(datas));
        if (datas.containsKey("list")) {
            JSONArray dataArr = JSONArray.fromObject(datas.get("list"));
            for (int i = 0; i < dataArr.size(); i++) {
                JSONObject jsonObject = dataArr.getJSONObject(i);
                // 三字码字段
                resultStr = jsonObject.getString(sysGeographyFieId);
            }
        }
        return resultStr;
    }

    private static String cutString(String str, int len, String charset) throws UnsupportedEncodingException {

        byte[] buf = str.getBytes(charset);
        if (len % 2 == 0) {
            return new String(buf, 0, len, charset);
        } else if (len % 2 == 1) {
            return new String(buf, 0, len - 1, charset);
        } else {
            return new String(buf, 0, len - 2, charset);
        }
    }

    /**
     * 推送申请单到商旅
     * @param appInfo   请求数据
     * @param guanllc   关联流程
     * @param systemId  商旅系统id
     * @param systemName 商旅系统名称
     * @param slurl   商旅申请单请求地址
     * @return
     */
    public String getWsdlResponseContent(String appInfo, String guanllc,String systemId,String systemName,String slurl) {
        String method = "调用" + simpleName + ".getWsdlResponseContent()-->";
        StringBuffer soapXML = new StringBuffer("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:ser=\"http://service.travel.std.hs.b2bjoy.cmdi.com/\"><soapenv:Header/><soapenv:Body>");
        soapXML.append("<ser:importTravelAppInfoSrv_1_3>");
        soapXML.append("<arg0>");
        soapXML.append("<systemId>"+systemId+"</systemId>");
        soapXML.append("<systemName>"+systemName+"</systemName>");
        soapXML.append("<version>V1.3</version>");
        soapXML.append("<appInfo>");
        soapXML.append(appInfo);
        soapXML.append("</appInfo>");
        soapXML.append("<dimension1>1</dimension1>");
        soapXML.append("<dimension2></dimension2>");
        soapXML.append("<dimension3>" + guanllc + "</dimension3>");
        soapXML.append("</arg0>");
        soapXML.append("</ser:importTravelAppInfoSrv_1_3>");
        soapXML.append("</soapenv:Body></soapenv:Envelope>");
        log.info("{}请求商旅参数：{}",method,soapXML.toString());
        String contentStr = "";
        OutputStream os = null;
        HttpURLConnection connection = null;
        try {
            URL url = new URL(slurl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "text/xml;charset=UTF-8");
            connection.setDoInput(true);
            connection.setDoOutput(true);
            connection.setConnectTimeout(5000);
            os = connection.getOutputStream();
            os.write((soapXML.toString()).getBytes("UTF-8"));

            int responseCode = connection.getResponseCode();
            if (200 == responseCode) {
                // 服务端响应成功
                contentStr = convertStreamToString(connection.getInputStream());
                log.info("{}请求商旅结果：{}", method, contentStr);
            } else {
                contentStr = "Error:" + convertStreamToString(connection.getErrorStream());
                log.warn("{}请求商旅返回异常：{}", method, contentStr);

            }

            os.close();
            connection.disconnect();
        } catch (Exception e) {
            contentStr = "Error:接口或网络异常(网络链接异常)，请联系管理员！";
            e.printStackTrace();
            log.error("{}{}", contentStr, e);
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (connection != null) {
                connection.disconnect();
            }

        }
        return contentStr;
    }

    /**
     * 请求商旅获取酒店订单
     * @param startTime 开始日期
     * @param endTime   结束日期
     * @param config    配置数据
     * @return
     */
    public String getJDWsdlResponseContent(String startTime, String endTime,JSONObject config){
        String method = "调用"+simpleName+".getJDWsdlResponseContent()-->";
        String contentStr =null;
        String systemName = config.getString("systemName");
        String systemId = config.getString("systemId");
        String companyCode = config.getString("companyCode");
        String des = config.getString("des");
        String des2 = config.getString("des2");
        String url =config.getString("url");
        String uid = config.getString("uid");
        try {
            systemName = new String (systemName.getBytes("ISO-8859-1"),"utf-8");
        }catch (UnsupportedEncodingException e){
            log.error("{}转换系统名称异常",method,e);
        }
        StringBuffer soapXML = new StringBuffer();
        soapXML.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
        soapXML.append("<HOTEL_CONFIRMATION_INFO_1_1>");
        soapXML.append("<MsgHeader>");
        soapXML.append("<SYSTEMID>"+systemId+"</SYSTEMID>");
        soapXML.append("<SYSTEMNAME>"+systemName+"</SYSTEMNAME>");
        soapXML.append("<PAGE_SIZE>0</PAGE_SIZE>");
        soapXML.append("<CURRENT_PAGE>1</CURRENT_PAGE>");
        soapXML.append("<TOTAL_RECORD></TOTAL_RECORD>");
        soapXML.append("<COMPANY_CODE>"+companyCode+"</COMPANY_CODE>");
        soapXML.append("<ENVIRONMENT_NAME></ENVIRONMENT_NAME>");
        soapXML.append("</MsgHeader>");
        soapXML.append("<EMPLOYEE_ID></EMPLOYEE_ID>");
        soapXML.append("<USER_NAME></USER_NAME>");
        soapXML.append("<START_LAST_UPDATE_DATE>"+startTime+"</START_LAST_UPDATE_DATE>");
        soapXML.append("<END_LAST_UPDATE_DATE>"+endTime+"</END_LAST_UPDATE_DATE>");
        soapXML.append("<DIMENSION1></DIMENSION1>");
        soapXML.append("<DIMENSION2></DIMENSION2>");
        soapXML.append("</HOTEL_CONFIRMATION_INFO_1_1>");
        log.info("{}请求商旅酒店接口报文：{}",method,soapXML.toString());
        // 对报文进行加密
        String reqXml = null;
        try {
            byte[] byteMing =soapXML.toString().getBytes("utf-8");
            Cipher cipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
            DESKeySpec desKeySpec = new DESKeySpec(des.getBytes("utf-8"));
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
            SecretKey secretKey = keyFactory.generateSecret(desKeySpec);
            IvParameterSpec iv2 = new IvParameterSpec(des2.getBytes("utf-8"));
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, iv2);
            byte[]  byteMi = cipher.doFinal(byteMing);
            reqXml = new String(org.apache.commons.codec.binary.Base64.encodeBase64(byteMi), "utf-8");
        }catch (Exception e){
            log.error("{}报文加密异常",method,e);
        }
        String identityXml = "&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&lt;IDENTITY_1_0>&lt;UID>"+uid+"&lt;/UID>&lt;/IDENTITY_1_0>";
        String postxml = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:ser=\"http://service.ws.b2bjoy.cmdi.com/\"><soapenv:Header/><soapenv:Body><ser:doHotelXml><ser:identityXml>"+identityXml+"</ser:identityXml><ser:reqXml>"+reqXml+"</ser:reqXml></ser:doHotelXml></soapenv:Body></soapenv:Envelope>";
        log.info("{}加密后的报文：{}",method,postxml);
        return contentStr;
    }

    public static String convertStreamToString(InputStream is) throws Exception {
        BufferedReader reader = new BufferedReader(new InputStreamReader(is, "UTF-8"));
        StringBuilder sb = new StringBuilder();

        String line = null;
        try {
            while ((line = reader.readLine()) != null) {
                sb.append(line + "\n");
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                is.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return sb.toString();
    }

//    public class Config {
//        /**
//         * 城市三字码表单ID
//         */
//        private Long sysGeographyObjId;
//        /**
//         * 三字码字段ID
//         */
//        private String sysGeographyFieId;
//
//        public Config(String environment) {
//            if (environment.equals("test21")) {
//                this.sysGeographyObjId = 110002760000010002L;
//                this.sysGeographyFieId = "110003720428841919";
//            } else if (environment.equals("test86")) {
//                this.sysGeographyObjId = 100002760000010002L;
//                this.sysGeographyFieId = "100003720428841919";
//            }
//        }
//
//        public Long getSysGeographyObjId() {
//            return sysGeographyObjId;
//        }
//
//        public String getSysGeographyFieId() {
//            return sysGeographyFieId;
//        }
//    }
}
