package com.weaver.seconddev.zyhlw.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.weaver.seconddev.zyhlw.service.ICmicService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import com.weaver.seconddev.zyhlw.util.http.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class   CmicServiceImpl implements ICmicService {
    @Resource
    private CmicProperties cmicProperties;
    private final String simpleName= CmicServiceImpl.class.getSimpleName();
    /**
     * SIM 快捷认证
     * @param msisdn  手机号
     * @param dataToBeDisplay  弹窗内容
     * @param operatetype 操作类型
     * @return
     */
    @Override
    public String simQuickAuth(String msisdn, String dataToBeDisplay, String operatetype)throws Exception {
        String methodName = "调用"+simpleName+".simQuickAuth()";
        log.info("{} 入参：msisdn：｛｝，dataToBeDisplay：{}，operatetype：{}",methodName,msisdn,dataToBeDisplay,operatetype);
        JSONObject paramJson = new JSONObject();
        paramJson.put("msisdn",msisdn);
        paramJson.put("dataToBeDisplay",dataToBeDisplay);
        paramJson.put("operatetype",operatetype);
        Map<String,Object> header = new HashMap<>(16);
        header.put("appId",cmicProperties.getAppid());
        header.put("appSecret",cmicProperties.getAppSecret());
        header.put("functionId","simQuickAuth");
        header.put("Content-Type","application/json; charset=utf-8");
        log.info("{} 请求SIM快捷认证地址：{}，参数：{}，header：{}",methodName,cmicProperties.getSimQuickAuthUrl(),paramJson.toString(),JSON.toJSONString(header));
        String sendPostData = HttpUtils.sendPost(cmicProperties.getSimQuickAuthUrl(), paramJson.toString(), header);
        log.info("{} 请求SIM快捷认证，返回结果：{}",methodName,sendPostData);
        return sendPostData;
    }
    /**
     * 同步人员数据
     * @param startDate  开始时间 yyyy-MM-dd HH:mm:ss
     * @param functionId 执行方法  query_all_emps
     * @param endDate  结束时间 yyyy-MM-dd HH:mm:ss
     * @return 结果
     * @throws Exception 异常
     */
    @Override
    public JSONArray syncEmp(String startDate, String functionId, String endDate) throws Exception {
        String methodName = "调用"+simpleName+".syncEmp()";
        JSONArray dataList = new JSONArray();
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String,Object> param = new HashMap<>(16);
        param.put("startDate",startDate);
        param.put("endDate",endDate);
        param.put("isValid",null);
        param.put("userType","all");
        String paramStr =objectMapper.writeValueAsString(param);
        Map<String,Object> header = new HashMap<>(16);
        header.put("appId", cmicProperties.getAppid());
        header.put("appSecret", cmicProperties.getAppSecret());
        header.put("functionId", functionId);
        header.put("source", "");
        header.put("Content-Type", "application/json; charset=utf-8");
        header.put("Connection", "Keep-Alive");
        header.put("Accept-Charset", "UTF-8");
        header.put("content-type", "application/json");
        String hrDataStr = HttpUtils.sendPost(cmicProperties.getSyncEmpUrl(), paramStr, header);
        if (StringUtils.isValidJson(hrDataStr)) {
            JSONObject jsonObject  = JSON.parseObject(hrDataStr);
            String codekey = "code";
            if (jsonObject.getInteger(codekey) == 0) {
                dataList =jsonObject.getJSONArray("employeeResult");
            }else {
                log.error("{} 请求cmic-portal-user-ability服务code非0，返回：{}",methodName,hrDataStr);
            }
        }else{
            log.error("{} 请求cmic-portal-user-ability服务返回非JSON格式数据，返回：{}",methodName,hrDataStr);
        }
        return  dataList;
    }

    /**
     *  同步组织结构
     * @param reqTimestamp  时间戳
     * @param functionId  调用方法 query_all_orgs
     * @param recordDate  记录日期
     * @return 结果
     * @throws Exception 异常
     */
    @Override
    public JSONArray syncOrg(String reqTimestamp, String functionId, String recordDate) throws Exception {
        JSONArray dataList = new JSONArray();
        String methodName = "调用"+simpleName+".syncOrg()";
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String,Object> param = new HashMap<>(16);
        param.put("isOpen",null);
        param.put("orgType","all");
        String paramStr =objectMapper.writeValueAsString(param);
        Map<String,Object> header = new HashMap<>(16);
        header.put("appId", cmicProperties.getAppid());
        header.put("appSecret", cmicProperties.getAppSecret());
        header.put("functionId", functionId);
        header.put("source", "");
        header.put("Content-Type", "application/json; charset=utf-8");
        header.put("Connection", "Keep-Alive");
        header.put("Accept-Charset", "UTF-8");
        header.put("content-type", "application/json");
        String hrDataStr = HttpUtils.sendPost(cmicProperties.getSyncOrgUrl(), paramStr, header);
        if (StringUtils.isValidJson(hrDataStr)) {
            JSONObject jsonObject  = JSON.parseObject(hrDataStr);
            String codeKey = "code";
            if (jsonObject.getInteger(codeKey) == 0) {
                dataList =jsonObject.getJSONArray("orgResult");
            }else {
                log.error("{} 请求cmic-portal-user-ability服务code非0，返回：{}",methodName,hrDataStr);
            }
        }else{
            log.error("{} 请求cmic-portal-user-ability服务返回非JSON格式数据，返回：{}",methodName,hrDataStr);
        }
        return  dataList;
    }
}
