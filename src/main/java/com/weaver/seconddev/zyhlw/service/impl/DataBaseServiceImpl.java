package com.weaver.seconddev.zyhlw.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.ebuilder.form.client.service.emobile.IEtFormDatasetService;
import com.weaver.framework.rpc.annotation.RpcReference;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.service.IPortalService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import com.weaver.seconddev.zyhlw.util.enums.DsLogicGroupIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DataBaseServiceImpl implements IDataBaseService {

    private String simpleName = DataBaseServiceImpl.class.getSimpleName();

    @Resource
    IDataSqlService dataSqlService;
    @Resource
    IPortalService portalService;
    @Resource
    IOpenPlatformService iOpenPlatformService;
    @RpcReference(group = "ebuilderform")
    IEtFormDatasetService iEtFormDatasetService;
    @Resource
    private CmicProperties cmicProperties;

    private Logger logger = LoggerFactory.getLogger(PortalServiceImpl.class);


    /**
     * @return 获取配置表中uf_basedata_config中有效的配置数据
     */
    @Override
    public List<Map<String, String>> getBaseDataList() {
//        String method = String.format("调用%s.getBaseDataConfigList(%s,%s)-->", SIMPLE_NAME, userId,objId);
//
//        List<Condition> conditions =new ArrayList<>();
//        String resultstr ="";
//        Query query = new Query();
//        Condition condition =new Condition();
//        //是否有效（0：有效，1：禁用）
//        condition.setConditionId("sfyx");
//        condition.setCompareType("eg");
//        condition.setConditionId("0");
//        conditions.add(condition);
//        query.setConditions(conditions);
//        query.setPageNo(1);
//        query.setPageNo(1000);
//        String querystr = Base64.getEncoder().encodeToString(JSONUtil.stringify(query).getBytes());
//        Long temp_BaseData_OjbId=1089784832775815169L;
//        if(!baseData_OjbId.equals(""))
//        {
//            temp_BaseData_OjbId=Long.parseLong(baseData_OjbId);
//        }
//
//        Map<String,Object> datas = iEtFormDatasetService.getDatas(temp_BaseData_OjbId, "", querystr, null);
//        logger.info("{}查询到数据:{}",method, JSONUtil.stringify(datas));
//        if(datas.containsKey("list")) {
//            JSONArray dataArr = JSONArray.fromObject(datas.get("list"));
//            for (int i = 0; i < dataArr.size(); i++) {
//                JSONObject jsonObject = dataArr.getJSONObject(i);
//                resultstr = jsonObject.getString("110003720428841919");
//
//            }
//        }


        List<Map<String, String>> baseDataList = new ArrayList<>();
        String baseDataSql = "select * from uf_basedata_config where sfyx=0 and IS_DELETE='0'";
        String countSql = "select count(*) as sumnum from uf_basedata_config where sfyx=0 and IS_DELETE='0'";
        int pageSize = 1000;
        int totalSize = 0;
        int totalPages = 0;
        try {
            List<Map<String, Object>> sumNumList = dataSqlService.ebuilderFromSql(countSql, 1, 1000, SourceType.LOGIC);
            if (!sumNumList.isEmpty()) {
                totalSize = Integer.parseInt(sumNumList.get(0).get("sumnum").toString());
                totalPages = (int) Math.ceil((double) totalSize / pageSize);
            }
            logger.info("{}.getBaseDataConfigLis()获取uf_basedata_config有效配置数量，页数{}：", simpleName, totalPages);
            for (int i = 1; i <= totalPages; i++) {
                List<Map<String, Object>> dataList = dataSqlService.ebuilderFromSql(baseDataSql, i, pageSize, SourceType.LOGIC);
                for (Map<String, Object> map : dataList) {
                    Map<String, String> dataMap = new HashMap<>();
                    //显示名称
                    String name = map.get("xsmc").toString();
                    //显示名称
                    String value = map.get("xsz").toString();
                    //显示名称
                    String typename = map.get("lxmc").toString();

                    dataMap.put("name", name);
                    dataMap.put("value", value);
                    dataMap.put("typename", typename);
                    baseDataList.add(dataMap);
                }
            }

        } catch (Exception ex) {
            logger.error("{}.getBaseDataConfigList()获取uf_basedata_config有效配置异常：{}", simpleName, ex.getMessage());

        }
        return baseDataList;
    }

    /**
     * 通过名称和类型名称获取配置表中uf_basedata_config中有效的配置数据中显示值
     *
     * @param selectName 配置项名称
     * @param typeName   类型名称
     * @return
     */
    @Override
    public String getBaseDataValue(String selectName, String typeName) {
        //配置的值
        String selectValue = "";
        List<Map<String, String>> baseDataList = new ArrayList<>();
        String baseDataSql = "select * from uf_basedata_config where sfyx=0 and trim(lxmc)=? and trim(xsmc)=? and IS_DELETE='0'";
        List<String> paramsList = new ArrayList<>();
        paramsList.add(typeName.trim());
        paramsList.add(selectName.trim());

        int pageSize = 1000;
        try {
            List<Map<String, Object>> dataList = dataSqlService.ebuilderFromSql(baseDataSql, 1, pageSize, SourceType.LOGIC, paramsList);
            selectValue = !dataList.isEmpty() ? dataList.get(0).get("xsz".toUpperCase()).toString() : "";

        } catch (Exception ex) {
            logger.error("{}.getBaseDataValue({},{})获取配置信息异常，SQL：{},异常原因：{}", simpleName, selectName, typeName, baseDataSql, ex.getMessage());

        }
        return selectValue;
    }

    @Override
    public List<Map<String, Object>> getBaseDataByTypeName(List<String> typeNames) {
        String method = String.format("调用%s.getBaseDataByTypeName()-->", simpleName);
        List<Map<String, Object>> dataList = new ArrayList<>();
        String baseDataSql = "select * from uf_basedata_config where sfyx=0 and lxmc in(%s)  and IS_DELETE='0'";
        String a = typeNames.stream()
                .map(s -> "'" + s.replace("'", "''") + "'") // 转义单引号
                .collect(Collectors.joining(", "));

        String c = String.format(baseDataSql, a);
        logger.info("{}查询SQL：{}", method, c);
        int pageSize = 1000;
        try {
            dataList = dataSqlService.ebuilderFromSql(c, 1, pageSize, SourceType.LOGIC, new ArrayList<>());

        } catch (Exception ex) {
            logger.error("{}获取配置信息异常，SQL：{}", method, baseDataSql, ex);

        }
        return dataList;
    }

    @Override
    public String getBaseValue(String selectName, String typeName) {
        String baseDataSql = "select * from uf_basedata where isdeleted=0 and trim(name_e10)='%s' and trim(typename)='%s' and IS_DELETE='0'";
        baseDataSql = String.format(baseDataSql, selectName, typeName);
        try {
            Map<String, Object> data = dataSqlService.eBuilderFromSqlOne(baseDataSql, SourceType.LOGIC);
            return data.getOrDefault("value", "").toString();
        } catch (Exception ex) {
            logger.error("{}.getBaseValue({},{})获取配置信息异常，SQL：{},异常原因：{}", simpleName, selectName, typeName, baseDataSql, ex.getMessage());
            return "";
        }
    }


    /**
     * 通过数据库表名称，获取数据库表结构中配置的对应表单ID
     *
     * @param tableName 数据库表名称
     * @return
     */
    @Override
    public String getTableFormIdValue(String tableName) {
        //配置的值
        String selectValue = "";
        List<Map<String, String>> baseDataList = new ArrayList<>();
        String configTableSql = "select * from uf_tableinfo_config where sfyx=0 and TRIM(LOWER(sjkbm))='" + tableName.toLowerCase().trim() + "' and IS_DELETE='0'";
        try {
            List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(configTableSql, SourceType.LOGIC);
            selectValue = !dataList.isEmpty() ? dataList.get(0).get("bdid").toString() : "";

        } catch (Exception ex) {
            logger.error("{}.getTableFormIdValue({})获取配置信息异常，SQL：{},异常原因：{}", simpleName, tableName, configTableSql, ex.getMessage());

        }
        return selectValue;
    }

    /**
     * 获取系统分类列表，返回id和名称的map
     *
     * @return
     */
    @Override
    public Map<String, String> getSystemCatagoryMap() {

        Map<String, String> catagoryMap = new HashMap<>();
        List<Map<String, Object>> catagoryList = new ArrayList<>();
        try {
            catagoryList = dataSqlService.ebuilderFromSql("select * from uf_xi_tflwh where zhuang_t=0 and IS_DELETE='0'", 1, 1000, SourceType.LOGIC);
            for (Map<String, Object> map : catagoryList) {
                String id = map.get("id").toString();
                String name = map.get("xi_tfl").toString();
                if (name != null && !name.isEmpty()) {
                    catagoryMap.put(id, name);
                }
            }
        } catch (Exception ex) {
            logger.error("{}.getSystemCatagoryList()获取uf_xi_tflwh集合信息异常：{}", simpleName, ex.getMessage());
        }
        return catagoryMap;
    }


    /**
     * 获取系统分类列表，返回List 集合
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> getSystemCatagoryList() {
        List<Map<String, Object>> catagoryList = new ArrayList<>();
        try {
            catagoryList = dataSqlService.ebuilderFromSql("select * from uf_xi_tflwh where zhuang_t=0 and IS_DELETE='0'", 1, 1000, SourceType.LOGIC);
            // 因返返回的Id值在前端转换为数值类型，需要转换为字符串类型
            catagoryList.forEach(dataItem -> {
                String form_data_id = dataItem.getOrDefault("form_data_id", "").toString();
                dataItem.put("form_data_id", form_data_id);
                String id = dataItem.getOrDefault("id", "").toString();
                dataItem.put("id", id);
            });

            logger.info("{}.getSystemCatagoryList()获取uf_xi_tflw集合信息：{}", simpleName, catagoryList);
        } catch (Exception ex) {
            logger.error("{}.getSystemCatagoryList()获取uf_xi_tflwh集合信息异常：{}", simpleName, ex.getMessage());
        }
        return catagoryList;
    }


    /**
     * 获取指定用户已收藏的应用列表
     *
     * @param employeeId
     * @return
     */
    @Override
    public List<Map<String, Object>> getSystemCollectList(Long employeeId) {
        List<Map<String, Object>> collectList = new ArrayList<>();
        List<String> paramsList = new ArrayList<>();
        paramsList.add(employeeId.toString());
        List<Map<String, Object>> newCollectList = new ArrayList<>();


        try {
            collectList = dataSqlService.ebuilderFromSql("select * from uf_user_collection where yong_xm=? and IS_DELETE='0'", 1, 1000, SourceType.LOGIC, paramsList);
            logger.info("{}.getSystemCollectList()获取uf_user_collection集合信息：{}", simpleName, JSONObject.toJSONString(collectList));
            if (!collectList.isEmpty()) {
                for (Map<String, Object> map : collectList) {
                    Map<String, Object> collectMap = new HashMap<>();
                    String yongXm = map.getOrDefault("yong_xm", "").toString();
                    String checkInSystem = map.getOrDefault("check_insystem", "").toString();
                    String checkDisplayGroup = map.getOrDefault("check_displaygroup", "").toString();
                    collectMap.put("yong_xm", yongXm);
                    collectMap.put("check_insystem", checkInSystem);
                    collectMap.put("check_displaygroup", checkDisplayGroup);
                    if (!"".equals(yongXm)) {
                        newCollectList.add(collectMap);
                    }

                }

            }
            if (newCollectList.isEmpty()) {
                Map<String, Object> collectMap = new HashMap<>();
                collectMap.put("yong_xm", String.valueOf(employeeId));
                collectMap.put("check_insystem", "1");
                collectMap.put("check_displaygroup", "0");
                newCollectList.add(collectMap);
            }
            logger.info("{}.getSystemCollectList({})获取uf_user_collection集合信息：{}", simpleName, employeeId, newCollectList);
        } catch (Exception ex) {
            logger.error("{}.getSystemCollectList({})获取uf_user_collection集合信息异常：{}", simpleName, employeeId, ex.getMessage());
        }
        return newCollectList;
    }


    /**
     * 判断IP地址是否在配置的IP地址范围内
     *
     * @return
     */
    @Override
    public Boolean checkIp(String ipStr) {
        Boolean result = false;
        Map<String, String> catagoryMap = new HashMap<>();
        List<Map<String, Object>> ipList = new ArrayList<>();
        try {
            ipList = dataSqlService.ebuilderFromSql("select * from uf_ip_xzpz where IS_DELETE='0'", 1, 1000, SourceType.LOGIC);
            for (Map<String, Object> map : ipList) {
                String ip_d = map.get("ip_d").toString();
                if (ipStr.indexOf(ip_d) == 0) {
                    result = true;
                    break;
                }
            }
        } catch (Exception ex) {
            logger.error("{}.checkIp()获取uf_ip_xzpz集合信息异常：{}", simpleName, ex.getMessage());
        }
        return result;
    }


    /**
     * 获取提定用户收藏的菜单列表
     *
     * @param employeeId
     * @return
     */
    @Override
    public List<Map<String, Object>> getMenuConfigList(Long employeeId) {

        List<Map<String, Object>> menuList = portalService.getAllSystemList("0");
        List<Map<String, Object>> newMenuList = new ArrayList<>();

        List<String> paramsList = new ArrayList<>();
        paramsList.add(employeeId.toString());
        try {
            menuList = dataSqlService.ebuilderFromSql("select * from uf_menuconfig where loginid=? and IS_DELETE='0'", 1, 1000, SourceType.LOGIC, paramsList);
            logger.info("{}.getMenuConfigList({})获取uf_menuconfig集合信息：{}", simpleName, employeeId, JSONObject.toJSONString(menuList));
            List<Map<String, Object>> finalMenuList = menuList;
            menuList.forEach(dataItem -> {
                Map<String, Object> temp = new HashMap<String, Object>();
                //JSONObject temp = new JSONObject();
                String menuid = StringUtils.null2String(dataItem.get("menuid".toUpperCase()));
                temp.put("id", StringUtils.null2String(dataItem.get("id".toUpperCase())));
                temp.put("menuid", menuid);
                temp.put("createby", StringUtils.null2String(dataItem.get("createby".toUpperCase())));
                temp.put("createdate", StringUtils.null2String(dataItem.get("createdate".toUpperCase())));
                temp.put("loginid", StringUtils.null2String(dataItem.get("loginid".toUpperCase())));
                //ja.add(temp);

                //把系统地址、浏览器类型、认证方式放于集合中
                Map<String, Object> systemMap = getMapBymenuId(menuid, finalMenuList);
                if (systemMap.containsKey("id".toUpperCase())) {
                    temp.put("system_id", systemMap.getOrDefault("id".toUpperCase(), ""));
                    temp.put("liu_lqlx", systemMap.getOrDefault("liu_lqlx".toUpperCase(), ""));
                    temp.put("ren_zfsh", systemMap.getOrDefault("ren_zfsh".toUpperCase(), ""));
                    temp.put("xi_tdz", systemMap.getOrDefault("xi_tdz".toUpperCase(), ""));
                    temp.put("ying_yxzh", systemMap.getOrDefault("ying_yxzh".toUpperCase(), ""));
                } else {
                    temp.put("system_id", "");
                    temp.put("liu_lqlx", "");
                    temp.put("ren_zfsh", "");
                    temp.put("xi_tdz", "");
                    temp.put("ying_yxzh", "");
                }
                newMenuList.add(temp);

                logger.info("{}.getMenuConfigList({})获取newMenuList集合信息：{}", simpleName, employeeId, newMenuList);
            });


        } catch (Exception ex) {
            logger.error("{}.getMenuConfigList({})获取uf_menuconfig集合信息异常：{}", simpleName, employeeId, ex.getMessage());
        }
        return newMenuList;
    }

    /**
     * 通过menuid 获取对应的map 集合
     *
     * @param menuid
     * @return
     */
    private Map<String, Object> getMapBymenuId(String menuid, List<Map<String, Object>> systemList) {

        Map<String, Object> newMap = new HashMap<>();
        for (Map<String, Object> map : systemList) {
            String temp_menuid = map.get("menuid".toUpperCase()) != null ? String.valueOf(map.get("menuid".toUpperCase())).trim() : "";
            if (temp_menuid.equals(menuid.trim())) {
                newMap = map;
                break;
            }


        }
        return newMap;

    }

    public Map<String, Object> getEmployeeInfo(String userId) {
        String sql = "SELECT *\r\n" + //
                "FROM (\r\n" + //
                "\tSELECT employee.*, user_info_login_param.login_value\r\n" + //
                "\tFROM eteams.employee\r\n" + //
                "\t\tLEFT JOIN user_info_login_param\r\n" + //
                "\t\tON user_info_login_param.login_type = ?\r\n" + //
                "\t\t\tAND employee.user_id = user_info_login_param.user_id\r\n" + //
                "\t\t\tAND employee.delete_type = ?\r\n" + //
                "\t\t\tAND employee.tenant_key = ?\r\n" + //
                "\t\t\tAND user_info_login_param.delete_type = ?\r\n" + //
                "\t\t\tAND user_info_login_param.tenant_key = ?\r\n" + //
                ") hrmresource\r\n" + //
                "WHERE hrmresource.id = ?\r\n" + //
                "\tAND hrmresource.delete_type = ?\r\n" + //
                "\tAND hrmresource.tenant_key = ?";
        List<String> sqlParams = new ArrayList<>();
        sqlParams.add("loginName");
        sqlParams.add("0");
        sqlParams.add(cmicProperties.getHostTenantKey());
        sqlParams.add("0");
        sqlParams.add(cmicProperties.getHostTenantKey());
        sqlParams.add(userId);
        sqlParams.add("0");
        sqlParams.add(cmicProperties.getHostTenantKey());
        return dataSqlService.executeCommonSqlOne(
                sql, SourceType.LOGIC, DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId(), sqlParams);
    }

}
