package com.weaver.seconddev.zyhlw.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.seconddev.zyhlw.domain.formdata.FormDataListReqDTO;

/**
 * 表单数据相关操作接口
 *
 * <AUTHOR>
 * @date 2025/5/6 15:24
 */
public interface FormDataService {
    /***
     * 获取表单数据
     *
     * <AUTHOR>
     * @date 2025/5/6 15:51
     * @param reqDTO  请求入参
     * @return com.alibaba.fastjson.JSONObject
     */
    WeaResult<JSONObject> getFormDataList(FormDataListReqDTO reqDTO);
}
