package com.weaver.seconddev.zyhlw.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.weaver.openapi.pojo.dept.res.DeptInfo;
import com.weaver.openapi.pojo.dept.res.DeptVo;
import com.weaver.openapi.pojo.user.res.vo.UserInfoResult;
import com.weaver.openapi.service.DeptService;
import com.weaver.seconddev.zyhlw.domain.hrm.GetDepartmentNameByStaffIdRespVO;
import com.weaver.seconddev.zyhlw.domain.portal.DepartmentModel;
import com.weaver.seconddev.zyhlw.service.IHrmDepartmentService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class HrmDepartmentServiceImpl implements IHrmDepartmentService {
    private final String simpleName = DataBaseServiceImpl.class.getSimpleName();

    @Resource
    private IOpenPlatformService openPlatformService;
    @Resource
    CmicProperties cmicProperties;


    /**
     * 根据部门id获取该部门id下面提有子部门集合
     *
     * @param departId
     * @return
     */
    @Override
    public List<DepartmentModel> getChildDepartListById(String departId, List<DepartmentModel> allDepartmentList) {

        List<DepartmentModel> selectList = new ArrayList<>();
        List<DepartmentModel> childList = getDepartmentListByParentId(allDepartmentList, allDepartmentList.stream().filter(e -> StringUtils.null2String(e.getParent()).equals(departId)).collect(Collectors.toList()), selectList);
        selectList.addAll(childList);
        log.info("调用getChildDepartListById方法，departId：{}，childList：{}", departId, JSONObject.toJSONString(childList));
        //递归获取所有子节点
        getDepartmentListByParentId(allDepartmentList, childList, selectList);
        return selectList;

    }

    /**
     * 通过递归的方式，把当前节点的所有子节点都添加到selectList中
     *
     * @param allDepartmentList 所有部门的信息
     * @param childDepartList   该节点下面的子节点信息
     * @param selectList        需要返回的集合
     * @return
     */
    private List<DepartmentModel> getDepartmentListByParentId(List<DepartmentModel> allDepartmentList, List<DepartmentModel> childDepartList, List<DepartmentModel> selectList) {
        try {
            for (DepartmentModel departmentModel : childDepartList) {
                log.info("getDepartmentListByParentId方法中获取departmentModel：{}", JSONObject.toJSONString(departmentModel));
                List<DepartmentModel> childList = getDepartmentListByParentId(allDepartmentList, allDepartmentList.stream().filter(e -> StringUtils.null2String(e.getParent()).equals(departmentModel.getId())).collect(Collectors.toList()), selectList);
                if (!childList.isEmpty()) {
                    log.info("getDepartmentListByParentId方法中获取childList：{}", JSONObject.toJSONString(childList));
                    //如果二级部门集合不为空则添加到待返加的集合中，再1
                    selectList.addAll(childList);
                    getDepartmentListByParentId(allDepartmentList, childList, selectList);
                }
            }
            log.info("getDepartmentListByParentId方法中获取selectList：{}", JSONObject.toJSONString(selectList));
        } catch (Exception ex) {
            log.info("getDepartmentListByParentId方法中获取selectList异常，selectList：{}，原因：{}：", JSONObject.toJSONString(selectList), ex);
        }
        return selectList;

    }

    /**
     * 获取组织架构信息（只获取有效的部门）
     *
     * @return
     */
    @Override
    public List<DepartmentModel> getAllDepartmentList() {
        String method = String.format("调用%s.getDepartmentList()-->", simpleName);
        List<DepartmentModel> departmentList = new ArrayList<>();

        try {
            log.info("调用getAllDepartmentList方法，cmicProperties：{}", JSONObject.toJSONString(cmicProperties));
            String TENANT_KEY = cmicProperties.getHostTenantKey();
            log.info("调用getAllDepartmentList方法，TENANT_KEY：{}", TENANT_KEY);

            int pageSize = 1000;
            int totalSize = 0;
            int totalPages = 0;

            JSONObject restfulQueryOrg = openPlatformService.restfulQueryOrg(true, true, true, null, 1, 1000, TENANT_KEY);
            log.info("调用getAllDepartmentList方法，restfulQueryOrg：{}", JSONObject.toJSONString(restfulQueryOrg));


            //  JSONObject dataJson = restfulQueryOrg.getJSONObject("data");
            totalSize = restfulQueryOrg.getInteger("total");
            JSONArray jsonArray = restfulQueryOrg.getJSONArray("data");
            for (int i = 0; i < jsonArray.size(); i++) {
                DepartmentModel departmentModel = jsonArray.getJSONObject(i).toJavaObject(DepartmentModel.class);
                if (!departmentModel.getIs_delete() && departmentModel.getStatus()) {
                    //只添加有效的部门
                    departmentList.add(departmentModel);
                }
            }
            totalPages = (int) Math.ceil((double) totalSize / pageSize);
            for (int i = 2; i <= totalPages; i++) {
                JSONObject restfulQueryOrg2 = openPlatformService.restfulQueryOrg(true, true, true, null, i, 1000, TENANT_KEY);
                JSONArray jsonArray2 = restfulQueryOrg2.getJSONArray("data");
                for (int j = 0; j < jsonArray2.size(); j++) {
                    DepartmentModel departmentModel = jsonArray2.getJSONObject(j).toJavaObject(DepartmentModel.class);
                    if (!departmentModel.getIs_delete() && departmentModel.getStatus()) {
                        //只添加有效的部门
                        departmentList.add(departmentModel);
                    }
                }
            }

        } catch (Exception e) {
            log.info("{} 调用getAllDepartmentList方法，原因：{}", method, e.getMessage());
        }
        log.info("调用getAllDepartmentList方法，departmentList：{}", JSONObject.toJSONString(departmentList));
        return departmentList;
    }

    /**
     * 根据id数组查询人员部门科室名称及id
     *
     * @param ids 人员id组，多个以逗号分隔
     * @return 包含人员部门信息的响应对象
     */
    @Override
    public GetDepartmentNameByStaffIdRespVO getDepartmentNameByStaffId(String ids) {
        log.info("ids: {}", ids);
        String[] split = ids.split(",");
        List<Map<String, String>> list = new ArrayList<>();
        try {
            for (String id : split) {
                // 使用API查询用户信息
                UserInfoResult user = openPlatformService.getUser(Long.parseLong(id));
                if (user == null || user.getUserid() == null) {
                    continue;
                }

                Map<String, String> map = new HashMap<>();
                String lastname = user.getName(); // 用户名称

                // 获取用户部门ID列表
                List<Long> departmentIds = user.getDepartment();
                if (departmentIds == null || departmentIds.isEmpty()) {
                    continue;
                }

                // 获取用户部门第一个id TODO 原E9这里获取到departmentId是单个，E10返回的是数组，先取第一个，有问题再处理
                Long departmentId = departmentIds.get(0);

                // 使用DeptService.getDeptV2查询部门
                DeptVo deptVo = DeptService.getDeptV2(openPlatformService.getAccessToken(),
                        departmentId, cmicProperties.getOpenPlatformUrl(), null);

                DeptInfo parent = deptVo.getDepartment().getParent();
                String sectionsName = null;
                String sectionsId = null;
                if (parent != null) {
                    sectionsName = parent.getName();
                    sectionsId = parent.getId();
                }

                map.put("id", id); // 人员id
                map.put("lastname", lastname); // 人员名称
                map.put("departmentId", departmentId.toString()); // 部门id
                map.put("departmentName", deptVo.getDepartment().getName()); // 部门名称
                map.put("sectionsName", sectionsName); // 科室名称
                map.put("sectionsId", sectionsId); // 科室id
                list.add(map);
            }

            log.info("查询到的数据: {}", list);
            return GetDepartmentNameByStaffIdRespVO.builder()
                    .data(list).code(0).msg("查询成功")
                    .build();
        } catch (Exception e) {
            log.error("错误信息: {}", e.getMessage(), e);
            return GetDepartmentNameByStaffIdRespVO.builder()
                    .data(list).code(-1).msg(e.getMessage())
                    .build();
        }
    }
}
