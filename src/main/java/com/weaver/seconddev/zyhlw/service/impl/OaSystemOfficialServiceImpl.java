package com.weaver.seconddev.zyhlw.service.impl;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.weaver.ebuilder.common.util.StringUtil;
import com.weaver.openapi.pojo.MessageResult;
import com.weaver.openapi.pojo.file.params.FileVo;
import com.weaver.openapi.pojo.file.res.FileResultVo;
import com.weaver.openapi.pojo.user.params.UserVo;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.openapi.pojo.user.res.vo.UserParam;
import com.weaver.openapi.service.FileService;
import com.weaver.seconddev.zyhlw.domain.oa.ApprovalInfo;
import com.weaver.seconddev.zyhlw.domain.oa.EnclosureInfo;
import com.weaver.seconddev.zyhlw.domain.oa.OaAnnouncementInfo;
import com.weaver.seconddev.zyhlw.domain.oa.SystemPost;
import com.weaver.seconddev.zyhlw.domain.request.eb.EbFormDataReq;
import com.weaver.seconddev.zyhlw.domain.response.form.EbFormDataResultVo;
import com.weaver.seconddev.zyhlw.exception.CustomException;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.service.OaSystemOfficialService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.ebuilder.EBuilderUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.util.*;

/**
 * OA公文推送服务实现类
 *
 * @date 2025-01-20
 */
@Service
@Slf4j
public class OaSystemOfficialServiceImpl implements OaSystemOfficialService {

    private final String simpleName = OaSystemOfficialServiceImpl.class.getSimpleName();
    @Resource
    IDataSqlService dataSqlService;
    @Resource
    IOpenPlatformService openPlatformService;
    @Resource
    CmicProperties cmicProperties;

    @Override
    public Map<String, Object> getOfficialDocumentList(OaAnnouncementInfo oaAnnouncementInfo) {
        String methodName = simpleName + ".getOfficialDocumentList";
        Map<String, Object> resultMap = new HashMap<>();
        String title = oaAnnouncementInfo.getTitle();
        String postNo = oaAnnouncementInfo.getPostNo();
        String startTime = oaAnnouncementInfo.getStartTime();
        String initiator = oaAnnouncementInfo.getInitiator();
        String departName = oaAnnouncementInfo.getDepartName();
        String endTime = oaAnnouncementInfo.getEndTime();
        List<EnclosureInfo> enclosureInfos = oaAnnouncementInfo.getEnclosureInfos();
        log.info("{} 入参：{};{};{};{};{};{};", methodName, title, postNo, startTime, initiator, departName, endTime);
        if (StringUtils.isBlank(title) || StringUtils.isBlank(postNo) || StringUtils.isBlank(startTime) || StringUtils.isBlank(initiator) || StringUtils.isBlank(departName) || StringUtils.isBlank(endTime)) {
            log.error("调用{} 参数不能为空", methodName);
            resultMap.put("code", 500);
            resultMap.put("msg", "参数不能为空");
            return resultMap;
        }
        String sql = "select id, ton_bzt from uf_gon_gxXjl where id = (select max(id) from uf_gon_gxXjl where gon_gbh = ? and ton_bzt =0 and is_delete = 0)";
        try {
            UserVo userVo = new UserVo();
            userVo.setNeedAccountInfo(true);
            String accessToken = openPlatformService.getAccessToken();
            List<EmployeeData> employeeDataList = queryEmployeeAll(userVo, accessToken);
            String userId = employeeDataList.stream().filter(employeeData ->
                            employeeData.getLoginid() != null && employeeData.getLoginid().equals(initiator) && employeeData.getId() != null)
                    .map(EmployeeData::getId)
                    .findFirst().orElse("");
            log.info("调用{} 根据loginId查询的用户id为：{}", methodName, userId);
            List<String> paramsList = new ArrayList<>();
            paramsList.add(postNo);
            List<Map<String, Object>> result = dataSqlService.ebuilderFromSql(sql, 1, 1, SourceType.LOGIC, paramsList);
            String objId = getBaseDataValue("uf_gon_gxXjl".toLowerCase(), "objId");
            log.info("调用{} 查询OA公文推送表单id：{}", methodName, objId);
            log.info("调用{} 查询OA公文推送详情数据：{}", methodName, result);
            String reason = "";
            String status = "";
            boolean flag = true;
            if (result != null && !result.isEmpty()) {
                reason = "OA系统公告信息已上传成功,请勿重复上传";
                status = "1";
                flag = false;
            }
            List<Map<String, Object>> dataList = new ArrayList<>();
            Map<String, Object> data = new HashMap<>();
            Map<String, Object> mainTable = new HashMap<>();
            mainTable.put("ton_bzt", status);
            mainTable.put("gon_gbh", postNo);
            mainTable.put("gon_departName".toLowerCase(), departName);
            mainTable.put("gon_endTime".toLowerCase(), endTime);
            mainTable.put("gon_gbt", title);
            mainTable.put("gon_gfQsj".toLowerCase(), startTime);
            mainTable.put("gon_gfQr".toLowerCase(), userId);
            mainTable.put("yuan_y", reason);
            data.put("mainTable", mainTable);
            dataList.add(data);
            EbFormDataResultVo ebFormDataResultVo = EBuilderUtil.saveFormDataV2(cmicProperties.getOpenPlatformUrl(), Long.valueOf(userId), objId, dataList, accessToken);
            String dataId = ebFormDataResultVo.getData().getDataJson().get(0).getDataIds().get(0);
            log.info("调用{} 保存表单数据返回Id：{}", methodName, dataId);
            if (enclosureInfos != null && !enclosureInfos.isEmpty()) {
                if (flag) {

                }
            } else {
                reason = "附件信息为空";
                log.info("调用{} 保存表单数据返回Id：{}", methodName, dataId);
                dataList.clear();
                mainTable.clear();
                mainTable.put("ton_bzt", "1");
                mainTable.put("yuan_y", reason);
                mainTable.put("id", dataId);
                data.put("mainTable", mainTable);
                dataList.add(data);
                EbFormDataReq ebFormDataReq = new EbFormDataReq.Builder().objId(objId).userId(userId).datas(dataList).build();
                EbFormDataResultVo ebFormDataResultVo1 = EBuilderUtil.updateFormDataV2(ebFormDataReq, accessToken, cmicProperties.getOpenPlatformUrl());
                log.info("调用{} 更新表单数据返回Id：{}", methodName, ebFormDataResultVo1.getData().getDataJson().get(0).getDataIds());
                resultMap.put("code", 1);
                resultMap.put("msg", reason);
            }
            downloadOfficialDocument(enclosureInfos, accessToken, Long.parseLong(userId), dataId);
            return resultMap;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("调用{} 查询OA公文推送详情数据异常：{}；{}", methodName, e.getMessage(), e);
            resultMap.put("code", 1);
            resultMap.put("msg", e.getMessage());
        }
        return resultMap;
    }

    @Override
    public Map<String, Object> saveSystemPost(SystemPost systemPost) {
        String methodName = simpleName + ".saveSystemPost";
        String title = systemPost.getTitle();
        String postNo = systemPost.getPostNo().trim();
        String startTime = systemPost.getStartTime();
        Map<String, Object> map = new HashMap<>();
        String sql = "select max(id) as id from uf_zhi_dNrb where ltrim(rtrim(gong_sfWh))= ?  and fa_bzt = 1 and is_delete = 0";
        try {
            List<Map<String, Object>> result = dataSqlService.ebuilderFromSql(sql, 1, 1, SourceType.LOGIC, Collections.singletonList(postNo));
            if (result != null && !result.isEmpty()) {
                map.put("code", "0");
                String msg = "接收成功(公司发文号:" + postNo + "在制度内容表中状态为已发布，不进行更新操作)";
                map.put("msg", msg);
            } else {
                UserVo userVo = new UserVo();
                userVo.setNeedAccountInfo(true);
                String accessToken = openPlatformService.getAccessToken();
                List<EmployeeData> employeeDataList = queryEmployeeAll(userVo, accessToken);
                String loginId = getBaseDataValue("制度管理发起人(OA工单)", "制度管理");
                String objId = getBaseDataValue("制度管理发起人(OA工单)", "制度管理");
                if (StringUtil.isNull(loginId)) {
                    loginId = "zhaozebin";
                }
                String finalLoginId = loginId;
                String userId = employeeDataList.stream().filter(employeeData ->
                                employeeData.getLoginid() != null && employeeData.getLoginid().equals(finalLoginId) && employeeData.getId() != null)
                        .map(EmployeeData::getId)
                        .findFirst().orElse("");
                List<EnclosureInfo> enclosureInfos = systemPost.getEnclosureInfos();
                List<Map<String, Object>> detail2List = new ArrayList<>();
                if (enclosureInfos != null && !enclosureInfos.isEmpty()) {
                    for (EnclosureInfo enclosureInfo : enclosureInfos) {
                        Map<String, Object> detail = new HashMap<>();
                        detail.put("name", enclosureInfo.getName());
                        detail.put("path", enclosureInfo.getPath());
                        detail.put("type", enclosureInfo.getType());
                        detail2List.add(detail);
                    }
                }
                List<ApprovalInfo> approvalInfos = systemPost.getApprovalInfos();
                List<Map<String, Object>> detail4List = new ArrayList<>();
                for (ApprovalInfo approvalInfo : approvalInfos) {
                    Map<String, Object> detail = new HashMap<>();
                    detail.put("shen_psj", approvalInfo.getApprovalTime());
                    detail.put("chu_lr1", approvalInfo.getHandler());
                    detail.put("shen_pjg", approvalInfo.getResult());
                    detail.put("jie_dmc", approvalInfo.getNodeName());
                    detail.put("dao_dsj", approvalInfo.getArriveTime());
                    detail4List.add(detail);
                }
                String sqlSelect = "select max(id) as id from uf_zhi_dnRb where ltrim(rtrim(gong_sfWh)) = ? ";
                List<Map<String, Object>> maps = dataSqlService.ebuilderFromSql(sqlSelect, 1, 1, SourceType.LOGIC, Collections.singletonList(postNo));
                if (maps != null && !maps.isEmpty()) {
                    if (maps.get(0).get("ID") != null) {
                        String id = maps.get(0).get("ID").toString();
                        Map<String, Object> data = new HashMap<>();
                        data.put("id", id);
                        data.put("fa_bsj", startTime);
                        data.put("gong_sfWh".toLowerCase(), postNo);
                        data.put("zhi_dmc", title);
                        Map<String, Object> dataS = new HashMap<>();
                        dataS.put("mainTable", data);
                        dataS.put("detail2", detail2List);
                        dataS.put("detail4", detail4List);
                        List<Map<String, Object>> updateData = new ArrayList<>();
                        updateData.add(dataS);
                        Map<String, String> operateType = new HashMap<>();
                        operateType.put("detail2", "cover");
                        operateType.put("detail4", "cover");
                        EbFormDataReq updatePolicy = new EbFormDataReq.Builder().userId(userId).objId(objId).datas(updateData).operateType(operateType).build();
                        EbFormDataResultVo resultVo = EBuilderUtil.updateFormDataV2(updatePolicy, accessToken, cmicProperties.getOpenPlatformUrl());
                        if (Long.parseLong(resultVo.getData().getDataJson().get(0).getDataIds().get(0)) > 0) {
                            map.put("code", "0");
                            String msg = "更新成功(公司发文号:" + postNo + "在制度内容表中状态为已发布，不进行更新操作)";
                        }
                    }
                } else {
                    Map<String, Object> mainTable = new HashMap<>();
                    mainTable.put("gong_sfWh".toLowerCase(), postNo);
                    mainTable.put("zhi_dmc", title);
                    mainTable.put("fa_bsj", startTime);
                    Map<String, Object> data = new HashMap<>();
                    data.put("mainTable", mainTable);
                    data.put("detail2", detail2List);
                    data.put("detail4", detail4List);
                    List<Map<String, Object>> insertData = new ArrayList<>();
                    insertData.add(data);
                    EbFormDataResultVo ebFormDataResultVo = EBuilderUtil.saveFormDataV2(cmicProperties.getOpenPlatformUrl(), Long.parseLong(userId), objId, insertData, accessToken);
                    long dataId = Long.parseLong(ebFormDataResultVo.getData().getDataJson().get(0).getDataIds().get(0));
                    if (dataId > 0) {
                        log.info("调用{} 插入OA公文推送详情数据成功：{}", methodName, ebFormDataResultVo);
                        map.put("code", "0");
                        map.put("msg", "插入OA公文推送详情数据成功");
                    } else {
                        log.error("调用{} 插入OA公文推送详情数据失败：{}", methodName, ebFormDataResultVo);
                        map.put("code", "1");
                        map.put("msg", "插入OA公文推送详情数据失败");
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("调用{} 查询OA公文推送详情数据异常：{}；{}", methodName, e.getMessage(), e);
            map.put("code", "1");
            map.put("msg", "查询OA公文推送详情数据异常:" + e.getMessage());
        }
        return map;
    }

    public String getBaseDataValue(String name, String type) {
        List<String> paramsList1 = new ArrayList<>();
        paramsList1.add(name);
        paramsList1.add(type);
        String objIdSql = "select value from uf_baseData where name_e10 = ? and typename = ? and is_delete = 0";
        try {
            List<Map<String, Object>> result = dataSqlService.ebuilderFromSql(objIdSql, 1, 1, SourceType.LOGIC, paramsList1);
            log.info("调用getBaseDataValue 查询eBuilder：{}", result);
            for (Map<String, Object> map : result) {
                log.info("调用getBaseDataValue 查询eBuilder值：{}", map.get("VALUE"));
                return map.get("VALUE").toString();
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("调用getBaseDataValue 查询OA公文推送详情数据异常：{}；{}", e.getMessage(), e);
        }
        return "";
    }

    void downloadOfficialDocument(List<EnclosureInfo> enclosureInfos, String accessToken, Long userId, String dataId) {
//        for (EnclosureInfo enclosureInfo : enclosureInfos) {
//            String type = enclosureInfo.getType();
//            String name = enclosureInfo.getName();
//            String path = enclosureInfo.getPath();
        String filePath = System.getProperties().getProperty("user.dir");

        String fileFolder = filePath + File.separatorChar + "filesystem"
                + File.separatorChar + "downloadBatchTemp" + File.separatorChar;
        File folder = new File(fileFolder);
        if (!folder.exists()) {
            folder.mkdirs();
        }
        File file = new File(fileFolder + "test2025.docx");
        try {
            if (file.exists()) {
                file.delete();
            }
            if (!file.exists()) {
                file.createNewFile();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        String s = "123456";

        try (FileOutputStream fos = new FileOutputStream(file)) {
            fos.write(s.getBytes());
            fos.flush();
            fos.close();
            log.info("文件保存成功");
        } catch (IOException e) {
            log.error("文件保存异常：{}", e.getMessage());
            e.printStackTrace();
        }
        FileVo fileVo = new FileVo();
        fileVo.setAccessToken(accessToken);
        fileVo.setName(file.getName());
        fileVo.setFile(file);
        fileVo.setUserid(userId);
        fileVo.setCreateDocument(true);
        FileResultVo normalRes = FileService.uploadFileV2(fileVo, cmicProperties.getOpenPlatformUrl(), null);
        log.info("normalRes:{}", JSON.toJSONString(normalRes));
//        }
    }


    public List<EmployeeData> queryEmployeeAll(UserVo userVo, String accessToken) {
        String method = String.format("调用%s.queryEmployeeAll-->", simpleName);
        List<EmployeeData> dataList = new ArrayList<>();
        if (StringUtils.isBlank(accessToken)) {
            throw new CustomException("获取accessToken失败", -1);
        }
        userVo.setAccessToken(accessToken);
        int pageSize = 500;
        int pageNumber = 1;
        while (true) {
            userVo.setCurrent(pageNumber);
            userVo.setPageSize(pageSize);
            try {
                QueryEmployeeResultVo resultVo = queryEmployee(userVo);
                if (resultVo == null || resultVo.getData() == null) {
                    log.warn("{}查询，当前页：{}，返回结果为空", method, pageNumber);
                    break;
                }
                List<EmployeeData> pageDataList = resultVo.getData().getData();
                if (pageDataList == null || pageDataList.isEmpty()) {
                    break;
                }
                dataList.addAll(pageDataList);
                long total = resultVo.getData().getTotal();
                if (dataList.size() >= total) {
                    break;
                }
                pageNumber++;
            } catch (Exception e) {
                log.error("{}查询，当前页：{}，发生异常", method, pageNumber, e);
                throw new CustomException(e);
            }
        }
        log.info("{}查询完成，共获取到{}条数据", method, dataList.size());
        return dataList;
    }

    private QueryEmployeeResultVo queryEmployee(UserVo userVo) {
        String methodName = simpleName + ".queryEmployee";
        log.info("{} 请求人员查询-高级搜索...", methodName);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("access_token", userVo.getAccessToken());
        paramMap.put("jobNumList", userVo.getJobNumList());
        paramMap.put("subcompanyIds", userVo.getSubcompanyIds());
        paramMap.put("departmentIds", userVo.getDepartmentIds());
        paramMap.put("ids", userVo.getIds());
        paramMap.put("containUserInfo", userVo.isContainUserInfo());
        paramMap.put("containEmployeeExtend", userVo.isContainEmployeeExtend());
        paramMap.put("containUserInfoExtend", userVo.isContainUserInfoExtend());
        paramMap.put("current", userVo.getCurrent());
        paramMap.put("pageSize", userVo.getPageSize());
        paramMap.put("userStatusList", userVo.getUserStatusList());
        paramMap.put("nameLikeList", userVo.getNameLikeList());
        paramMap.put("returnFieldList", userVo.getReturnFieldList());
        paramMap.put("multiFieldList", userVo.getMultiFieldList());
        paramMap.put("account", userVo.getAccount());
        paramMap.put("needAccountInfo", userVo.isNeedAccountInfo());
        log.info("queryEmployee请求参数：{}", JSONObject.toJSONString(paramMap));
        String resJson = HttpRequest.post(cmicProperties.getOpenPlatformUrl() + "/api/hrm/restful/queryEmployee").body(JSONObject.toJSONString(paramMap)).headerMap(null, true).execute().body();
        return JSONObject.parseObject(resJson, QueryEmployeeResultVo.class);
    }

    @Data
    @EqualsAndHashCode(callSuper = false)
    static class QueryEmployeeResultVo extends MessageResult {
        private QueryEmployeeData data;
    }

    @Data
    static class QueryEmployeeData implements Serializable {
        private Integer current;
        private Long total;
        private List<EmployeeData> data;
        private Integer pageSize;
    }

    @Data
    static class EmployeeData implements Serializable {
        private UserParam education;
        private String accumfundaccount;
        private UserParam nation;
        private String residence_place;
        private String modifier;
        private String active_date;
        private String type;
        private String bankid;
        private String id_no;
        private String bank_accountname;
        private String id;
        private String bank_account;
        private String created;
        private UserParam degree;
        private String first_work_date;
        private Integer work_year;
        private String formdata;
        private String login_status;
        private String native_place;
        private String last_update_time;
        private String pinyin;
        private String user_id;
        private String child_status;
        private String birthday;
        private String tenant_key;
        private String family_contact;
        private Integer sec_level;
        private String update_time;
        private String job_num;
        private String department;
        private String logogram;
        private String personnel_status;
        private String im_uid;
        private UserParam household_type;
        private String graduate_school;
        private String marital_status;
        private UserParam politics_status;
        private Integer age;
        private String username;
        private String loginid;
    }
}
