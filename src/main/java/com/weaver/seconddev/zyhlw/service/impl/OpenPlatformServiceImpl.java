package com.weaver.seconddev.zyhlw.service.impl;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.weaver.common.cache.base.BaseCache;
import com.weaver.framework.rpc.annotation.RpcReference;
import com.weaver.openapi.pojo.NormalRes;
import com.weaver.openapi.pojo.NormalResult;
import com.weaver.openapi.pojo.auth.params.AccessTokenParam;
import com.weaver.openapi.pojo.auth.params.CodeParam;
import com.weaver.openapi.pojo.auth.res.AccessToken;
import com.weaver.openapi.pojo.auth.res.Code;
import com.weaver.openapi.pojo.basicserver.params.BasicVo;
import com.weaver.openapi.pojo.basicserver.res.BasicResultVo;
import com.weaver.openapi.pojo.basicserver.res.vo.AccountVo;
import com.weaver.openapi.pojo.dept.params.DeptListParam;
import com.weaver.openapi.pojo.dept.res.DeptInfo;
import com.weaver.openapi.pojo.dept.res.DeptList;
import com.weaver.openapi.pojo.eb.params.form.EbFormVo;
import com.weaver.openapi.pojo.eb.res.form.EbFormResultVo;
import com.weaver.openapi.pojo.flow.params.FlowCondition;
import com.weaver.openapi.pojo.flow.params.FlowVo;
import com.weaver.openapi.pojo.flow.params.UnOperatorParam;
import com.weaver.openapi.pojo.flow.res.*;
import com.weaver.openapi.pojo.flow.res.vo.*;
import com.weaver.openapi.pojo.user.params.UserVo;
import com.weaver.openapi.pojo.user.res.QueryEmployeeResultVo;
import com.weaver.openapi.pojo.user.res.UserResultVo;
import com.weaver.openapi.pojo.user.res.vo.EmployeeData;
import com.weaver.openapi.pojo.user.res.vo.UserInfoResult;
import com.weaver.openapi.pojo.user.res.vo.UserResult;
import com.weaver.openapi.service.*;
import com.weaver.seconddev.zyhlw.domain.response.todo.FlowLastOperatorResponse;
import com.weaver.seconddev.zyhlw.domain.response.todo.FlowListResultResponse;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.DateUtils;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import com.weaver.seconddev.zyhlw.util.cache.CacheKey;
import com.weaver.seconddev.zyhlw.util.cache.CacheModuleKey;
import com.weaver.seconddev.zyhlw.util.http.HostUtil;
import com.weaver.seconddev.zyhlw.util.http.HttpUtils;
import com.weaver.teams.core.orm.mybatis.Page;
import com.weaver.teams.domain.comment.Comment;
import com.weaver.teams.domain.user.SimpleEmployee;
import com.weaver.workflow.core.api.rest.flow.WfcRequestCommonRest;
import com.weaver.workflow.core.api.rest.publicapi.WfcGetRequestDataRest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 请求开放平台业务类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OpenPlatformServiceImpl implements IOpenPlatformService {
    @Resource
    BaseCache baseCache;
    @Resource
    CmicProperties cmicProperties;
    String simpleName = OpenPlatformServiceImpl.class.getSimpleName();
    String tenantKeylab = "tenantKey";

    String contentTypeLab = "Content-Type";

    String contentTypeVal = "application/json";
    String messageKey = "message";
    String dataRuleKey = "dataRule";
    String statusKey = "status";
    String errcodeKey = "errcode";
    String successKey = "success";

    String accessTokenKey = "access_token";

    /**
     * 获取access_token 通过缓存机制保存，有效或者不存在重新获取并保存缓存
     *
     * @return access_token
     */
    @Override
    public String getAccessToken() {
        String methodName = "调用" + simpleName + ".getAccessToken()";
        String accessToken = null;
        boolean baseCacheFlag = true;
        // 需要重新获取access_token
        boolean flag = true;
        if (baseCache == null) {
            baseCacheFlag = false;
            flag = false;
        } else {
            boolean containsKey = baseCache.containsKey(CacheModuleKey.ACCESS_MODULE, CacheKey.ACCESS_TOKEN_KEY);
            if (containsKey) {
                accessToken = (String) baseCache.get(CacheModuleKey.ACCESS_MODULE, CacheKey.ACCESS_TOKEN_KEY);
            } else {
                flag = false;
            }
        }
        if (!flag) {
            CodeParam codeParam = new CodeParam(cmicProperties.getCorpid(), "code", UUID.randomUUID().toString().replace("-", ""));
            Code code = AuthService.getAuthCode(codeParam, cmicProperties.getOpenPlatformUrl(), null);
            AccessTokenParam tokenParam = new AccessTokenParam(cmicProperties.getDefaultAppKey(), cmicProperties.getDefaultAppSecret(), "authorization_code", code.getCode());
            AccessToken authAccessToken = AuthService.getAuthAccessToken(tokenParam, cmicProperties.getOpenPlatformUrl(), null);
            accessToken = authAccessToken.getAccessToken();
            if (baseCacheFlag) {
                boolean success = baseCache.set(CacheModuleKey.ACCESS_MODULE, CacheKey.ACCESS_TOKEN_KEY, authAccessToken.getAccessToken(), authAccessToken.getExpiresIn());
                if (success) {
                    log.info("{} access_token写入缓存成功：{}", methodName, authAccessToken.getAccessToken());
                } else {
                    log.error("{} access_token写入缓存失败：{}", methodName, authAccessToken.getAccessToken());
                }
            }
        }
        return accessToken;
    }

    /**
     * 请求开放平台 部门自定义字段信息接口
     *
     * @param tenantKey 团队key
     * @return 结果
     */
    @Override
    public JSONArray listDepartmentField(String tenantKey) {
        String methodName = "调用" + simpleName + ".listDepartmentField()";
        JSONArray dataJsonArr = new JSONArray();
        Map<String, Object> header = new HashMap<>(16);
        header.put(tenantKeylab, tenantKey);
        header.put(contentTypeLab, contentTypeVal);
        String url = cmicProperties.getOpenPlatformUrl() + "/api/hrm/restful/listDepartmentField";
        String accessToken = getAccessToken();
        if (accessToken != null) {
            url += "?" + accessTokenKey + "=" + accessToken;
        }
        String fileIdData = HttpUtils.sendPost(url, new JSONObject().toString(), header);
        if (fileIdData != null && !"".equals(fileIdData) && StringUtils.isValidJson(fileIdData)) {
            JSONObject fileIdDataJson = JSON.parseObject(fileIdData);

            if (!fileIdDataJson.containsKey(messageKey)) {
                log.error("{} 解析开放平台接口结果异常，JSON格式错误：{}", methodName, fileIdData);
            } else {
                JSONObject messageJson = fileIdDataJson.getJSONObject(messageKey);
                String successCode = "0";
                if (successCode.equals(messageJson.getString(errcodeKey))) {
                    dataJsonArr = fileIdDataJson.containsKey("data") ? fileIdDataJson.getJSONArray("data") : new JSONArray();
                }
            }
        } else {
            log.error("{} 解析开放平台接口结果异常，非JSON格式：{}", methodName, fileIdData);
        }
        return dataJsonArr;
    }

    /**
     * 请求开放平台 查询部门接口
     *
     * @param containDisableOrg 是否含停用部门
     * @param containExtend     是否包含自定义
     * @param loadAllChilds     是否含下级
     * @param codeList          部门编号集合
     * @param current           分页-当前页
     * @param pageSize          分页-页大小
     * @param tenantKey         租户Key
     * @return 结果
     */
    @Override
    public JSONObject restfulQueryOrg(Boolean containDisableOrg, Boolean containExtend, Boolean loadAllChilds, List<String> codeList, Integer current, Integer pageSize, String tenantKey) {
        String methodName = "调用" + simpleName + ".restfulQueryOrg()";
        Map<String, Object> header = new HashMap<>(16);
        header.put(tenantKeylab, tenantKey);
        header.put(contentTypeLab, contentTypeVal);
        JSONObject param = new JSONObject();
        if (codeList != null && !codeList.isEmpty()) {
            param.put("codeList", codeList);
        }
        param.put("loadAllChilds", loadAllChilds);
        param.put("containDisableOrg", containDisableOrg);
        param.put("containExtend", containExtend);
        param.put("current", current);
        param.put("pageSize", pageSize);
        JSONObject dataJson = new JSONObject();
        String url = cmicProperties.getOpenPlatformUrl() + "/api/hrm/restful/queryOrg";
        long l = System.currentTimeMillis();
        String accessToken = getAccessToken();
        if (accessToken != null) {
            url += "?" + accessTokenKey + "=" + accessToken;
        }
        String orgCodeData = HttpUtils.sendPost(url, param.toString(), header);
        log.info("{} 请求开放平台：【{}】耗时：【{}ms】", methodName, url, (System.currentTimeMillis() - l));
        if (orgCodeData != null && !"".equals(orgCodeData) && StringUtils.isValidJson(orgCodeData)) {
            JSONObject jsonObject = JSON.parseObject(orgCodeData);
            JSONObject messageJson = jsonObject.getJSONObject(messageKey);
            String successCode = "0";
            if (successCode.equals(messageJson.getString(errcodeKey))) {
                dataJson = jsonObject.getJSONObject("data");
            } else {
                log.error("{} 调用开放平台查询部门接口异常：{}", methodName, orgCodeData);
            }
        } else {
            log.error("{} 调用开放平台查询部门接口返回非JSON格式数：{}", methodName, orgCodeData);
        }
        return dataJson;
    }

    /**
     * 请求开放平台 同步部门接口
     *
     * @param dataList  部门数据
     * @param tenantKey 租户Key
     * @return 结果
     */
    @Override
    public Map<String, Object> restfulSyncDepartment(List<Map<String, Object>> dataList, String tenantKey) {
        String methodName = "调用" + simpleName + ".restfulSyncDepartment()";
        Integer errorNum = 0;
        Integer addNum = 0;
        Integer updateNum = 0;
        Integer successNum = 0;
        List<String> errorList = new ArrayList<>();
        // 设置 请求头
        Map<String, Object> header = new HashMap<>(16);
        header.put(tenantKeylab, tenantKey);
        header.put(contentTypeLab, contentTypeVal);
        JSONObject dataRuleJson = new JSONObject();
        dataRuleJson.put("department", "code");
        JSONObject param = new JSONObject();
        param.put("data", dataList);
        param.put(dataRuleKey, dataRuleJson);
        String url = cmicProperties.getOpenPlatformUrl() + "/api/hrm/restful/syncDepartment";
        String accessToken = getAccessToken();
        if (accessToken != null) {
            url += "?" + accessTokenKey + "=" + accessToken;
        }
        String positCodeData = HttpUtils.sendPost(url, param.toString(), header);
        if (positCodeData != null && !"".equals(positCodeData) && StringUtils.isValidJson(positCodeData)) {
            JSONObject jsonObject = JSON.parseObject(positCodeData);
            JSONObject messageJson = jsonObject.getJSONObject(messageKey);
            String successCode = "0";
            if (successCode.equals(messageJson.getString(errcodeKey))) {
                JSONArray dataJsonArr = jsonObject.getJSONArray("data");
                for (int i = 0; i < dataJsonArr.size(); i++) {
                    JSONObject dataJson = dataJsonArr.getJSONObject(i);
                    String status = dataJson.getString(statusKey);
                    String operate = dataJson.getString("operate");
                    String keyValue = dataJson.getString("keyValue");
                    if ("SUCCESS".equals(status)) {
                        successNum++;
                        if ("ADD".equals(operate)) {
                            addNum++;
                        } else if ("UPDATE".equals(operate)) {
                            updateNum++;
                        }
                        String id = dataJson.getString("id");
                        for (Map<String, Object> map : dataList) {
                            if (map.get("code").toString().equals(keyValue)) {
                                map.put("id", id);
                                break;
                            }
                        }
                    } else {
                        errorNum++;
                        errorList.add(dataJson.toString());
                    }
                }
            } else {
                log.error("{} 调用开放平台同步部门接口异常：{}", methodName, positCodeData);
            }
        } else {
            log.error("{} 调用开放平台同步部门接口返回非JSON数据：{}", methodName, positCodeData);
        }

        Map<String, Object> resultMap = new HashMap<>(16);
        resultMap.put("successNum", successNum);
        resultMap.put("errorNum", errorNum);
        resultMap.put("addNum", addNum);
        resultMap.put("updateNum", updateNum);
        resultMap.put("errorData", errorList);
        return resultMap;
    }

    /**
     * 请求开放平台 封存部门
     *
     * @param ids       部门ID
     * @param tenantKey 租户key
     * @return 结果
     */
    @Override
    public List<Map<String, Object>> restfulDisableDepartment(List<Map<String, Object>> ids, String tenantKey) {
        String methodName = "调用" + simpleName + ".restfulDisableDepartment()";
        Map<String, Object> header = new HashMap<>(16);
        header.put(tenantKeylab, tenantKey);
        header.put(contentTypeLab, contentTypeVal);
        JSONObject param = new JSONObject();
        param.put("datas", ids);
        Integer successNum = 0;
        Integer errorNum = 0;
        List<Map<String, Object>> czzzIds = new ArrayList<>();
        String url = cmicProperties.getOpenPlatformUrl() + "/api/hrm/restful/disableDepartment";
        String accessToken = getAccessToken();
        if (accessToken != null) {
            url += "?" + accessTokenKey + "=" + accessToken;
        }
        String disableDepartmentCode = HttpUtils.sendPost(url, param.toString(), header);
        if (disableDepartmentCode != null && !"".equals(disableDepartmentCode) && StringUtils.isValidJson(disableDepartmentCode)) {
            JSONObject jsonObject = JSON.parseObject(disableDepartmentCode);
            if (!jsonObject.containsKey(messageKey)) {
                log.error("{} 调用开放平台封存部门异常：{}", methodName, disableDepartmentCode);
            } else {
                JSONObject messageJson = jsonObject.getJSONObject(messageKey);
                String successCode = "0";
                if (successCode.equals(messageJson.getString(errcodeKey))) {
                    JSONArray dataJsonArr = jsonObject.containsKey("data") ? jsonObject.getJSONArray("data") : new JSONArray();
                    for (int i = 0; i < dataJsonArr.size(); i++) {
                        JSONObject itemJson = dataJsonArr.getJSONObject(i);
                        if ("SUCCESS".equals(itemJson.getString(statusKey))) {
                            successNum++;
                        } else if ("ERROR".equals(itemJson.getString(statusKey))) {
                            String errcode = itemJson.getString(errcodeKey);
                            if (errcode.contains("存在下级组织")) {
                                log.info("{} 存在下级组织，数据：{}", methodName, itemJson);
                                Map<String, Object> map = new HashMap<>(16);
                                map.put("id", itemJson.getLong("id"));
                                czzzIds.add(map);
                            } else if (errcode.contains("该组织下存在成员")) {
                                log.warn("{} 封存失败，该部门存在成员，数据：{}", methodName, itemJson);
                                errorNum++;
                            } else {
                                log.warn("{} 封存失败，数据：{}", methodName, itemJson);
                                errorNum++;
                            }
                        }
                    }
                } else {
                    log.error("{} 调用开放平台封存部门异常：{}", methodName, disableDepartmentCode);
                }
            }
        } else {
            log.error("{} 调用开放平台封存部门返回非JSON数据：{}", methodName, disableDepartmentCode);
        }
        if (!czzzIds.isEmpty()) {
            restfulDisableDepartment(czzzIds, tenantKey);
        }
        return null;
    }

    /**
     * 请求开放平台 同步人员
     *
     * @param userList  用户数据
     * @param tenantKey 租户key
     * @return 结果
     */
    @Override
    public String restfulSyncEmployee(List<Map<String, Object>> userList, String tenantKey) {
        String methodName = "调用" + simpleName + ".restfulSyncEmployee()";
        JSONObject param = new JSONObject();
        JSONObject dataRuleJson = new JSONObject();
        dataRuleJson.put("employee", "job_num");
        dataRuleJson.put("department", "code");
        dataRuleJson.put("position", "name");
        param.put("data", userList);
        param.put(dataRuleKey, dataRuleJson);
        Map<String, Object> header = new HashMap<>(16);
        header.put(tenantKeylab, tenantKey);
        header.put(contentTypeLab, contentTypeVal);
        String url = cmicProperties.getOpenPlatformUrl() + "/api/hrm/restful/syncEmployee";
        String accessToken = getAccessToken();
        if (accessToken != null) {
            url += "?" + accessTokenKey + "=" + accessToken;
        }
        return HttpUtils.sendPost(url, param.toString(), header);
    }

    /**
     * 同步岗位数据
     *
     * @param positList 岗位数据
     * @param tenantKey 团队key
     * @return 结果
     */
    @Override
    public String restfulSyncPosition(List<Map<String, String>> positList, String tenantKey) {
        Map<String, Object> header = new HashMap<>(16);
        header.put(tenantKeylab, tenantKey);
        header.put(contentTypeLab, contentTypeVal);
        JSONObject param = new JSONObject();
        JSONObject dataRuleJson = new JSONObject();
        dataRuleJson.put("position", "name");
        param.put("data", positList);
        param.put(dataRuleKey, dataRuleJson);
        String url = cmicProperties.getOpenPlatformUrl() + "/api/hrm/restful/syncPosition";
        String accessToken = getAccessToken();
        if (accessToken != null) {
            url += "?" + accessTokenKey + "=" + accessToken;
        }
        return HttpUtils.sendPost(url, param.toString(), header);
    }

    /**
     * 获取免登票据
     *
     * @param appKey      应用key
     * @param appSecurity 应用密钥
     * @param account     认证数据
     * @param authType    认证类型
     * @return 结果
     */
    @Override
    public String restfilGetLoginToken(String appKey, String appSecurity, String account, String authType) {
        String url = cmicProperties.getOpenPlatformUrl() + "/oauth2/get_logintoken";
        url += "?app_key=" + appKey + "&app_security=" + appSecurity + "&account=" + account + "&authType=" + authType;
        Map<String, Object> header = new HashMap<>(16);
        return HttpUtils.sendPost(url, null, header);
    }

    /**
     * 请求开放平台 所有流程列表
     *
     * @param userId         用户ID
     * @param requestname    标题
     * @param workflowid     流程ID(多个以,分隔)
     * @param flowStatusList 流程流转的状态;0-草稿,1-审批中,2-已退回,3-正常归档,4-强制结束,5-已作废,6-待提交,7-暂停,8-撤销,-99-已删除(例："flowStatusList":[0,1,2,3,4,5])
     * @return 结果
     */
    @Override
    public FlowListResultVo getAllToDoWorkflowRequestList(Long userId, String requestname, String workflowid, List<Integer> flowStatusList) {
        Integer allWorkflowRequestCount = getAllWorkflowRequestCount(userId, 1, 1, requestname, workflowid, flowStatusList);
        int pages = (int) Math.ceil((double) allWorkflowRequestCount / 100);
        FlowListResultVo result = new FlowListResultVo();
        List<FlowList> list = new ArrayList<>();
        NormalResult resultMess = new NormalResult();
        boolean item = true;
        for (int i = 0; i < pages; i++) {
            FlowListResultVo requestList = getAllToDoWorkflowRequestList(userId, (i + 1), 100, requestname, workflowid, flowStatusList);
            NormalResult message = requestList.getMessage();
            resultMess = message;
            if (message.getErrcode() != 0) {
                item = false;
                break;
            }
            list.addAll(requestList.getData());
        }
        if (item) {
            result.setData(list);
        }
        result.setMessage(resultMess);
        return result;
    }


    /**
     * 请求开放平台 所有流程列表 存在分页
     *
     * @param userId         用户ID
     * @param pageNo         页数
     * @param pageSize       单页大小
     * @param requestname    标题
     * @param workflowid     流程ID(多个以,分隔)
     * @param flowStatusList 流程流转的状态;0-草稿,1-审批中,2-已退回,3-正常归档,4-强制结束,5-已作废,6-待提交,7-暂停,8-撤销,-99-已删除(例："flowStatusList":[0,1,2,3,4,5])
     * @return 结果
     */
    @Override
    public FlowListResultVo getAllToDoWorkflowRequestList(Long userId, Integer pageNo, Integer pageSize, String requestname, String workflowid, List<Integer> flowStatusList) {
        String methodName = "调用" + simpleName + ".getAllToDoWorkflowRequestList() ";
        FlowVo flowVo = new FlowVo();
        flowVo.setAccessToken(getAccessToken());
        flowVo.setUserid(userId);
        flowVo.setPageNo(pageNo);
        flowVo.setPageSize(pageSize);
        FlowCondition condition = new FlowCondition();
        if (requestname != null && !"".equals(requestname)) {
            condition.setRequestname(requestname);
        }
        if (workflowid != null && !"".equals(workflowid)) {
            condition.setWorkflowid(workflowid);
        }
        if (flowStatusList != null && !flowStatusList.isEmpty()) {
            condition.setFlowStatusList(flowStatusList);
        }
        // 流程未操作者信息
        UnOperatorParam unOperatorParam = new UnOperatorParam();
        unOperatorParam.setNeedUnOperators(true);
        unOperatorParam.setTypeList(new ArrayList<>(Arrays.asList("0", "1", "3")));
        condition.setUnOperatorParam(unOperatorParam);
        flowVo.setCondition(condition);
        FlowListResultVo listV1 = FlowService.getAllWorkflowRequestListV1(flowVo, cmicProperties.getOpenPlatformUrl(), null);
        log.info("{} 请求开放平台所有流程列表接口返回：{}", methodName, listV1.getMessage().getErrmsg());
        return listV1;
    }

    /**
     * 请求开放平台 所有流程数量
     *
     * @param userId         用户ID
     * @param pageNo         页数
     * @param pageSize       单页大小
     * @param requestname    标题
     * @param workflowid     流程ID(多个以,分隔)
     * @param flowStatusList 流程流转的状态;0-草稿,1-审批中,2-已退回,3-正常归档,4-强制结束,5-已作废,6-待提交,7-暂停,8-撤销,-99-已删除(例："flowStatusList":[0,1,2,3,4,5])
     * @return 结果
     */
    @Override
    public Integer getAllWorkflowRequestCount(Long userId, Integer pageNo, Integer pageSize, String requestname, String workflowid, List<Integer> flowStatusList) {
        String methodName = "调用" + simpleName + ".getAllWorkflowRequestCount()";
        FlowVo flowVo = new FlowVo();
        flowVo.setAccessToken(getAccessToken());
        flowVo.setUserid(userId);
        flowVo.setPageNo(pageNo);
        flowVo.setPageSize(pageSize);
        FlowCondition condition = new FlowCondition();
        if (requestname != null && !"".equals(requestname)) {
            condition.setRequestname(requestname);
        }
        if (workflowid != null && !"".equals(workflowid)) {
            condition.setWorkflowid(workflowid);
        }
        if (flowStatusList != null && !flowStatusList.isEmpty()) {
            condition.setFlowStatusList(flowStatusList);
        }
        int result = 0;
        flowVo.setCondition(condition);
        FlowResultVo resultVo = FlowService.getAllWorkflowRequestCountV1(flowVo, cmicProperties.getOpenPlatformUrl(), null);
        if (successKey.equals(resultVo.getMessage().getErrmsg())) {
            result = Integer.parseInt(resultVo.getData().getCount());
        } else {
            log.error("{} 请求开放平台接口返回异常：{}", methodName, JSON.toJSONString(resultVo));
        }
        return result;
    }

    /**
     * 请求开放平台 待办流程数量
     *
     * @param userId         用户ID
     * @param pageNo         页数
     * @param pageSize       单页大小
     * @param requestname    标题
     * @param workflowid     流程ID(多个以,分隔)
     * @param flowStatusList 流程流转的状态;0-草稿,1-审批中,2-已退回,3-正常归档,4-强制结束,5-已作废,6-待提交,7-暂停,8-撤销,-99-已删除(例："flowStatusList":[0,1,2,3,4,5])
     * @return 结果
     */
    @Override
    public Integer getToDoWorkflowRequestCount(Long userId, Integer pageNo, Integer pageSize, String requestname, String workflowid, List<Integer> flowStatusList) {
        String methodName = "调用" + simpleName + ".getToDoWorkflowRequestCount()";
        FlowVo flowVo = new FlowVo();
        flowVo.setAccessToken(getAccessToken());
        flowVo.setUserid(userId);
        flowVo.setPageNo(pageNo);
        flowVo.setPageSize(pageSize);
        FlowCondition condition = new FlowCondition();
        if (requestname != null && !"".equals(requestname)) {
            condition.setRequestname(requestname);
        }
        if (workflowid != null && !"".equals(workflowid)) {
            condition.setWorkflowid(workflowid);
        }
        if (flowStatusList != null && !flowStatusList.isEmpty()) {
            condition.setFlowStatusList(flowStatusList);
        }
        Integer result = 0;
        flowVo.setCondition(condition);
        FlowResultVo resultVo = FlowService.getToDoWorkflowRequestCountV1(flowVo, cmicProperties.getOpenPlatformUrl(), null);
        if (successKey.equals(resultVo.getMessage().getErrmsg())) {
            result = Integer.parseInt(resultVo.getData().getCount());
        } else {
            log.error("{} 请求开放平台待办流程数量接口返回异常：{}", methodName, JSON.toJSONString(resultVo));
        }
        return result;
    }

    /**
     * 请求开放平台 待办流程列表
     *
     * @param userId         用户ID
     * @param pageNo         页数
     * @param pageSize       单页大小
     * @param requestname    标题
     * @param workflowid     流程ID(多个以,分隔)
     * @param flowStatusList 流程流转的状态;0-草稿,1-审批中,2-已退回,3-正常归档,4-强制结束,5-已作废,6-待提交,7-暂停,8-撤销,-99-已删除(例："flowStatusList":[0,1,2,3,4,5])
     * @return 结果
     */
    @Override
    public FlowListResultVo getToDoWorkflowRequestList(Long userId, Integer pageNo, Integer pageSize, String requestname, String workflowid, List<Integer> flowStatusList) {
        String methodName = "调用" + simpleName + ".getToDoWorkflowRequestList() ";
        FlowVo flowVo = new FlowVo();
        flowVo.setAccessToken(getAccessToken());
        flowVo.setUserid(userId);
        flowVo.setPageNo(pageNo);
        flowVo.setPageSize(pageSize);
        FlowCondition condition = new FlowCondition();
        if (requestname != null && !"".equals(requestname)) {
            condition.setRequestname(requestname);
        }
        if (workflowid != null && !"".equals(workflowid)) {
            condition.setWorkflowid(workflowid);
        }
        if (flowStatusList != null && !flowStatusList.isEmpty()) {
            condition.setFlowStatusList(flowStatusList);
        }
        // 流程未操作者信息
        UnOperatorParam unOperatorParam = new UnOperatorParam();
        unOperatorParam.setNeedUnOperators(true);
        unOperatorParam.setTypeList(new ArrayList<>(Arrays.asList("0", "1", "3")));
        condition.setUnOperatorParam(unOperatorParam);
        flowVo.setCondition(condition);
        FlowListResultVo listV1 = FlowService.getToDoWorkflowRequestListV1(flowVo, cmicProperties.getOpenPlatformUrl(), null);
        if (listV1.getMessage().getErrcode() != 0) {
            log.error("{} 请求待办流程列表返回异常：{}", methodName, JSON.toJSONString(listV1));
        }
//        log.info("{} 请求开放平台待办流程列表接口返回：{}", methodName, listV1.getMessage().getErrcode());
        return listV1;
    }

    /**
     * 请求开放平台 待办流程列表 循环查询所有数据
     *
     * @param userId         用户ID
     * @param requestname    标题
     * @param workflowid     流程ID(多个以,分隔)
     * @param flowStatusList 流程流转的状态;0-草稿,1-审批中,2-已退回,3-正常归档,4-强制结束,5-已作废,6-待提交,7-暂停,8-撤销,-99-已删除(例："flowStatusList":[0,1,2,3,4,5])
     * @return 结果
     */
    @Override
    public FlowListResultVo getToDoWorkflowRequestList(Long userId, String requestname, String workflowid, List<Integer> flowStatusList) {
        Integer count = getToDoWorkflowRequestCount(userId, 1, 1, requestname, workflowid, flowStatusList);
        int pages = (int) Math.ceil((double) count / 100);
        FlowListResultVo result = new FlowListResultVo();
        List<FlowList> list = new ArrayList<>();
        NormalResult resultMess = new NormalResult();
        boolean item = true;
        for (int i = 0; i < pages; i++) {
            FlowListResultVo requestList = getToDoWorkflowRequestList(userId, (i + 1), 100, requestname, workflowid, flowStatusList);
            NormalResult message = requestList.getMessage();
            resultMess = message;
            if (message.getErrcode() != 0) {
                item = false;
                continue;
            }
            list.addAll(requestList.getData());
        }
        if (item) {
            result.setData(list);
        }
        result.setMessage(resultMess);
        return result;
    }

    /**
     * 待办数据列表 包含签字意见数据--线程池
     *
     * @param userId         用户ID
     * @param requestname    标题
     * @param workflowid     流程ID(多个以,分隔)
     * @param flowStatusList 流程流转的状态;0-草稿,1-审批中,2-已退回,3-正常归档,4-强制结束,5-已作废,6-待提交,7-暂停,8-撤销,-99-已删除(例："flowStatusList":[0,1,2,3,4,5])
     * @return 待办数据包含签字意见
     */
    @Override
    public List<FlowListResultResponse> getTodoContainSignList(Long userId, String requestname, String workflowid, List<Integer> flowStatusList) {
        String methodName = "调用" + simpleName + ".getTodoContainSignList()";
        Integer count = getToDoWorkflowRequestCount(userId, 1, 1, requestname, workflowid, flowStatusList);
        int pages = (int) Math.ceil((double) count / 100);
        List<FlowListResultResponse> resultList = new ArrayList<>();
        for (int i = 0; i < pages; i++) {
            FlowListResultVo requestList = getToDoWorkflowRequestList(userId, (i + 1), 100, requestname, workflowid, flowStatusList);
            NormalResult message = requestList.getMessage();
            if (message.getErrcode() != 0) {
                continue;
            }
            log.info("{} 查询第{}页，总数据量：{}", methodName, (i + 1), requestList.getData().size());
            CountDownLatch countDownLatch = new CountDownLatch(requestList.getData().size());
            //开线程池，线程数量为要遍历的对象的长度
            ExecutorService executor = Executors.newFixedThreadPool(requestList.getData().size());
            for (FlowList datum : requestList.getData()) {
                FlowListResultResponse resultResponse = new FlowListResultResponse();
                resultResponse.setFlow(datum);
                CompletableFuture<Void> voidCompletableFuture = CompletableFuture.runAsync(() -> {
                    List<FlowSign> requestLog = getRequestLog(userId, datum.getRequestid());
                    resultResponse.setSignList(requestLog);
                    countDownLatch.countDown();
                }, executor);
                CompletableFuture.allOf(voidCompletableFuture);
                resultList.add(resultResponse);
            }
            try {
                countDownLatch.await(3, TimeUnit.MINUTES);
            } catch (InterruptedException e) {
                log.error("{} CountDownLatch异常", methodName, e);
            }
            executor.shutdown();
        }
        return resultList;
    }

    @RpcReference
    WfcGetRequestDataRest wfcGetRequestDataRest;
    @RpcReference(group = "workflow")
    WfcRequestCommonRest wfcRequestCommonRest;

    /**
     * 待办数据列表 包含最后操作人数据
     *
     * @param userId         用户ID
     * @param requestname    标题
     * @param workflowid     流程ID(多个以,分隔)
     * @param flowStatusList 流程流转的状态;0-草稿,1-审批中,2-已退回,3-正常归档,4-强制结束,5-已作废,6-待提交,7-暂停,8-撤销,-99-已删除(例："flowStatusList":[0,1,2,3,4,5])
     * @return 待办数据包含签字意见
     */
    @Override
    public List<FlowListResultResponse> getTodoContainLastOperatorList(Long userId, String requestname, String workflowid, List<Integer> flowStatusList) {
        String methodName = "调用" + simpleName + ".getTodoContainLastOperatorList()";
        Integer count = getToDoWorkflowRequestCount(userId, 1, 1, requestname, workflowid, flowStatusList);
        int pages = (int) Math.ceil((double) count / 100);
        List<FlowListResultResponse> resultList = new ArrayList<>();
        SimpleEmployee simpleEmployee = new SimpleEmployee();
        simpleEmployee.setId(userId);
        for (int i = 0; i < pages; i++) {
            FlowListResultVo requestList = getToDoWorkflowRequestList(userId, (i + 1), 100, requestname, workflowid, flowStatusList);
            NormalResult message = requestList.getMessage();
            if (message.getErrcode() != 0) {
                continue;
            }
            log.info("{} 查询第{}页，总数据量：{}", methodName, (i + 1), requestList.getData().size());

//            if (count > 50) { // 待办数量大于50 使用多线程
//                log.info("{} 使用多线程", methodName);
//                CountDownLatch countDownLatch = new CountDownLatch(requestList.getData().size());
//                //开线程池，线程数量为要遍历的对象的长度
//                ExecutorService executor = Executors.newFixedThreadPool(requestList.getData().size());
//                for (FlowList datum : requestList.getData()) {
//                    FlowListResultResponse resultResponse = new FlowListResultResponse();
//                    resultResponse.setFlow(datum);
//                    Long requestid = Long.parseLong(datum.getRequestid());
//                    FlowLastOperatorResponse lastOperatorResponse = new FlowLastOperatorResponse();
//
//                    CompletableFuture<Void> voidCompletableFuture = CompletableFuture.runAsync(() -> {
//                        Page<Comment> flowComment = wfcRequestCommonRest.getFlowComment(requestid, userId, 1, 1);
//                        lastOperatorResponse.setLastOperator(Long.parseLong(datum.getCreatorId()));
//                        lastOperatorResponse.setLastOperatorName(datum.getCreatorName());
//                        lastOperatorResponse.setLastOperateDateTime(datum.getCreateTime());
//                        if (flowComment != null) {
//                            log.info("{} flowComment.getResult().isEmpty()：{}", methodName, flowComment.getResult().isEmpty());
//                            if (!flowComment.getResult().isEmpty()) {
//                                List<Comment> commentList = flowComment.getResult();
//                                Comment comment = commentList.get(0);
//                                if (comment.getCommentor() != null) {
//                                    SimpleEmployee commentor = comment.getCommentor();
//                                    lastOperatorResponse.setLastOperator(commentor.getId());
//                                    lastOperatorResponse.setLastOperatorName(commentor.getUsername());
//                                    Instant instant = Instant.ofEpochMilli(comment.getCreateTime().getTime());
//                                    LocalDateTime dateTime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
//                                    lastOperatorResponse.setLastOperateDateTime(DateUtils.formatTime(dateTime, "yyyy-MM-dd HH:mm:ss"));
//                                }
//                            }
//                        }
//                        resultResponse.setLastOperator(lastOperatorResponse);
//                        countDownLatch.countDown();
//                    }, executor);
//                    CompletableFuture.allOf(voidCompletableFuture);
//                    resultList.add(resultResponse);
//                }
//                try {
//                    countDownLatch.await(3, TimeUnit.MINUTES);
//                } catch (InterruptedException e) {
//                    log.error("{} CountDownLatch异常", methodName, e);
//                }
//                executor.shutdown();
//            } else {
                log.info("{} 直接请求rpc接口",methodName);
                for (FlowList datum : requestList.getData()) {
                    FlowListResultResponse resultResponse = new FlowListResultResponse();
                    resultResponse.setFlow(datum);
                    log.info("{} 待办数据：{}", methodName, JSON.toJSONString(datum));
                    Long requestid = Long.parseLong(datum.getRequestid());
                    FlowLastOperatorResponse lastOperatorResponse = new FlowLastOperatorResponse();
                    try {
                        Page<Comment> flowComment = wfcRequestCommonRest.getFlowComment(requestid, userId, 1, 1);
                        lastOperatorResponse.setLastOperator(Long.parseLong(datum.getCreatorId()));
                        lastOperatorResponse.setLastOperatorName(datum.getCreatorName());
                        lastOperatorResponse.setLastOperateDateTime(datum.getCreateTime());
                        if (flowComment != null) {
                            log.info("{} flowComment.getResult().isEmpty()：{}", methodName, flowComment.getResult().isEmpty());
                            if (!flowComment.getResult().isEmpty()) {
                                List<Comment> commentList = flowComment.getResult();
                                Comment comment = commentList.get(0);
                                if (comment.getCommentor() != null) {
                                    SimpleEmployee commentor = comment.getCommentor();
                                    lastOperatorResponse.setLastOperator(commentor.getId());
                                    lastOperatorResponse.setLastOperatorName(commentor.getUsername());
                                    Instant instant = Instant.ofEpochMilli(comment.getCreateTime().getTime());
                                    LocalDateTime dateTime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
                                    lastOperatorResponse.setLastOperateDateTime(DateUtils.formatTime(dateTime, "yyyy-MM-dd HH:mm:ss"));
                                }
                            }
                        }
                        resultResponse.setLastOperator(lastOperatorResponse);
                    } catch (Exception e) {
                        log.error("{} 获取审批意见异常", message, e);
                    }
                    resultResponse.setLastOperator(lastOperatorResponse);
                    resultList.add(resultResponse);
                }
//            }
        }
        return resultList;
    }


    /**
     * 请求开放平台 已办流程数量
     *
     * @param userId         用户ID
     * @param pageNo         页数
     * @param pageSize       单页大小
     * @param requestname    标题
     * @param workflowid     流程ID(多个以,分隔)
     * @param flowStatusList 流程流转的状态;0-草稿,1-审批中,2-已退回,3-正常归档,4-强制结束,5-已作废,6-待提交,7-暂停,8-撤销,-99-已删除(例："flowStatusList":[0,1,2,3,4,5])
     * @return 结果
     */
    @Override
    public Integer getHandledWorkflowRequestCount(Long userId, Integer pageNo, Integer pageSize, String requestname, String workflowid, List<Integer> flowStatusList) {
        String methodName = "调用" + simpleName + ".getHandledWorkflowRequestCount()";
        FlowVo flowVo = new FlowVo();
        flowVo.setAccessToken(getAccessToken());
        flowVo.setUserid(userId);
        flowVo.setPageNo(pageNo);
        flowVo.setPageSize(pageSize);
        FlowCondition condition = new FlowCondition();
        if (requestname != null && !"".equals(requestname)) {
            condition.setRequestname(requestname);
        }
        if (workflowid != null && !"".equals(workflowid)) {
            condition.setWorkflowid(workflowid);
        }
        if (flowStatusList != null && !flowStatusList.isEmpty()) {
            condition.setFlowStatusList(flowStatusList);
        }
        Integer result = 0;
        flowVo.setCondition(condition);
        FlowResultVo resultVo = FlowService.getHandledWorkflowRequestCountV1(flowVo, cmicProperties.getOpenPlatformUrl(), null);
        if (successKey.equals(resultVo.getMessage().getErrmsg())) {
            result = Integer.parseInt(resultVo.getData().getCount());
        } else {
            log.error("{} 请求开放平台已办流程数量接口返回异常：{}", methodName, JSON.toJSONString(resultVo));
        }
        return result;
    }

    /**
     * 请求开放平台 已办流程列表
     *
     * @param userId         用户ID
     * @param pageNo         页数
     * @param pageSize       单页大小
     * @param requestname    标题
     * @param workflowid     流程ID(多个以,分隔)
     * @param flowStatusList 流程流转的状态;0-草稿,1-审批中,2-已退回,3-正常归档,4-强制结束,5-已作废,6-待提交,7-暂停,8-撤销,-99-已删除(例："flowStatusList":[0,1,2,3,4,5])
     * @return 结果
     */
    @Override
    public FlowListResultVo getHandledWorkflowRequestList(Long userId, Integer pageNo, Integer pageSize, String requestname, String workflowid, List<Integer> flowStatusList) {
        String methodName = "调用" + simpleName + ".getHandledWorkflowRequestList() ";
        FlowVo flowVo = new FlowVo();
        flowVo.setAccessToken(getAccessToken());
        flowVo.setUserid(userId);
        flowVo.setPageNo(pageNo);
        flowVo.setPageSize(pageSize);
        FlowCondition condition = new FlowCondition();
        if (requestname != null && !"".equals(requestname)) {
            condition.setRequestname(requestname);
        }
        if (workflowid != null && !"".equals(workflowid)) {
            condition.setWorkflowid(workflowid);
        }
        if (flowStatusList != null && !flowStatusList.isEmpty()) {
            condition.setFlowStatusList(flowStatusList);
        }
        // 流程未操作者信息
        UnOperatorParam unOperatorParam = new UnOperatorParam();
        unOperatorParam.setNeedUnOperators(true);
        unOperatorParam.setTypeList(new ArrayList<>(Arrays.asList("0", "1", "3")));
        condition.setUnOperatorParam(unOperatorParam);
        flowVo.setCondition(condition);
        FlowListResultVo listV1 = FlowService.getHandledWorkflowRequestListV1(flowVo, cmicProperties.getOpenPlatformUrl(), null);
        log.info("{} 请求开放平台已办流程列表接口返回：{}", methodName, listV1.getMessage().getErrmsg());
        return listV1;
    }

    @Override
    public FlowListResultVo getHandledWorkflowRequestList(Long userId, String requestname, String workflowid, List<Integer> flowStatusList) {
        Integer count = getHandledWorkflowRequestCount(userId, 1, 1, requestname, workflowid, flowStatusList);
        int pages = (int) Math.ceil((double) count / 100);
        FlowListResultVo result = new FlowListResultVo();
        List<FlowList> list = new ArrayList<>();
        NormalResult resultMess = new NormalResult();
        boolean item = true;
        for (int i = 0; i < pages; i++) {
            FlowListResultVo requestList = getHandledWorkflowRequestList(userId, (i + 1), 100, requestname, workflowid, flowStatusList);
            NormalResult message = requestList.getMessage();
            resultMess = message;
            if (message.getErrcode() != 0) {
                item = false;
                break;
            }
            list.addAll(requestList.getData());
        }
        if (item) {
            result.setData(list);
        }
        result.setMessage(resultMess);
        return result;
    }

    /**
     * 请求开放平台 我发起的流程数量
     *
     * @param userId         用户ID
     * @param pageNo         页数
     * @param pageSize       单页大小
     * @param requestname    标题
     * @param workflowid     流程ID(多个以,分隔)
     * @param flowStatusList 流程流转的状态;0-草稿,1-审批中,2-已退回,3-正常归档,4-强制结束,5-已作废,6-待提交,7-暂停,8-撤销,-99-已删除(例："flowStatusList":[0,1,2,3,4,5])
     * @return 结果
     */
    @Override
    public Integer getMyWorkflowRequestCount(Long userId, Integer pageNo, Integer pageSize, String requestname, String workflowid, List<Integer> flowStatusList) {
        String methodName = "调用" + simpleName + ".getMyWorkflowRequestCount()";
        FlowVo flowVo = new FlowVo();
        flowVo.setAccessToken(getAccessToken());
        flowVo.setUserid(userId);
        flowVo.setPageNo(pageNo);
        flowVo.setPageSize(pageSize);
        FlowCondition condition = new FlowCondition();
        if (requestname != null && !"".equals(requestname)) {
            condition.setRequestname(requestname);
        }
        if (workflowid != null && !"".equals(workflowid)) {
            condition.setWorkflowid(workflowid);
        }
        if (flowStatusList != null && !flowStatusList.isEmpty()) {
            condition.setFlowStatusList(flowStatusList);
        }
        Integer result = 0;
        flowVo.setCondition(condition);
        FlowResultVo resultVo = FlowService.getMyWorkflowRequestCountV1(flowVo, cmicProperties.getOpenPlatformUrl(), null);
        if (successKey.equals(resultVo.getMessage().getErrmsg())) {
            result = Integer.parseInt(resultVo.getData().getCount());
        } else {
            log.error("{} 请求开放平台我发起的流程数量接口返回异常：{}", methodName, JSON.toJSONString(resultVo));
        }
        return result;
    }

    /**
     * 请求开放平台 我发起的流程列表
     *
     * @param userId         用户ID
     * @param pageNo         页数
     * @param pageSize       单页大小
     * @param requestname    标题
     * @param workflowid     流程ID(多个以,分隔)
     * @param flowStatusList 流程流转的状态;0-草稿,1-审批中,2-已退回,3-正常归档,4-强制结束,5-已作废,6-待提交,7-暂停,8-撤销,-99-已删除(例："flowStatusList":[0,1,2,3,4,5])
     * @return 结果
     */
    @Override
    public FlowListResultVo getMyWorkflowRequestList(Long userId, Integer pageNo, Integer pageSize, String requestname, String workflowid, List<Integer> flowStatusList) {
        String methodName = "调用" + simpleName + ".getMyWorkflowRequestList() ";
        FlowVo flowVo = new FlowVo();
        flowVo.setAccessToken(getAccessToken());
        flowVo.setUserid(userId);
        flowVo.setPageNo(pageNo);
        flowVo.setPageSize(pageSize);
        FlowCondition condition = new FlowCondition();
        if (requestname != null && !"".equals(requestname)) {
            condition.setRequestname(requestname);
        }
        if (workflowid != null && !"".equals(workflowid)) {
            condition.setWorkflowid(workflowid);
        }
        if (flowStatusList != null && !flowStatusList.isEmpty()) {
            condition.setFlowStatusList(flowStatusList);
        }
        // 流程未操作者信息
        UnOperatorParam unOperatorParam = new UnOperatorParam();
        unOperatorParam.setNeedUnOperators(true);
        unOperatorParam.setTypeList(new ArrayList<>(Arrays.asList("0", "1", "3")));
        condition.setUnOperatorParam(unOperatorParam);
        flowVo.setCondition(condition);
        FlowListResultVo listV1 = FlowService.getMyWorkflowRequestListV1(flowVo, cmicProperties.getOpenPlatformUrl(), null);
        log.info("{} 请求开放平台已办流程列表接口返回：{}", methodName, listV1.getMessage().getErrmsg());
        return listV1;
    }

    /**
     * 请求开放平台 我发起的流程列表 循环查询所有数据
     *
     * @param userId         用户ID
     * @param requestname    标题
     * @param workflowid     流程ID(多个以,分隔)
     * @param flowStatusList 流程流转的状态;0-草稿,1-审批中,2-已退回,3-正常归档,4-强制结束,5-已作废,6-待提交,7-暂停,8-撤销,-99-已删除(例："flowStatusList":[0,1,2,3,4,5])
     * @return 结果
     */
    @Override
    public FlowListResultVo getMyWorkflowRequestList(Long userId, String requestname, String workflowid, List<Integer> flowStatusList) {
        Integer count = getMyWorkflowRequestCount(userId, 1, 1, requestname, workflowid, flowStatusList);
        int pages = (int) Math.ceil((double) count / 100);
        FlowListResultVo result = new FlowListResultVo();
        List<FlowList> list = new ArrayList<>();
        NormalResult resultMess = new NormalResult();
        boolean item = true;
        for (int i = 0; i < pages; i++) {
            FlowListResultVo requestList = getMyWorkflowRequestList(userId, (i + 1), 100, requestname, workflowid, flowStatusList);
            NormalResult message = requestList.getMessage();
            resultMess = message;
            if (message.getErrcode() != 0) {
                item = false;
                break;
            }
            list.addAll(requestList.getData());
        }
        if (item) {
            result.setData(list);
        }
        result.setMessage(resultMess);
        return result;
    }

    /**
     * 请求开放平台 获取节点未操作者
     *
     * @param userId            用户ID 必填
     * @param requestId         流程ID 必填
     * @param userCurrentNodeId 节点ID
     */
    @Override
    public List<NodeOperator> getNodeNotOperator(Long userId, String requestId, Long userCurrentNodeId) {
        String methodName = "调用" + simpleName + "getNodeNotOperator()";
        FlowVo flowVo = new FlowVo();
        flowVo.setAccessToken(getAccessToken());
        flowVo.setUserid(userId);
        flowVo.setRequestId(requestId);
        if (userCurrentNodeId != null) {
            flowVo.setUserCurrentNodeId(userCurrentNodeId);
        }
        FlowNodeOperatorResultVo nodeNotOperator = FlowService.getNodeNotOperator(flowVo, cmicProperties.getOpenPlatformUrl(), null);
        List<NodeOperator> data = new ArrayList<>();
        if (nodeNotOperator.getMessage().getErrcode() == 0) {
            data = nodeNotOperator.getData();
        } else {
            log.error("{} 请求开放平台 获取节点未操作者接口返回异常：{}", methodName, JSON.toJSONString(nodeNotOperator));
        }
        return data;
    }

    @Override
    public UserInfoResult getUser(long userId) {
        String methodName = String.format("调用%s.getUser(%s)", simpleName, userId);
        UserVo userVo = new UserVo();
        userVo.setAccessToken(getAccessToken());
        userVo.setUserid(userId);
        UserResultVo normalRes = UserService.getUsersV3(userVo, cmicProperties.getOpenPlatformUrl(), null);
        log.info("{} 获取到的用户信息：{}", methodName, JSON.toJSONString(normalRes));
        UserInfoResult user = new UserInfoResult();
        if (normalRes.getMessage().getErrcode() == 0) {
            user = normalRes.getUser();
        } else {
            log.error("｛｝ 获取用户信息异常：{}", methodName, JSON.toJSONString(normalRes.getMessage()));
        }
        return user;
    }

    /**
     * 请求开放平台  获取流程基础信息
     *
     * @param userid    用户ID
     * @param requestId 流程ID
     * @return 流程信息
     * @throws Exception
     */
    @Override
    public FlowBasicInfo getRequestInfo(Long userid, String requestId) {
        String methodName = String.format("调用%s.getRequestInfo(%s,%s)", simpleName, userid, requestId);
        FlowVo flowVo = new FlowVo();
        flowVo.setAccessToken(getAccessToken());
        flowVo.setUserid(userid);
        flowVo.setRequestId(requestId);
        FlowBasicInfoResultVo requestInfo = FlowService.getRequestInfo(flowVo, cmicProperties.getOpenPlatformUrl(), null);
        log.info("{} 获取到的流程信息：{}", methodName, JSON.toJSONString(requestInfo));
        FlowBasicInfo data = new FlowBasicInfo();
        if (requestInfo.getMessage().getErrcode() == 0) {
            data = requestInfo.getData();
        } else {
            log.error("｛｝ 获取流程信息异常：{}", methodName, JSON.toJSONString(requestInfo.getMessage()));
        }
        return data;
    }

    /**
     * 请求开放平台 获取节点操作者
     * 获取节点未操作者，如果传递了节点ID则获取指定节点的未操作者。如果没传递节点ID则获取当前节点操作者（当前节点可能有多个）。
     * 本接口默认只返回提交权限的操作者数据，如果需要获取所有操作者（转发接收人需反馈、抄送接收人许反馈等）则需要onlyOperator传递false.
     *
     * @param userid            用户Id
     * @param requestId         请求ID
     * @param onlyOperator      是否只获取操作者。默认值：true。true：只获取提交权限的操作者。false：获取所有操作者（提交、抄送接收人、转发接收人等
     * @param userCurrentNodeId 节点ID
     * @return {
     * "1022951027002875955": {
     * "nodeId": "1022951027002875955",
     * "nodeName": "1级审批",
     * "operatorUsers": [
     * {
     * "userId": "110001700000001879",
     * "userName": "郑启辉",
     * "mainUserId": "110001700000001879",
     * "departMentId": "110001590000000287",
     * "isRemark": 100
     * },
     * {
     * "userId": "110001700000001877",
     * "userName": "dwliaoqiaofeng",
     * "mainUserId": "110001700000001877",
     * "departMentId": "110001590000000287",
     * "isRemark": 100
     * }
     * ]
     * }
     */
    @Override
    public JSONObject getNodeOperator(Long userid, Long requestId, Boolean onlyOperator, Long userCurrentNodeId) {
        String methodName = String.format("调用%s.getNodeOperator(%s,%s,%s,%s)", simpleName, userid, requestId, onlyOperator, userCurrentNodeId);
        Map<String, String> headers = new HashMap<>();
        String host = HostUtil.beforeRequestCheckHeaders(cmicProperties.getOpenPlatformUrl(), headers, contentTypeVal);
        host += "/api/workflow/core/paService/v1/getNodeOperator";
        log.info("{} 请求地址：{}", methodName, host);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("access_token", getAccessToken());
        paramMap.put("userid", userid);
        paramMap.put("requestId", requestId);
        if (onlyOperator != null) {
            paramMap.put("onlyOperator", onlyOperator);
        }
        if (userCurrentNodeId != null) {
            paramMap.put("userCurrentNodeId", userCurrentNodeId);
        }
        String resJson = "";
        try {

            resJson = (HttpRequest.post(host).body(JSON.toJSONString(paramMap)).headerMap(headers, true)).execute().body();
        } catch (Exception e) {
            log.error("｛｝ 请求开放平台获取节点操作者异常", simpleName, e);
        }
        log.info("{} 获取到的节点操作者数据：{}", methodName, resJson);
        JSONObject dataJson = new JSONObject();
        if (!resJson.equals("")) {
            JSONObject jsonObject = JSON.parseObject(resJson);

            if (jsonObject.containsKey(messageKey)) {
                JSONObject messageJson = jsonObject.getJSONObject(messageKey);
                String errcode = messageJson.getString(errcodeKey);
                if (errcode.equals("0")) {
                    dataJson = jsonObject.getJSONObject("data");
                } else {
                    log.error("{} 获取到节点操作者数据异常", methodName, JSON.toJSONString(resJson));
                }

            } else {
                log.error("{} 获取到节点操作者message为空", methodName, JSON.toJSONString(resJson));
            }
        } else {
            log.error("｛｝ 获取到节点操作者数据为空", methodName);
        }

        return dataJson;
    }

    /**
     * 请求开放平台 获取流程信息
     *
     * @param userid    用户ID
     * @param requestId 审批数据ID
     * @return
     */
    @Override
    public WorkFlowRequestInfo getWorkflowRequest(Long userid, String requestId) {
        String methodName = String.format("调用%s.getWorkflowRequest(%s,%s)", simpleName, userid, requestId);
        FlowVo flowVo = new FlowVo();
        flowVo.setAccessToken(getAccessToken());
        flowVo.setUserid(userid);
        flowVo.setRequestId(requestId);
        WorkFlowRequestInfoResultVo normalRes = FlowService.getWorkflowRequestV1(flowVo, cmicProperties.getOpenPlatformUrl(), null);
        WorkFlowRequestInfo data = new WorkFlowRequestInfo();
        if (normalRes.getMessage() != null) {
            Integer errcode = normalRes.getMessage().getErrcode();
            if (errcode == 0) {
                data = normalRes.getData();
            } else {
                log.error("{} 请求获取流程信息异常{}", methodName, JSON.toJSONString(normalRes));
            }
        } else {
            log.error("{} 请求获取流程信息为空", simpleName);
        }

        return data;
    }

    /**
     * 请求开放平台 获取团队人员账号信息
     *
     * @param userid 用户ID
     * @return 账号信息
     * @throws Exception
     */
    @Override
    public AccountVo findAccount(long userid) {
        String methodName = "调用" + simpleName + ".findAccount()";
        BasicVo basicVo = new BasicVo();
        basicVo.setAccessToken(getAccessToken());
        basicVo.setUserid(userid);
        BasicResultVo account = BasicService.findAccount(basicVo, cmicProperties.getOpenPlatformUrl(), null);
        if (account.getCode() != 200) {
            log.error("{} 请求开放平台获取团队人员账号信息接口异常：{}", methodName, JSON.toJSONString(account));
        }
        return account.getData();
    }


    /**
     * 请求开放平台 查询所有人员
     *
     * @return List<UserInfoResult>
     * <AUTHOR>
     * @Date 11:06 2024/7/4
     **/
    @Override
    public List<UserInfoResult> findAllUsersV3() {
        return findAllUsersRecursive(1, new ArrayList<>());
    }

    /**
     * 请求开放平台  获取流程签字意见
     *
     * @param userid    用户ID
     * @param requestId 审批数据ID
     * @return 结果
     */
    @Override
    public List<FlowSign> getRequestLog(Long userid, String requestId) {
        String methodName = String.format("调用%s.getRequestLog(%s,%s)", simpleName, userid, requestId);
        List<FlowSign> data = new ArrayList<>();
        FlowVo flowVo = new FlowVo();
        flowVo.setAccessToken(getAccessToken());
        flowVo.setUserid(userid);
        flowVo.setRequestId(requestId);
        Map<String, Object> otherParams = new HashMap<>();
        otherParams.put("pageSize", 100);
        otherParams.put("pageNumber", 1);
        flowVo.setOtherParams(JSON.toJSONString(otherParams));
        FlowSignResultVo resultVo = FlowService.getRequestLogV1(flowVo, cmicProperties.getOpenPlatformUrl(), null);
//        log.info("{} 获取到的流程签字意见：{}",methodName,JSON.toJSONString(resultVo));
        if (resultVo.getMessage() != null) {
            if (resultVo.getMessage().getErrcode() == 0) {
                data.addAll(resultVo.getData());
            } else {
                log.error("{} 获取流程签字意见异常：{}", methodName, JSON.toJSONString(resultVo));
            }
        } else {
            log.error("{} 获取流程签字意见为空", methodName);
        }
        return data;
    }

    /**
     * 请求开放平台  查询人员
     *
     * @param userid 用户ID
     * @param name   用户名
     * @param email  邮箱
     * @param mobile 手机号
     * @param jobNum 编号（登录名）
     * @return 结果  List<UserResult>
     */
    @Override
    public List<UserResult> findUser(String userid, String name, String email, String mobile, String jobNum) {
        String methodName = String.format("调用%s.findUser(%s,%s,%s,%s,%s)", simpleName, userid, name, email, mobile, jobNum);
        List<UserResult> data = new ArrayList<>();
        UserVo userVo = new UserVo();
        userVo.setAccessToken(getAccessToken());

        if (userid != null && !userid.isEmpty()) {
            userVo.setUserid(Long.getLong(userid));
        }
        if (name != null && !name.isEmpty()) {
            userVo.setName(name);
        }
        if (email != null && !email.isEmpty()) {
            userVo.setEmail(email);
        }
        if (mobile != null && !mobile.isEmpty()) {
            userVo.setMobile(mobile);
        }
        if (jobNum != null && !jobNum.isEmpty()) {
            userVo.setJobNum(jobNum);
        }
        log.info("{} 请求查询人员信息参数： {}", methodName, JSON.toJSONString(userVo));
        NormalRes<List<UserResult>> normalRes = UserService.findUserV3(userVo, cmicProperties.getOpenPlatformUrl(), null);
        log.info("{} 查询人员信息：{}", methodName, JSON.toJSONString(normalRes));
        if (normalRes.getMessage().getErrcode() == 0) {
            data = normalRes.getData();
        } else {
            log.error("{} 查询用户信息返回异常：{}", methodName, JSON.toJSONString(normalRes));
        }
        return data;
    }


    /**
     * 递归查询用户
     *
     * @param page     分页页码，从1开始（不传值则默认为1，每页1000条数据）
     * @param allUsers 用户数据
     * @return com.weaver.openapi.pojo.user.res.UserResultVo
     * <AUTHOR>
     * @Date 11:17 2024/7/4
     **/
    public List<UserInfoResult> findAllUsersRecursive(Integer page, List<UserInfoResult> allUsers) {
        UserVo userVo = new UserVo();
        userVo.setAccessToken(getAccessToken());
        userVo.setPage(page);
        int pageCount = 1000;
        String logMsg = "开放平台，查询所有用户，当前页";
        UserResultVo result = new UserResultVo();
        try {
            result = UserService.findAllUsersV3(userVo, cmicProperties.getOpenPlatformUrl(), null);
            // 打印当前返回结果数量
            log.info("{}：{}; 返回数量：{}", logMsg, page, result.getData().size());
            if (result.getMessage().getErrcode() != 0) {
                log.info("{}：{}; 查询失败：{}", logMsg, page, result.getMessage());
            } else {
                if (!result.getData().isEmpty()) {
                    // 将当前页的用户数据添加到总用户列表中
                    allUsers.addAll(result.getData());
                }
            }
        } catch (Exception e) {
            log.info("{}：{}; 查询异常：{}", logMsg, page, e.getMessage());
        }
        // 判断是否需要继续递归查询
        if (result.getData().size() == pageCount) {
            // 继续递归查询下一页
            return findAllUsersRecursive(page + 1, allUsers);
        } else {
            // 返回所有用户数据
            return allUsers;
        }
    }

    /**
     * 人员查询-高级搜索
     *
     * @param jobNumList    工号
     * @param subcompanyIds 分部id
     * @param departmentIds 岗位id
     * @param ids           人员id
     * @param current       分页-当前页
     * @param pageSize      分页-页大小（最大1000）
     * @param nameLikeList  名称模糊查询
     * @param account       账号（支持： 卡片-账号信息中 账号、绑定手机、绑定邮箱、登录名（weaver-hrm-service 1.14后支持））
     */
    @Override
    public List<EmployeeData> queryEmployee(List<String> jobNumList, List<String> subcompanyIds, List<String> departmentIds, List<String> ids, Integer current, Integer pageSize, List<String> nameLikeList, String account) {
        String methodName = String.format("调用%s.queryEmployee()", simpleName);
        UserVo userVo = new UserVo();
        List<EmployeeData> data = new ArrayList<>();
        userVo.setAccessToken(getAccessToken());
        userVo.setContainUserInfo(true);
        userVo.setContainEmployeeExtend(true);
        userVo.setContainUserInfoExtend(true);
        userVo.setNeedAccountInfo(true);
        if (!jobNumList.isEmpty()) {
            userVo.setJobNumList(jobNumList);
        }
        if (!subcompanyIds.isEmpty()) {
            userVo.setSubcompanyIds(subcompanyIds);
        }
        if (!departmentIds.isEmpty()) {
            userVo.setDepartmentIds(departmentIds);
        }
        if (!ids.isEmpty()) {
            userVo.setIds(ids);
        }
        if (!nameLikeList.isEmpty()) {
            userVo.setNameLikeList(nameLikeList);
        }
        if (account != null && !"".equals(account)) {
            userVo.setAccount(account);
        }
        QueryEmployeeResultVo queryEmployeeResultVo = UserService.queryEmployee(userVo, cmicProperties.getOpenPlatformUrl(), null);
        if (queryEmployeeResultVo.getMessage() != null) {
            if (queryEmployeeResultVo.getMessage().getErrcode() == 0) {
                data.addAll(queryEmployeeResultVo.getData().getData());
            } else {
                log.error("{} 请求人员查询-高级搜索异常：{}", methodName, JSON.toJSONString(queryEmployeeResultVo));
            }
        } else {
            log.error("{} 请求人员查询-高级搜索为空", methodName);
        }
        return data;
    }

    /**
     * 获取下一节点操作者信息
     *
     * @param userid    用户ID
     * @param requestId 审批数据ID
     */
    @Override
    public List<WorkFlowRequestNextOperator> getRequestNextOperator(Long userid, String requestId) {
        String methodName = String.format("调用%s.getRequestNextOperator(%s,%s)", simpleName, userid, requestId);
        FlowVo flowVo = new FlowVo();
        flowVo.setAccessToken(getAccessToken());
        flowVo.setUserid(userid);
        flowVo.setRequestId(requestId);
        WorkFlowNextOperatorResultVo normalRes = FlowService.getRequestNextOperatorV1(flowVo, cmicProperties.getOpenPlatformUrl(), null);
        List<WorkFlowRequestNextOperator> workFlowRequestNextOperator = new ArrayList<>();
        log.info("{} 请求获取下一节点操作者信息获得数据：{}", methodName, JSON.toJSONString(normalRes));
        if (normalRes.getMessage() != null) {
            if (normalRes.getMessage().getErrcode() == 0) {
                workFlowRequestNextOperator = normalRes.getData();
            } else {
                log.error("{} 获取下一节点操作者信息异常：{}", methodName, JSON.toJSONString(normalRes));
            }
        } else {
            log.error("{} 获取下一节点操作者信息为空", methodName);
        }

        return workFlowRequestNextOperator;
    }

    /**
     * 创建审批请求
     *
     * @param userid      用户ID
     * @param requestName 审批名称
     * @param workflowId  审批流程ID
     * @param formData    表单数据
     * @param remark      备注
     * @param isNextFlow  是否自动流转下一节点
     * @return 请求ID
     */
    @Override
    public String createRequest(Long userid, String requestName, String workflowId, String formData, String remark, Integer isNextFlow) {
        String methodName = String.format("调用%s.createRequest(%s,%s,%s,%s,%s,%s)", simpleName, userid, requestName, workflowId, formData, remark, isNextFlow);
        FlowVo flowVo = new FlowVo();
        flowVo.setAccessToken(getAccessToken());
        flowVo.setUserid(userid);
        flowVo.setRequestname(requestName);
        flowVo.setWorkflowId(workflowId);
        flowVo.setFormData(formData);
        flowVo.setRemark(remark);
        Map<String, Object> otherParams = new HashMap<>();
        otherParams.put("isNextFlow".toLowerCase(), isNextFlow);
        flowVo.setOtherParams(JSONObject.toJSONString(otherParams));
        FlowRequestResultVo flowRequestResultVo = FlowService.doCreateRequestV1(flowVo, cmicProperties.getOpenPlatformUrl(), null);
        log.info("{} 请求创建审批请求获得数据：{}", methodName, JSON.toJSONString(flowRequestResultVo));
        return flowRequestResultVo.getMessage().getRequestId();
    }

    /**
     * 获取所有数据(分页)
     *
     * @param headers  请求参数{"checkRight": "n","isReturnDetail": "y","objId": 647423567994028033,"recordCount": "y"}
     *                 checkRight是否校验权限(默认校验; n不校验; y校验)非必填
     *                 isReturnDetail是否需要返回明细(默认不返回; y返回; n不返回)非必填
     *                 recordCount是否统计表单数据(默认不统计; y统计; n不统计)非必填
     *                 objId 表单id(必填)
     * @param pageInfo 分页信息 {"pageNo": "9","pageSize": "10"}
     *                 pageNo 当前页码 默认1 非必填
     *                 pageSize 当前分页大小 默认20 非必填
     * @param userid   用户ID 必填
     * @return 数据
     */
    @Override
    public String getAllPaginationData(Map<String, Object> headers, Map<String, Object> pageInfo, Long userid) {
        String methodName = String.format("调用%s.getAllPaginationData(%s,%s,%s)", simpleName, headers, pageInfo, userid);
        Map<String, Object> paramMap = new HashMap<>();
        Map<String, Object> dataJson = new HashMap<>();
        dataJson.put("header", headers);
        dataJson.put("pageInfo", pageInfo);
        paramMap.put("access_token", getAccessToken());
        paramMap.put("userid", userid);
        paramMap.put("dataJson".toLowerCase(), dataJson);
        String resJson = HttpRequest.post(cmicProperties.getOpenPlatformUrl() + "/api/ebuilder/form/dataset/v1/getAllData").body(JSONObject.toJSONString(paramMap)).headerMap(null, true).execute().body();
        log.info("{} 请求获取所有数据获得数据：{}", methodName, resJson);
        Map<String, Object> resMap = JSONObject.parseObject(resJson);
        Object data = resMap.get("data");
        Map<String, Object> paginationData = JSONObject.parseObject(JSONObject.toJSONString(data));
        return paginationData.get("dataS".toLowerCase()).toString();
    }

    /**
     * 获取所有数据总数(暂不支持筛选)
     *
     * @param userid 用户ID(必填)
     * @param objId  表单id(必填)
     * @param header {"ebbusinessid": "785723653804818434"}
     * @return 数据总数
     */
    @Override
    public Integer getAllDataCount(Long userid, Long objId, Map<String, String> header) {
        String methodName = String.format("调用%s.getAllDataCount(%s,%s,%s)", simpleName, userid, objId, header);
        EbFormVo ebFormVo = new EbFormVo();
        ebFormVo.setAccessToken(getAccessToken());
        ebFormVo.setUserid(userid);
        ebFormVo.setObjId(objId);
        EbFormResultVo result = EbFormService.getAllDataCount(ebFormVo, cmicProperties.getOpenPlatformUrl(), header);
        log.info("{} 请求获取所有数据总数获得数据：{}", methodName, JSON.toJSONString(result));
        return result.getData().getDataCount();
    }

    @Override
    public List<String> getAllSubDepartmentId(Long departmentId) {
        String methodName = String.format("调用%s.getAllSubDepartmentId(%s)", simpleName, departmentId);
        DeptListParam deptListParam = new DeptListParam(getAccessToken(), null, null, departmentId);
        DeptList deptList = DeptService.listDeptV2(deptListParam, cmicProperties.getOpenPlatformUrl(), null);
        log.info("{} 请求获取所有子部门获得数据：{}", methodName, JSON.toJSONString(deptList));
        return deptList.getDepartment().stream().map(DeptInfo::getId).collect(Collectors.toList());
    }

}
