package com.weaver.seconddev.zyhlw.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.ebuilder.common.util.PinyinUtil;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.ebuilder.form.client.service.emobile.IEtFormDatasetService;
import com.weaver.framework.rpc.annotation.RpcReference;
import com.weaver.openapi.pojo.flow.res.vo.FlowList;
import com.weaver.seconddev.zyhlw.domain.portalworkflow.AddCollectionDTO;
import com.weaver.seconddev.zyhlw.domain.portalworkflow.NewWorkFlowDTO;
import com.weaver.seconddev.zyhlw.domain.request.eb.EbFormDataReq;
import com.weaver.seconddev.zyhlw.domain.response.form.EbFormDataResultVo;
import com.weaver.seconddev.zyhlw.service.*;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.ebuilder.EBuilderUtil;
import com.weaver.seconddev.zyhlw.util.workflow.WfcUtil;
import com.weaver.teams.domain.user.SimpleEmployee;
import com.weaver.teams.security.context.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.weaver.teams.print.util.date.DateType.b;

/**
 * <h1>PortalWorkflowServiceImpl 流程中心</h1>
 *
 * <p>Description: </p>
 *
 * <p>Note: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@Service
public class PartyStyleServiceImpl implements IPartyStyleService {

    private final String SIMPLE_NAME = PartyStyleServiceImpl.class.getSimpleName();

    @RpcReference(group = "ebuilderform")
    IEtFormDatasetService iEtFormDatasetService;

    @Resource
    CmicProperties cmicProperties;
    @Resource
    IOpenPlatformService iOpenPlatformService;

    @Resource
    IDataSqlService dataSqlService;

    @Resource
    IDataBaseService iDataBaseService;

    @Override
    public WeaResult<Object> fxlyByClause(String year, String quarterly, String branch, String area) {
        String method = String.format("调用%s.fxlyByClause-->", SIMPLE_NAME);
        log.info("{}开始执行, 调用fxly2", method);

        return fxly2(year, quarterly, branch, area);
    }

    @Override
    public WeaResult<Object> fxly2(String year, String quarterly, String branch, String area) {
        String method = String.format("调用%s.fxly2-->", SIMPLE_NAME);
        log.info("{}开始执行, year: {}, quarterly: {}, branch: {}, area: {}", method, year, quarterly, branch, area);
        int q = Integer.parseInt(quarterly);
        // 转换成月份
        int endMonth = q * 3 + 1;
        // 拼成日期: yyyy-MM-dd
        LocalDate endDate = LocalDate.of(Integer.parseInt(year), endMonth, 1);
        String sql = String.format("SELECT * FROM uf_feng_xxxk where is_delete=0 and create_time <= to_date('%s', 'yyyy-MM-dd') and fEng_xzt = 0", endDate);

        StringBuilder sqlBuilder = new StringBuilder(sql);
        // 部门
        if (StringUtils.isNotBlank(branch)) {
            sqlBuilder.append(" and ze_rbm=").append(branch);
        }
        // 领域
        if (StringUtils.isNotBlank(area)) {
            sqlBuilder.append(" and feng_xly=").append(area);
        }

        List<Map<String, Object>> dataResult = dataSqlService.eBuilderFromSqlAll(sqlBuilder.toString(), SourceType.LOGIC);

        // 查询风险领域数据
        String fxSql = "select * from uf_feng_xly where is_delete=0";
        List<Map<String, Object>> fxDataList = dataSqlService.eBuilderFromSqlAll(fxSql, SourceType.LOGIC);
        Map<String, String> fxMap = fxDataList.stream()
                .collect(Collectors.toMap(k -> k.get("id").toString(), v -> v.get("feng_xly").toString()));

        int nums = dataResult.size();
        // 领域加等级,统计每个数量
        HashMap<String, Integer> lyRank = new HashMap<>();
        // 等级A，B，C，统计每类数量
        HashMap<String, Integer> rank = new HashMap<>();
        // 领域，只统计总共多少领域，即size()
        HashMap<String, Integer> ly = new HashMap<>();

        for (Map<String, Object> objectMap : dataResult) {
            // 风险领域id
            String riskAreaId = (String) objectMap.get("feng_xly");
            // 风险等级
            String riskLevel = (String) objectMap.get("feng_xdj");
            // 风险名称
            String name = fxMap.get(riskAreaId);
            // 用风险领域名称和风险等级合并
            String key = name + "_" + riskLevel;

            lyRank.put(key, lyRank.getOrDefault(key, 0) + 1);
            rank.put(riskLevel, rank.getOrDefault(riskLevel, 0) + 1);
            ly.put(name, 1);
        }

        JSONObject result = new JSONObject();
        // 总共风险点数量
        result.put("riskNums", nums);
        // 领域数量
        result.put("areaNums", ly.size());
        // 领域名称list
        result.put("areaList", new JSONArray(new ArrayList<>(ly.keySet())));
        // 遍历rank得到每个等级的数量
        result.putAll(rank);
        result.putAll(lyRank);

        return WeaResult.success(result);
    }

    @Override
    public WeaResult<Object> allFxly() {
        String method = String.format("调用%s.allFxly-->", SIMPLE_NAME);
        log.info("{}开始执行", method);
        String fxSql = "select * from uf_feng_xly where is_delete=0";
        List<Map<String, Object>> fxDataList = dataSqlService.eBuilderFromSqlAll(fxSql, SourceType.LOGIC);
        List<Map<String, String>> fxList = new ArrayList<>();
        for (Map<String, Object> objectMap : fxDataList) {
            Map<String, String> fxMap = new HashMap<>();
            fxMap.put("id", objectMap.get("id").toString());
            fxMap.put("name", objectMap.get("feng_xly").toString());
            fxList.add(fxMap);
        }
        return WeaResult.success(fxList);
    }

    @Override
    public WeaResult<Object> other(String year, String quarterly, String tableName) {
        String method = String.format("调用%s.other-->", SIMPLE_NAME);
        log.info("{}开始执行, year: {}, quarterly: {}, tableName: {}", method, year, quarterly, tableName);
        int q = Integer.parseInt(quarterly);
        // 转换成月份
        int endMonth = q * 3 + 1;
        int startMonth = endMonth - 3;
        // 拼成日期: yyyy-MM-dd
        String endDate = String.format("%s-%02d-01", year, endMonth);
        String startDate = String.format("%s-%02d-01", year, startMonth);
        // 风险点新增
        String sql = String.format("select b.*,a.requestid from wfc_form_data a,%s b where a.dataid=b.form_data_id and a.delete_type=0 and a.tenant_key='%s' and b.shen_qrq < '%s' and b.shen_qrq >= '%s' and b.delete_type = 0", tableName, cmicProperties.getHostTenantKey(), endDate, startDate);
        List<Map<String, Object>> dataResult = dataSqlService.workflowFromSqlAll(sql, SourceType.LOGIC);
        int nums = dataResult.size();
        Map<String, String> wfcMap = new HashMap<>();
        // 查询流程节点
        if(!dataResult.isEmpty()) {
            String requestIds = StringUtils.join(dataResult.stream().map(k -> k.get("requestid")).collect(Collectors.toList()), ",");
            String wfcSql = String.format("select r.*,c.nodetype from wfc_requestbase r left join wfc_currentnode c on r.requestid=c.requestid where r.tenant_key='%s' and r.requestid in(%s) and r.delete_type = 0", cmicProperties.getHostTenantKey(), requestIds);
            List<Map<String, Object>> wfcList = dataSqlService.workflowFromSqlAll(wfcSql, SourceType.LOGIC);
            wfcMap = wfcList.stream()
                    .collect(Collectors.toMap(k -> k.get("requestid").toString(), v -> v.get("nodetype").toString()));
        }
        // 已审批
        int approve = 0;
        // 未审批
        int unApprove = 0;
        // 等级A，B，C，统计每类数量
        HashMap<String, Integer> rank = new HashMap<>();
        for (Map<String, Object> objectMap : dataResult) {
            String riskLevel = (String) objectMap.get("feng_xdj");
            // 为3为已审批，其他未审批
            String type = wfcMap.get(objectMap.get("requestid").toString());
            if ("3".equals(type)) {
                approve++;
            } else {
                unApprove++;
            }
            rank.put(riskLevel, rank.getOrDefault(riskLevel, 0) + 1);
        }
        JSONObject result = new JSONObject();
        result.put("nums", nums);
        result.put("approve", approve);
        result.put("unApprove", unApprove);
        // 遍历rank得到每个等级的数量
        result.putAll(rank);

        return WeaResult.success(result);
    }

    @Override
    public WeaResult<Object> allDept() {
        String method = String.format("调用%s.allDept-->", SIMPLE_NAME);
        log.info("{}开始执行", method);
        //查询顶级部门
        String sql = "SELECT hrmDepartment.id AS id, hrmDepartment.fullname AS departmentname FROM eteams.department hrmDepartment" +
                " WHERE hrmDepartment.rank = 3 AND hrmDepartment.status != 0 AND hrmDepartment.id != 204 AND hrmDepartment.id != 1021" +
                " AND hrmDepartment.id != 4524" +
                " AND hrmDepartment.type = 'department'" +
                " AND hrmDepartment.virtualid = 1" +
                " AND hrmDepartment.delete_type = 0" +
                " AND hrmDepartment.tenant_key = '" + cmicProperties.getHostTenantKey() + "'";
        List<Map<String, Object>> dataResult = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        List<Map<String, String>> bmList = new ArrayList<>();
        for (Map<String, Object> objectMap : dataResult) {
            Map<String, String> bmMap = new HashMap<>();
            bmMap.put("id", objectMap.get("id").toString());
            bmMap.put("name", objectMap.get("departmentname").toString());
            bmList.add(bmMap);
        }
        return WeaResult.success(bmList);

    }

    @Override
    public WeaResult<Object> getRiskDomain() {
        String method = String.format("调用%s.getRiskDomain-->", SIMPLE_NAME);
        log.info("{}开始执行", method);
        try {
            String sql = "select c.id,c.feng_xly  from  uf_feng_xly  c ,(select  to_char(feng_xly) feng_xly  from  uf_feng_xxxk   where feng_xzt ='0' and delete_type = 0 group  by  to_char(feng_xly))  e  where  e.feng_xly  =  c.id  order  by  c.xu_h  asc";
            List<Map<String, Object>> dataResult = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
            dataResult.forEach(map -> map.put("id", map.get("id").toString()));
            return WeaResult.success(dataResult);
        } catch (Exception e) {
            log.info("{}开始执行, 异常：{}", method, e.getMessage());
            return WeaResult.fail(e.getMessage());
        }
    }

    @Override
    public WeaResult<Object> getRiskDepartment() {
        String method = String.format("调用%s.getRiskDepartment-->", SIMPLE_NAME);
        log.info("{}开始执行", method);
        try {
            String sql = "SELECT a.bu_mmc AS bu_mmc, b.fullname AS departmentname FROM uf_zhu_ztbmpx a JOIN eteams.department b ON a.bu_mmc = b.id AND b.type = 'department' AND b.virtualid = 1 AND b.delete_type = 0 AND b.tenant_key = '%s'  ORDER BY bu_mxh asc";
            sql = String.format(sql, cmicProperties.getHostTenantKey());
            List<Map<String, Object>> dataResult = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
            dataResult.forEach(map -> map.put("id", map.get("bu_mmc").toString()));
            return WeaResult.success(dataResult);
        } catch (Exception e) {
            log.info("{}开始执行, 异常：{}", method, e.getMessage());
            return WeaResult.fail(e.getMessage());
        }
    }

    @Override
    public WeaResult<Object> RiskStatisticalStatement() {
        String method = String.format("调用%s.RiskStatisticalStatement-->", SIMPLE_NAME);
        log.info("{}开始执行", method);
        try {
            //风险数据集合
            Map<String, Integer> map = new HashMap<>();
            SimpleDateFormat ymd = new SimpleDateFormat("yyyy-MM-dd");      //日期格式化：年-月-日
            String now = ymd.format(new Date()); //当前日期
            //最新的数据总览
            String nowSql = "select id,zong_fxly,zong_fxd,a_jfxd,b_jfxd,c_jfxd from uf_shu_jzljl where id = (select max(id) as id from uf_shu_jzljl)";
            Map<String, Object> dataScreening = dataSqlService.eBuilderFromSqlOne(nowSql, SourceType.LOGIC);

            //总风险领域     label:"标题",value:数量值,compare:今月与上月比较值
            Integer riskAreaSum = this.riskArea("");
            map.put("riskAreaValue", riskAreaSum);
            Integer thisMonth = this.riskArea(now);
            Integer lastMonth = (Integer) dataScreening.getOrDefault("zong_fxly", 0);
            if (thisMonth != null) {
                Integer compare = thisMonth - lastMonth;
                map.put("riskAreaCompare", compare);
            }

            //总风险点 A级风险点 B级风险点 C级风险点
            String sql = "select count(*) sum,count(CASE WHEN d.feng_xdj= '0' THEN 1 END) a,count(CASE WHEN d.feng_xdj = '1' THEN 1 END) b,count(CASE WHEN d.feng_xdj='2' THEN 1 END)c from uf_feng_xxxk d where d.create_time <= to_date('%s', 'yyyy-MM-dd') and feng_xzt !=1 and delete_type = 0";
            sql = String.format(sql, now);
            Map<String, Object> data = dataSqlService.eBuilderFromSqlOne(sql, SourceType.LOGIC);
            if (!data.isEmpty()) {
                int sum = (int) data.getOrDefault("sum", 0);
                int a = (int) data.getOrDefault("a", 0);
                int b = (int) data.getOrDefault("b", 0);
                int c = (int) data.getOrDefault("c", 0);
                map.put("riskPointSumValue", sum);
                map.put("riskPointAValue", a);
                map.put("riskPointBValue", b);
                map.put("riskPointCValue", c);
                sum = sum - Integer.parseInt(dataScreening.get("zong_fxd").toString());
                a = a - Integer.parseInt(dataScreening.get("a_jfxd").toString());
                b = b - Integer.parseInt(dataScreening.get("b_jfxd").toString());
                c = c - Integer.parseInt(dataScreening.get("c_jfxd").toString());
                map.put("riskPointSumCompare", sum);
                map.put("riskPointACompare", a);
                map.put("riskPointBCompare", b);
                map.put("riskPointCCompare", c);
            }
            return WeaResult.success(map);
        } catch (Exception e) {
            log.info("{}开始执行, 异常：{}", method, e.getMessage());
            return WeaResult.fail(e.getMessage());

        }
    }

    @Override
    public WeaResult<Object> HuoQuRiskTongJi(String lx, String sj, String dType) {
        String method = String.format("调用%s.HuoQuRiskTongJi-->", SIMPLE_NAME);
        log.info("{}开始执行，lx：{}，sj：{}，dType：{}", method, lx, sj, dType);
        try {
            HashMap<String, String> map = new HashMap<>();
            JSONObject jsonObject = new JSONObject();
            List<String> dates = Arrays.asList(sj.split(","));
            if ("1".equals(dType)) {
                JSONObject dataType = getDataType(lx);
                jsonObject.put("xAxisData", dataType.get("xAxisData"));
                jsonObject.put("series", dataType.get("series"));
            } else if ("2".equals(dType)) {
                //风险点变化分析数据
                HashMap<String, ArrayList<Map<String, Object>>> levelArray2 = getArray2(dates);
                if (!levelArray2.isEmpty()) {
                    jsonObject.put("series2", levelArray2.get("series2"));
                    jsonObject.put("riskPointList", levelArray2.get("riskPointList"));
                } else {
                    jsonObject.put("series2", map);
                    jsonObject.put("riskPointList", map);
                }
            } else if ("3".equals(dType)) {
                //互联网风险点与集团映射关系
                HashMap<String, ArrayList<Map<String, Object>>> riskMapping = getRiskMapping();
                if (!riskMapping.isEmpty()) {
                    jsonObject.put("series3", riskMapping.get("data"));
                    jsonObject.put("mappingList", riskMapping.get("mappingList"));
                } else {
                    jsonObject.put("series3", map);
                    jsonObject.put("mappingList", map);
                }
            }
            return WeaResult.success(jsonObject);

        } catch (Exception ex) {
            log.info("{}开始执行, 异常：{}", method, ex.getMessage());
            return WeaResult.fail(ex.getMessage());
        }
    }

    @Override
    public WeaResult<Object> ResponsiblePersonStatistics(String dep) {
        String method = String.format("调用%s.ResponsiblePersonStatistics-->", SIMPLE_NAME);
        log.info("{}开始执行，dep：{}", method, dep);
        Map<Object, Object> resultMap;
        try {
            if (dep.isEmpty()) {
                String sql = "select zhi_jzrr,ze_rld,zhu_zbmfkzrr from uf_feng_xxxk where feng_xzt = 0 and delete_type = 0";
                List<Map<String, Object>> data = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
                StringBuilder stringBuilder = new StringBuilder();
                StringBuilder stringBuilder2 = new StringBuilder();
                StringBuilder stringBuilder3 = new StringBuilder();
                StringBuilder stringBuilder4 = new StringBuilder();
                StringBuilder stringBuilder5 = new StringBuilder();
                StringBuilder stringBuilder6 = new StringBuilder();
                if (!data.isEmpty()) {
                    String zhiJzrrs = data.stream()
                            .map(map -> map.get("zhi_jzrr"))
                            .filter(Objects::nonNull)
                            .map(Object::toString)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.joining(","));

                    String zeRlds = data.stream()
                            .map(map -> map.get("ze_rld"))
                            .filter(Objects::nonNull)
                            .map(Object::toString)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.joining(","));

                    String zhuZbmfkzrrs = data.stream()
                            .map(map -> map.get("zhu_zbmfkzrr"))
                            .filter(Objects::nonNull)
                            .map(Object::toString)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.joining(","));

                    stringBuilder.append(zhiJzrrs);
                    stringBuilder2.append(zeRlds);
                    stringBuilder3.append(zhuZbmfkzrrs);
                }

                String sql2 = "select a.zhi_xbmzjzrr,a.zhi_xbmfkzrr,a.zhi_xbmzrld from uf_feng_xxxk_dt2 a join uf_feng_xxxk b on a.form_data_id = b.id where b.feng_xzt = 0 and b.delete_type = 0";
                List<Map<String, Object>> data2 = dataSqlService.eBuilderFromSqlAll(sql2, SourceType.LOGIC);

                if (!data2.isEmpty()) {
                    String zhiXbmzjzrr = data2.stream()
                            .map(map -> map.get("zhi_xbmzjzrr"))
                            .filter(Objects::nonNull)
                            .map(Object::toString)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.joining(","));
                    String zhiXbmfkzrr = data2.stream()
                            .map(map -> map.get("zhi_xbmfkzrr"))
                            .filter(Objects::nonNull)
                            .map(Object::toString)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.joining(","));
                    String zhiXbmzrld = data2.stream()
                            .map(map -> map.get("zhi_xbmzrld"))
                            .filter(Objects::nonNull)
                            .map(Object::toString)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.joining(","));

                    stringBuilder4.append(zhiXbmzjzrr);
                    stringBuilder5.append(zhiXbmfkzrr);
                    stringBuilder6.append(zhiXbmzrld);
                }

                resultMap = getReturnCollection(stringBuilder, stringBuilder2, stringBuilder3, stringBuilder4, stringBuilder5, stringBuilder6);
                log.info("{}, map:{} ", method, resultMap);
            } else {
                String depSql = "SELECT id AS id, parent AS supdepid, rank AS tlevel FROM eteams.department WHERE type = 'department' AND virtualid = 1 AND delete_type = 0 AND tenant_key = '%s' START WITH id = %s CONNECT BY PRIOR id = parent";
                depSql = String.format(depSql, cmicProperties.getHostTenantKey(), dep);
                ArrayList<Object> objects = new ArrayList<>();
                List<Map<String, Object>> data = dataSqlService.eBuilderFromSqlAll(depSql, SourceType.LOGIC);
                for (Map<String, Object> datum : data) {
                    objects.add(datum.get("id"));
                }
                log.info("{}部门下辖科室objects: {}", method, objects);
                StringBuilder stringBuilder = new StringBuilder();
                StringBuilder stringBuilder2 = new StringBuilder();
                StringBuilder stringBuilder3 = new StringBuilder();
                StringBuilder stringBuilder4 = new StringBuilder();
                StringBuilder stringBuilder5 = new StringBuilder();
                StringBuilder stringBuilder6 = new StringBuilder();
                StringBuilder sb = new StringBuilder();
                for (Object obj : objects) {
                    sb.append(obj.toString()).append(", ");
                }

                if (sb.length() > 0) {
                    sb.setLength(sb.length() - 2);
                }

                String result = sb.toString();
                String sql1 = "select zhi_jzrr,ze_rld,zhu_zbmfkzrr from uf_feng_xxxk where feng_xzt = 0 and ze_rbm in (" + result + ") and delete_type=0";
                List<Map<String, Object>> data2 = dataSqlService.eBuilderFromSqlAll(sql1, SourceType.LOGIC);
                if (!data2.isEmpty()) {
                    String zhiJzrrs = data.stream()
                            .map(map -> map.get("zhi_jzrr"))
                            .filter(Objects::nonNull)
                            .map(Object::toString)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.joining(","));

                    String zeRlds = data.stream()
                            .map(map -> map.get("ze_rld"))
                            .filter(Objects::nonNull)
                            .map(Object::toString)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.joining(","));

                    String zhuZbmfkzrrs = data.stream()
                            .map(map -> map.get("zhu_zbmfkzrr"))
                            .filter(Objects::nonNull)
                            .map(Object::toString)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.joining(","));

                    stringBuilder.append(zhiJzrrs);
                    stringBuilder2.append(zeRlds);
                    stringBuilder3.append(zhuZbmfkzrrs);

                }

                for (Object obj : objects) {
                    String sql = "select a.zhi_xbmzjzrr,a.zhi_xbmfkzrr,a.zhi_xbmzrld from uf_feng_xxxk_dt2 a join uf_feng_xxxk b on a.form_data_id = b.id where b.feng_xzt = 0 and a.zhi_xbm like '%" + obj +"%' and a.delete_type=0 and b.delete_type=0";
                    List<Map<String, Object>> dataObj = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);

                    if (!dataObj.isEmpty()) {
                        String zhiXbmzjzrr = data.stream()
                                .map(map -> map.get("zhi_xbmzjzrr"))
                                .filter(Objects::nonNull)
                                .map(Object::toString)
                                .filter(StringUtils::isNotBlank)
                                .collect(Collectors.joining(","));

                        String zhiXbmfkzrr = data.stream()
                                .map(map -> map.get("zhi_xbmfkzrr"))
                                .filter(Objects::nonNull)
                                .map(Object::toString)
                                .filter(StringUtils::isNotBlank)
                                .collect(Collectors.joining(","));

                        String zhiXbmzrld = data.stream()
                                .map(map -> map.get("zhi_xbmzrld"))
                                .filter(Objects::nonNull)
                                .map(Object::toString)
                                .filter(StringUtils::isNotBlank)
                                .collect(Collectors.joining(","));

                        stringBuilder4.append(zhiXbmzjzrr);
                        stringBuilder5.append(zhiXbmfkzrr);
                        stringBuilder6.append(zhiXbmzrld);

                    }
                }
                resultMap = getReturnCollection(stringBuilder, stringBuilder2, stringBuilder3, stringBuilder4, stringBuilder5, stringBuilder6);

                log.info("{}, dep:{}, map:{} ", method, dep, resultMap);

            }
            return WeaResult.success(resultMap);
        } catch (Exception e) {
            String errorMsg = "执行异常：" + e + "@" + e.getMessage();
            log.info("{}{}", method, errorMsg);
            return WeaResult.fail(errorMsg);
        }
    }

    @Override
    public WeaResult<Object> fxlyBydp(String branch) {
        String method = String.format("调用%s.fxlyBydp-->", SIMPLE_NAME);
        log.info("{}开始执行", method);
        //查询该季度风险信息库的内容
        String sql = "SELECT to_char(feng_xly) feng_xly FROM uf_fEng_xxXk where delete_type=0 and ze_rbm=" + branch + " group by to_char(feng_xly)";
        List<Map<String, Object>> data = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        String fxSql = "select * from uf_feng_xly where delete_type=0";
        List<Map<String, Object>> fxDataList = dataSqlService.eBuilderFromSqlAll(fxSql, SourceType.LOGIC);
        Map<String, String> fxMap = fxDataList.stream()
                .collect(Collectors.toMap(k -> k.get("id").toString(), v -> v.get("feng_xly").toString()));

        List<Map<String, Object>> list = new ArrayList<>();
        for (Map<String, Object> map : data) {
            //风险领域id
            String riskAreaId = map.get("feng_xly").toString();
            //风险名称
            String name = fxMap.getOrDefault(riskAreaId, "");
            Map<String, Object> temp = new HashMap<>();
            temp.put("name", name);
            temp.put("id", riskAreaId);
            list.add(temp);
        }
        return WeaResult.success(list);
    }

    @Override
    public WeaResult<Object> selectDeptByLy(String lyid) {
        String method = String.format("调用%s.selectDeptByLy-->", SIMPLE_NAME);
        log.info("{}开始执行", method);
        //根据领域id查询管理部门
        String sql = "SELECT d.id AS id, d.fullname AS departmentName FROM eteams.department d WHERE d.id IN ( SELECT ze_rbm AS ze_rbm FROM uf_feng_xxxk WHERE to_char(feng_xly) = %s ) AND d.rank = 3 AND d.status != 0 AND d.id != 204 AND d.id != 1021 AND d.id != 4524 AND d.type = 'department' AND d.virtualid = 1 AND d.delete_type = 0 AND d.tenant_key = '%s'";
        sql = String.format(sql, lyid, cmicProperties.getHostTenantKey());
        log.info("{}查询sql:{}", method, sql);
        List<Map<String, Object>> dataResult = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        return WeaResult.success(dataResult);
    }

    /**
     * 风险领域总数量
     *
     * @param date
     * @return
     */
    private Integer riskArea(String date) {
        String sql = "select count(1) as sum from uf_feng_xly where delete_type = 0";
        if (!date.isEmpty()) {
            sql = sql + String.format(" and create_time <= to_date('%s', 'yyyy-MM-dd')", date);
        }
        Map<String, Object> data = dataSqlService.eBuilderFromSqlOne(sql, SourceType.LOGIC);
        return (Integer) data.get("sum");
    }

    /**
     * 获取领域或者部门数据
     * type
     */
    private JSONObject getDataType(String type) {
        HashMap<String, String> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        if ("1".equals(type)) {
            //领域名称数组
            ArrayList<String> fields = getFields();
            jsonObject.put("xAxisData", fields);
            HashMap<String, HashMap<String, Object>> fieldsInfo = getFieldsInfo();
            if (fieldsInfo.isEmpty() || fields.isEmpty()) {
                jsonObject.put("series", map);
            } else {
                List<Map<String, Object>> levelArray = getLevelArray(fieldsInfo, fields);
                jsonObject.put("series", levelArray);
            }
        } else if ("2".equals(type)) {
            //部门名称数组
            ArrayList<String> departments = getDepartment();
            jsonObject.put("xAxisData", departments);
            HashMap<String, HashMap<String, Object>> departmentsInfos = getDepartmentsInfo();
            if (departmentsInfos.isEmpty() || departments.isEmpty()) {
                jsonObject.put("series", map);
            } else {
                List<Map<String, Object>> levelArray = getLevelArray(departmentsInfos, departments);
                jsonObject.put("series", levelArray);
            }
        }
        return jsonObject;
    }

    /**
     * 获取领域名称数组
     *
     * @return
     */
    private ArrayList<String> getFields() {
        ArrayList<String> fields = new ArrayList<>();
        String sql = "select  c.feng_xly  from  uf_feng_xly  c ,(select  to_char(feng_xly) feng_xly  from  uf_feng_xxxk   where feng_xzt ='0' and delete_type = 0 group  by  to_char(feng_xly))  e  where  e.feng_xly  =  c.id  order  by  c.xu_h  asc";
        List<Map<String, Object>> list = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        list.forEach(map -> fields.add(map.get("feng_xly").toString()));
        return fields;
    }

    /**
     * 获取领域风险统计详细数据
     *
     * @return
     */
    private HashMap<String, HashMap<String, Object>> getFieldsInfo() {
        HashMap<String, HashMap<String, Object>> fieldsInfors = new HashMap<>();
        String sql = "select f.feng_xly,fx.a,fx.b,fx.c from (select to_char(feng_xly) feng_xly,count(CASE WHEN  feng_xdj= '0' THEN 1 END)  a,count(CASE WHEN feng_xdj = '1' THEN 1 END) b,count(CASE WHEN feng_xdj='2' THEN 1 END)  c  from  uf_feng_xxxk   where feng_xzt ='0' and delete_type = 0  group by to_char(feng_xly))  fx ,uf_feng_xly  f  where fx.feng_xly= f.id";
        List<Map<String, Object>> list = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        for (Map<String, Object> objectMap : list) {
            fieldsInfors.put(objectMap.get("feng_xly").toString(), (HashMap<String, Object>) objectMap);
        }
        return fieldsInfors;
    }

    /**
     * 获取数组形式的级别数据
     * getfieldsInfor
     * fields
     */
    private List<Map<String, Object>> getLevelArray(HashMap<String, HashMap<String, Object>> getfieldsInfor, ArrayList<String> fields) {
        HashMap<String, Object> mapA = new HashMap<>();
        mapA.put("name", "A级");
        HashMap<String, Object> mapB = new HashMap<>();
        mapB.put("name", "B级");
        HashMap<String, Object> mapC = new HashMap<>();
        mapC.put("name", "C级");
        ArrayList<Object> a = new ArrayList<>();
        ArrayList<Object> b = new ArrayList<>();
        ArrayList<Object> c = new ArrayList<>();
        ArrayList<Map<String, Object>> maps = new ArrayList<>();
        for (String field : fields) {
            if (getfieldsInfor.containsKey(field)) {
                HashMap<String, Object> map = getfieldsInfor.get(field);
                a.add(map.get("a"));
                b.add(map.get("b"));
                c.add(map.get("c"));
            } else {
                a.add(0);
                b.add(0);
                c.add(0);
            }
        }
        mapA.put("data", a);
        mapB.put("data", b);
        mapC.put("data", c);
        maps.add(mapA);
        maps.add(mapB);
        maps.add(mapC);
        return maps;
    }

    /**
     * 获取柱状图部门排序配置信息数组
     *
     * @return
     */
    private ArrayList<String> getDepartment() {
        ArrayList<String> departments = new ArrayList<>();
        String sql = "SELECT b.fullname AS departmentname FROM uf_zhu_ztbmpx a JOIN eteams.department b ON a.bu_mmc = b.id AND b.type = 'department' AND b.virtualid = 1" +
                " AND b.delete_type = 0 AND b.tenant_key = '%s' ORDER BY bu_mxh ASC";
        sql = String.format(sql, cmicProperties.getHostTenantKey());
        List<Map<String, Object>> list = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        list.forEach(map -> departments.add(map.get("departmentname").toString()));
        return departments;
    }

    /**
     * 获取部门风险统计详细数据
     *
     * @return
     */
    private HashMap<String, HashMap<String, Object>> getDepartmentsInfo() {
        String sql = "select e.a,e.b,e.c,f.departmentname from (select ze_rbm,count(CASE  WHEN  feng_xdj= '0' THEN 1 END) a,count(CASE WHEN feng_xdj = '1' THEN 1 END) b,count(CASE WHEN feng_xdj='2' THEN 1 END) c  from  uf_feng_xxxk   where feng_xzt ='0' and delete_type = 0  group by ze_rbm)  e,hrmdepartment  f where f.id = e.ze_rbm";
        List<Map<String, Object>> list = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);

        HashMap<String, HashMap<String, Object>> departmentsInfos = new HashMap<>();
        list.forEach(map -> departmentsInfos.put(map.get("departmentname").toString(), (HashMap<String, Object>) map));
        return departmentsInfos;
    }

    /**
     * 获取风险点变化数据
     * dates
     */
    private HashMap<String, ArrayList<Map<String, Object>>> getArray2(List<String> dates) {
        ArrayList<HashMap<String, Integer>> maps = new ArrayList<>();
        //映射关系更
        String mappingRelation = iDataBaseService.getBaseDataValue("映射关系变更申请流程", "党建管理");
        HashMap<String, Integer> mR = getChangeInfos(dates, mappingRelation);
        HashMap<String, Integer> mRS = resultConversion(mR);
        //责任人变更
        String personLiable = iDataBaseService.getBaseDataValue("责任人变更申请流程", "党建管理");
        HashMap<String, Integer> pL = getChangeInfos2(dates, personLiable, "_dt3", "lian_jfxbh", "feng_xbh");
        HashMap<String, Integer> pLs = resultConversion(pL);
        //风险点变更
        String riskChange = iDataBaseService.getBaseDataValue("风险点变更申请流程", "党建管理");
        HashMap<String, Integer> rC = getChangeInfos2(dates, riskChange, "_dt3", "feng_xbh", "feng_xbh");
        HashMap<String, Integer> rCS = resultConversion(rC);
        //风险点新增
        String newRisks = iDataBaseService.getBaseDataValue("风险点新增流程", "党建管理");
        HashMap<String, Integer> nR = getChangeInfos(dates, newRisks);
        HashMap<String, Integer> nRS = resultConversion(nR);
        //风险点撤销
        String riskCancellation = iDataBaseService.getBaseDataValue("风险点撤销申请流程", "党建管理");
        HashMap<String, Integer> rCl = getChangeInfos2(dates, riskCancellation, "_dt4", "lian_jfxbh", "feng_xbh");
        HashMap<String, Integer> rClS = resultConversion(rCl);
        //变更执行部门
        String changeExecutive = iDataBaseService.getBaseDataValue("变更执行部门申请流程（子流程）", "党建管理");
        HashMap<String, Integer> cE = getChangeInfos3(dates, changeExecutive);
        HashMap<String, Integer> cES = resultConversion(cE);

        Collections.addAll(maps, mRS, pLs, rClS, rCS, nRS, cES);
        return getLevelArray2(maps);
    }

    /**
     * 获取规定时间范围数据
     * date
     * tableName
     */
    private HashMap<String, Integer> getChangeInfos(List<String> date, String tableName) {
        String sql2 = "SELECT count(CASE WHEN feng_xdj = '0' THEN 1 END) AS a, count(CASE WHEN feng_xdj = '1' THEN 1 END) AS b, count(CASE WHEN feng_xdj = '2' THEN 1 END) AS c FROM ( SELECT a.feng_xdj AS feng_xdj FROM ( SELECT b.*, a.requestid FROM wfc_form_data a, %s b WHERE a.dataid = b.form_data_id AND a.delete_type = 0 AND a.tenant_key = '%s' ) a JOIN ( SELECT wfc_requestbase.*, wfc_currentnode.nodetype FROM wfc_requestbase LEFT JOIN wfc_currentnode ON wfc_requestbase.requestid = wfc_currentnode.requestid AND wfc_requestbase.delete_type = 0 AND wfc_requestbase.tenant_key = '%s' AND wfc_currentnode.delete_type = 0 AND wfc_currentnode.tenant_key = '%s' ) b ON b.requestid = a.requestid AND b.nodetype = 3 AND a.feng_xbh IS NOT NULL AND b.delete_type = 0 AND b.tenant_key = '%s' )";
        String sql = "SELECT count(CASE WHEN feng_xdj = '0' THEN 1 END) AS a, count(CASE WHEN feng_xdj = '1' THEN 1 END) AS b , count(CASE WHEN feng_xdj = '2' THEN 1 END) AS c FROM ( SELECT a.feng_xdj AS feng_xdj FROM ( SELECT b.*, a.requestid FROM wfc_form_data a, formtable_main_91 b WHERE a.dataid = b.form_data_id AND a.delete_type = 0 AND a.tenant_key = '%s' ) a JOIN ( SELECT wfc_requestbase.*, wfc_currentnode.nodetype FROM wfc_requestbase LEFT JOIN wfc_currentnode ON wfc_requestbase.requestid = wfc_currentnode.requestid AND wfc_requestbase.delete_type = 0 AND wfc_requestbase.tenant_key = '%s' AND wfc_currentnode.delete_type = 0 AND wfc_currentnode.tenant_key = '%s' ) b ON b.requestid = a.requestid AND b.nodetype = 3 AND a.feng_xbh IS NOT NULL AND a.gui_dsj >= '%s' AND a.gui_dsj <= '%s' AND b.delete_type = 0 AND b.tenant_key = '%s' )";
        Map<String, Object> data;
        if (date.isEmpty()) {
            sql2 = String.format(sql2, tableName, cmicProperties.getHostTenantKey(), cmicProperties.getHostTenantKey(), cmicProperties.getHostTenantKey(), cmicProperties.getHostTenantKey());
            data = dataSqlService.workflowFromSqlOne(sql2, SourceType.LOGIC);

        } else {
            sql = String.format(sql, cmicProperties.getHostTenantKey(), cmicProperties.getHostTenantKey(), cmicProperties.getHostTenantKey(), date.get(0), date.get(1), cmicProperties.getHostTenantKey());
            data = dataSqlService.workflowFromSqlOne(sql, SourceType.LOGIC);
        }
        HashMap<String, Integer> map = new HashMap<>();
        if (!data.isEmpty()) {
            map.put("a", (Integer) data.get("a"));
            map.put("b", (Integer) data.get("b"));
            map.put("c", (Integer) data.get("c"));
        } else {
            map.put("a", 0);
            map.put("b", 0);
            map.put("c", 0);
        }
        return map;
    }

    /**
     * 结果转换
     */
    private HashMap<String, Integer> resultConversion(HashMap<String, Integer> mr) {
        HashMap<String, Integer> map = new HashMap<>();
        Integer a = mr.get("a");
        Integer b = mr.get("b");
        Integer c = mr.get("c");
        Integer nums = a + b + c;
        map.put("num", nums);
        map.put("a", a);
        map.put("b", b);
        map.put("c", c);
        return map;
    }

    private HashMap<String, Integer> getChangeInfos2(List<String> date, String tableName, String detail, String fileName, String fileName2) {
        String sql = "SELECT DISTINCT b.feng_xdj AS feng_xdj, a.form_data_id AS mainid, to_char(a." + fileName + ") AS lian_jfxbh FROM " + tableName + detail + " a JOIN e10_ebuilder.uf_feng_xxxk b ON to_char(a." + fileName + ") = b.id JOIN ( SELECT b.*, a.requestid FROM wfc_form_data a, " + tableName + " b WHERE a.dataid = b.form_data_id AND a.delete_type = 0 AND a.tenant_key = '" + cmicProperties.getHostTenantKey() + "') c ON a.form_data_id = c.form_data_id JOIN ( SELECT wfcr.*, wfcc.nodetype FROM wfc_requestbase wfcr LEFT JOIN wfc_currentnode wfcc ON wfcr.requestid = wfcc.requestid AND wfcr.delete_type = 0 AND wfcr.tenant_key = '" + cmicProperties.getHostTenantKey() + "' AND wfcc.delete_type = 0 AND wfcc.tenant_key = '" + cmicProperties.getHostTenantKey() + "' ) d ON c.requestid = d.requestid AND d.delete_type = 0 AND d.tenant_key = '" + cmicProperties.getHostTenantKey() + "' WHERE d.nodetype = 3";
        if (!"".equals(fileName2)) {
            sql += " and c." + fileName2 + " is null";
        }
        if (!date.isEmpty()) {
            sql += " and c.gui_dsj >= '" + date.get(0) + "' and c.gui_dsj <= '" + date.get(1) + "'";
        }
        sql = "select count(CASE  WHEN  feng_xdj= '0'  THEN 1 END) a,count(CASE WHEN feng_xdj = '1' THEN 1 END) b,count(CASE WHEN feng_xdj='2' THEN 1 END) c from (" + sql + ")";
        log.info("getChangeInfos2 SQL_MSG-->{}", sql);

        HashMap<String, Integer> map = new HashMap<>();
        HashMap<String, Integer> stringIntegerHashMap = new HashMap<>();
        if (!"".equals(fileName2)) {
            stringIntegerHashMap = this.getChangeInfos(date, tableName);
        }
        Map<String, Object> data = dataSqlService.workflowFromSqlOne(sql, SourceType.LOGIC);
        if (!data.isEmpty()) {

            int a = (int) data.get("a");
            int b = (int) data.get("b");
            int c = (int) data.get("c");
            if (stringIntegerHashMap.isEmpty()) {
                map.put("a", a);
                map.put("b", b);
                map.put("c", c);
            } else {
                map.put("a", a + stringIntegerHashMap.get("a"));
                map.put("b", b + stringIntegerHashMap.get("b"));
                map.put("c", c + stringIntegerHashMap.get("c"));
            }
        }
        return map;
    }

    private HashMap<String, Integer> getChangeInfos3(List<String> date, String tableName) {
        String sql = "select count(CASE  WHEN  lian_jfxdj = '0'  THEN 1 END) a,count(CASE WHEN lian_jfxdj = '1' THEN 1 END) b,count(CASE WHEN lian_jfxdj = '2' THEN 1 END) c from " + tableName + " where shi_ftyzwzxbm = 0 and delete_type = 0 and gui_dsj is not null";
        if (!date.isEmpty()) {
            sql += " and gui_dsj >= '" + date.get(0) + "' and gui_dsj <= '" + date.get(1) + "'";
        }
        Map<String, Object> data = dataSqlService.workflowFromSqlOne(sql, SourceType.LOGIC);
        HashMap<String, Integer> map = new HashMap<>();
        if (!data.isEmpty()) {
            map.put("a", (Integer) data.get("a"));
            map.put("b", (Integer) data.get("b"));
            map.put("c", (Integer) data.get("c"));
        } else {
            map.put("a", 0);
            map.put("b", 0);
            map.put("c", 0);
        }
        return map;
    }

    /**
     * 获取风险操作数据数组的级别数据
     * maps
     */
    private HashMap<String, ArrayList<Map<String, Object>>> getLevelArray2(ArrayList<HashMap<String, Integer>> maps) {
        HashMap<String, ArrayList<Map<String, Object>>> map1 = new HashMap<>();
        HashMap<String, Object> mapA = new HashMap<>();
        mapA.put("name", "A级");
        HashMap<String, Object> mapB = new HashMap<>();
        mapB.put("name", "B级");
        HashMap<String, Object> mapC = new HashMap<>();
        mapC.put("name", "C级");
        ArrayList<Integer> a = new ArrayList<>();
        ArrayList<Integer> b = new ArrayList<>();
        ArrayList<Integer> c = new ArrayList<>();
        ArrayList<Integer> num = new ArrayList<>();
        ArrayList<Map<String, Object>> listMap = new ArrayList<>();
        ArrayList<Map<String, Object>> listMap2 = new ArrayList<>();
        ArrayList<String> name = new ArrayList<>();
        for (HashMap<String, Integer> map : maps) {
            a.add(map.get("a"));
            b.add(map.get("b"));
            c.add(map.get("c"));
            num.add(map.get("num"));
        }
        mapA.put("data", a);
        mapB.put("data", b);
        mapC.put("data", c);
        Collections.addAll(listMap, mapA, mapB, mapC);
        Collections.addAll(name, "映射关系变更", "责任人变更", "撤销风险点", "变更风险点", "新增风险点", "变更执行部门");
        int numSize = num.size();
        if (numSize == name.size()) {
            for (int i = 0; i < numSize; i++) {
                HashMap<String, Object> map = new HashMap<>();
                map.put("label", name.get(i));
                map.put("value", num.get(i));
                listMap2.add(map);
            }
        }
        map1.put("series2", listMap);
        map1.put("riskPointList", listMap2);
        return map1;
    }

    /**
     * 查询风险状态信息
     */
    private HashMap<String, ArrayList<Map<String, Object>>> getRiskMapping() {
        ArrayList<String> list = new ArrayList<>();
        ArrayList<Integer> list1 = new ArrayList<>();
        HashMap<String, ArrayList<Map<String, Object>>> map1 = new HashMap<>();
        ArrayList<Map<String, Object>> maps = new ArrayList<>();
        ArrayList<Map<String, Object>> maps2 = new ArrayList<>();
        //查询风险状态
        this.getRiskType(list, list1);
        //查询风险点与集团映射状态
        this.getMappingStatus(list, list1);

        if (!list1.isEmpty()) {
            if (list.size() == list1.size()) {
                for (int i = 0; i < list.size(); i++) {
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("label", list.get(i));
                    map.put("value", list1.get(i));
                    maps.add(map);
                }
            }
            if (list.size() == list1.size()) {
                for (int i = 0; i < list.size(); i++) {
                    HashMap<String, Object> map = new HashMap<>();
                    if (!("删除".equals(list.get(i)))) {
                        map.put("value", list1.get(i));
                        map.put("name", list.get(i));
                        maps2.add(map);
                    }
                }
            }
            log.info("数组数据为:{}", maps);
            log.info("数组1数据为:{}", maps2);
        }
        log.info("数据添加中");
        map1.put("mappingList", maps);
        map1.put("data", maps2);
        log.info("数据为:{}", map1);
        return map1;

    }

    private void getRiskType(ArrayList<String> list, ArrayList<Integer> list1) {
        String sql = "select  count(CASE WHEN  yu_jtscdysgx= '0' THEN 1 END) a,count(CASE WHEN  yu_jtscdysgx= '1' THEN 1 END) b,count(CASE WHEN  yu_jtscdysgx= '2' THEN 1 END) c,count(CASE WHEN  yu_jtscdysgx= '3' THEN 1 END) d  from uf_feng_xxxk where feng_xzt = 0 and delete_type = 0";
        Map<String, Object> data = dataSqlService.eBuilderFromSqlOne(sql, SourceType.LOGIC);
        if (!data.isEmpty()) {
            Integer a = (Integer) data.get("a");
            list.add("保留");
            list1.add(a);
            Integer b = (Integer) data.get("b");
            list.add("合并");
            list1.add(b);
            Integer c = (Integer) data.get("c");
            list.add("拆分");
            list1.add(c);
            Integer d = (Integer) data.get("d");
            list.add("个性化");
            list1.add(d);

        }
    }

    private void getMappingStatus(ArrayList<String> list, ArrayList<Integer> list1) {
        String sql = "select count(CASE WHEN  ying_sgxzt= '1' THEN 1 END) e from  uf_ji_tscfxb where delete_type = 0";
        Map<String, Object> data = dataSqlService.eBuilderFromSqlOne(sql, SourceType.LOGIC);
        if (!data.isEmpty()) {
            Integer e = (Integer) data.get("e");
            list.add("删除");
            list1.add(e);
        }
    }

    /**
     * 去重所有责任人并获取数量
     *
     * @param stringBuilder  管理部门直接责任人
     * @param stringBuilder2 管理部门责任领导
     * @param stringBuilder3 管理部门防控责任人
     * @param stringBuilder4 执行部门直接责任人
     * @param stringBuilder5 执行部门防控责任人
     * @param stringBuilder6 执行部门责任领导
     * @return 返回map集合
     */
    private Map<Object, Object> getReturnCollection(StringBuilder stringBuilder, StringBuilder stringBuilder2, StringBuilder stringBuilder3, StringBuilder stringBuilder4, StringBuilder stringBuilder5, StringBuilder stringBuilder6) {
        Map<Object, Object> map = new HashMap<>();
        String string = stringBuilder.toString();
        String string2 = stringBuilder2.toString();
        String string3 = stringBuilder3.toString();
        String string4 = stringBuilder4.toString();
        String string5 = stringBuilder5.toString();
        String string6 = stringBuilder6.toString();

        Set<String> stringSet = new HashSet<>(Arrays.asList(string.split(",")));
        Set<String> stringSet2 = new HashSet<>(Arrays.asList(string2.split(",")));
        Set<String> stringSet3 = new HashSet<>(Arrays.asList(string3.split(",")));
        Set<String> stringSet4 = new HashSet<>(Arrays.asList(string4.split(",")));
        Set<String> stringSet5 = new HashSet<>(Arrays.asList(string5.split(",")));
        Set<String> stringSet6 = new HashSet<>(Arrays.asList(string6.split(",")));

        int size = stringSet.size();
        int size2 = stringSet2.size();
        int size3 = stringSet3.size();
        int size4 = stringSet4.size();
        int size5 = stringSet5.size();
        int size6 = stringSet6.size();

        map.put("perDirResManDep", size); //管理部门直接责任人
        map.put("ResLeaderManDep", size2);//管理部门责任领导
        map.put("PerChaPreManDep", size3);//管理部门防控责任人
        map.put("perDirExeManDep", size4);//执行部门直接责任人
        map.put("PerChaConExeDep", size5);//执行部门防控责任人
        map.put("ExeDepResLeader", size6);//执行部门责任领导
        return map;
    }


}
