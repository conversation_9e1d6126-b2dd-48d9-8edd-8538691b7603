package com.weaver.seconddev.zyhlw.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.ebuilder.form.client.service.emobile.IEtFormDatasetService;
import com.weaver.framework.rpc.annotation.RpcReference;
import com.weaver.seconddev.zyhlw.domain.portal.DepartmentModel;
import com.weaver.seconddev.zyhlw.domain.portal.PortalMenuDTO;
import com.weaver.seconddev.zyhlw.domain.request.eb.EbFormDataReq;
import com.weaver.seconddev.zyhlw.domain.response.form.EbFormDataResultVo;
import com.weaver.seconddev.zyhlw.service.*;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.IpUtils;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import com.weaver.seconddev.zyhlw.util.ebuilder.EBuilderUtil;
import com.weaver.teams.domain.department.SimpleDepartment;
import com.weaver.teams.domain.user.SimpleEmployee;
import com.weaver.teams.security.context.UserContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PortalServiceImpl implements IPortalService {
    private final String simpleName = PortalServiceImpl.class.getSimpleName();


    @Resource
    IHrmDepartmentService hrmDepartmentService;

    @Resource
    CmicProperties cmicProperties;

    @Resource
    IDataSqlService dataSqlService;

    @Resource
    IDataBaseService dataBaseService;

    @Resource
    IOpenPlatformService iOpenPlatformService;

    private Logger logger = LoggerFactory.getLogger(PortalServiceImpl.class);

    @RpcReference(group = "ebuilderform")
    IEtFormDatasetService iEtFormDatasetService;

    @Resource
    IDataBaseService iDataBaseService;


    /**
     * 获取应用系统信息
     *
     * @param currentUser  当前用户信息
     * @param groupAppFlag 是否为集团应用，1：是
     * @return
     */
    @Override
    public List<Map<String, Object>> getSystemList(SimpleEmployee currentUser, String groupAppFlag) {
        List<Map<String, Object>> systemList = new ArrayList<>();
        String adminLoginStr = iDataBaseService.getBaseDataValue("系统管理员账号loginId", "门户管理");
        String oaMiddleUrl = iDataBaseService.getBaseDataValue("OA系统中间页面URL", "门户管理");
        String e9MiddleUrl = iDataBaseService.getBaseDataValue("E9单点中间页面URL", "门户管理");
        try {

            String departId = "";
            SimpleDepartment currentDepart = currentUser.getDepartment();
            if (currentDepart != null) {
                departId = currentDepart.getId().toString();
            }
            logger.info("{}.getSystemList方法，获取当前用户departId:{}：", simpleName, departId);
            List<String> departIdList = getDepartIdList(departId);
            logger.info("{}.getSystemList方法，获取当前用户departIdList:{}：", simpleName, JSONObject.toJSONString(departIdList));
            String[] adminLoginIdGroup = adminLoginStr.split(",");
            List<String> adminList = Arrays.asList(adminLoginIdGroup);
            logger.info("{}.getSystemList方法，获取当前用户adminList:{}：", simpleName, JSONObject.toJSONString(adminList));
            String currentLoginId = StringUtils.null2String(currentUser.getLoginid());
            Long currentEmployId = currentUser.getEmployeeId();
            Map<String, String> categoryMap = dataBaseService.getSystemCatagoryMap();
            logger.info("{}.getSystemList方法，获取当前用户categoryMap:{}：", simpleName, JSONObject.toJSONString(categoryMap));

            StringBuffer sb = new StringBuffer();
            StringBuffer sbCount = new StringBuffer();
            sb.append("select * from uf_dan_ddl where shi_fglysy=1 and is_delete=0 ");
            sbCount.append("select count(*) as sumnum from uf_dan_ddl where shi_fglysy=1 and is_delete=0 ");

            if (StringUtils.isNotEmpty(groupAppFlag)) {
                sb.append(" and lei_bie=1 ");
                sbCount.append(" and lei_bie=1 ");
            }
            sb.append(" order by bian_h asc");

            String countSql = sbCount.toString();
            int pageSize = 1000;
            int totalSize = 0;
            int totalPages = 0;


            List<Map<String, Object>> sumNumList = dataSqlService.ebuilderFromSql(countSql, 1, 100, SourceType.LOGIC);
            if (!sumNumList.isEmpty()) {
                totalSize = Integer.parseInt(sumNumList.get(0).get("sumnum").toString());
                totalPages = (int) Math.ceil((double) totalSize / pageSize);
            }
            logger.info("{}.getSystemList({},{})获取应用系统信息，页数{}：", simpleName, currentUser.getEmployeeId(), groupAppFlag, totalPages);
            for (int i = 1; i <= totalPages; i++) {
                List<Map<String, Object>> dataList = dataSqlService.ebuilderFromSql(sb.toString(), i, pageSize, SourceType.LOGIC);
                systemList.addAll(dataList);

            }
            logger.info("{}.getSystemList方法，获取当前用户systemList:{}：", simpleName, JSONObject.toJSONString(systemList));
            if (!currentUser.isSysAdmin() && !adminList.contains(currentLoginId)) {
                // 过滤科室范围和人员范围
                systemList = systemList.stream().filter(dataItem -> {

                    String chaKksfw = StringUtils.null2String(dataItem.get("cha_kksfw")); // 查看科室范围
                    String cha_kryfw = StringUtils.null2String(dataItem.get("cha_kryfw")); // 查看人员范围
                    String[] chaKksfw_ids = chaKksfw.split(",");
                    String[] cha_kryfw_ids = cha_kryfw.split(",");
                    // 是否是科室范围
                    boolean isDepScope = false;
                    // 是否是人员范围
                    boolean isHrmScope = false;
                    if ((StringUtils.equals("", chaKksfw) && StringUtils.equals("", cha_kryfw))) {
                        isDepScope = true;
                        isHrmScope = true;
                    } else {
                        // 查看是否在科室范围内
                        for (String chaKksfw_id : chaKksfw_ids) {
                            if (departIdList.contains(chaKksfw_id)) {
                                //如果子部门科室ID包含，则显示
                                isDepScope = true;
                            }
                        }

                        // 查看是否在人员范围内
                        for (String cha_kryfw_id : cha_kryfw_ids) {
                            if (!cha_kryfw_id.trim().equals("") && currentEmployId == Long.parseLong(cha_kryfw_id.trim())) {
                                isHrmScope = true;
                            }
                        }
                    }
                    return isDepScope || isHrmScope;
                }).collect(Collectors.toList());
            }
            logger.info("{}.getSystemList方法，获取当前用户过滤后的systemList:{}：", simpleName, JSONObject.toJSONString(systemList));
            systemList.forEach(dataItem -> {
                String xiTmc = dataItem.getOrDefault("xi_tmc", "").toString();
                String leiBie = dataItem.getOrDefault("lei_bie", "").toString();
                String submitValue = dataItem.getOrDefault("tjfs", "").toString();
                dataItem.put("xi_tmc_py", StringUtils.getPingYin(xiTmc));
                dataItem.put("xi_tmc_py_szm", StringUtils.getPingYinJianCheng(xiTmc));
                dataItem.put("lei_bie_text", getLieBeiName(leiBie));
                dataItem.put("tjfs_text", getSubmitName(submitValue));
                String tokenValue = dataItem.getOrDefault("ren_zfsh", "").toString();
                dataItem.put("ren_zfsh_text", getTokenName(tokenValue));
                String categoryId = dataItem.getOrDefault("xi_tfl", "").toString();
                dataItem.put("xi_tfl_text", categoryMap.getOrDefault(categoryId, ""));
                String isOUtSystemValue = dataItem.getOrDefault("ying_yxzh", "").toString();
                dataItem.put("ying_yxzh_text", getIsOutSystemName(isOUtSystemValue));

                String leiYewu = dataItem.getOrDefault("lei_yewu", "").toString();
                dataItem.put("lei_yewu_text", getLieYeWuName(leiYewu));
                String systemDesc = dataItem.getOrDefault("xtms", "").toString();
                dataItem.put("xtms_text", getLieYeWuName(systemDesc));
                String xi_tdz = dataItem.getOrDefault("xi_tdz", "").toString().replace("&amp;", "&");
                dataItem.put("xi_tdz", xi_tdz);
                String ren_zfsh = dataItem.getOrDefault("ren_zfsh", "").toString();
                if ("7".equals(ren_zfsh)) {
                    /*
                     * 0：Token认证* 1：集中化ERPSMAP_SESSION_DATA认证* 2：集中化人力ticken* 3：天工OA认证* 4：安全网关认证* 5：PortalToken2认证* 6：e10Token认证
                     * 7：e9Token认证
                     */
                    String newUrl=xi_tdz;
                    if (!"".equals(e9MiddleUrl)) {
                        try {
                            newUrl = e9MiddleUrl + "?goUrl=" + URLEncoder.encode(xi_tdz, "UTF-8") + "&portaltoken=";
                        } catch (UnsupportedEncodingException e) {
                            throw new RuntimeException(e);
                        }
                        dataItem.put("xi_tdz", newUrl);
                    }


                }


            });
            logger.info("{}.getSystemList({},{})获取应用系统信息，结果：{}：", simpleName, currentUser.getEmployeeId(), groupAppFlag, systemList);


        } catch (Exception ex) {
            logger.error("{}.getSystemList({},{})获取应用系统信息异常，原因：{}：", simpleName, currentUser.getEmployeeId(), groupAppFlag, ex.getMessage());

        }


        return systemList;
    }


    /**
     * 获取该部门和部门下所有子部门科室ID集合
     *
     * @param departId
     * @return
     */
    @Override
    public List<String> getDepartIdList(String departId) {
        List<String> departIdList = new ArrayList<>();
        try {
            departIdList.add(departId.toString());
            //获取该部门和部门下所有子部门科室
            List<DepartmentModel> allDepartmentList = hrmDepartmentService.getAllDepartmentList();
            List<DepartmentModel> childDepartList = hrmDepartmentService.getChildDepartListById(departId, allDepartmentList);

            for (DepartmentModel departmentModel : childDepartList) {
                departIdList.add(departmentModel.getId());
            }
        } catch (Exception ex) {
            logger.error("{}.getDepartIdList({})获取该部门和部门下所有子部门科室ID集合异常，原因：{}：", simpleName, departId, ex.getMessage());
        }
        return departIdList;

    }


    /**
     * 获取所有应用系统基础信息
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> getAllSystemList(String groupAppFlag) {
        List<Map<String, Object>> allSystemList = new ArrayList<>();

        try {

            StringBuffer sb = new StringBuffer();
            StringBuffer sbCount = new StringBuffer();
            sb.append("select * from uf_dan_ddl where shi_fglysy=1 ");
            sbCount.append("select count(*) as sunnum from uf_dan_ddl where shi_fglysy=1 ");

            if (StringUtils.isNotEmpty(groupAppFlag)) {
                sb.append(" and lei_bie=1 ");
                sbCount.append(" and lei_bie=1 ");
            }
            sb.append(" order by bian_h asc");

            String countSql = sbCount.toString();
            int pageSize = 1000;
            int totalSize = 0;
            int totalPages = 0;

            logger.info("执行getAllSystemList方法，countSql：{}", countSql);
            List<Map<String, Object>> sumNumList = dataSqlService.ebuilderFromSql(countSql, 1, 100, SourceType.LOGIC);
            if (!sumNumList.isEmpty()) {
                totalSize = Integer.parseInt(sumNumList.get(0).get("sunnum").toString());
                totalPages = (int) Math.ceil((double) totalSize / pageSize);
            }
            logger.info("{}.getAllSystemList({})获取应用系统信息，页数{}：", simpleName, groupAppFlag, totalPages);
            for (int i = 1; i <= totalPages; i++) {
                List<Map<String, Object>> dataList = dataSqlService.ebuilderFromSql(sb.toString(), i, pageSize, SourceType.LOGIC);
                allSystemList.addAll(dataList);

            }
        } catch (Exception ex) {
            logger.info("{}.getAllSystemList({})获取应用系统信息异常，原因{}：", simpleName, groupAppFlag, ex);
        }
        return allSystemList;
    }

    /**
     * 找到子部门目标层级的父部门id
     *
     * @param departments 全部部门列表
     * @param departId    子部门id
     * @param dateRank    目标层级
     * @return 子部门目标层级的父部门id
     */
    @Override
    public String getDepartIdBySubSector(List<DepartmentModel> departments, String departId, Integer dateRank) {
        DepartmentModel targetDepartment = departments.stream()
                .filter(dept -> dept.getId().equals(departId))
                .findFirst()
                .orElse(null);
        if (targetDepartment != null) {
            if (targetDepartment.getDatarank() != null && targetDepartment.getDatarank() >= dateRank) {
                return targetDepartment.getId();
            }
            String parent = targetDepartment.getParent();
            if (StringUtils.isEmpty(parent)) {
                return "";
            } else {
                return getDepartIdBySubSector(departments, parent, dateRank);
            }
        }
        return "";
    }

    /**
     * update
     * <p>
     * 前端传入json格式的body;{"add":"[4]","delete":"[1,2,3]"}
     * 前端思路如下：
     * 用户add操作时，直接添加到add队列；用户删除操作时，查询add队列中是否存在，若存在则删除，否则添加到delete队列中；
     * 至于add为什么不查询delete队列，因为先删除后加入（相当于修改，因为时间字段影响排序）。所有队列均不能重复添加。
     * 原思路再一个用户多处同时登录且同时进行修改时有影响，现改为，加入之前若存在则修改，若超过8个则加入失败。。
     * ps：注释是e9搬过来的，非本人设计。原e9接口 /zyhlw/web/menu/update
     * <p>
     *
     * @param portalMenuDTO 入参
     * @return 成功失败
     * <AUTHOR>
     * @time 2025年01月22 11:32:35
     * @since 1.0
     */
    @Override
    public WeaResult<Object> update(PortalMenuDTO portalMenuDTO) {
        String method = String.format("调用%s.update-->", simpleName);
        logger.info("{}开始执行", method);
        SimpleEmployee currentUser = UserContext.getCurrentUser();
        String uid = StringUtils.null2String(currentUser.getUid());
        String userName = StringUtils.null2String(currentUser.getUsername());
        JSONArray addList = portalMenuDTO.getAdd();
        JSONArray deleList = portalMenuDTO.getDelete();
        Boolean isSort = portalMenuDTO.getIsSort(); //进行了排序操作？
        logger.info("{}rpc获取基础表表单id", method);

        String objId = EBuilderUtil.getFormObjId(iEtFormDatasetService, cmicProperties.getOpenPlatformUrl(), uid, iOpenPlatformService.getAccessToken(), "uf_menuconfig", "objId");
        //先删后增
        logger.info("{}开始删除操作", method);
        if (isSort) {//排序了则删除所有，重新添加
            deleteAll(uid, objId);
        } else {
            for (int i = 0; i < deleList.size(); i++) {
                String menuId = deleList.getString(i);
                delete(uid, menuId, objId);
            }
        }

        //增加
        logger.info("{}开始增加操作", method);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");//设置日期格式

        Date date = new Date();

        List<Map<String, Object>> datas = new ArrayList<>();
        for (int i = 0; i < addList.size(); i++) {
            Map<String, Object> dataMap = new HashMap<>(2);
            JSONObject mainTableObject = new JSONObject();
            mainTableObject.put("menuid", addList.getString(i));
            mainTableObject.put("loginid", uid);
            mainTableObject.put("createby", userName);
            mainTableObject.put("createdate", dateFormat.format(date));
            dataMap.put("mainTable", mainTableObject);
            datas.add(dataMap);
        }
        Map<String, Object> mainTableField = new HashMap<>(1);
        List<String> mainTableFields = new ArrayList<>();
        mainTableFields.add("loginid");
        mainTableFields.add("menuid");
        mainTableField.put("mainTable", mainTableFields);
        EbFormDataReq builder = new EbFormDataReq.Builder()
                .userId(uid)
                .objId(objId)
                .needAdd("true")
                .updateType("updatePolicy")
                .updateField(mainTableField)
                .datas(datas)
                .build();
        EbFormDataResultVo resultVo = EBuilderUtil.updateFormDataV2(builder, iOpenPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());
        return WeaResult.success(resultVo, "执行成功");
    }

    /**
     * addlog
     * <p>
     * 添加日志接口。
     * ps：原e9接口 /zyhlw/web/LogYingYongData/addlog
     * <p>
     *
     * @param danId     传过来的ID
     * @param danName   应用名称
     * @param userAgent userAgent
     * @return 成功失败
     * <AUTHOR>
     * @time 2025年01月22 11:32:35
     * @since 1.0
     */
    @Override
    public WeaResult<Object> addlog(String danId, String danName, String userAgent) {
        String method = String.format("调用%s.addlog-->", simpleName);
        logger.info("{}开始执行", method);
        SimpleEmployee currentUser = UserContext.getCurrentUser();

        try {
            String ip = IpUtils.getClientIP();//ip地址
            String uid = StringUtils.null2String(currentUser.getUid());//主键ID
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");//设置日期格式
            String createTime = StringUtils.null2String(df.format(new Date()));//创建时间
            Long bu_m = currentUser.getDepartment().getId();     //获取用户的部门ID
            logger.info("当前用户信息：{}", JSON.toJSONString(currentUser));

            if (danId.isEmpty()) {
                return WeaResult.fail("访问菜单没有关联更多应用中的应用ID，不插入访问记录");
            }
            String objId = EBuilderUtil.getFormObjId(iEtFormDatasetService, cmicProperties.getOpenPlatformUrl(), uid, iOpenPlatformService.getAccessToken(), "uf_dan_log", "objId");


            List<Map<String, Object>> datas = new ArrayList<>();
            Map<String, Object> dataMap = new HashMap<>(2);
            JSONObject mainTableObject = new JSONObject();
            mainTableObject.put("ip", ip);
            mainTableObject.put("loginid", currentUser.getLoginid());
            mainTableObject.put("dan_id", danId);
            mainTableObject.put("username", uid);
            mainTableObject.put("dan_name", danName);
            mainTableObject.put("user_agent", userAgent);
            mainTableObject.put("bu_m", bu_m);
            mainTableObject.put("createtime", createTime);
            dataMap.put("mainTable", mainTableObject);
            datas.add(dataMap);
            logger.info("{}打印更新信息：{}", method, JSON.toJSONString(datas));
            EbFormDataResultVo resultVo = EBuilderUtil.saveFormDataV2(cmicProperties.getOpenPlatformUrl(), Long.valueOf(uid), objId, datas, iOpenPlatformService.getAccessToken());
            return WeaResult.success(resultVo, "插入成功");

        } catch (Exception e) {
            return WeaResult.fail(e.getMessage());
        }
    }

    /**
     * updateChecked
     * <p>
     *
     * @param type    1.流程中心-应用内流程 2.系统中心-只显示集团
     * @param checked 0.取消选中 1.选中
     * @return 勾选状态
     * @description 获取用的流程中心与系统中心的勾选状态
     * ps：e9原接口 /zyhlw/web/portal/NewWorkflowApi/updateChecked
     * <p>
     * <AUTHOR>
     * @time 2025年01月23 11:22:11
     * @since 1.0
     */
    @Override
    public WeaResult<Object> updateChecked(String type, String checked) {
        String method = String.format("调用%s.updateChecked-->", simpleName);
        logger.info("{}开始执行", method);
        SimpleEmployee currentUser = UserContext.getCurrentUser();
        String uid = StringUtils.null2String(currentUser.getUid());
        try {
            // 获取表单id
            String objId = EBuilderUtil.getFormObjId(iEtFormDatasetService, cmicProperties.getOpenPlatformUrl(), uid, iOpenPlatformService.getAccessToken(), "uf_user_collection", "objId");
            List<Map<String, Object>> datas = new ArrayList<>();
            Map<String, Object> dataMap = new HashMap<>(2);
            JSONObject mainTableObject = new JSONObject();
            mainTableObject.put("yong_xm", uid);
            if (StringUtils.equals("1", type)) {
                mainTableObject.put("check_insystem", checked);
            } else {
                mainTableObject.put("check_displaygroup", checked);
            }
            dataMap.put("mainTable", mainTableObject);
            datas.add(dataMap);
            Map<String, Object> mainTableField = new HashMap<>(1);
            List<String> mainTableFields = new ArrayList<>();
            mainTableFields.add("yong_xm");
            mainTableField.put("mainTable", mainTableFields);
            EbFormDataReq builder = new EbFormDataReq.Builder()
                    .userId(uid)
                    .objId(objId)
                    .needAdd("true")
                    .updateType("updatePolicy")
                    .updateField(mainTableField)
                    .datas(datas)
                    .build();
            EbFormDataResultVo resultVo = EBuilderUtil.updateFormDataV2(builder, iOpenPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());
            return WeaResult.success(resultVo);
        } catch (Exception e) {
            logger.error("{}操作异常:{}", method, e.getMessage());
        }
        return WeaResult.fail("操作失败");
    }

    /**
     * 通过类别值获取类别名称
     *
     * @param selectValue
     * @return
     */
    private String getLieBeiName(String selectValue) {
        String selectName = "";
        switch (selectValue) {
            case "0":
                selectName = "互联网公司专业系统";
                break;
            case "1":
                selectName = "集团系统应用";
                break;
            case "2":
                selectName = "通用应用";
                break;
            case "3":
                selectName = "采购管理";
                break;
            case "4":
                selectName = "综合管理";
                break;
            case "5":
                selectName = "党建管理";
                break;
            case "6":
                selectName = "运维支撑";
                break;
            case "7":
                selectName = "其他";
                break;
            default:
                selectName = "";
                break;

        }
        return selectName;
    }

    /**
     * 通过提交方式值获取提交方式名称
     *
     * @param selectValue
     * @return
     */
    private String getSubmitName(String selectValue) {
        String selectName = "";
        switch (selectValue) {
            case "0":
                selectName = "post";
                break;
            case "1":
                selectName = "get";
                break;
            default:
                selectName = "";
                break;
        }
        return selectName;
    }

    /**
     * 通过票据认证方式获取票据认证方式名称
     *
     * @param selectValue
     * @return
     */
    private String getTokenName(String selectValue) {
        String selectName = "";
        switch (selectValue) {
            case "0":
                selectName = "Token认证";
                break;
            case "1":
                selectName = "集中化ERPSMAP_SESSION_DATA认证";
                break;
            case "2":
                selectName = "集中化人力ticken";
                break;
            case "3":
                selectName = "天工OA认证";
                break;
            case "4":
                selectName = "安全网关认证";
                break;
            case "5":
                selectName = "PortalToken2认证";
                break;
            case "6":
                selectName = "e10Token认证";
                break;
            default:
                selectName = "";
                break;

        }
        return selectName;
    }


    /**
     * 通过提交方式值获取提交方式名称
     *
     * @param selectValue
     * @return
     */
    private String getIsOutSystemName(String selectValue) {
        String selectName = "";
        switch (selectValue) {
            case "0":
                selectName = "EC平台";
                break;
            case "1":
                selectName = "外部系统";
                break;
            default:
                selectName = "";
                break;
        }
        return selectName;
    }

    /**
     * 通过类别值获取类别名称
     *
     * @param selectValue
     * @return
     */
    private String getLieYeWuName(String selectValue) {
        String selectName = "";
        switch (selectValue) {
            case "0":
                selectName = "OA系统";
                break;
            case "1":
                selectName = "人力管理";
                break;
            case "2":
                selectName = "财务管理";
                break;
            case "3":
                selectName = "采购管理";
                break;
            case "4":
                selectName = "综合管理";
                break;
            case "5":
                selectName = "党建管理";
                break;
            case "6":
                selectName = "运维支撑";
                break;
            case "7":
                selectName = "其他";
                break;
            case "8":
                selectName = "基础模块";
                break;
            case "9":
                selectName = "品质服务管理";
                break;
            default:
                selectName = "";
                break;

        }
        return selectName;
    }

    private void deleteAll(String uid, String objId) {
        Map<String, Object> mainTable = new HashMap<>();
        mainTable.put("loginid", uid);
        EBuilderUtil.deleteDataV2(cmicProperties.getOpenPlatformUrl(), objId, mainTable, uid, iOpenPlatformService.getAccessToken());
    }

    private void delete(String uid, String menuId, String objId) {
        Map<String, Object> mainTable = new HashMap<>();
        mainTable.put("loginid", uid);
        mainTable.put("menuid", menuId);
        EBuilderUtil.deleteDataV2(cmicProperties.getOpenPlatformUrl(), objId, mainTable, uid, iOpenPlatformService.getAccessToken());
    }


    /**
     * 获取指定系统编码的系统名称
     *
     * @param systemCode 系统编码
     * @return
     */
    @Override
    public Map<String, Object> getSystemInfoBySystemCode(String systemCode) {
        Map<String, Object> systemMap = new HashMap<>();
        String e9MiddleUrl = iDataBaseService.getBaseDataValue("E9单点中间页面URL", "门户管理");
        try {

            Map<String, String> categoryMap = dataBaseService.getSystemCatagoryMap();

            StringBuffer sb = new StringBuffer();
            sb.append("select * from uf_dan_ddl where shi_fglysy=1 and is_delete=0 ");
            if (StringUtils.isNotEmpty(systemCode)) {
                sb.append(" and xi_tbh='" + systemCode + "'");
            } else {
                sb.append(" and 1=0 ");
            }


            int pageSize = 1000;

            List<Map<String, Object>> dataList = dataSqlService.ebuilderFromSql(sb.toString(), 0, pageSize, SourceType.LOGIC);

            logger.info("{}.getSystemList方法，getSystemInfoBySystemCode:{}：", simpleName, JSONObject.toJSONString(dataList));

            dataList.forEach(dataItem -> {
                String xiTmc = dataItem.getOrDefault("xi_tmc", "").toString();
                String leiBie = dataItem.getOrDefault("lei_bie", "").toString();
                String submitValue = dataItem.getOrDefault("tjfs", "").toString();
                dataItem.put("xi_tmc_py", StringUtils.getPingYin(xiTmc));
                dataItem.put("xi_tmc_py_szm", StringUtils.getPingYinJianCheng(xiTmc));
                dataItem.put("lei_bie_text", getLieBeiName(leiBie));
                dataItem.put("tjfs_text", getSubmitName(submitValue));
                String tokenValue = dataItem.getOrDefault("ren_zfsh", "").toString();
                dataItem.put("ren_zfsh_text", getTokenName(tokenValue));
                String categoryId = dataItem.getOrDefault("xi_tfl", "").toString();
                dataItem.put("xi_tfl_text", categoryMap.getOrDefault(categoryId, ""));
                String isOUtSystemValue = dataItem.getOrDefault("ying_yxzh", "").toString();
                dataItem.put("ying_yxzh_text", getIsOutSystemName(isOUtSystemValue));

                String leiYewu = dataItem.getOrDefault("lei_yewu", "").toString();
                dataItem.put("lei_yewu_text", getLieYeWuName(leiYewu));
                String systemDesc = dataItem.getOrDefault("xtms", "").toString();
                dataItem.put("xtms_text", getLieYeWuName(systemDesc));
                String xi_tdz = dataItem.getOrDefault("xi_tdz", "").toString().replace("&amp;", "&");
                dataItem.put("xi_tdz", xi_tdz);
                String ren_zfsh = dataItem.getOrDefault("ren_zfsh", "").toString();
                if ("7".equals(ren_zfsh)) {
                    /*
                     * 0：Token认证* 1：集中化ERPSMAP_SESSION_DATA认证* 2：集中化人力ticken* 3：天工OA认证* 4：安全网关认证* 5：PortalToken2认证* 6：e10Token认证
                     * 7：e9Token认证
                     */
                    String newUrl=xi_tdz;
                    if (!"".equals(e9MiddleUrl)) {
                        try {
                            newUrl = e9MiddleUrl + "?goUrl=" + URLEncoder.encode(xi_tdz, "UTF-8") + "&portaltoken=";
                        } catch (UnsupportedEncodingException e) {
                            throw new RuntimeException(e);
                        }
                        dataItem.put("xi_tdz", newUrl);
                    }


                }


            });
            if (dataList.size() > 0) {
                systemMap = dataList.get(0);
            }
            logger.info("{}.dataList({})获取应用系统信息，结果：{}：", simpleName, systemCode, systemMap);


        } catch (Exception ex) {
            logger.error("{}.getSystemList({})获取应用系统信息异常，原因：{}：", simpleName, systemCode, ex.getMessage());

        }


        return systemMap;

    }

}
