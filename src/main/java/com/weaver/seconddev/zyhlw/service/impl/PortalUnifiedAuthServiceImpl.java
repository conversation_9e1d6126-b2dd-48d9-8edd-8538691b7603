package com.weaver.seconddev.zyhlw.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.weaver.seconddev.zyhlw.service.PortalUnifiedAuthenticationService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.DateUtils;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import com.weaver.seconddev.zyhlw.util.encrypt.MD5;
import com.weaver.seconddev.zyhlw.util.http.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * Portal统一认证相关接口
 *
 * <AUTHOR>
 * @Date 17:44 2024/7/1
 **/
@Slf4j
@Service
public class PortalUnifiedAuthServiceImpl implements PortalUnifiedAuthenticationService {

    @Resource
    public CmicProperties cmicProperties;

    /**
     * 获取授权appToken https://auth.portal.cmic:30443/cmic-portal-auth/apptoken/getToken
     *
     * @return void
     * <AUTHOR>
     * @Date 17:45 2024/7/1
     * @Param targetType 目标业务方appid
     *        162： 集团统一待办信息推送接口
     **/
    @Override
    public String getToken(String targetAppId) {
        String appId = cmicProperties.getAppid();
        String url = cmicProperties.getPortalApptokenGetToken();
        String systemtime = DateUtils.getCurrentTimestamp();
        String appSecret = cmicProperties.getAppSecret();
        String sign = MD5.encrypt(appId + targetAppId + appSecret + systemtime).toUpperCase();
        JSONObject paramJson = new JSONObject();
        paramJson.put("appId", appId);
        paramJson.put("msgid", systemtime);
        paramJson.put("sign", sign);
        paramJson.put("systemtime", systemtime);
        paramJson.put("targetAppId", targetAppId);
        Map<String, Object> header = new HashMap<>(16);
        header.put("Content-Type", "application/json");
        header.put("Accept", "application/json");
        header.put("appId", appId);
        header.put("appSecret", appSecret);
        String json = HttpUtils.sendPost(url, paramJson.toString(), header);
        String token = "";
        if (StringUtils.isValidJson(json)) {
            JSONObject jsonObject = JSON.parseObject(json);
            String codeKey = "code";
            if (jsonObject.getInteger(codeKey) == 0) {
                token = jsonObject.getString("apptoken");
            } else {
                log.error("调用PortalUnifiedAuthServiceImpl.getToken 请求获取portal统一认证token code非0，返回：{}", json);
            }
        } else {
            log.error("调用PortalUnifiedAuthServiceImpl.getToken 请求获取portal统一认证token返回非JSON格式数据，返回：{}", json);
        }
        return token;

    }

//    public static void main(String[] args) {
//        String appId = "140";
//        String targetAppId ="162";
//        String url = "http://auth.portal.cmic:30080/cmic-portal-auth/apptoken/getToken";
//        String systemtime = DateUtils.getCurrentTimestamp();
//        String appSecret = "x1HZisCbln38ftog1RgdjkriLSzCOgdX6Dlvr1Ww";
//        String sign = MD5.encrypt(appId + targetAppId + appSecret + systemtime).toUpperCase();
//        JSONObject paramJson = new JSONObject();
//        paramJson.put("appId", appId);
//        paramJson.put("msgid", systemtime);
//        paramJson.put("sign", sign);
//        paramJson.put("systemtime", systemtime);
//        paramJson.put("targetAppId", targetAppId);
//        Map<String, Object> header = new HashMap<>(16);
//        header.put("Content-Type", "application/json");
//        header.put("Accept", "application/json");
//        header.put("appId", appId);
//        header.put("appSecret", appSecret);
//        String json = HttpUtils.sendPost(url, paramJson.toString(), header);
//        String token = "";
//        if (StringUtils.isValidJson(json)) {
//            JSONObject jsonObject = JSON.parseObject(json);
//            String codeKey = "code";
//            if (jsonObject.getInteger(codeKey) == 0) {
//                token = jsonObject.getString("apptoken");
//                System.out.println(token);
//            } else {
//                log.error("调用PortalUnifiedAuthServiceImpl.getToken 请求获取portal统一认证token code非0，返回：{}", json);
//            }
//        } else {
//            log.error("调用PortalUnifiedAuthServiceImpl.getToken 请求获取portal统一认证token返回非JSON格式数据，返回：{}", json);
//        }
//    }
}
