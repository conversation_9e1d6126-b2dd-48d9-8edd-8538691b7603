package com.weaver.seconddev.zyhlw.service.impl;

import com.alibaba.fastjson.JSON;
import com.weaver.common.hrm.remote.HrmRemotingService;
import com.weaver.ebuilder.form.client.entity.data.*;
import com.weaver.ebuilder.form.client.entity.field.ModuleField;
import com.weaver.ebuilder.form.client.entity.obj.Obj;
import com.weaver.ebuilder.form.client.service.data.RemoteSimpleDataService;
import com.weaver.ebuilder.form.client.service.emobile.IEtFormDatasetService;
import com.weaver.framework.rpc.annotation.RpcReference;
import com.weaver.framework.rpc.context.impl.TenantRpcContext;
import com.weaver.seconddev.zyhlw.domain.request.todo.TodoAuditRequest;
import com.weaver.seconddev.zyhlw.domain.request.todo.TodoItemRequest;
import com.weaver.seconddev.zyhlw.domain.response.todo.TodoAuditResponse;
import com.weaver.seconddev.zyhlw.domain.response.todo.TodoItemResponse;
import com.weaver.seconddev.zyhlw.service.IPortalWaitService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.service.PortalUnifiedAuthenticationService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.TodoUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 流程待办实现
 *
 * <AUTHOR>
 * @Date 2024-07-04 10:06
 */
@Slf4j
@Service
public class PortalWaitServiceImpl implements IPortalWaitService {
    @Resource
    private IOpenPlatformService iOpenPlatformService;
    @Resource
    private PortalUnifiedAuthenticationService portalUnifiedAuthenticationService;
    @Resource
    private CmicProperties cmicProperties;
    @RpcReference
    HrmRemotingService hrRemote;

    private String simpleName = PortalWaitServiceImpl.class.getSimpleName();
    /**
     * 分页推送待办数据到统一待办服务
     *
     * <AUTHOR>
     * @Date 14:43 2024/7/8
     **/
    @Override
    public void pushTodoDataToJtBatch(List<TodoItemRequest> todoItemRequestList,List<Map<String,Object>> recordList, String logMsg) {
        String methodName = "调用"+simpleName+".pushTodoDataToJtBatch()";
        int pageSize = 1000;
        int page = 0;
        int totalItems = todoItemRequestList.size();
        while (page * pageSize < totalItems) {
            int fromIndex = page * pageSize;
            int toIndex = Math.min(fromIndex + pageSize, totalItems);
            List<TodoItemRequest> currentPageItems = todoItemRequestList.subList(fromIndex, toIndex);
            // 模拟推送当前页数据的操作
            String appToken = portalUnifiedAuthenticationService.getToken(cmicProperties.getCmicPortalApptokenTargetAppId());
            TodoItemResponse todoItemResponse = TodoUtil.syncSysDataTodoBatch(cmicProperties.getCmicPortalWaitDatalist(), currentPageItems, appToken);
            log.info("{}，批量推送待办数据到统一待办服务，当前页：{}，响应：{}", methodName, page, JSON.toJSONString(todoItemResponse));
            Integer pushStatus = 1;
            if (todoItemResponse.getCode() != 0) {
                pushStatus = 0;
            }
            for (Map<String, Object> stringObjectMap : recordList) {
                stringObjectMap.put("push_status",pushStatus);
                stringObjectMap.put("push_back",JSON.toJSONString(todoItemResponse));
            }
            page++;
        }
        addRecord(cmicProperties.getTodoAppid(),recordList);
    }

    /**
     * 推送数据到统一待办
     * @param todoItemRequest
     */
    @Override
    public void pushTodoData(TodoItemRequest todoItemRequest) {
        String appToken = portalUnifiedAuthenticationService.getToken(cmicProperties.getCmicPortalApptokenTargetAppId());
        TodoItemResponse todoItemResponse = TodoUtil.syncSysDataTodo(cmicProperties.getCmicPortalWaitData(), todoItemRequest, appToken);
        Map<String,Object> todoResordMap = new HashMap<>();
        // 关联流程
        todoResordMap.put("related_processes",todoItemRequest.getRequestId());
        // 当前处理人
        todoResordMap.put("receiver_user",todoItemRequest.getReceiver());
        // 待办类型
        todoResordMap.put("todo_type",todoItemRequest.getItemType());
        // 上一处理人
        todoResordMap.put("prev_user",todoItemRequest.getPrev());
        // 推送参数数据
        todoResordMap.put("push_parameter",JSON.toJSONString(todoItemRequest));
        // 唯一标识
        todoResordMap.put("original_id",todoItemRequest.getOriginalId());
        if (todoItemResponse.getCode() == 0) {
            todoResordMap.put("push_status",1);
        }else{
            todoResordMap.put("push_status",0);
        }
        todoResordMap.put("push_back",JSON.toJSONString(todoItemResponse));
        List<Map<String,Object>> list = new ArrayList<>();
        list.add(todoResordMap);
        addRecord(cmicProperties.getTodoAppid(),list);
    }

    /**
     * 获取用户稽核数据
     *
     * <AUTHOR>
     * @Date 14:43 2024/7/8
     **/
    @Override
    public List<TodoAuditResponse.Item> auditUserTodoData(Long userId) {
        return findAllAuditUserTodoDataRecursive(1, userId, new ArrayList<>());
    }

    /**
     * 递归查询用户稽核数据
     *
     * <AUTHOR>
     * @Date 14:43 2024/7/8
     **/
    private List<TodoAuditResponse.Item> findAllAuditUserTodoDataRecursive(Integer page, Long operatorId, List<TodoAuditResponse.Item> allItems) {
        String logMsg = "统一待办服务，业务稽核查询接口，当前页";
        TodoAuditResponse todoItemResponse = new TodoAuditResponse();
        int pageCount = 1000;
        try {
            String appToken = portalUnifiedAuthenticationService.getToken(cmicProperties.getCmicPortalApptokenTargetAppId());
            TodoAuditRequest todoAuditRequest = TodoAuditRequest.builder()
                    .receiverUserId(String.valueOf(operatorId))
                    .pageNumber(page).pageSize(pageCount).build();
            todoItemResponse = TodoUtil.auditSysDataTodo(cmicProperties.getCmicPortalWaitAudit(), todoAuditRequest, appToken);
            // 打印当前返回结果数量
            log.info("{}：{}; 返回数量：{}", logMsg, page, todoItemResponse.getItemList().size());
            if (todoItemResponse.getCode() != 0) {
                log.info("{}：{}; 查询失败：{}", logMsg, page, todoItemResponse.getMessage());
            } else {
                if (!todoItemResponse.getItemList().isEmpty()) {
                    // 将当前页的用户数据添加到总用户列表中
                    allItems.addAll(todoItemResponse.getItemList());
                }
            }
        } catch (Exception e) {
            log.info("{}：{}; 查询异常：{}", logMsg, page, e.getMessage());
        }
        // 判断是否需要继续递归查询
        if (todoItemResponse.getItemList().size() == pageCount) {
            // 继续递归查询下一页
            return findAllAuditUserTodoDataRecursive(page + 1, operatorId, allItems);
        } else {
            // 返回所有用户数据
            return allItems;
        }

    }
    @RpcReference(group = "ebuilderform")
    IEtFormDatasetService iEtFormDatasetService;
    @RpcReference(group = "ebuilderform")
    RemoteSimpleDataService remoteSimpleDataService;

    private void addRecord(String appid,List<Map<String,Object>> data){
        String methodName = "调用"+simpleName+".addRecord()";
        String tableName = "uf_todo_push_records";
        String objId = "";
        log.info("{} TenantRpcContext.getTenantKey()：{}", methodName,TenantRpcContext.getTenantKey());

        TenantRpcContext.setTargetTenantKey(TenantRpcContext.getTenantKey());
        try {
            List<Obj> tables = iEtFormDatasetService.getTables(appid);
            for (Obj table : tables) {
                if (table.getTableName().equals(tableName)) {
                    objId = String.valueOf(table.getId());
                    break;
                }
            }
        }catch (Exception e){
            log.error("{} 查询应用表数据异常",methodName,e);
        }
        List<ModuleField> fields = new ArrayList<>();
        try {
            fields = iEtFormDatasetService.getFields(null, Long.parseLong(objId), "1", true);
        }catch (Exception e){
            log.error("{} 查询对应表字段数据异常",methodName,e);
        }
        EBDataChangeReqDto ebDataChangeReqDto = new EBDataChangeReqDto();// 构建基础参数; objId 表单id, operator 操作人, tenantKey 租户
        ebDataChangeReqDto.setHeader(new EBDataReqHeader(objId, "110001700000001879",cmicProperties.getHostTenantKey()));
        // 若数据写入后就要从前台看到数据或者立即操作本次写入的数据, 需要调整后置处理为同步 权限处理完成再返回
        EBDataReqOperation operation = new EBDataReqOperation();
        operation.setAsyncPostProcess(false);
        ebDataChangeReqDto.setOperation(operation);
        List<EBDataReqDto> datas = new ArrayList<>();
        for (Map<String, Object> datum : data) {
            EBDataReqDto ebDataReqDto = new EBDataReqDto();
            List<EBDataReqDetailDto> mainData =new ArrayList<>();
            for (String key : datum.keySet()) {
                for (ModuleField field : fields) {
                    if (field.getColumnName().equals(key)) {
                        mainData.add(new EBDataReqDetailDto(String.valueOf(field.getId()),datum.get(key).toString()));
                        break;
                    }
                }
            }
            ebDataReqDto.setMainDatas(mainData);
            datas.add(ebDataReqDto);
        }
        ebDataChangeReqDto.setDatas(datas);
        try {
            log.info("{} 插入数据：{}",methodName,JSON.toJSONString(ebDataChangeReqDto));
            EBDataChangeResult ebDataChangeResult = remoteSimpleDataService.saveFormData(ebDataChangeReqDto);
            log.info("{} 新增数据返回：{}",methodName,JSON.toJSONString(ebDataChangeResult));
        }catch (Exception e){
            log.error("{} 新增记录异常",methodName,e);
        }

    }
}
