package com.weaver.seconddev.zyhlw.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.ebuilder.common.util.PinyinUtil;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.ebuilder.form.client.service.emobile.IEtFormDatasetService;
import com.weaver.framework.rpc.annotation.RpcReference;
import com.weaver.openapi.pojo.flow.res.vo.CreateWorkFlow;
import com.weaver.openapi.pojo.flow.res.vo.FlowList;
import com.weaver.seconddev.zyhlw.domain.portalworkflow.AddCollectionDTO;
import com.weaver.seconddev.zyhlw.domain.portalworkflow.NewWorkFlowDTO;
import com.weaver.seconddev.zyhlw.domain.request.eb.EbFormDataReq;
import com.weaver.seconddev.zyhlw.domain.response.form.EbFormDataResultVo;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.service.IPortalWorkflowService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.ebuilder.EBuilderUtil;
import com.weaver.seconddev.zyhlw.util.workflow.WfcUtil;
import com.weaver.teams.domain.user.SimpleEmployee;
import com.weaver.teams.security.context.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <h1>PortalWorkflowServiceImpl 流程中心</h1>
 *
 * <p>Description: </p>
 *
 * <p>Note: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@Service
public class PortalWorkflowServiceImpl implements IPortalWorkflowService {

    private final String SIMPLE_NAME = PortalWorkflowServiceImpl.class.getSimpleName();

    @RpcReference(group = "ebuilderform")
    IEtFormDatasetService iEtFormDatasetService;

    @Resource
    CmicProperties cmicProperties;
    @Resource
    IOpenPlatformService iOpenPlatformService;

    @Resource
    IDataSqlService dataSqlService;
    @Resource
    IDataBaseService iDataBaseService;

    @Override
    public WeaResult<Object> getNewWorkflowDataList(NewWorkFlowDTO newWorkFlowDTO) {
        String e9MiddleUrl = iDataBaseService.getBaseDataValue("E9单点中间页面URL", "门户管理");

        String method = String.format("调用%s.getNewWorkflowDataList-->", SIMPLE_NAME);
        log.info("{}开始执行", method);
        StringBuffer sql = new StringBuffer("select * from uf_xin_jlcpz where is_delete=0 ");
        handleNewWorkflowSql(sql, newWorkFlowDTO);
        sql.append(" order by to_char(yi_jfl),pai_x");
        List<Map<String, Object>> dataResult = dataSqlService.eBuilderFromSqlAll(sql.toString(), SourceType.LOGIC);

        dataResult.forEach(workflow -> {
            // 流程名称转拼音
            String liuCmc = (String) workflow.getOrDefault("liu_cmc", "");
            workflow.put("liu_cmc_py", PinyinUtil.getPingYin(liuCmc));
            workflow.put("liu_cmc_py_szm", PinyinUtil.getPingYin(liuCmc, true));
            workflow.put("id", workflow.get("id").toString());
            // 流程类型 0: 内部流程，1：外部流程，2：e9外部流程
            String 	liu_clx = (String) workflow.getOrDefault("liu_clx", "");
            //流程跳转地址
            String tiao_zlj = (String) workflow.getOrDefault("tiao_zlj", "");

            String newUrl=tiao_zlj;
            if("2".equals(liu_clx))
            {
                // 如果为E9流程则需要转换为E9中间页面
                if (!"".equals(e9MiddleUrl)) {
                    try {
                        newUrl = e9MiddleUrl + "?goUrl=" + URLEncoder.encode(tiao_zlj, "UTF-8") + "&portaltoken=";
                        workflow.put("tiao_zlj",newUrl);
                    } catch (UnsupportedEncodingException e) {
                        throw new RuntimeException(e);
                    }

                }
            }

        });
        return WeaResult.success(dataResult);
    }

    @Override
    public WeaResult<Object> getCollection(NewWorkFlowDTO newWorkFlowDTO) {
        String method = String.format("调用%s.getCollection-->", SIMPLE_NAME);
        log.info("{}开始执行", method);
        SimpleEmployee currentUser = UserContext.getCurrentUser();
        String uid = String.valueOf(currentUser.getUid());
        List<Map<String, Object>> userCollectionInfos = getCollectionByUserid(uid);
        log.info("{}userCollectionInfos：{}", method, userCollectionInfos);
        if (userCollectionInfos.isEmpty()) {
            return WeaResult.success(null);
        }
        String ids = userCollectionInfos.stream()
                .map(userCollectionInfo -> userCollectionInfo.get("new_wf_coll").toString())
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining(","));
        if (ids.isEmpty()) {
            return WeaResult.success(null);
        }
        newWorkFlowDTO.setId(ids);

        WeaResult<Object> result = getNewWorkflowDataList(newWorkFlowDTO);

        List<Map<String, Object>> dataList = (List<Map<String, Object>>) result.getData();

        // 按照id排序
        List<String> idList = Arrays.asList(ids.split(","));
        Map<String, Integer> idMap = IntStream.range(0, idList.size())
                .boxed()
                .collect(Collectors.toMap(idList::get, i -> i));

        dataList = dataList.stream()
                .sorted(Comparator.comparing(o -> idMap.getOrDefault(o.get("id"), 9999)))
                .collect(Collectors.toList());
        return WeaResult.success(dataList);
    }

    @Override
    public WeaResult<Object> addCollection(AddCollectionDTO addCollectionDTO) {
        String method = String.format("调用%s.addCollection-->", SIMPLE_NAME);
        log.info("{}开始执行", method);
        SimpleEmployee currentUser = UserContext.getCurrentUser();
        String uid = String.valueOf(currentUser.getUid());
        String querySql = "select * from uf_my_collection where is_delete=0 and userid=" + uid;
        try {
            Map<String, Object> collectionDataMap = dataSqlService.eBuilderFromSqlOne(querySql, SourceType.LOGIC);
            String newWfColl;
            if(collectionDataMap.isEmpty()) {
                newWfColl = addCollectionDTO.getWorkflowId();
            } else {
                newWfColl = collectionDataMap.get("new_wf_coll").toString();
                List<String> newWfCollList = Arrays.asList(newWfColl.split(","));
                if(newWfCollList.contains(addCollectionDTO.getWorkflowId())) {
                    log.info("{}流程已存在, 流程ID：", addCollectionDTO.getWorkflowId());
                    return WeaResult.fail("流程已存在, 流程ID：" + addCollectionDTO.getWorkflowId());
                }
                newWfColl = addCollectionDTO.getWorkflowId() + "," + newWfColl;
            }
            String objId = EBuilderUtil.getFormObjId(iEtFormDatasetService, cmicProperties.getOpenPlatformUrl(), uid, iOpenPlatformService.getAccessToken(), "uf_my_collection", "objId");
            List<Map<String, Object>> datas = new ArrayList<>();
            Map<String, Object> dataMap = new HashMap<>(2);
            JSONObject mainTableObject = new JSONObject();
            mainTableObject.put("userid", uid);
            mainTableObject.put("new_wf_coll", newWfColl);
            dataMap.put("mainTable", mainTableObject);
            datas.add(dataMap);
            Map<String, Object> mainTableField = new HashMap<>(1);
            List<String> mainTableFields = new ArrayList<>();
            mainTableFields.add("userid");
            mainTableField.put("mainTable", mainTableFields);
            EbFormDataReq builder = new EbFormDataReq.Builder()
                    .userId(uid)
                    .objId(objId)
                    .needAdd("true")
                    .updateType("updatePolicy")
                    .updateField(mainTableField)
                    .datas(datas)
                    .build();
            EbFormDataResultVo resultVo = EBuilderUtil.updateFormDataV2(builder, iOpenPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());
            return WeaResult.success(resultVo);
        } catch (Exception e) {
            return WeaResult.fail(e.getMessage());
        }
    }

    @Override
    public WeaResult<Object> cancelCollection(AddCollectionDTO addCollectionDTO) {
        String method = String.format("调用%s.cancelCollection-->", SIMPLE_NAME);
        log.info("{}开始执行", method);
        SimpleEmployee currentUser = UserContext.getCurrentUser();
        String uid = String.valueOf(currentUser.getUid());
        String querySql = "select * from uf_my_collection where is_delete=0 and userid=" + uid;
        try {
            Map<String, Object> collectionDataMap = dataSqlService.eBuilderFromSqlOne(querySql, SourceType.LOGIC);
            if(collectionDataMap.isEmpty()) {
                return WeaResult.fail("用户流程不存在");
            }
            String newWfColl = collectionDataMap.get("new_wf_coll").toString();
            List<String> newWfCollList = new ArrayList<>(Arrays.asList(newWfColl.split(",")));
            newWfCollList.remove(addCollectionDTO.getWorkflowId());
            String newWfCollStr = StringUtils.join(newWfCollList, ",");
            String objId = EBuilderUtil.getFormObjId(iEtFormDatasetService, cmicProperties.getOpenPlatformUrl(), uid, iOpenPlatformService.getAccessToken(), "uf_my_collection", "objId");
            List<Map<String, Object>> datas = new ArrayList<>();
            Map<String, Object> dataMap = new HashMap<>(2);
            JSONObject mainTableObject = new JSONObject();
            mainTableObject.put("userid", uid);
            mainTableObject.put("new_wf_coll", newWfCollStr);
            dataMap.put("mainTable", mainTableObject);
            datas.add(dataMap);
            Map<String, Object> mainTableField = new HashMap<>(1);
            List<String> mainTableFields = new ArrayList<>();
            mainTableFields.add("userid");
            mainTableField.put("mainTable", mainTableFields);
            EbFormDataReq builder = new EbFormDataReq.Builder()
                    .userId(uid)
                    .objId(objId)
                    .needAdd("true")
                    .updateType("updatePolicy")
                    .updateField(mainTableField)
                    .datas(datas)
                    .build();
            EbFormDataResultVo resultVo = EBuilderUtil.updateFormDataV2(builder, iOpenPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());
            return WeaResult.success(resultVo);
        } catch (Exception e) {
            log.info("{}取消用户收藏失败，{}", method, e.getMessage());
            return WeaResult.fail(e.getMessage());
        }
    }

    @Override
    public WeaResult<Object> getMyWorkflowRequestList() {
        String method = String.format("调用%s.getMyWorkflowRequestList-->", SIMPLE_NAME);
        log.info("{}开始执行", method);
        SimpleEmployee currentUser = UserContext.getCurrentUser();
        List<FlowList> flowVos = WfcUtil.getMyWorkflowRequestAllList(iOpenPlatformService.getAccessToken(), currentUser.getUid(), cmicProperties.getOpenPlatformUrl());

        return WeaResult.success(flowVos);
    }

    @Override
    public WeaResult<Object> getOptions() {
        Map<String, Object> result = new HashMap<>();
        // 获取一级分类
        String yiSql = "select * from uf_xi_tflwh where is_delete=0";
        // 获取二级分类
        String erSql = "select fp.* from field_option fp LEFT JOIN form_field ff on fp.field_id = ff.id LEFT JOIN form_table ft on ft.form_id = ff.form_id WHERE ft.table_name ='uf_xin_jlcpz' and ff.data_key = 'er_jfl' AND (  fp.archive IS NULL OR fp.archive = 0)";
        try {
            List<Map<String, Object>> oneDataList = dataSqlService.eBuilderFromSqlAll(yiSql, SourceType.LOGIC);
            for (Map<String, Object> stringObjectMap : oneDataList) {
                stringObjectMap.put("id", stringObjectMap.get("id"));
            }
            result.put("yi_jfl", oneDataList);
            List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(erSql, SourceType.LOGIC);
            for (Map<String, Object> stringObjectMap : dataList) {
                stringObjectMap.put("id", stringObjectMap.get("id"));
            }
            result.put("er_jfl", dataList);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return WeaResult.success(result);
    }

    @Override
    public WeaResult<Object> getCreateWorkflowList() {
        String method = String.format("调用%s.getCreateWorkflowList-->", SIMPLE_NAME);
        log.info("{}开始执行", method);
        SimpleEmployee currentUser = UserContext.getCurrentUser();
        List<CreateWorkFlow> flowVos = WfcUtil.getCreateWorkflowList(iOpenPlatformService.getAccessToken(), currentUser.getUid(), cmicProperties.getOpenPlatformUrl(), 1, new ArrayList<>(), "");

        return WeaResult.success(flowVos);
    }

    private List<Map<String, Object>> getCollectionByUserid(String uid) {
        try {
            String querySql = "select * from uf_my_collection where is_delete=0 and userid=" + uid;
            log.info("getCollectionByUserid获取新建流程我的收藏数据sql：" + querySql);
            return dataSqlService.eBuilderFromSqlAll(querySql, SourceType.LOGIC);
        } catch (Exception e) {
            log.info("oops getCollectionByUserid catch exception：" + e + "@" + e.getMessage());
            return new ArrayList<>();
        }
    }


    /**
     * handleNewWorkflowSql
     * <p>
     *
     * @param
     * @return List<Condition>
     * @description 。
     * <p>
     * <AUTHOR>
     * @time 2025年02月13 11:02:54
     * @since 1.0
     */
    private void handleNewWorkflowSql(StringBuffer sql, NewWorkFlowDTO newWorkFlowDTO) {
        if (StringUtils.isNotBlank(newWorkFlowDTO.getId())) {
            sql.append(" and id in (").append(newWorkFlowDTO.getId()).append(")");
        }
        if (StringUtils.isNotBlank(newWorkFlowDTO.getWorkflowId())) {
            sql.append(" and liu_c = ").append(newWorkFlowDTO.getWorkflowId());
        }
        if (StringUtils.isNotBlank(newWorkFlowDTO.getWorkflowName())) {
            sql.append(" and liu_cmc like '%").append(newWorkFlowDTO.getWorkflowName()).append("%'");
        }
        if (StringUtils.isNotBlank(newWorkFlowDTO.getIsShow())) {
            sql.append(" and shi_fxs = ").append(newWorkFlowDTO.getIsShow());
        }
        if (StringUtils.isNotBlank(newWorkFlowDTO.getWorkflowOneClassify())) {
            sql.append(" and yi_jfl in (").append(newWorkFlowDTO.getWorkflowOneClassify()).append(")");
        }
        if (StringUtils.isNotBlank(newWorkFlowDTO.getWorkflowTwoClassify())) {
            sql.append(" and er_jfl in (").append(newWorkFlowDTO.getWorkflowTwoClassify()).append(")");
        }
        if (StringUtils.isNotBlank(newWorkFlowDTO.getSystem())) {
            sql.append(" and suo_sxt = ").append(newWorkFlowDTO.getSystem());
        }
        if (StringUtils.isNotBlank(newWorkFlowDTO.getIsMobileShow())) {
            sql.append(" and (yi_ddsfxs in (").append(newWorkFlowDTO.getIsMobileShow()).append(") or yi_ddsfxs is null)");
        }
    }

}
