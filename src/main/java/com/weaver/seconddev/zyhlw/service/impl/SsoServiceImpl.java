package com.weaver.seconddev.zyhlw.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.weaver.openapi.pojo.user.res.vo.UserResult;
import com.weaver.seconddev.zyhlw.service.ICmicService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.service.ISsoService;
import com.weaver.seconddev.zyhlw.service.ITodoService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.TodoUtil;
import com.weaver.seconddev.zyhlw.util.http.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SsoServiceImpl implements ISsoService {

    private final String simpleName = SsoServiceImpl.class.getSimpleName();
    @Resource
    private IOpenPlatformService iOpenPlatformService;
    @Resource
    private ICmicService iCmicService;
    @Resource
    private CmicProperties cmicProperties;
    @Resource
    private ITodoService iTodoService;
    private String msgErr = "获取令牌异常";

    private String errLogInfo = "请求获取本系统令牌接口异常";

    /**
     * 云桌面单点
     *
     * @param token 票据
     * @return 结果
     */
    @Override
    public Map<String, Object> dcLogin(String token) {
        String methodName = "调用" + simpleName + ".dcLogin()";
        log.info("{} 入参：{}", methodName, token);
        log.info("{} 云桌面token认证接口地址：{}", methodName, cmicProperties.getLdapUrl());
        Map<String, Object> result = new HashMap<>(16);
        if (token == null || "".equals(token)) {
            log.warn("{} token参数缺失", methodName);
            result.put("code", 2001);
            result.put("msg", "token 参数缺失");
            return result;
        }
        try {
            token = URLEncoder.encode(token, "UTF-8");
            log.info("{} 对token进行编码：{}", methodName, token);
        } catch (UnsupportedEncodingException e) {
            log.error("{} token编码异常！", methodName, e);
            result.put("code", 2002);
            result.put("msg", "token 编码异常");
            return result;
        }
        try {
            String sendGetData = HttpUtils.sendGet(cmicProperties.getLdapUrl(), "token=" + token);
            log.info("{} 请求云桌面token认证接口，返回：{}", methodName, sendGetData);
            String loginId = sendGetData.substring(sendGetData.indexOf("VDI\\") + 5, sendGetData.length() - 1);
            String loginTokenStr = iOpenPlatformService.restfilGetLoginToken(cmicProperties.getLdapAppKey(), cmicProperties.getLdapAppSecurity(), loginId, "loginID");
            log.info("{} 请求获取本系统令牌接口返回：{}", methodName, loginTokenStr);
            JSONObject loginTokenJson = JSON.parseObject(loginTokenStr);
            String errcodeKey = "errcode";
            String successCode = "0";
            if (successCode.equals(loginTokenJson.getString(errcodeKey))) {
                String etLoginToken = loginTokenJson.getString("etLoginToken");
                String url = cmicProperties.getSingleSignonUrl() + "?singleToken=" + etLoginToken + "&oauthType=singlesign&redirect_uri=" + cmicProperties.getLdapRedirectUri();

                result.put("code", 0);
                result.put("msg", "成功");
                result.put("url", url);

                Long userid = getUserIdByJobNum(loginId);
                log.info("｛｝ 当前用户ID：", methodName, userid);
                if (!userid.equals("0")) {
                    iTodoService.cacheTodo(userid);
                }
                TodoUtil.startGetSysData(cmicProperties.getStartGetSysDataUrl(), loginId);
                return result;
            } else {
                log.error("{} " + errLogInfo, methodName);
                result.put("code", 2004);
                result.put("msg", msgErr);
                return result;
            }
        } catch (Exception e) {
            log.error("{} 发送云桌面token认证请求异常！", methodName, e);
            result.put("code", 2003);
            result.put("msg", "token 请求接口异常");
            return result;
        }
    }

    /**
     * @param token
     * @return
     */
    @Override
    public Map<String, Object> portalTokenLogin(String token) {
        return null;
    }

    /**
     * SIM认证登录
     *
     * @param phone 手机号码
     * @return
     */
    @Override
    public Map<String, Object> simLogin(String phone) {
        String methodName = "调用" + simpleName + ".SIMLogin()";
        Map<String, Object> result = new HashMap<>(16);
        String simQuickAuthData = null;
        try {
            simQuickAuthData = iCmicService.simQuickAuth(phone, "您好！您正在PC端通过SIM盾进行登录测试环境的E10，请确认是否继续进行登录操作。", "02");
            log.info("{} 请求SIM快捷认证接口，返回：{}", methodName, simQuickAuthData);
            JSONObject simQuickAuthDataJson = JSON.parseObject(simQuickAuthData);
            Integer code = simQuickAuthDataJson.getInteger("code");
            // 取消sim认证接口状态
            Integer cancelCode = 262580;
            if (code == 0) {

                String loginTokenStr = iOpenPlatformService.restfilGetLoginToken(cmicProperties.getSimQuickAuthAppKey(), cmicProperties.getSimQuickAuthAppSecurity(), phone, "mobile");
                log.info("{} 请求获取本系统令牌接口返回：{}", methodName, loginTokenStr);
                JSONObject loginTokenJson = JSON.parseObject(loginTokenStr);
                String errcodeKey = "errcode";
                if (loginTokenJson.containsKey(errcodeKey)) {
                    String successCode = "0";
                    if (successCode.equals(loginTokenJson.getString(errcodeKey))) {
                        Long userid = getUserIdByMobile(phone);
                        log.info("{} 当前用户ID：{}", methodName, userid);
                        if (!userid.equals("0")) {
                            iTodoService.cacheTodo(userid);
                        }
                        String etLoginToken = loginTokenJson.getString("etLoginToken");
                        String url = cmicProperties.getSingleSignonUrl() + "?singleToken=" + etLoginToken + "&oauthType=singlesign&redirect_uri=" + cmicProperties.getSimQuickAuthRedirectUri();
                        result.put("code", 0);
                        result.put("msg", "成功");
                        result.put("url", url);

                        return result;
                    } else {
                        log.error("{} " + errLogInfo, methodName);
                        result.put("code", 2004);
                        result.put("msg", msgErr);
                        return result;
                    }
                } else {
                    log.error("{} " + errLogInfo, methodName);
                    result.put("code", 2004);
                    result.put("msg", msgErr);
                    return result;
                }
            } else if (code.equals(cancelCode)) {
                log.error("{} 取消SIM快捷认证", methodName);
                result.put("code", 2003);
                result.put("msg", "您已取消手机登录！请重新操作");
                return result;
            } else {
                log.error("{} 请求SIM快捷认证接口返回异常", methodName);
                result.put("code", 2002);
                result.put("msg", msgErr);
                return result;
            }
        } catch (Exception e) {
            log.error("{} 请求异常", methodName, e);
            result.put("code", 2001);
            result.put("msg", "请求异常");
            return result;
        }
    }

    private Long getUserIdByMobile(String mobile) {
        return getUserId(mobile, null);
    }

    private Long getUserIdByJobNum(String jobNum) {
        return getUserId(null, jobNum);
    }

    private Long getUserId(String mobile, String jobNum) {
        Long userId = 0L;
        List<UserResult> user = new ArrayList<>();
        if (mobile != null && !mobile.isEmpty()) {
            user = iOpenPlatformService.findUser(null, null, null, mobile, null);
        }
        if (jobNum != null && !jobNum.isEmpty()) {
            user = iOpenPlatformService.findUser(null, null, null, null, jobNum);
        }
        for (UserResult userResult : user) {
            if (userResult.getFormData().getFormData().getTenantKey().equals(cmicProperties.getHostTenantKey()) && userResult.getStatus().equals("normal")) {
                userId = userResult.getUserid();
            }
        }
        return userId;
    }


   /**
     * 通过安全网关认证登录后，返回到到指定的页面中
     *
     * @param phone 手机号码
    *  @param goUrl 登录成功后返回的Url地址
     * @return
     */
    @Override
    public Map<String, Object> mobileLogin(String phone,String goUrl) {

        Map<String, Object> result = new HashMap<>(16);
        String methodName = "调用" + simpleName + ".mobileLogin()";
        try {
            String loginTokenStr = iOpenPlatformService.restfilGetLoginToken(cmicProperties.getSimQuickAuthAppKey(), cmicProperties.getSimQuickAuthAppSecurity(), phone, "mobile");
            log.info("{} 请求获取本系统令牌接口返回：{}", methodName, loginTokenStr);
            JSONObject loginTokenJson = JSON.parseObject(loginTokenStr);
            String errcodeKey = "errcode";
            if (loginTokenJson.containsKey(errcodeKey)) {
                String successCode = "0";
                if (successCode.equals(loginTokenJson.getString(errcodeKey))) {
                    Long userid = getUserIdByMobile(phone);
                    log.info("{} 当前用户ID：{}", methodName, userid);
//                    if (!userid.equals("0")) {
//                        iTodoService.cacheTodo(userid);
//                    }
                    String etLoginToken = loginTokenJson.getString("etLoginToken");
                    String url = cmicProperties.getSingleSignonUrl() + "?singleToken=" + etLoginToken + "&oauthType=singlesign&redirect_uri=" + goUrl;
                    result.put("code", 0);
                    result.put("msg", "成功");
                    result.put("url", url);

                    return result;
                } else {
                    log.error("{} " + errLogInfo, methodName);
                    result.put("code", 2004);
                    result.put("msg", msgErr);
                    result.put("url", "");
                    return result;
                }
            } else {
                log.error("{} " + errLogInfo, methodName);
                result.put("code", 2004);
                result.put("msg", msgErr);
                return result;
            }
        } catch (Exception e) {
            log.error("{} 请求异常", methodName, e);
            result.put("code", 2001);
            result.put("msg", "请求异常");
            return result;
        }

    }


}
