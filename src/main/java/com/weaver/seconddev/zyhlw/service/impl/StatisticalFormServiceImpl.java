package com.weaver.seconddev.zyhlw.service.impl;

import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.openapi.pojo.dept.res.DeptVo;
import com.weaver.openapi.service.DeptService;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.service.IPortalService;
import com.weaver.seconddev.zyhlw.service.StatisticalFormService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @date 2025-05-26
 */
@Service
@Slf4j
public class StatisticalFormServiceImpl implements StatisticalFormService {

    @Resource
    private IDataSqlService dataSqlService;
    @Resource
    private IPortalService portalService;
    @Resource
    private IOpenPlatformService openPlatformService;
    @Resource
    CmicProperties cmicProperties;

    @Override
    public List<Map<String, Object>> getDataOverview(String year, String quarter, String departmentId) {
        String condition = getCondition(year, quarter, departmentId);
        String sql = "SELECT a.wen_tly as name,a.id, COALESCE(b.count, 0) AS count FROM uf_sourceProblem a LEFT JOIN (SELECT wen_tly, sum(wen_tsl) AS count FROM uf_disIssuesList where 1=1 " + condition + " GROUP BY wen_tly) b ON a.id = b.wen_tly order by a.id";
        List<Map<String, Object>> list = new ArrayList<>();
        try {
            List<Map<String, Object>> maps = dataSqlService.ebuilderFromSql(sql, 1, 100, SourceType.LOGIC);
            for (Map<String, Object> map : maps) {
                String id = (String) map.get("id");
                String name = (String) map.get("name");
                String count = (String) map.get("count");
                map.put("id", id);
                map.put("name", name);
                map.put("count", count);
                list.add(map);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return list;
    }

    @Override
    public Map<String, Object> getClassifiedStatistic(String year, String quarter, String departmentId) {
        String condition = getCondition(year, quarter, departmentId);
        Map<String, Object> map1 = new HashMap<>();
        try {
            String tableName = "";
            String field = "";
            String key = "";
            for (int i = 0; i < 3; i++) {
                if (i == 0) {
                    tableName = "uf_proresarea";
                    field = "wen_tzrly";
                    key = "domain";
                }
                if (i == 1) {
                    tableName = "uf_sourceproblem";
                    field = "wen_tly";
                    key = "source";
                }
                if (i == 2) {
                    tableName = "uf_problemnature";
                    field = "wen_txz";
                    key = "property";
                }
                String sql = "SELECT a." + field + " as name,a.id, COALESCE(b.count, 0) AS count FROM " + tableName + " a LEFT JOIN (SELECT " + field + ", sum(wen_tsl) AS count FROM uf_disissueslist where 1=1 " + condition + " GROUP BY " + field + ") b ON a.id = b." + field + " order by a.id";
                List<Map<String, Object>> maps = dataSqlService.ebuilderFromSql(sql, 1, 100, SourceType.LOGIC);
                List<Map<String, Object>> list = new ArrayList<>();
                for (Map<String, Object> map : maps) {
                    String id = (String) map.get("id");
                    String name = (String) map.get("name");
                    String count = (String) map.get("count");
                    map.put("id", id);
                    map.put("name", name);
                    map.put("count", count);
                    list.add(map);
                }
                map1.put(key, list);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return map1;
    }

    @Override
    public List<Map<String, Object>> getProblemSources() {
        log.info("开始执行党风廉政主动发现问题管理部门分组操作");
        String sql = "select id,wen_tly from uf_sourceProblem order by id";
        List<Map<String, Object>> list = new ArrayList<>();
        try {
            List<Map<String, Object>> maps = dataSqlService.ebuilderFromSql(sql, 1, 100, SourceType.LOGIC);
            for (Map<String, Object> map : maps) {
                String id = (String) map.get("id");
                String name = (String) map.get("wen_tly");
                map.put("id", id);
                map.put("name", name);
                list.add(map);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return list;
    }

    @Override
    public Map<String, Object> getDepartmentalGrouping(String year, String quarter, String departmentId, String source) {
        log.info("开始执行党风廉政主动发现问题管理部门分组模块操作");
        Map<String, Object> dataMap = new HashMap<>();
        String condition = getCondition(year, quarter, departmentId);
        String sql;
        if (!source.isEmpty()) {
            sql = "select a.bu_mmc,b.* from uf_zhu_ztbmpx a left join (SELECT tian_bbm,sum(wen_tsl) AS count FROM uf_disissueslist where wen_tly = " + source + condition + " GROUP BY tian_bbm) b on a.bu_mmc = b.tian_bbm order by a.bu_mxh asc";
            log.info("sql：{}", sql);
            try {
                List<Map<String, Object>> maps = dataSqlService.ebuilderFromSql(sql, 1, 100, SourceType.LOGIC);
                List<Integer> seriesList = new ArrayList<>();
                List<Object> xAxisIdList = new ArrayList<>();
                List<Object> xAxisDataList = new ArrayList<>();
                for (Map<String, Object> map : maps) {
                    String id = (String) map.get("id");
                    String name = (String) map.get("bu_mmc");
                    String count = (String) map.get("count");
                    map.put("id", id);
                    map.put("name", name);
                    map.put("count", count);
                    xAxisIdList.add(name);
                    DeptVo deptVo = DeptService.getDeptV2(openPlatformService.getAccessToken(),
                            Long.valueOf(name), cmicProperties.getOpenPlatformUrl(), null);
                    String departmentName = deptVo.getDepartment().getName();
                    xAxisDataList.add(departmentName);
                    seriesList.add(Integer.parseInt(StringUtils.null2String(map.get("count"), "0")));
                }

                dataMap.put("series", seriesList);
                dataMap.put("xAxisId", xAxisIdList);
                dataMap.put("xAxisData", xAxisDataList);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } else {
            try {
                sql = "select id,wen_tly from uf_sourceProblem order by id asc";
                List<Map<String, Object>> sourceList = new ArrayList<>();
                List<Map<String, Object>> maps = dataSqlService.ebuilderFromSql(sql, 1, 100, SourceType.LOGIC);
                int a = 0;
                StringBuilder fileName = new StringBuilder();
                for (Map<String, Object> map : maps) {
                    a++;
                    String id = (String) map.get("id");

                    String name = (String) map.get("wen_tly");
                    fileName.append(",SUM(CASE WHEN wen_tly = ").append(id).append(" THEN wen_tsl ELSE 0 END) AS count").append(a);
                    map.put("id", id);
                    map.put("wen_tly", name);
                    sourceList.add(map);
                }
                sql = "select a.bu_mmc,b.* from uf_zhu_ztbmpx a left join (SELECT tian_bbm" + fileName + " FROM uf_disIssuesList where 1=1 " + condition + " GROUP BY tian_bbm) b on a.bu_mmc = b.tian_bbm order by a.bu_mxh asc";
                log.info("sql：{}", sql);
                List<Map<String, Object>> seriesList = new ArrayList<>();
                List<Object> xAxisIdList = new ArrayList<>();
                List<Object> xAxisDataList = new ArrayList<>();
                int size = sourceList.size();
                List<Integer>[] valueLists = new ArrayList[size];
                for (int i = 0; i < size; i++) {
                    valueLists[i] = new ArrayList<>();
                }

                Integer[] intArray = new Integer[size];
                for (int i = 0; i < size; i++) {
                    intArray[i] = 0;
                }

                List<Map<String, Object>> maps1 = dataSqlService.ebuilderFromSql(sql, 1, 100, SourceType.LOGIC);
                for (Map<String, Object> map : maps1) {
                    String id = (String) map.get("id");
                    String name = (String) map.get("bu_mmc");
                    DeptVo deptVo = DeptService.getDeptV2(openPlatformService.getAccessToken(),
                            Long.valueOf(name), cmicProperties.getOpenPlatformUrl(), null);
                    String departmentName = deptVo.getDepartment().getName();
                    xAxisIdList.add(name);
                    xAxisDataList.add(departmentName);
                    for (int i = 0; i < size; i++) {
                        valueLists[i].add(Integer.parseInt(StringUtils.null2String(map.get("count" + (i + 1)), "0")));
                    }
                }

                for (int i = 0; i < size; i++) {
                    Map<String, Object> sourceMap = new HashMap<>();
                    sourceMap.put("id", sourceList.get(i).get("id"));
                    sourceMap.put("name", sourceList.get(i).get("wen_tly"));
                    sourceMap.put("value", valueLists[i]);
                    seriesList.add(sourceMap);
                }

                dataMap.put("series", seriesList);
                dataMap.put("xAxisId", xAxisIdList);
                dataMap.put("xAxisData", xAxisDataList);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return dataMap;
    }

    @Override
    public List<Map<String, String>> getDepartmentalProportionStatistics(String year, String quarter, String departmentId) {
        try {
            String condition = getCondition(year, quarter, departmentId);
            String sql = "select a.bu_mmc,b.* from uf_zhu_ztbmpx a left join (SELECT tian_bbm, ROUND((SUM(wen_tsl) / (SUM(wen_tsl) + SUM(LENGTH(wen_tzrbm) - LENGTH(REPLACE(wen_tzrbm, ',', '')) + 1))), 2) AS count FROM uf_disissueslist where 1=1 " + condition + " GROUP BY tian_bbm) b on a.bu_mmc = b.tian_bbm order by a.bu_mxh asc";
            log.info("sql：{}", sql);

            List<Map<String, String>> list = new ArrayList<>();
            List<Map<String, Object>> maps = dataSqlService.ebuilderFromSql(sql, 1, 100, SourceType.LOGIC);
            for (Map<String, Object> map : maps) {
                Map<String, String> map1 = new HashMap<>();
                String id = (String) map.get("id");
                String name = (String) map.get("name");
                String count = (String) map.get("count");
                DeptVo deptVo = DeptService.getDeptV2(openPlatformService.getAccessToken(),
                        Long.valueOf(name), cmicProperties.getOpenPlatformUrl(), null);
                String departmentName = deptVo.getDepartment().getName();
                map1.put("id", id);
                map1.put("name", departmentName);
                map1.put("count", count);
                list.add(map1);
            }
            return list;
        } catch (Exception e) {
            log.error("getDepartmentalProportionStatistics异常的原因：{}", String.valueOf(e));
            throw new RuntimeException(e);
        }
    }

    //条件拼接
    private String getCondition(String year, String quarter, String department) {
        String condition = "";
        if (!year.isEmpty()) {
            condition += " and tian_bsj like '" + year + "%'";
        }
        if (!department.isEmpty()) {
            //获取所有的下级部门
            List<String> departIdList = portalService.getDepartIdList(department);
            String subordinateDepartment = String.join(",", departIdList);
            String[] parts = subordinateDepartment.split(",");
            int[] array = new int[parts.length];
            StringBuilder tian_bbmCondition = new StringBuilder();
            for (int k = 0; k < parts.length; k++) {
                array[k] = Integer.parseInt(parts[k]);
                tian_bbmCondition.append("tian_bbm = ").append(array[k]);
                if (k != parts.length - 1) {
                    tian_bbmCondition.append(" or ");
                }
            }
            condition += " and (" + tian_bbmCondition + ")";
        }
        if (!quarter.isEmpty()) {
            List<Map<String, Object>> list = acquisitionQuarter(Integer.parseInt(year));
            condition += " and tian_bsj > '" + list.get(Integer.parseInt(quarter)).get("starTime") + "'";
            condition += " and tian_bsj < '" + list.get(Integer.parseInt(quarter)).get("endTime") + "'";
        }
        return condition;
    }

    //根据年份获取季度
    private static List<Map<String, Object>> acquisitionQuarter(int year) {
        List<Map<String, Object>> list = new ArrayList<>();
        for (int quarter = 1; quarter <= 4; quarter++) {
            Map<String, Object> map = new HashMap<>();
            YearMonth startMonth = getStartMonthOfQuarter(year, quarter);
            YearMonth endMonth = getEndMonthOfQuarter(year, quarter);
            map.put("starTime", startMonth.format(DateTimeFormatter.ofPattern("yyyy-MM")));
            endMonth = endMonth.plusMonths(1);
            String formattedEndMonth = endMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            map.put("endTime", formattedEndMonth);
            list.add(map);
        }
        return list;
    }

    private static YearMonth getStartMonthOfQuarter(int year, int quarter) {
        int startMonth = (quarter - 1) * 3 + 1;
        return YearMonth.of(year, startMonth);
    }

    private static YearMonth getEndMonthOfQuarter(int year, int quarter) {
        int startMonth = (quarter - 1) * 3 + 1;
        int endMonth = startMonth + 2;
        return YearMonth.of(year, endMonth);
    }

}
