package com.weaver.seconddev.zyhlw.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.weaver.common.hrm.domain.hrmfield.HrmSyncScopeBean;
import com.weaver.common.hrm.dto.hrmintegration.HrmIntegrationDataRule;
import com.weaver.common.hrm.dto.hrmintegration.HrmIntegrationDataType;
import com.weaver.common.hrm.dto.hrmintegration.HrmIntegrationSyncData;
import com.weaver.common.hrm.dto.hrmintegration.HrmIntegrationSyncDataResult;
import com.weaver.common.hrm.remote.HrmRemoteIntegrationSyncDataService;
import com.weaver.framework.rpc.annotation.RpcReference;
import com.weaver.openapi.pojo.user.res.vo.UserInfoResult;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.service.ISynHrService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.CmicUtil;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import com.weaver.seconddev.zyhlw.util.page.PageInfo;
import com.weaver.seconddev.zyhlw.util.page.PaginationUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SynHrServiceImpl implements ISynHrService {
    @Resource
    private IOpenPlatformService openPlatformService;
//    @Resource
//    CmicProperties cmicProperties;

    private final String TENANT_KEY = "t6il1ypj4w";
    //cmicProperties.getHostTenantKey();
    @RpcReference
    private HrmRemoteIntegrationSyncDataService hrmRemoteIntegrationSyncDataService;

    @Resource
    IDataSqlService dataSqlService;

    @Override
    public Map<String, Object> synHrmData() throws Exception {

        JSONArray empDataArr = CmicUtil.syncEmp("", "query_all_emps", "");
        // 获取所有用户
        List<UserInfoResult> allUsers = openPlatformService.findAllUsersV3();
        Map<String, Map<String, String>> departmentMangerMap = new HashMap<>();

        JSONArray departmentFieldArr = openPlatformService.listDepartmentField(TENANT_KEY);
        // 部门自定义字段 组织ID 字段ID
        String orgidFieldId = getDepartmentFieldId(departmentFieldArr, "orgid");
        JSONObject orgJson = openPlatformService.restfulQueryOrg(true, true, true, null, 1, 1000, TENANT_KEY);
        // 岗位数据
        String positStr = "|,|";
        String positCodeStr = "|,|";
        // 职务数据
        String jobSetsStr = "|,|";
        List<Map<String, Object>> userList = new ArrayList<>();
        for (int i = 0; i < empDataArr.size(); i++) {
            JSONObject empData = empDataArr.getJSONObject(i);
            String positName = empData.getString("positName");
            String positCode = empData.getString("positCode");
            String positionName = empData.getString("positionName");
            if (positName != null && !"中移互联网岗位体系".equals(positName)) {
                if (positStr.indexOf("|,|" + positName + "|,|") < 0) {
                    positStr += positName + "|,|";
                    positCodeStr += positCode + "|,|";
                }
            }
            if (positionName != null) {
                if (jobSetsStr.indexOf("|,|" + positionName + "|,|") < 0) {
                    jobSetsStr += positionName + "|,|";
                }
            }
            Map<String, Object> userMap = new HashMap<>();
            // 姓名
            userMap.put("username", empData.getString("userName"));
            // userMap.put("username", empData.getString("userName") + "（" + empData.getString("portalUserId") + "）");
            // 状态 1:试用；2:试用延期；3:正式；4:临时;5:实习;6:离职;7:退休
            String personnel_status = "6";
            if ("1".equals(empData.getString("isValid"))) {
                personnel_status = "3";
            }
            userMap.put("personnel_status", personnel_status);

            JSONObject orgData = getExtendByOrgData(empData.getString("orgId"), orgidFieldId, orgJson);
            // 所属组织
            userMap.put("department", orgData.getString("code"));
            // 手机
            userMap.put("mobile", empData.getString("mobilePhone"));
            // 登录名
            userMap.put("loginid", empData.getString("portalUserId"));
            // 工号
            userMap.put("job_num", empData.getString("portalUserId"));
            // 邮箱
            userMap.put("email", empData.getString("email"));
            // 账号
            if (empData.getString("portalUserId").startsWith("dw") || empData.getString("portalUserId").startsWith("st")) {
                userMap.put("account", empData.getString("email"));
            } else {
                userMap.put("account", empData.getString("portalUserId") + "@cmic.cmcc");
            }
            // 岗位
            userMap.put("position", empData.getString("positName"));
            //性别
            String sex = "";
            // 男
            if ("1".equals(empData.getString("sex")) || "男".equals(empData.getString("sex"))) {
                sex = "male";
            } else if ("2".equals(empData.getString("sex")) || "女".equals(empData.getString("sex"))) {
                sex = "female";
            }
            userMap.put("sex", sex);
            userMap.put("password", "Cmic@2022_.");
            userList.add(userMap);
            //岗位编码
            String position = empData.getString("position") != null ? empData.getString("position") : "";

            Integer seclevel = getWorkPostMap(position);
            String portalUserId = empData.getString("portalUserId");
            String userId = "";
            List<UserInfoResult> usrelist = allUsers.stream().filter(p -> p.getAccount().replace("@cmic.cmcc","").equals(portalUserId)).collect(Collectors.toList());
            if (!usrelist.isEmpty()) {
                userId = usrelist.get(0).getUserid().toString();
            }
            if (seclevel != null && !userId.isEmpty()) {
                departmentMangerMap = getDepartMangerMap(departmentMangerMap, empData.getString("orgId"), seclevel, userId);
            }


        }
        String[] positSplit = positStr.split("\\|,\\|");
        String[] positCodeSplit = positCodeStr.split("\\|,\\|");
        List<Map<String, Object>> positList = new ArrayList<>();

        for (int i = 0; i < positSplit.length; i++) {
            Map<String, Object> positMap = new HashMap<>();
            positMap.put("name", positSplit[i]);
            positMap.put("code", positCodeSplit[i]);
            positMap.put("parent", "中移互联网岗位体系");
            positList.add(positMap);
        }

        //同步岗位数据
        if (positList.size() > 0) {
            HrmIntegrationSyncData hrmIntegrationSyncData = new HrmIntegrationSyncData();
            hrmIntegrationSyncData.setTenantKey(TENANT_KEY);


            HrmIntegrationDataRule hrmIntegrationDataRule = new HrmIntegrationDataRule();
            Map<HrmIntegrationDataType, HrmSyncScopeBean> fieldTypeRule = new HashMap();
            fieldTypeRule.put(HrmIntegrationDataType.position, HrmSyncScopeBean.builder().name("name").build());
            hrmIntegrationDataRule.setFieldTypeRule(fieldTypeRule);
            hrmIntegrationSyncData.setRule(hrmIntegrationDataRule);
            hrmIntegrationSyncData.setDataitems(positList);
            HrmIntegrationSyncDataResult hrmIntegrationSyncDataResult = hrmRemoteIntegrationSyncDataService.syncPosition(hrmIntegrationSyncData);
            log.info("调用SynHrServiceImpl.synHrmData 调用同步岗位接口返回数据：{}", JSON.toJSONString(hrmIntegrationSyncDataResult));
//            String positCodeData = openPlatformService.restfulSyncPosition(positList, TENANT_KEY);
//            Map<String, Object> poditCodeResult = analysisResult(positCodeData);
//            log.info("调用SynHrServiceImpl.synHrmData 调用开放平台同步岗位接口返回数据：{}",JSON.toJSONString(poditCodeResult));
        }

//         System.out.println("职务："+jobSetsStr);

        log.info("调用SynHrServiceImpl.synHrmData  用户数：{}", userList.size());
        if (userList.size() > 0) {
            PageInfo<Map<String, Object>> page1Data = PaginationUtil.paginate(userList, 1, 500);
            int totalPages = page1Data.getTotalPages();


            HrmIntegrationSyncData hrmIntegrationSyncData = new HrmIntegrationSyncData();
            hrmIntegrationSyncData.setTenantKey(TENANT_KEY);

            HrmIntegrationDataRule hrmIntegrationDataRule = new HrmIntegrationDataRule();
            Map<HrmIntegrationDataType, HrmSyncScopeBean> fieldTypeRule = new HashMap();
            fieldTypeRule.put(HrmIntegrationDataType.position, HrmSyncScopeBean.builder().name("name").build());
            fieldTypeRule.put(HrmIntegrationDataType.department, HrmSyncScopeBean.builder().name("code").build());
            fieldTypeRule.put(HrmIntegrationDataType.employee, HrmSyncScopeBean.builder().name("job_num").build());

            hrmIntegrationDataRule.setFieldTypeRule(fieldTypeRule);
            hrmIntegrationSyncData.setRule(hrmIntegrationDataRule);
            hrmIntegrationSyncData.setDataitems(page1Data.getData());
            long l = System.currentTimeMillis();
            HrmIntegrationSyncDataResult hrmIntegrationSyncDataResult = hrmRemoteIntegrationSyncDataService.syncEmployee(hrmIntegrationSyncData);
            log.info("调用SynHrServiceImpl.synHrmData 耗时：{}", (System.currentTimeMillis() - l));
            log.info("调用SynHrServiceImpl.synHrmData  同步用户数据结果：{}", JSON.toJSONString(hrmIntegrationSyncDataResult));
            for (int i = 1; i < totalPages; i++) {
                PageInfo<Map<String, Object>> paginate = PaginationUtil.paginate(userList, i + 1, 500);
                HrmIntegrationSyncData hrmIntegrationSyncData1 = new HrmIntegrationSyncData();
                hrmIntegrationSyncData1.setTenantKey(TENANT_KEY);
                hrmIntegrationSyncData1.setRule(hrmIntegrationDataRule);
                hrmIntegrationSyncData1.setDataitems(paginate.getData());
                hrmRemoteIntegrationSyncDataService.syncEmployee(hrmIntegrationSyncData1);
            }
        }

        //人员信息同步后，更新部门扩展信息
        updateDepartmentMap(orgJson, departmentFieldArr, departmentMangerMap);

//        Integer successNum = 0;
//        Integer errorNum =0;
//        List<Map<String,Object>> successData = new ArrayList<>();
//        List<Map<String,Object>> errorData = new ArrayList<>();
//            PageInfo<Map<String,Object>> page1Data = PaginationUtil.paginate(userList,1,100);
//            int totalPages = page1Data.getTotalPages();
//        Map<String, Object> restfulSyncEmployee = analysisResult(openPlatformService.restfulSyncEmployee(page1Data.getData(),TENANT_KEY));
//        Boolean code = (Boolean) restfulSyncEmployee.get("code");
//        if (code) {
//            successNum += (Integer) restfulSyncEmployee.get("successNum");
//            errorNum += (Integer) restfulSyncEmployee.get("errorNum");
//            successData.addAll((List<Map<String,Object>>)restfulSyncEmployee.get("successData"));
//            errorData.addAll((List<Map<String,Object>>)restfulSyncEmployee.get("errorData"));
//        }
//        for (int i = 1; i < totalPages; i++) {
//            PageInfo<Map<String, Object>> paginate = PaginationUtil.paginate(userList, i + 1, 100);
//            Map<String, Object> restfulSyncEmployee1 = analysisResult(openPlatformService.restfulSyncEmployee(paginate.getData(),TENANT_KEY)) ;
//            Boolean code1 = (Boolean) restfulSyncEmployee1.get("code");
//            if (code1) {
//                successNum += (Integer) restfulSyncEmployee1.get("successNum");
//                errorNum += (Integer) restfulSyncEmployee1.get("errorNum");
//                successData.addAll((List<Map<String,Object>>)restfulSyncEmployee1.get("successData"));
//                errorData.addAll((List<Map<String,Object>>)restfulSyncEmployee1.get("errorData"));
//            }
//        }
//        log.info("成功总数：{}，失败总数：{}，失败数据：{}",successNum,errorNum,JSON.toJSONString(errorData));
        Map<String, Object> result = new HashMap<>();
        result.put("data", "");
        return result;
    }

    @Override
    public Map<String, Object> synOrg() throws Exception {
        // 请求用户能力接口
        JSONArray orgsDataArr = CmicUtil.syncOrg("", "query_all_orgs", "");
        if (orgsDataArr.size() == 0) {
            Map<String, Object> resultMap = new HashMap<>();
            return resultMap;
        }
        log.info("接口中获取部门信息："+JSON.toJSONString(orgsDataArr));
        // 获取一级部门数据
        List<Map<String, Object>> parentData = new ArrayList<>();
        for (int i = 0; i < orgsDataArr.size(); i++) {
            JSONObject orgsData = orgsDataArr.getJSONObject(i);
            if (orgsData.getString("newParentOrgId") != null && orgsData.getLong("newParentOrgId") == 0L) {
                Map<String, Object> map = new HashMap<>();
                map.put("orgId", orgsData.getLong("orgId"));
                map.put("orgCode", orgsData.getString("orgCode"));
                parentData.add(map);
                break;
            }
        }
        // 获取部门自定义字段数据
        // 设置 请求头
        JSONArray fieldIdJsonArr = openPlatformService.listDepartmentField(TENANT_KEY);
        log.info("获取一级部门信息："+JSON.toJSONString(parentData));
        log.info("部门信息扩展属性："+JSON.toJSONString(fieldIdJsonArr));
        addDepartment(parentData, orgsDataArr, fieldIdJsonArr);

        List<String> disableDepartmentList = new ArrayList<>();
        for (int i = 0; i < orgsDataArr.size(); i++) {
            JSONObject orgsData = orgsDataArr.getJSONObject(i);
            if ("2".equals(orgsData.getString("isOpen"))) {
                disableDepartmentList.add(orgsData.getString("orgCode"));
            }
        }
        JSONObject restfulQueryOrg = openPlatformService.restfulQueryOrg(true, true, true, disableDepartmentList, 1, 1000, TENANT_KEY);

        int total = restfulQueryOrg.containsKey("total") ? restfulQueryOrg.getInteger("total") : 0;
        JSONArray orgDataJsonArr = restfulQueryOrg.containsKey("data") ? restfulQueryOrg.getJSONArray("data") : new JSONArray();
        List<Map<String, Object>> orgIdList = new ArrayList<>();
        for (int i = 0; i < orgDataJsonArr.size(); i++) {
            Long id = orgDataJsonArr.getJSONObject(i).getLong("id");
            Map<String, Object> idMap = new HashMap<>();
            idMap.put("id", id);
            log.info("封存部门数据：｛｝", orgDataJsonArr.getJSONObject(i).toString());
            orgIdList.add(idMap);

        }
        log.info("需要封存的数据：{}", JSON.toJSONString(orgIdList));
//        openPlatformService.restfulDisableDepartment(orgIdList,TENANT_KEY);
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("msg", "同步完成");
        return result;
    }


    /**
     * 处理各个级别的部门数据
     *
     * @param parentData     父级数据
     * @param orgsDataArr    部门数据
     * @param fieldIdJsonArr 部门自定义数据
     */
    private void addDepartment(List<Map<String, Object>> parentData, JSONArray orgsDataArr, JSONArray fieldIdJsonArr) {
        List<Map<String, Object>> parentDataTemp = new ArrayList<>();
        List<Map<String, Object>> orgsDataTemp = new ArrayList<>();
        for (Map<String, Object> parentDatum : parentData) {

            int c = 0;
            for (int i = 0; i < orgsDataArr.size(); i++) {
                JSONObject orgsData = orgsDataArr.getJSONObject(i);
//                if (orgsData.getString("parentOrgId").equals(parentDatum.get("orgId").toString()) && "1".equals(orgsData.getString("isOpen"))) {
                if (orgsData.getString("parentOrgId").equals(parentDatum.get("orgId").toString())) {
                    log.info("父级数据：{}", JSON.toJSONString(parentDatum));
                    c++;
                    log.info("----取到的下级数据，第{}条 -- {}，对应的parentOrgId：{}", c, orgsData.toString(),orgsData.getString("parentOrgId"));
                    Map<String, Object> orgsMap = new HashMap<>();
                    orgsMap.put("name", orgsData.getString("orgName"));
                    orgsMap.put("parent", parentDatum.get("orgCode").toString());
                    orgsMap.put("code", orgsData.getString("orgCode"));
                    orgsMap.put("status", "1");
                    orgsMap.put("disporder", orgsData.getInteger("sortSeq"));
                    JSONObject departmentExtend = new JSONObject();
                    // 部门自定义字段 组织ID
                    departmentExtend.put(getDepartmentFieldId(fieldIdJsonArr, "orgid"), orgsData.getString("orgId"));
                    //部门自定义字段 组织负责人,如果有值，则添加，否则不添加
                    // String managerId=orgsData.getString("managerId")!=null?orgsData.getString("managerId"):"";
                    // departmentExtend.put(getDepartmentFieldId(fieldIdJsonArr, "sjz"), managerId);

                    orgsMap.put("departmentExtend", departmentExtend);
                    orgsDataTemp.add(orgsMap);


                    Map<String, Object> parentMap = new HashMap<>();
                    parentMap.put("orgId", orgsData.getString("orgId"));
                    parentMap.put("orgCode", orgsData.getString("orgCode"));
                    parentMap.put("name", orgsData.getString("orgName"));
                    parentDataTemp.add(parentMap);
                }
            }
        }

        log.info("新的父级数据：{}", JSON.toJSONString(parentDataTemp));
        log.info("获取到的部门数据：{}", JSON.toJSONString(orgsDataTemp));
        if (!orgsDataTemp.isEmpty()) {
            HrmIntegrationSyncData hrmIntegrationSyncData1 = new HrmIntegrationSyncData();
            hrmIntegrationSyncData1.setTenantKey(TENANT_KEY);

            HrmIntegrationDataRule hrmIntegrationDataRule1 = new HrmIntegrationDataRule();

            Map<HrmIntegrationDataType, HrmSyncScopeBean> fieldTypeRule = new HashMap();
            fieldTypeRule.put(HrmIntegrationDataType.department, HrmSyncScopeBean.builder().name("code").build());

            hrmIntegrationDataRule1.setFieldTypeRule(fieldTypeRule);

            hrmIntegrationSyncData1.setRule(hrmIntegrationDataRule1);


            PageInfo<Map<String, Object>> page1Data = PaginationUtil.paginate(orgsDataTemp, 1, 500);
            int totalPages = page1Data.getTotalPages();
            log.info("部门数据分页总数：{}", totalPages);


            hrmIntegrationSyncData1.setDataitems(page1Data.getData());
            hrmRemoteIntegrationSyncDataService.syncDepartment(hrmIntegrationSyncData1);
            log.info("1、部门信息同步完成，hrmIntegrationSyncData1："+JSON.toJSONString(hrmIntegrationSyncData1));
            for (int i = 1; i < totalPages; i++) {
                PageInfo<Map<String, Object>> paginate = PaginationUtil.paginate(orgsDataTemp, i + 1, 500);
                HrmIntegrationSyncData hrmIntegrationSyncData2 = new HrmIntegrationSyncData();
                hrmIntegrationSyncData2.setTenantKey(TENANT_KEY);
                hrmIntegrationSyncData2.setRule(hrmIntegrationDataRule1);
                hrmIntegrationSyncData2.setDataitems(paginate.getData());
                hrmRemoteIntegrationSyncDataService.syncDepartment(hrmIntegrationSyncData2);
                log.info("2、部门信息同步完成，hrmIntegrationSyncData2："+JSON.toJSONString(hrmIntegrationSyncData2));
            }



        }
        if(!parentDataTemp.isEmpty())
        {
            addDepartment(parentDataTemp, orgsDataArr, fieldIdJsonArr);
            log.info("递归结束，parentDataTemp："+JSON.toJSONString(parentDataTemp));
        }
    }

    /**
     * 获取部门自定义字段的字段ID
     *
     * @param dataJsonArr 部门自定义字段集合
     * @param dataKey     字段key
     * @return
     */
    private String getDepartmentFieldId(JSONArray dataJsonArr, String dataKey) {
        String fieldId = dataKey;
        for (int i = 0; i < dataJsonArr.size(); i++) {
            JSONObject dataJson = dataJsonArr.getJSONObject(i);
            if (dataKey.equals(dataJson.getString("dataKey"))) {
                fieldId = dataJson.getString("fieldId");
                break;
            }

        }
        return fieldId;
    }

    /**
     * 通过部门自定义字段获取部门信息
     *
     * @param dataId  数据ID
     * @param fieldId 对应字段ID
     * @param orgJson 部门数据
     * @return
     */
    private JSONObject getExtendByOrgData(String dataId, String fieldId, JSONObject orgJson) {
        JSONObject resultJson = new JSONObject();


        JSONArray orgDataJsonArr = orgJson.containsKey("data") ? orgJson.getJSONArray("data") : new JSONArray();
        for (int i = 0; i < orgDataJsonArr.size(); i++) {
            JSONObject orgDataJson = orgDataJsonArr.getJSONObject(i);
            if (orgDataJson.containsKey("extend")) {
                JSONObject extend = orgDataJson.getJSONObject("extend");
                if (extend.containsKey(fieldId)) {
                    JSONObject fieldData = extend.getJSONObject(fieldId);
                    if (fieldData.getString("value").equals(dataId)) {
                        resultJson = orgDataJson;
                        break;
                    }
                } else {
                    log.error("调用SynHrServiceImpl.getExtendByOrgData 对应字段数据不存在，数据：{}，dataId：{}", orgDataJson, dataId);
                }
            } else {
                log.error("调用SynHrServiceImpl.getExtendByOrgData 组织自定义不存在，数据：{}，dataId：{}", orgDataJson, dataId);
            }
        }
        return resultJson;
    }


    /**
     * 解析开放平台接口结果
     * {
     * "message":{
     * "errcode":"0",
     * "errmsg":"success"
     * },
     * "data":[
     * ｛
     * "keyValue":"公司党委书记、董事长、总经理",
     * "id":972505081327943680,
     * "operate":"UPDATE",
     * "status":"SUCCESS",
     * "errcode":"SUCCESS",
     * "needRepush":false
     * ｝
     * ]
     * ｝
     *
     * @param jsonStr
     * @return
     */
    private Map<String, Object> analysisResult(String jsonStr) {
        Boolean code = true;
        Integer successNum = 0;
        Integer errorNum = 0;
        List<Map<String, Object>> successDataList = new ArrayList<>();
        List<Map<String, Object>> errorNumDataList = new ArrayList<>();
        if (jsonStr != null && !"".equals(jsonStr) && StringUtils.isValidJson(jsonStr)) {
            JSONObject jsonObject = JSON.parseObject(jsonStr);
            if (!jsonObject.containsKey("message")) {
                log.error("调用SynHrServiceImpl.analysisResult 解析开放平台接口结果异常，JSON格式错误：{}", jsonStr);
                code = false;
            } else {
                JSONObject messageJson = jsonObject.getJSONObject("message");
                if ("0".equals(messageJson.getString("errcode"))) {
                    JSONArray dataJsonArr = jsonObject.containsKey("data") ? jsonObject.getJSONArray("data") : new JSONArray();
                    for (int i = 0; i < dataJsonArr.size(); i++) {
                        JSONObject itemJson = dataJsonArr.getJSONObject(i);
                        if ("SUCCESS".equals(itemJson.getString("status"))) {
                            successNum++;
                            successDataList.add(itemJson);
                        } else if ("ERROR".equals(itemJson.getString("status"))) {
                            errorNum++;
                            errorNumDataList.add(itemJson);
                        }
                    }
                } else {
                    log.error("调用SynHrServiceImpl.analysisResult 解析开放平台接口结果 errcode 不等于0：{}", jsonStr);
                    code = false;
                }
            }
        } else {
            log.error("调用SynHrServiceImpl.analysisResult 解析开放平台接口结果异常，接收到非JSON数据：{}", jsonStr);
            code = false;
        }
        Map<String, Object> result = new HashMap<>();
        result.put("code", code);
        result.put("successNum", successNum);
        result.put("errorNum", errorNum);
        result.put("successData", successDataList);
        result.put("errorData", errorNumDataList);
        return result;
    }


    /**
     * 通过岗位编码获取用户的安全级别，来判断是普通员工，室总监，部门经理，公司领导
     *
     * @param position
     * @return
     */
    private Integer getWorkPostMap(String position) {
        Integer seclevel = 10;
        position = position.isEmpty() ? "1" : position;
        switch (position) {
            case "1":
                seclevel = 10;
            case "2":
                seclevel = 30;
                break;
            case "3":
            case "7":
            case "8":
                seclevel = 50;
                break;
            case "4":
                seclevel = 60;
                break;
            case "5":
            case "9":
                seclevel = 80;
                break;
            case "10":
                seclevel = 95;
            case "6":
                seclevel = 100;
                break;
            default:
                seclevel = 10;
                break;

        }

        return seclevel;
    }

    /**
     * 获取部门组织数据的扩展字段信息，获取
     *
     * @param orgJson          获取的部门组织数据
     * @param fieldIdJsonArr   扩展字段信息
     * @param departManagerMap 人员架构同步时组织负责人，部门领导，部门副总经理
     * @return
     */
    public void updateDepartmentMap(JSONObject orgJson, JSONArray fieldIdJsonArr, Map<String, Map<String, String>> departManagerMap) {
       log.info("用SynHrServiceImpl.getUpdateDepartmentMap方法，orgJson：{}",orgJson);
       log.info("用SynHrServiceImpl.getUpdateDepartmentMap方法，fieldIdJsonArr：{}",fieldIdJsonArr);
       log.info("用SynHrServiceImpl.getUpdateDepartmentMap方法，departManagerMap：{}",departManagerMap);
        Map<String, String> departmentExtendMap = new HashMap<>();
        Map<String, Object> detailMap = new HashMap<>();

        List<Map<String, Object>> listMap = new ArrayList<>();

        JSONArray orgDataJsonArr = orgJson.containsKey("data") ? orgJson.getJSONArray("data") : new JSONArray();
        // 组织负责人
        String szj_fileId = getDepartmentFieldId(fieldIdJsonArr, "szj");
        // 部门领导
        String bmld_fileId = getDepartmentFieldId(fieldIdJsonArr, "bmld");
        // 部门副总经理
        String bnmfj_fileId = getDepartmentFieldId(fieldIdJsonArr, "bnmfj");
        // 部门总经理
        String bmzjl_fileId = getDepartmentFieldId(fieldIdJsonArr, "bmzjl");
        log.info("调用SynHrServiceImpl.getUpdateDepartmentMap 获取的szj_fileId：{}，bmld_fileId：{}，bnmfj_fileId：{}，bmzjl_fileId：{}", szj_fileId, bmld_fileId, bnmfj_fileId, bmzjl_fileId);

        for (int i = 0; i < orgDataJsonArr.size(); i++) {
            JSONObject orgDataJson = orgDataJsonArr.getJSONObject(i);

            if (orgDataJson.containsKey("extend")) {
                // 部门编号
                String code = orgDataJson.getString("code");
                // 上级组织
                String parent = orgDataJson.getString("parent");
                if (departManagerMap.containsKey(code)) {
                    Map<String, String> managerMap = departManagerMap.get(code);
                    // 获取组织负责人
                    String szj_ManagerUserId = managerMap.getOrDefault("szj", "");
                    //获取部门领导
                    String bmld_ManagerUserId = managerMap.getOrDefault("bmld", "");
                    //获取部门副总经理
                    String bnmfj_ManagerUserId = managerMap.getOrDefault("bnmfj", "");
                    //获取部门主管领导
                    String bmzjl_ManagerUserId = managerMap.getOrDefault("bmzjl", "");

                    JSONObject extend = orgDataJson.getJSONObject("extend");
                    JSONObject fieldData = extend.getJSONObject(szj_fileId);
                    String fieldValue = fieldData.getString("value");
                    if (extend.containsKey(szj_fileId)) {
                        String newFieldValue = getFieldValue(fieldValue, szj_ManagerUserId);
                        if (!newFieldValue.isEmpty()) {
                            departmentExtendMap.put(szj_fileId, newFieldValue);
                        }
                    }
                    if (extend.containsKey(bmld_fileId)) {
                        String newFieldValue = getFieldValue(fieldValue, bmld_ManagerUserId);
                        if (!newFieldValue.isEmpty()) {
                            departmentExtendMap.put(bmld_fileId, newFieldValue);
                        }
                    }

                    if (extend.containsKey(bnmfj_fileId)) {
                        String newFieldValue = getFieldValue(fieldValue, bnmfj_ManagerUserId);
                        if (!newFieldValue.isEmpty()) {
                            departmentExtendMap.put(bnmfj_fileId, newFieldValue);
                        }
                    }
                    if (extend.containsKey(bmzjl_fileId)) {
                        String newFieldValue = getFieldValue(fieldValue, bmzjl_ManagerUserId);
                        if (!newFieldValue.isEmpty()) {
                            departmentExtendMap.put(bmzjl_fileId, newFieldValue);
                        }
                    }

                    detailMap.put("code", code);
                    detailMap.put("parent", parent);
                    detailMap.put("departmentExtend", departmentExtendMap);
                    if (!listMap.contains(detailMap)) {
                        listMap.add(detailMap);
                    }

                }
            } else {
                log.error("调用SynHrServiceImpl.getUpdateDepartmentMap 获取的extend为空");
            }
        }

        if (listMap.size() > 0) {
            HrmIntegrationSyncData hrmIntegrationSyncData1 = new HrmIntegrationSyncData();
            hrmIntegrationSyncData1.setTenantKey(TENANT_KEY);
            HrmIntegrationDataRule hrmIntegrationDataRule1 = new HrmIntegrationDataRule();
            Map<HrmIntegrationDataType, HrmSyncScopeBean> fieldTypeRule = new HashMap();
            fieldTypeRule.put(HrmIntegrationDataType.department, HrmSyncScopeBean.builder().name("code").build());
            hrmIntegrationDataRule1.setFieldTypeRule(fieldTypeRule);
            hrmIntegrationSyncData1.setRule(hrmIntegrationDataRule1);
            hrmIntegrationSyncData1.setDataitems(listMap);
            hrmRemoteIntegrationSyncDataService.syncDepartment(hrmIntegrationSyncData1);
            log.info("调用SynHrServiceImpl.getUpdateDepartmentMap 部门扩展洗更新操作，hrmIntegrationSyncData1：{}", JSON.toJSONString(hrmIntegrationSyncData1));
        } else {
            log.info("调用SynHrServiceImpl.getUpdateDepartmentMap 获取的部门组织数据扩展字段信息为空，不进行更新操作：{}");
        }

    }


    /**
     * 判断字段是否重复，不重复则添加
     *
     * @param oldFieldValue 原值
     * @param newFieldValue 则添加的值
     * @return
     */
    private String getFieldValue(String oldFieldValue, String newFieldValue) {
        String[] fieldGroup = oldFieldValue.split(",");
        List<String> list = new ArrayList<>();
        for (int i = 0; i < fieldGroup.length; i++) {
            String fileValue = fieldGroup[i].trim();
            if (!fileValue.isEmpty()) {
                if (!list.contains(fileValue)) {
                    list.add(fileValue);
                }
            }
        }
        return String.join(",", list);

    }

    /**
     * 获取部门组织数据扩展字段信息（室总监，部门副总经理，部门领导，部门领导），把人员组合起来返回一个新集合
     *
     * @param managerMap
     * @param departCode
     * @param secLevel
     * @param userId
     * @return
     */
    private Map<String, Map<String, String>> getDepartMangerMap(Map<String, Map<String, String>> managerMap, String departCode, Integer secLevel, String userId) {
        if (managerMap.containsKey(departCode)) {
            Map<String, String> departManagerMap = managerMap.get(departCode);
            if (secLevel == 30) {
                // 室总监
                String oldManagerUserId = departManagerMap.get("szj");
                String newManagerUserId = getFieldValue(oldManagerUserId, userId);
                departManagerMap.put("szj", newManagerUserId);
            }
            if (secLevel == 50) {
                // 部门副总经理
                String oldManagerUserId = departManagerMap.get("bnmfj");
                String newManagerUserId = getFieldValue(oldManagerUserId, userId);
                departManagerMap.put("bnmfj", newManagerUserId);
            }
            if (secLevel == 50 || secLevel == 60) {
                // 部门领导
                String oldManagerUserId = departManagerMap.get("bmld");
                String newManagerUserId = getFieldValue(oldManagerUserId, userId);
                departManagerMap.put("bmld", newManagerUserId);
            }
            if (secLevel == 100) {
                // 部门领导
                String oldManagerUserId = departManagerMap.get("bmzjl");
                String newManagerUserId = getFieldValue(oldManagerUserId, userId);
                departManagerMap.put("bmzjl", newManagerUserId);
            }

            managerMap.put(departCode, departManagerMap);
        }
        return managerMap;
    }


//    public static void main(String[] args) {
//        int a = 42+43+34+34+139+164+83+164+131+52+25+36+38+37+134+39+37;
//        int b = 96+44+50+36+131+80+33+98+48+66+60+39+23+43+41+43+37+40;
//        System.out.println(a);
//        System.out.println(b);
//        int c = 42+43+34+34+139+164+83+164+131+23+25+36+38+37+134+39+37;
//        int d = 96+44+50+36+131+80+33+98+48+66+60+39+52+43+41+43+37+40;
//        System.out.println(c);
//        System.out.println(d);
//    }


}
