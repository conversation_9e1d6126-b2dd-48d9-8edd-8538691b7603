package com.weaver.seconddev.zyhlw.service.impl;


import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.common.hrm.domain.organization.HrmOrgEmpCondition;
import com.weaver.common.hrm.dto.syncdata.HrmSyncDataConfig;
import com.weaver.ebuilder.common.util.JSONUtil;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.ebuilder.form.client.entity.obj.Obj;
import com.weaver.ebuilder.form.client.service.emobile.IEtFormDatasetService;
import com.weaver.ebuilder.teams.etform.base.query.Condition;
import com.weaver.ebuilder.teams.etform.base.query.ConditionTreeDto;
import com.weaver.ebuilder.teams.etform.base.query.Query;
import com.weaver.framework.rpc.annotation.RpcReference;
import com.weaver.seconddev.zyhlw.domain.response.todo.TodoRemindResponse;
import com.weaver.seconddev.zyhlw.service.*;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import com.weaver.seconddev.zyhlw.util.ebuilder.EBuilderUtil;
import com.weaver.teams.domain.user.SimpleEmployee;
import com.weaver.teams.security.context.UserContext;
import com.weaver.workflow.common.dto.list.listdata.RequestListResultDataDto;
import com.weaver.workflow.common.entity.list.api.RequestListConditionApiEntity;
import com.weaver.workflow.common.entity.list.api.publicapi.RequestListInfoPAEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Map;

import static com.weaver.ebuilder.form.client.entity.data.EBDataUpdateType.conditions;

/**
 * <AUTHOR> by herry on 2025-02-07.
 * Update date:
 * Time: 11:52
 * Project: ecology
 * Package: com.weaver.seconddev.zyhlw.service.impl
 * Command:
 * <p>
 * Status：Using online
 * <p>
 * Please note:
 * Must be checked once every time you submit a configuration file is correct!
 * Data is priceless! Accidentally deleted the consequences!
 */
@Service
@Slf4j
public class TodoRemindServiceImpl implements ITodoRemindService {

    private final String simpleName = TodoRemindServiceImpl.class.getSimpleName();

    @Resource
    CmicProperties cmicProperties;

    @Resource
    private IHrmPpcService hrmPpcService;

    @Resource
    private IWflPpcService wflPpcService;

    @Resource
    private IDataSqlService dataSqlService;

    @RpcReference(group = "ebuilderform")
    IEtFormDatasetService iEtFormDatasetService;
    @Resource
    IOpenPlatformService iOpenPlatformService;

    /**
     * queryTodo
     * <p>
     * 获取代办数据
     * 传递用户查当前用户，没有查全部用户
     * TODO：接口参数不足。不满足实际使用
     * <p>
     *
     * @return 所有待办数据
     * <AUTHOR>
     * @time 2025年02月07 09:58:00
     * @since 1.0
     */
    @Override
    public WeaResult<List<TodoRemindResponse>> queryTodoAll() {
        String method = String.format("调用%s.queryTodoAll-->", simpleName);
        log.info("{}开始执行", method);
        List<TodoRemindResponse> todoList = new ArrayList<>();
        // 获取全部用户
        HrmOrgEmpCondition condition = new HrmOrgEmpCondition();
        HrmSyncDataConfig dataConfig = new HrmSyncDataConfig();
        condition.setTenantKey(cmicProperties.getHostTenantKey());
        // 查询全部用户数据
        List<Map<String, Object>> employeeDataAllList = hrmPpcService.queryEmployeeDataAllList(condition, dataConfig);
        List<RequestListInfoPAEntity> requestListInfoPAEntities = new ArrayList<>();
        for (Map<String, Object> map : employeeDataAllList) {
            // 遍历用户查询待办数量
            SimpleEmployee employee = new SimpleEmployee();
            employee.setId((Long) map.get("id"));
            RequestListConditionApiEntity conditionEntity = new RequestListConditionApiEntity();
            RequestListResultDataDto resultDataDto = new RequestListResultDataDto();
            resultDataDto.setNeedCurrentNode(true);
            resultDataDto.setNeedWfNames(true);
            conditionEntity.setResultDataDto(resultDataDto);

            log.info("{}获取getToDoWorkflowRequestList rpc", method);
            List<RequestListInfoPAEntity> todoResult = wflPpcService.getToDoWorkflowRequestAllList(employee, conditionEntity);
            log.info("{}getToDoWorkflowRequestList响应成功，待办数量:{}", method, todoResult.size());
            requestListInfoPAEntities.addAll(todoResult);
        }
        // requestListInfoPAEntities数据转换为List<TodoRemindResponse>
        convertTodoData(todoList, requestListInfoPAEntities, employeeDataAllList);
        return WeaResult.success(todoList);
    }

    /**
     * queryTodoAllList
     * <p>
     * 获取代办数据
     * sql的形式
     * <p>
     *
     * @return 所有待办数据
     * <AUTHOR>
     * @time 2025年02月07 09:58:00
     * @since 1.0
     */
    @Override
    public WeaResult<List<TodoRemindResponse>> queryTodoAllList() {
        String method = String.format("调用%s.queryTodoAllList-->", simpleName);
        log.info("{}开始执行", method);
        List<Map<String, Object>> result = new ArrayList<>();
        int pageSize = 1000;
        int pageNumber = 1;
        String sql = "SELECT ( chr ( 10 ) || '标题：' || T.REQUESTNAME || chr ( 10 ) || '发送人：' || H3.username ) TITLE_NAME,'https://cmic.hfx.net/spa/OpenWorkflowGateway.jsp?requestid=' || T.REQUESTID SEND_URL," +
                "'0' SEND_GATEWAY_SHORT_URL,H3.MOBILE SEND_TO_USER_MOBILE,H3.username SEND_TO_USER_NAME,'CMIC_M_TX' SEND_TEMP_TYPE,C.NODEID SEND_TO_NODE_ID,C.ID CUR_NUMBER_ID,T.REQUESTID SEND_BUS_ID," +
                "T.REQUESTNAME SEND_BUS_NAME,T.WORKFLOWID SEND_BUS_FLOW_ID,T.REQUESTLEVEL SEND_BUS_LEVEL,T.LASTOPERATOR SEND_BUS_LAST_USER_ID,H.username SEND_BUS_LAST_USER_NAME,H.MOBILE SEND_BUS_LAST_USER_MOBILE," +
                "T.CREATER SEND_BUS_CREATE_USER_ID,H2.username SEND_BUS_CREATE_USER_NAME,H2.MOBILE SEND_BUS_CREATE_USER_MOBILE,C.USERID SEND_USER_ID" +
                " FROM wfc_requestbase T LEFT JOIN wfc_operate_todo C ON T.requestid = C.requestid LEFT JOIN eteams.employee H ON H.ID = T.LASTOPERATOR" +
                " LEFT JOIN eteams.employee H2 ON H2.ID = T.CREATER LEFT JOIN eteams.employee H3 ON H3.ID = C.USERID WHERE T.tenant_key = '" + cmicProperties.getHostTenantKey() + "' AND C.tenant_key = '" + cmicProperties.getHostTenantKey() + "' AND H.tenant_key = '" + cmicProperties.getHostTenantKey() + "'" +
                " AND H2.tenant_key = '" + cmicProperties.getHostTenantKey() + "' AND H3.tenant_key = '" + cmicProperties.getHostTenantKey() + "' AND TO_CHAR ( c.RECEIVEDATETIME, 'YYYY-MM-DD' ) >= TO_CHAR ( SYSDATE - 1, 'YYYY-MM-DD' ) AND NVL ( C.nodetype, '0' ) != '3'";
        while (true) {
            try {
                List<Map<String, Object>> resultVo = dataSqlService.workflowFromSql(sql, pageNumber, pageSize, SourceType.LOGIC, new ArrayList<>());
                if (resultVo == null || resultVo.isEmpty()) {
                    log.info("{}查询，当前页：{}，返回结果为空", method, pageNumber);
                    break;
                }
                result.addAll(resultVo);
                pageNumber++;
            } catch (Exception e) {
                log.error("{}查询，当前页：{}，发生异常", method, pageNumber, e);
                throw new RuntimeException(e);
            }
        }

        log.info("{}获取待发送的建模数据", method);
        SimpleEmployee currentUser = UserContext.getCurrentUser();
        String uid = StringUtils.null2String(currentUser.getUid());
        log.info("{}当前用户：{}", method, uid);
        String objId = EBuilderUtil.getFormObjId(iEtFormDatasetService, cmicProperties.getOpenPlatformUrl(), uid, iOpenPlatformService.getAccessToken(), "uf_dbsjtxjlb", "objId");
        // 查询所有表单数据
        ConditionTreeDto conditionTreeDto = new ConditionTreeDto();
        conditionTreeDto.setConditionId("sffs");
        conditionTreeDto.setCompareType("eq");
        conditionTreeDto.setConditionValue("0");
        Query query = new Query();
        query.setConditionTrees(conditionTreeDto);
        query.setPageNo(1);
        query.setPageSize(1000);
        String queryStr = Base64.getEncoder().encodeToString(JSONUtil.stringify(query).getBytes());
        Map<String, Object> datas = iEtFormDatasetService.getDatas(Long.valueOf(objId), "", queryStr, null);
        log.info("{}查询到数据：{}",method,JSONUtil.stringify(datas));
        return null;
    }

    private void convertTodoData(List<TodoRemindResponse> todoList, List<RequestListInfoPAEntity> requestListInfoPAEntities, List<Map<String, Object>> employeeDataAllList) {

        for (RequestListInfoPAEntity request : requestListInfoPAEntities) {
            // 获取最后操作人信息
            Map<String, Object> lastUserOperator = (Map<String, Object>) employeeDataAllList.stream().filter(map -> map.get("id").equals(request.getLastOperatorId()));
            Map<String, Object> creatUserOperator = (Map<String, Object>) employeeDataAllList.stream().filter(map -> map.get("id").equals(request.getCreatorId()));
            TodoRemindResponse todoRemindResponse = TodoRemindResponse.builder()
//                    .sendToNodeId()
                    .sendTempType("CMIC_M_TX")
                    .sendBusLastUserMobile(lastUserOperator.get("employee.mobile").toString())
//                    .sendUserId()
                    .sendBusName(request.getRequestname())
                    .sendUrl("https://cmic.hfx.net/spa/OpenWorkflowGateway.jsp?requestid=" + request.getRequestid())
//                    .sendIsChatbotSend()
//                    .sendBusLevel()
                    .sendBusLastUserName(request.getLastOperatorName())
                    .sendBusCreateUserName(request.getCreatorName())
                    .sendBusFlowId(String.valueOf(request.getWorkflowid()))
                    .sendBusCreateUserId(String.valueOf(request.getCreatorId()))
                    .sendBusLastUserId(String.valueOf(request.getLastOperatorId()))
                    .sendBusId(String.valueOf(request.getRequestid()))
//                    .sendToUserMobile()
//                    .sendBusFlowNode()
//                    .sendToUserName()
//                    .sendIsSend()
                    // 发送人或创建人无法获取？
                    .titleName("\n标题：" + request.getRequestname() + "\n发送人：" )
//                    .sendErrorCt()
                    .sendBusCreateUserMobile(creatUserOperator.get("employee.mobile").toString())
//                    .curNumberId()
                    .sendGatewayShortUrl(String.valueOf(0))
                    .build();

            todoList.add(todoRemindResponse);
        }
    }
}
