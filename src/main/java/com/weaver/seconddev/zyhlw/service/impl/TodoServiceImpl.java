package com.weaver.seconddev.zyhlw.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.weaver.common.cache.base.BaseCache;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.openapi.pojo.basicserver.res.vo.AccountVo;
import com.weaver.openapi.pojo.flow.res.FlowListResultVo;
import com.weaver.openapi.pojo.flow.res.vo.*;
import com.weaver.seconddev.zyhlw.domain.request.todo.TodoGrayRequest;
import com.weaver.seconddev.zyhlw.domain.request.todo.TodoRequest;
import com.weaver.seconddev.zyhlw.domain.response.todo.*;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.service.ITodoService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.DateUtils;
import com.weaver.seconddev.zyhlw.util.TodoUtil;
import com.weaver.seconddev.zyhlw.util.cache.CacheKey;
import com.weaver.seconddev.zyhlw.util.cache.CacheModuleKey;
import com.weaver.seconddev.zyhlw.util.page.PageInfo;
import com.weaver.seconddev.zyhlw.util.page.PaginationUtil;
import com.weaver.workflow.common.entity.core.flow.RequestBaseInfoEntity;
import com.weaver.workflow.common.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class TodoServiceImpl implements ITodoService {

    @Resource
    IDataSqlService dataSqlService;
    @Resource
    CmicProperties cmicProperties;
    @Resource
    IOpenPlatformService iOpenPlatformService;
    @Resource
    BaseCache baseCache;
    private static final String TOTALKEY = "total";
    private static final String ALLKEY = "all";
    private static final String JTKEY = "jt";
    private static final String DYKEY = "dy";

    private Integer cacheTime = 3600 * 2;
    private String guanllcKey = "guan_llc";
    /**
     * 查询流程配置初始化sql
     */
    private String workflowConfigSql = "select * from uf_liu_cpz where is_delete=0";
    private final String simpleName = TodoServiceImpl.class.getSimpleName();
    // 待办的流程流转的状态 1 审批中 6 待提交 7 暂停 8 撤销
    private List<Integer> todoFlowStatusList = new ArrayList<>(Arrays.asList());

    DateTimeFormatter yyyyMMddHHmmss = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    @Override
    public TodoTabResponse getTab(TodoRequest todoRequest) {
        String methodName = simpleName + ".getTab()";
        TodoTabResponse todoTabResponse = new TodoTabResponse();
        log.info("调用{} 入参为：{}", methodName, JSON.toJSONString(todoRequest));
        RequestBaseInfoEntity requestBaseInfoEntity = new RequestBaseInfoEntity();
        requestBaseInfoEntity.setUserId(todoRequest.getUserId());
        String jobNum = getJobNum(todoRequest.getUserId());
        String email = "";

        if ("".equals(jobNum)) {
            log.error("调用{} 获取当前用户账号为空", methodName);
            todoTabResponse.setCount(0);
            return todoTabResponse;
        }

        String fenLpzSql = "select * from uf_fen_lpz where is_delete=0 and fen_lbm='" + todoRequest.getCode() + "'";
        // 分类名称
        String fenLmc = "";
        // 分类编码
        String fenLbm = "";
        // 分类ID
        String fenId = "";
        try {

            List<Map<String, Object>> fenLpzRecords = dataSqlService.ebuilderFromSql(fenLpzSql, 1, 100, SourceType.LOGIC);
            log.info("调用{} 查询分类配置数据：{}", methodName, JSON.toJSONString(fenLpzRecords));
            for (Map<String, Object> fenLpzRecord : fenLpzRecords) {
                fenLmc = fenLpzRecord.get("fen_lmc").toString();
                fenLbm = fenLpzRecord.get("fen_lbm").toString();
                fenId = fenLpzRecord.get("id").toString();
            }
        } catch (Exception e) {
            log.error("调用{} 查询分类配置表异常，sql语句：{}", methodName, fenLpzSql, e);
            todoTabResponse.setCount(0);
            return todoTabResponse;
        }
        todoTabResponse.setTabName(fenLmc);
        todoTabResponse.setTabCode(fenLbm);
        Integer count = getCount(fenId, fenLbm, jobNum, todoRequest.getTitle(), email, todoRequest.getUserId());
        todoTabResponse.setCount(count);
        return todoTabResponse;
    }

    @Override
    public TodoResponse getData(TodoRequest todoRequest) {
        String methodName = String.format("%s.getData(%s)", simpleName, JSON.toJSONString(todoRequest));
        RequestBaseInfoEntity requestBaseInfoEntity = new RequestBaseInfoEntity();
        requestBaseInfoEntity.setUserId(todoRequest.getUserId());
        String jobNum = getJobNum(todoRequest.getUserId());
        String email = "";
        TodoResponse todoResponse = new TodoResponse();
        List<TodoDataResponse> dataList = new ArrayList<>();
        todoResponse.setTotal(0);
        todoResponse.setData(dataList);
        if ("".equals(jobNum)) {
            log.error("调用{} 获取当前用户账号为空", methodName);
            return todoResponse;
        }
        int total = 0;

        if (!JTKEY.equals(todoRequest.getCode()) && !DYKEY.equals(todoRequest.getCode())) {
            if (ALLKEY.equals(todoRequest.getCode())) {
                // 集团数据
                TodoResponse jtData = getJtTodoData(jobNum, email, todoRequest.getTitle());
                dataList.addAll(jtData.getData());
                total += jtData.getTotal();
            }
            // 互联网系统数据
            TodoResponse cmicTodoData = getCmicTodoData(todoRequest.getCode(), todoRequest.getTitle(), jobNum);
            total += cmicTodoData.getTotal();
            dataList.addAll(cmicTodoData.getData());
            // 系统待办
            TodoResponse systemTodoData = getSystemTodoData(todoRequest.getCode(), todoRequest.getUserId(), todoRequest.getTitle());
            total += systemTodoData.getTotal();
            dataList.addAll(systemTodoData.getData());

        } else if (JTKEY.equals(todoRequest.getCode())) {
            // 集团待办
            TodoResponse jtData = getJtTodoData(jobNum, email, todoRequest.getTitle());
            dataList.addAll(jtData.getData());
            total = jtData.getTotal();
        } else if (DYKEY.equals(todoRequest.getCode())) {
            // 待阅
        }
        sort(dataList);
        PageInfo<TodoDataResponse> paginate = PaginationUtil.paginate(dataList, Integer.parseInt(todoRequest.getPageNo()), Integer.parseInt(todoRequest.getPageSize()));
        log.info("{}  分页工具返回的数据：CurrentPage：{}，TotalPages：{}，PageSize：{}",methodName,paginate.getCurrentPage(),paginate.getTotalPages(),paginate.getPageSize());
        todoResponse.setData(paginate.getData());
        todoResponse.setTotal(total);
        return todoResponse;
    }

    /**
     * 获取互联网系统待办
     *
     * @param fenlCode     分类编号
     * @param title        标题
     * @param portalUserId 用户名
     * @return TodoResponse
     */
    private TodoResponse getCmicTodoData(String fenlCode, String title, String portalUserId) {
        String methodName = "调用" + simpleName + ".getCmicTodoData()";
        TodoResponse todoResponse = new TodoResponse();
        String fenSql = "SELECT b.fen_lmc,b.fen_ljc,b.fen_lbm,a.xi_tmc,a.xi_tjc,a.xi_tbm,a.dai_bslbs,a.dai_yslbs,a.guan_lfl,a.fu_wbb,a.ren_zlx,a.liu_lqlx,a.piao_jcsm FROM uf_todo_xtpz a INNER JOIN uf_fen_lpz b ON a.guan_lfl=b.id where a.is_delete=0 and b.is_delete=0";
        if (!ALLKEY.equals(fenlCode) && !JTKEY.equals(fenlCode)) {
            fenSql += fenSql + "  b.fen_lbm='" + fenlCode + "'";
        }
        List<Map<String, Object>> ebuilderFenLData = null;
        try {
            ebuilderFenLData = dataSqlService.ebuilderFromSql(fenSql, 1, 1000, SourceType.LOGIC);
        } catch (Exception e) {
            log.error("{} 查询分类对应待办系统数据异常", methodName, e);
        }
        List<TodoDataResponse> data = new ArrayList<>();
        Integer total = 0;
        for (Map<String, Object> fenLDatum : ebuilderFenLData) {
            String fuWbb = fenLDatum.get("fu_wbb").toString();
            String xiTbm = fenLDatum.get("xi_tbm").toString();
            String renZlx = fenLDatum.get("ren_zlx").toString();
            String xiTjc = fenLDatum.get("xi_tjc").toString();
            String liuLqlx = fenLDatum.get("liu_lqlx").toString();
            String piaoJcsm = fenLDatum.get("piao_jcsm").toString();
            if ("0".equals(fuWbb)) {

                // 待办服务V1
                log.info("{} 待办服务V1：{}", methodName, xiTbm);
                List<Map<String, Object>> todoDataV1 = TodoUtil.getTodoDataV1(cmicProperties.getWaitItem1QueryListUrl(), portalUserId, null, null, 0, 0, null, "1", xiTbm, 1, 10000);
                log.info("{}查询到的待办数据：{}",methodName,JSON.toJSONString(todoDataV1));
                total += todoDataV1.size();
                for (Map<String, Object> todo : todoDataV1) {
//                    log.info("{} 待办数据：{}", methodName, JSON.toJSONString(todo));
                    String id = todo.get("id").toString();
                    JSONObject recordTime = JSON.parseObject(todo.get("recordTime").toString());
                    Long time = recordTime.getLong("time");
                    LocalDateTime localDateTime = DateTimeUtil.parse(time);
                    String lastUpdateTime = DateTimeUtil.format(localDateTime);
                    TodoDataResponse todoDataResponse = new TodoDataResponse();
                    todoDataResponse.setId(id);
                    todoDataResponse.setTitle(todo.get("subTitle").toString());
                    todoDataResponse.setUniqueId(id);
                    todoDataResponse.setLastUpdateTime(lastUpdateTime);
                    todoDataResponse.setCreateTime(lastUpdateTime);
                    todoDataResponse.setSystemName(todo.get("sysSourceName").toString());
                    todoDataResponse.setSystemCode(todo.get("sysSource").toString());
                    todoDataResponse.setIsGray(todo.get("isGray").toString());
                    todoDataResponse.setLastUser(todo.get("preNodeUser").toString());
                    todoDataResponse.setPcUrl(todo.get("detailUrl").toString());
                    todoDataResponse.setAuthType(renZlx);
                    todoDataResponse.setItemAppId(null);
                    todoDataResponse.setSystemAbbreviation(xiTjc);
                    todoDataResponse.setBrowserType(liuLqlx);
                    todoDataResponse.setServiceVersion(fuWbb);
                    todoDataResponse.setBillParamName(piaoJcsm);
                    getTodoDataResponse(todoDataResponse);
//                    log.info("{} 封装后的数据：{}", methodName, JSON.toJSONString(todoDataResponse));
                    data.add(todoDataResponse);
                }
            } else if ("1".equals(fuWbb)) {
                // 待办服务V2
                log.info("{} 待办服务V2：{}", methodName, xiTbm);
                List<Map<String, Object>> todoDataV2 = TodoUtil.getTodoDataV2(portalUserId, 1, xiTbm, "item", title, cmicProperties.getWaitItem2QueryListUrl());
                log.info("{} 查询到的数据：{}", methodName, todoDataV2);
                todoDataV2.forEach(item -> {
                    String id = item.get("originalId").toString();
                    TodoDataResponse todoDataResponse = new TodoDataResponse();
                    todoDataResponse.setId(id);
                    todoDataResponse.setTitle(item.get("itemTitle").toString());
                    todoDataResponse.setUniqueId(id);
                    todoDataResponse.setLastUpdateTime(item.get("receiverTime").toString());
                    todoDataResponse.setCreateTime(item.get("createTime").toString());
                    todoDataResponse.setSystemName(item.get("systemName").toString());
                    todoDataResponse.setSystemCode(item.get("systemCode").toString());
                    todoDataResponse.setIsGray(item.get("hasGray").toString());
                    todoDataResponse.setLastUser(item.get("prevCnName").toString());
                    todoDataResponse.setPcUrl(item.get("webUrl").toString());
                    todoDataResponse.setAuthType(renZlx);
                    todoDataResponse.setItemAppId(null);
                    todoDataResponse.setSystemAbbreviation(xiTjc);
                    todoDataResponse.setBrowserType(liuLqlx);
                    todoDataResponse.setServiceVersion(fuWbb);
                    todoDataResponse.setBillParamName(piaoJcsm);
                    getTodoDataResponse(todoDataResponse);
                    data.add(todoDataResponse);
                });
            }

        }
        todoResponse.setTotal(total);
        todoResponse.setData(data);
        return todoResponse;
    }

    /**
     * 请求集团待办
     *
     * @param portalUserId 登录名
     * @param email        邮箱
     * @param title        标题
     * @return TodoResponse
     */
    private TodoResponse getJtTodoData(String portalUserId, String email, String title) {
        TodoResponse todoResponse = new TodoResponse();

        List<TodoDataResponse> dataResponses = new ArrayList<>();

        String pageSize = "";
        String methodName = "调用" + simpleName + "getJtData()";
        // 先取一次，用于确定待办数量
        Integer jtCount = getJtCount(portalUserId, email, title);
        todoResponse.setTotal(jtCount);
        pageSize = jtCount.toString();

        // 集团待办
        Map<String, Object> tdUnifyList = new HashMap<>(16);
        try {
            tdUnifyList = TodoUtil.getTdUnifyList(portalUserId, "111", "0", email, "1", pageSize, cmicProperties.getWaitTdunifyGetTdUnifyList(), title);
        } catch (Exception e) {
            log.error("{} 调用待办服务--第二次集团待办接口异常", methodName, e);
        }
        // 查询集团待办配置
        List<Map<String, Object>> xtpzRecords = null;
        try {
            xtpzRecords = dataSqlService.ebuilderFromSql("select * from uf_ji_txtpz where is_delete=0", 1, 500, SourceType.LOGIC);
        } catch (Exception e) {
            log.error("{} 查询集团配置表数据异常", methodName, e);
        }
        if (tdUnifyList.containsKey(TOTALKEY)) {
            Integer total = (Integer) tdUnifyList.get(TOTALKEY);
            todoResponse.setTotal(total);
            List<Map<String, Object>> tdData = (List<Map<String, Object>>) tdUnifyList.get("result");
            for (Map<String, Object> tdDatum : tdData) {
                String itemUrl = tdDatum.get("uItemUrl").toString();
                String uItemAppId = tdDatum.get("uItemAppId").toString();
                Map<String, String> jitxtpzInfo = getJitxtpzInfo(xtpzRecords, uItemAppId);
                TodoDataResponse todoDataResponse = new TodoDataResponse();
                todoDataResponse.setId(tdDatum.get("uItemId").toString());
                todoDataResponse.setTitle(tdDatum.get("uItemTitle").toString());
                todoDataResponse.setLastUpdateTime(tdDatum.containsKey("uLastUpdateTime") ? tdDatum.get("uLastUpdateTime").toString() : "");
                todoDataResponse.setUniqueId(tdDatum.get("uAppItemId").toString());
                todoDataResponse.setCreateTime(tdDatum.containsKey("uCreateTime") ? tdDatum.get("uCreateTime").toString() : "");
                todoDataResponse.setSystemName(tdDatum.get("uName").toString());
                todoDataResponse.setSystemCode("JTDB");
                todoDataResponse.setIsGray(tdDatum.get("isGray").toString());
                todoDataResponse.setLastUser(tdDatum.get("uLastUid").toString());
                todoDataResponse.setPcUrl(itemUrl);
                todoDataResponse.setAuthType(jitxtpzInfo.get("authType"));
                todoDataResponse.setItemAppId(uItemAppId);
                todoDataResponse.setSystemAbbreviation(jitxtpzInfo.get("systemAbbreviation"));
                todoDataResponse.setBrowserType(jitxtpzInfo.get("liuLqlx"));
                todoDataResponse.setServiceVersion("0");
                todoDataResponse.setBillParamName(jitxtpzInfo.get("renZcsm"));
                getTodoDataResponse(todoDataResponse);
                dataResponses.add(todoDataResponse);
            }

        }

        todoResponse.setData(dataResponses);
        return todoResponse;
    }

    /**
     * 获取系统待办
     *
     * @param fenlCode
     * @param userId
     * @param title
     * @return
     */
    private TodoResponse getSystemTodoData(String fenlCode, long userId, String title) {
        String methodName = String.format("调用%s.getSystemTodoData(%s,%s,%s)", simpleName, fenlCode, userId, title);
        log.info("{}", methodName);
        TodoResponse todoRespon = new TodoResponse();
        List<TodoDataResponse> dataResponseList = new ArrayList<>();

        String workflowSql = "";
        if (!fenlCode.equals(ALLKEY) && !fenlCode.equals(DYKEY)) {
            workflowSql = workflowConfigSql;
            workflowSql += String.format(" and fen_code='%s'", fenlCode);
        }
        log.info("{} 查询流程配置sql：{}", methodName, workflowSql);
        List<Map<String, Object>> workflowList = new ArrayList<>();
        if (!workflowSql.equals("")) {
            try {
                workflowList = dataSqlService.ebuilderFromSql(workflowSql, 1, 1000, SourceType.LOGIC);
            } catch (Exception e) {
                log.error("{} 获取流程配置异常：sql=={}", methodName, workflowSql, e);
                todoRespon.setTotal(0);
                todoRespon.setData(new ArrayList<>());
            }
        }
        List<String> workflowIdList = getWorkflowIds(workflowList);
        log.info("{} 需要获取待办的流程ID：{}", methodName, JSON.toJSONString(workflowIdList));
        List<FlowListResultResponse> todoContainSignList = iOpenPlatformService.getTodoContainLastOperatorList(userId, title, String.join(",", workflowIdList), todoFlowStatusList);
        log.info("{} 查询到的待办数量：{}",methodName,todoContainSignList.size());
        todoRespon.setTotal(todoContainSignList.size());
        for (FlowListResultResponse resultResponse : todoContainSignList) {
            TodoDataResponse todoDataResponse = getSystemTodoDataResponse(resultResponse,workflowList);
            dataResponseList.add(todoDataResponse);
        }
        todoRespon.setData(dataResponseList);
        return todoRespon;
    }

    /**
     * 获取系统待办最后处理信息，如果没有就用创建信息
     *
     * @param userId           用户ID
     * @param requestid        流程请求ID
     * @param todoDataResponse 待办数据
     */
    private void getSystemsLastInfo(Long userId, String requestid, TodoDataResponse todoDataResponse) {
        String methodName = "调用" + simpleName + ".getSystemsLastInfo()";
        List<FlowSign> requestLog = iOpenPlatformService.getRequestLog(userId, requestid);
        log.info("{} 查询到的审批数据：{}", methodName, requestLog.size());
        if (!requestLog.isEmpty()) {
            FlowSign flowSign = requestLog.get(0);
            todoDataResponse.setLastUser(flowSign.getCommentor().getUsername());
            Instant instant = Instant.ofEpochMilli(Long.parseLong(flowSign.getAddTime()));
            LocalDateTime dateTime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime();

            todoDataResponse.setLastUpdateTime(DateUtils.formatTime(dateTime, "yyyy-MM-dd HH:mm:ss"));
        } else {
            todoDataResponse.setLastUpdateTime(todoDataResponse.getCreateTime());
            todoDataResponse.setLastUser(todoDataResponse.getCreateName());
        }
    }

    /**
     * E10待办获取流程对应的系统信息
     *
     * @param flowList
     * @param workflowList
     * @param todoDataResponse
     */
    private void getSystemInfo(FlowList flowList, List<Map<String, Object>> workflowList, TodoDataResponse todoDataResponse) {
        for (Map<String, Object> map : workflowList) {
            String workflowId = "," + flowList.getWorkflowid() + ",";
            String guanLlcId = "," + map.get(guanllcKey).toString() + ",";
            if (guanLlcId.indexOf(workflowId) > -1) {
                todoDataResponse.setSystemName(map.get("system_full_name").toString());
                todoDataResponse.setSystemAbbreviation(map.get("system_abbreviation").toString());
                break;
            }
        }
    }


    /**
     * 获取对应集团系统的配置
     *
     * @param xtpzRecords 配置数据
     * @param uItemAppId  appid
     * @return
     */
    private Map<String, String> getJitxtpzInfo(List<Map<String, Object>> xtpzRecords, String uItemAppId) {
        // 认证类型 默认 0
        String authType = "0";
        // 认证参数
        String renZcsm = "ticket";
        // 系统简称
        String systemAbbreviation = "";
        // 浏览器类型
        String liuLqlx = "0";
        for (Map<String, Object> xtpzRecord : xtpzRecords) {
            if (xtpzRecord.containsKey("clientid") && xtpzRecord.get("clientid").toString().equals(uItemAppId)) {
                renZcsm = xtpzRecord.get("ren_zcsm").toString();
                authType = xtpzRecord.get("rzfs").toString();
                systemAbbreviation = xtpzRecord.get("xtjc").toString();
                liuLqlx = xtpzRecord.get("liu_lqlx").toString();
                break;
            }
        }
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("renZcsm", renZcsm);
        resultMap.put("authType", authType);
        resultMap.put("systemAbbreviation", systemAbbreviation);
        resultMap.put("liuLqlx", liuLqlx);
        return resultMap;
    }

    /**
     * 构造TodoDataResponse 转换数据
     *
     * @param todoDataResponse
     * @return TodoDataResponse
     */
    private static TodoDataResponse getTodoDataResponse(TodoDataResponse todoDataResponse) {
        // 标题转换成带标签
        todoDataResponse.setTitleHtml(TodoUtil.getHtmlStrByGray(todoDataResponse.getIsGray(), todoDataResponse.getTitle()));
        // 接收人转换成带标签
        todoDataResponse.setLastuserHtml(TodoUtil.getHtmlStrByGray(todoDataResponse.getIsGray(), todoDataResponse.getLastUser()));
        // 接收时间
        int subLength = 10;
        if ((!"".equals(todoDataResponse.getLastUpdateTime())) && (todoDataResponse.getLastUpdateTime().length() > subLength)) {
            todoDataResponse.setFormatDate(todoDataResponse.getLastUpdateTime().substring(0, subLength));
        }
        if (todoDataResponse.getFormatDate() == null || todoDataResponse.getFormatDate().isEmpty()) {
            if (!"".equals(todoDataResponse.getCreateTime())) {
                todoDataResponse.setFormatDate(todoDataResponse.getCreateTime().substring(0, subLength));
            } else {
                todoDataResponse.setFormatDate("1970-01-01");
            }
        }
        // 接收时间转换成带标签
        todoDataResponse.setFormatDataHtml(TodoUtil.getHtmlStrByGray(todoDataResponse.getIsGray(), todoDataResponse.getFormatDate()));
        // 系统名称转换成带标签
        todoDataResponse.setSystemNameHtml(TodoUtil.getHtmlStrByGray(todoDataResponse.getIsGray(), todoDataResponse.getSystemName()));
        // 系统简称转换成带标签
        todoDataResponse.setSystemAbbreviationHtml(TodoUtil.getHtmlStrByGray(todoDataResponse.getIsGray(), todoDataResponse.getSystemAbbreviation()));

        return todoDataResponse;
    }


    /**
     * 查询每个分类的数量
     *
     * @param fenlId       分类ID
     * @param fenLbm       分类编码
     * @param portalUserId 登录名
     * @param title        待办标题
     * @param email        用户邮箱
     * @param userId       用户ID
     * @return 结果
     */
    private Integer getCount(String fenlId, String fenLbm, String portalUserId, String title, String email, Long userId) {
        String methodName = "调用" + simpleName + ".getCount()";
        try {
            TodoUtil.startGetSysData(cmicProperties.getStartGetSysDataUrl(), portalUserId);
        } catch (Exception e) {
            log.error("{}更新待办缓存数据接口异常", methodName,e);
        }
        // 待办数量
        Integer systemNum = 0;
        //互联网系统待办数量 v1 接口
        Integer sysNum = 0;
        // 集团待办数量
        Integer jtNum = 0;
        Map<String, Object> todoTotalV1 = new HashMap<>(16);
        try {
            todoTotalV1 = TodoUtil.getTodoTotalV1(portalUserId, cmicProperties.getGetBenchDaiCountUrl());
            log.info("{} 查询待办数量V1接口返回数据：{}", methodName, JSON.toJSONString(todoTotalV1));
        } catch (Exception e) {
            log.error("{} 调用待办服务-待办数量V1接口异常", methodName, e);
        }
        Map<String, Object> todoTotalV2 = new HashMap<>(16);
        try {
            todoTotalV2 = TodoUtil.getTodoTotalV2(portalUserId, 1, null, title, cmicProperties.getWaitItem2QueryAlltotal());
            log.info("{} 查询待办数量V2接口返回数据：{}", methodName, JSON.toJSONString(todoTotalV2));
        } catch (Exception e) {
            log.error("{} 调用待办服务-待办数量V2接口异常", methodName, e);
        }
        // 查询互联网系统 开始
        String sql = "select * from uf_todo_xtpz where is_delete=0";

        if (!ALLKEY.equals(fenLbm) && !DYKEY.equals(fenLbm)) {
            sql += " and guan_lfl=" + fenlId;

        }
        List<Map<String, Object>> xtpzRecords = null;
        try {
            xtpzRecords = dataSqlService.ebuilderFromSql(sql, 1, 100, SourceType.LOGIC);
        } catch (Exception e) {
            log.error("{} 获取待办系统配置表数据异常", methodName, e);
        }

        log.info("{} 查询系统配置数据：{}", methodName, JSON.toJSONString(xtpzRecords));
        for (Map<String, Object> xtpzRecord : xtpzRecords) {
            sysNum += getCmicSystemCount(xtpzRecord, fenLbm, todoTotalV1, todoTotalV2);
        }
        // 获取系统待办数量 开始 问题1：全部分类不需要查流程配置 问题2：查了流程配置，但是没有数据，导致查询所有待办
        List<Map<String, Object>> workflowDataList = new ArrayList<>();
            Boolean isTodo = false;
        if (!ALLKEY.equals(fenLbm) && !DYKEY.equals(fenLbm)) {
            String workflowSql = workflowConfigSql + " and fen_code='%s'";;
            try {
                workflowDataList = dataSqlService.ebuilderFromSql(String.format(workflowSql, fenLbm), 1, 1000, SourceType.LOGIC);
                if (workflowDataList.size() > 0) {
                    isTodo = true;
                }
            } catch (Exception e) {
                log.error("{} 查询流程配置异常：sql==={}", methodName, workflowSql, e);
            }

        }
        try {
            if (ALLKEY.equals(fenLbm) || isTodo) {
                List<String> workflowIds = getWorkflowIds(workflowDataList);
                log.info("{} 对应流程id：{}", methodName, workflowIds);
                systemNum = iOpenPlatformService.getToDoWorkflowRequestCount(userId, 1, 1, title, String.join(",", workflowIds), todoFlowStatusList);
                log.info("{} 查询系统待办数量：{}", methodName, systemNum);
            }
        }catch (Exception e){
            log.error("{} 查询系统待办数量异常",methodName,e);
        }
        // 获取系统待办数量 结束
        boolean existed = (ALLKEY.equals(fenLbm) || JTKEY.equals(fenLbm));
        if (existed) {
            jtNum += getJtCount(portalUserId, email, title);
        }
        log.info("{} 分类对应数量，分类编号：{}，系统待办数量：{}，集团待办数量：{}，互联网系统接口待办数量：{}", methodName, fenLbm, systemNum, jtNum, sysNum);
        return systemNum + jtNum + sysNum;
    }

    @NotNull
    private List<String> getWorkflowIds(List<Map<String, Object>> workflowDataList) {
        List<String> workflowIds = new ArrayList<>();
        try {
            for (Map<String, Object> item : workflowDataList) {
                workflowIds.add(item.get(guanllcKey).toString());
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return workflowIds;
    }

    /**
     * @param xtpzRecord
     * @param fenLbm
     * @param todoTotalV1
     * @param todoTotalV2
     * @return
     */
    private Integer getCmicSystemCount(Map<String, Object> xtpzRecord, String fenLbm, Map<String, Object> todoTotalV1, Map<String, Object> todoTotalV2) {
        String methodName = "调用"+simpleName+".getCmicSystemCount()";
        Integer count = 0;
        String key = "";
        String fuWbbVal = xtpzRecord.get("fu_wbb").toString();
        if (DYKEY.equals(fenLbm)) {
            if (xtpzRecord.containsKey("dai_yslbs")) {
                key = xtpzRecord.get("dai_yslbs").toString();
            }
        } else {
            key = xtpzRecord.get("dai_bslbs").toString();
        }
        if ("0".equals(fuWbbVal) && todoTotalV1.containsKey(key)) {
            count += (Integer) todoTotalV1.get(key);
        } else if ("1".equals(fuWbbVal) && todoTotalV2.containsKey(key)) {
            log.info("{} v2接口待办数量：{}",methodName,key);
            count += (Integer) todoTotalV2.get(key);
        }
        return count;
    }

    /**
     * 集团待办数量
     *
     * @param portalUserId 登录名
     * @param email        邮箱
     * @param title        标题
     * @return 数量
     */
    private Integer getJtCount(String portalUserId, String email, String title) {
        Integer count = 0;
        String methodName = "调用" + simpleName + ".getJtCount()";
        try {
            Map<String, Object> tdUnifyList = TodoUtil.getTdUnifyList(portalUserId, "111", "0", email, "1", "1", cmicProperties.getWaitTdunifyGetTdUnifyList(), title);
            log.info("{} 查询集团待办数量接口返回数据：{}", methodName, JSON.toJSONString(tdUnifyList));
            count = (Integer) tdUnifyList.get(TOTALKEY);
        } catch (Exception e) {
            log.error("{} 调用待办服务--集团待办接口异常", methodName, e);
        }
        return count;
    }

    /**
     * 新待办服务置灰接口
     *
     * @param todoGrayRequest 置灰参数
     * @return 结果
     */
    @Override
    public Boolean updateGray(TodoGrayRequest todoGrayRequest) {
        String methodName = "调用" + simpleName + ".updateGray()";
        if (todoGrayRequest.getEmployType() == 0) {
            // 集团待办 todoType 需要传 JTDB
            try {
                Map<String, Object> grayData = TodoUtil.setGrayV1(todoGrayRequest.getLoginId(), todoGrayRequest.getUniqueId(), todoGrayRequest.getTodoType(), cmicProperties.getWaitTodoSetGray());
                log.info("{} 请求更新置灰状态接口结果：{}", methodName, grayData);
                return true;
            } catch (Exception e) {
                log.error("{} 调用旧待办服务异常", methodName, e);
                return false;
            }
        } else if (todoGrayRequest.getEmployType() == 1) {
            try {
                Map<String, Object> grayData = TodoUtil.setGrayV2(todoGrayRequest.getLoginId(), todoGrayRequest.getUniqueId(), "1", cmicProperties.getWaitItem2ChangeSetGray());
                String successCode = "0";
                String codeKey = "code";
                return successCode.equals(grayData.get(codeKey).toString());
            } catch (Exception e) {
                log.error("{} 调用新待办服务异常", methodName, e);
                return false;
            }
        } else {
            log.error("{}  employType 传参错误，参数值：{}", methodName, todoGrayRequest.getEmployType());
            return false;
        }
    }

    /**
     * 异步
     *
     * @param userid
     * @return
     */
    @Override
    @Async
    public Boolean cacheTodo(Long userid) {
        String methodName = String.format("调用%s.cacheTodo(%s)", simpleName, userid);
//        FlowListResultVo requestList = iOpenPlatformService.getToDoWorkflowRequestList(userid, null, null, todoFlowStatusList);
//        List<TodoDataResponse> dataResponseList = new ArrayList<>();
//        if (requestList.getMessage() != null) {
//            if (requestList.getMessage().getErrcode() == 0) {
//                String workflowSql = workflowConfigSql;
//                log.info("{} 查询流程配置sql：{}", methodName, workflowSql);
//                List<Map<String, Object>> workflowList = new ArrayList<>();
//                try {
//                    workflowList = dataSqlService.ebuilderFromSql(workflowSql, 1, 1000, SourceType.LOGIC);
//                } catch (Exception e) {
//                    log.error("{} 获取流程配置异常：sql=={}", methodName, workflowSql, e);
//
//                }
//                for (FlowList flowList : requestList.getData()) {
//                    log.info("{} 开放平台获取待办原始数据：{}", methodName, JSON.toJSONString(flowList));
//                    try {
////                        TodoDataResponse todoDataResponse = getSystemTodoDataResponse( flowList, workflowList);
////                        log.info("{} 获取到系统待办数据：{}", methodName, JSON.toJSONString(todoDataResponse));
////                        dataResponseList.add(todoDataResponse);
//                    } catch (Exception e) {
//                        log.error("{} 转换数据异常", methodName, e);
//                    }
//
//                }
//            }
//        }
//        if (!dataResponseList.isEmpty()) {
//            return baseCache.set(CacheModuleKey.TODO_SERVICE_MODULE, CacheKey.TODO_SERVICE_KEY, "todo-" + userid, dataResponseList, cacheTime);
//        } else {
//            return false;
//        }
        return true;
    }

    /**
     * 封装系统待办数据
     * @param resultResponse
     * @param workflowList
     * @return
     */
    @NotNull
    private TodoDataResponse getSystemTodoDataResponse(FlowListResultResponse resultResponse, List<Map<String, Object>> workflowList) {
        TodoDataResponse todoDataResponse = new TodoDataResponse();
        String methodName = "调用"+simpleName+".getSystemTodoDataResponse()";
        try {
            todoDataResponse.setId(resultResponse.getFlow().getRequestid());
            todoDataResponse.setTitle(resultResponse.getFlow().getRequestname());
            todoDataResponse.setUniqueId(resultResponse.getFlow().getRequestid());
            todoDataResponse.setCreateTime(resultResponse.getFlow().getCreateTime());
            todoDataResponse.setCreateName(resultResponse.getFlow().getCreatorName());
            todoDataResponse.setSystemCode("ECOLOGY10");
            //  flowList.getReadMark()   0 已查询  1 未读 3 待处理
            todoDataResponse.setIsGray(resultResponse.getFlow().getReadMark() == 1 ? "N" : "Y");
            todoDataResponse.setPcUrl(resultResponse.getFlow().getPcUrl());
            todoDataResponse.setAuthType("-1");
            todoDataResponse.setBrowserType("0");
            todoDataResponse.setServiceVersion("1");
            // 获取对应系统信息
            getSystemInfo(resultResponse.getFlow(), workflowList, todoDataResponse);
            // 获取最后处理信息
//            if (resultResponse.getSignList().isEmpty()) {
//                todoDataResponse.setLastUpdateTime(resultResponse.getFlow().getCreateTime());
//                todoDataResponse.setLastUser(resultResponse.getFlow().getCreatorName());
//            }else {
                FlowLastOperatorResponse lastOperator = resultResponse.getLastOperator();
                todoDataResponse.setLastUser(lastOperator.getLastOperatorName());
                todoDataResponse.setLastUpdateTime(lastOperator.getLastOperateDateTime());
//            }
            // 转换带HTML标签的数据
            getTodoDataResponse(todoDataResponse);
        }catch (Exception e){
            log.error("{} 封装系统待办数据异常",methodName,e);
        }

        return todoDataResponse;
    }

    /**
     * 获得账户
     * @param userId
     * @return
     */
    private String getJobNum(Long userId){
        String jobNum = "";
        String methodName = String.format("调用%s.getJobNum(%s)",simpleName,userId);
        try {
            AccountVo account = iOpenPlatformService.findAccount(userId);
            log.info("调用{} 查询当前用户信息：{}", methodName, JSON.toJSONString(account));
            if (account == null) {
                log.error("调用{} 获取当前用户账号信息为空",methodName);
            }
            if (account.getLoginName() != null) {
                jobNum = account.getLoginName();
            } else {
                jobNum = account.getJobNum();
            }

        } catch (Exception e) {
            log.error("调用{} 获取当前用户信息异常！", methodName, e);

        }
        return  jobNum;
    }

    private List<TodoDataResponse> sort(List<TodoDataResponse> data){
        String methodName = "调用"+simpleName+".sort()";
        try {
            Collections.sort(data, new Comparator<TodoDataResponse>() {
                @Override
                public int compare(TodoDataResponse o1, TodoDataResponse o2) {
                    if (o1.getLastUpdateTime() == null && o2.getLastUpdateTime() != null ) {
                        return  1;
                    }
                    if (o1.getLastUpdateTime() != null && o2.getLastUpdateTime() == null) {
                        return -1;
                    }
                    if (o1.getLastUpdateTime().equals("") && !o2.getLastUpdateTime().equals("")) {
                        return  1;
                    }
                    if (!o1.getLastUpdateTime().equals("") && o2.getLastUpdateTime().equals("")) {
                        return  -1;
                    }
                    if (o1.getLastUpdateTime().length() <= 10) {
                        o1.setLastUpdateTime(o1.getLastUpdateTime() + " 00:00:00");
                    }
                    if (o2.getLastUpdateTime().length() <= 10) {
                        o2.setLastUpdateTime(o2.getLastUpdateTime() + " 00:00:00");
                    }
                    LocalDate date1 = LocalDate.parse(o1.getLastUpdateTime(),yyyyMMddHHmmss);
                    LocalDate date2 =  LocalDate.parse(o2.getLastUpdateTime(),yyyyMMddHHmmss);
                    return date2.compareTo(date1);
                }
            });
        }catch (Exception e){
            log.error("{} 数据进行排序异常",methodName,e);
        }
        return data;
    }

}
