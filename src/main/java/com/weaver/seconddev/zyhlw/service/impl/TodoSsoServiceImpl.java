package com.weaver.seconddev.zyhlw.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.weaver.seconddev.zyhlw.domain.request.todo.TodoSsoRequest;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.service.ITodoSsoService;
import com.weaver.seconddev.zyhlw.service.ITokenService;
import com.weaver.seconddev.zyhlw.util.http.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
@Service
@Slf4j
public class TodoSsoServiceImpl implements ITodoSsoService {
    String simpleName = TodoSsoServiceImpl.class.getSimpleName();

    @Resource
    ITokenService iTokenService;
    @Resource
    IDataBaseService iDataBaseService;


    @Override
    public Map<String, Object> sso(TodoSsoRequest param) {
        String method = "调用"+simpleName+".sso()-->";
        log.info("{}入参：{}",method, JSON.toJSONString(param));
        Config config = new Config(param.getSysCode());
        log.info("{}获取配置信息：{}",method,JSON.toJSONString(config));
        Map<String,Object> resultMap = new HashMap<>();
        String portalUserId = "";
        if ("token".equals(param.getAuthType())) {
            // 验证token
            if (param.getToken() == null) {
                resultMap.put("code",201);
                resultMap.put("msg","票据参数token为空");
            }else{

            }
        } else if ("portalToken".equals(param.getAuthType())) {
            // 验证portalToken
            if (param.getPortalToken() == null) {
                resultMap.put("code",201);
                resultMap.put("msg","票据参数portalToken为空");
            }else{
                String responseStr = iTokenService.validatePortalToken(param.getPortalToken(), "1");
                if ("".equals(responseStr)) {
                    resultMap.put("code",201);
                    resultMap.put("msg","解析票据异常");
                }else {
                    JSONObject responseJson = JSON.parseObject(responseStr);
                    if (responseJson.containsKey("portalUserId")) {
                        portalUserId = responseJson.getString("portalUserId");
                    }
                }

            }

        }else if("ticket".equals(param.getAuthType())){
            // 验证ticket
            String token = "";
            if (param.getTicket() == null) {
                token = param.getToken();
            }else{
                token = param.getTicket();
            }
            if ("".equals(token)) {
                resultMap.put("code",201);
                resultMap.put("msg","票据参数token和ticket都为空");
            }else{
                String responseStr = iTokenService.validateTicket(token);
                if ("".equals(responseStr)) {
                    resultMap.put("code",202);
                    resultMap.put("msg","解析票据异常");
                }else{
                    JSONObject responseJson = JSON.parseObject(responseStr);
                    if (responseJson.containsKey("portalUserId")) {
                        portalUserId = responseJson.getString("portalUserId");
                    }
                }
            }
        }else{
            resultMap.put("code",400);
            resultMap.put("msg","认证类型为接入，请联系管理员！！！");
        }
        if (!"".equals(portalUserId)) {
            log.info("{}获取到的配置2：{}",method,JSON.toJSONString(config));
            String loginTokenStr = restfilGetLoginToken(config.getHost(), config.getAppKey(), config.getAppSecurity(), portalUserId, "loginID");
            log.info("{} 请求获取本系统令牌接口返回：{}", method, loginTokenStr);
            JSONObject loginTokenJson = JSON.parseObject(loginTokenStr);
            String errcodeKey = "errcode";
            String successCode = "0";
            if (successCode.equals(loginTokenJson.getString(errcodeKey))) {
                String etLoginToken = loginTokenJson.getString("etLoginToken");
                String redirectUri =  param.getTourl();
                if (param.getFlushPortalUrl() != null) {
                    String flushPortalUrl = URLDecoder.decode(param.getFlushPortalUrl());
                    String decode = URLDecoder.decode(param.getTourl());
                    log.info("{}重定向初始地址解码：{}",method,decode);
                    String a = "?uniqueId="+param.getUniqueId()+"&itemId="+param.getItemId()+"&appId=uni_110_cmic_oa";
                    log.info("{}回刷地址的参数：{}",method,a);
                    String encode = URLEncoder.encode(flushPortalUrl + a);
                    log.info("{}编码后的回刷地址：{}",method,encode);
                    redirectUri  = URLEncoder.encode(decode + "?flushPortalUrl=" + encode);
                    log.info("{}最终的重定向地址{}",method,redirectUri);

                }
                String url = config.getHost()+"/papi/open/singleSignon?singleToken=" + etLoginToken + "&oauthType=singlesign&redirect_uri="+redirectUri;
                resultMap.put("code", 0);
                resultMap.put("msg", "成功");
                resultMap.put("url", url);
            } else {
                resultMap.put("code", 204);
                resultMap.put("msg","处理跳转异常");
                return resultMap;
            }
        }else{
            resultMap.put("code","205");
            resultMap.put("msg","票据解析失败导致portalUserId为空");
        }


        return resultMap;
    }
    /**
     * 获取免登票据
     *
     * @param appKey      应用key
     * @param appSecurity 应用密钥
     * @param account     认证数据
     * @param authType    认证类型
     * @return 结果
     */
    public String restfilGetLoginToken(String host,String appKey, String appSecurity, String account, String authType) {
        String url = host+"/papi/openapi/oauth2/get_logintoken";
        url += "?app_key=" + appKey + "&app_security=" + appSecurity + "&account=" + account + "&authType=" + authType;
        Map<String, Object> header = new HashMap<>(16);
        return HttpUtils.sendPost(url, null, header);
    }

    public class Config{
        private String host;

        private String appKey;

        private String appSecurity;

        public Config(){
            List<String> typeNames = new ArrayList<>();
            typeNames.add("环境基础配置");
            List<Map<String, Object>> baseDataByTypeNameList = iDataBaseService.getBaseDataByTypeName(typeNames);
            log.info("查询的到配置信息：{}",JSON.toJSONString(baseDataByTypeNameList));
            baseDataByTypeNameList.forEach(z ->{
                if ("环境地址".equals(z.get("xsmc".toUpperCase()).toString())) {
                    this.host = z.get("xsz".toUpperCase()).toString();
                }

            });
        }


        /**
         * 获取每个租户免登应用配置
         * @param app CMIC_GYL
         */
        public Config(String app){
            List<String> typeNames = new ArrayList<>();
            typeNames.add("环境基础配置");
            typeNames.add(app+"应用配置");
            List<Map<String, Object>> baseDataByTypeNameList = iDataBaseService.getBaseDataByTypeName(typeNames);
            baseDataByTypeNameList.forEach(z ->{
                log.info("单条数据：{}",JSON.toJSONString(z));
                if ("appKey".equals(z.get("xsmc".toLowerCase()).toString())) {
                    this.setAppKey(z.get("xsz".toLowerCase()).toString());
                } else if ("appSecurity".equals(z.get("xsmc".toLowerCase()).toString())) {
                    this.setAppSecurity(z.get("xsz".toLowerCase()).toString());
                }else  if ("环境地址".equals(z.get("xsmc".toLowerCase()).toString())) {
                    this.setHost(z.get("xsz".toLowerCase()).toString());
                }

            });
            log.info("配置信息{}，{}，{}",this.getAppKey(),this.getAppSecurity(),this.getHost());
        }
        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public String getAppKey() {
            return appKey;
        }

        public void setAppKey(String appKey) {
            this.appKey = appKey;
        }

        public String getAppSecurity() {
            return appSecurity;
        }

        public void setAppSecurity(String appSecurity) {
            this.appSecurity = appSecurity;
        }
    }
}
