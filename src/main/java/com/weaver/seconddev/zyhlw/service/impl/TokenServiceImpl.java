package com.weaver.seconddev.zyhlw.service.impl;

import com.alibaba.fastjson.JSON;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.service.ITokenService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.DateUtils;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import com.weaver.seconddev.zyhlw.util.http.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
@Service
@Slf4j
public class TokenServiceImpl implements ITokenService {

    private String simpleName = ITokenService.class.getSimpleName();
    @Resource
    CmicProperties cmicProperties;
    Config config= null;
    @Resource
    IDataBaseService iDataBaseService;
    @Override
    public void init() {
        config = new Config();
    }

    @Override
    public String generateTicket(String portalUserId) {
        if (config == null) {
            config = new Config();
        }
        String resultStr = "";
        String method =String.format("调用%s.generateTicket(%s)",simpleName,portalUserId);
//        String url = config.getAuthHost()+"/cmic-portal-auth/hr/getTicket";
//        String url = "http://*************/cmic-portal-auth/hr/getTicket";
        JSONObject paramJson = new JSONObject();
        paramJson.put("moa","MOA");
        paramJson.put("clientType","MOA");
        paramJson.put("account",portalUserId+"@cmic.cmcc");
        Map<String,Object> header = new HashMap<>();
        header.put("appId", cmicProperties.getAppid());
        header.put("appSecret",cmicProperties.getAppSecret());
        header.put("source","1");
        header.put("functionId","getHRTicket");
        header.put("Content-Type","application/json");
        String responseStr = HttpUtils.sendPost(config.generateTicketUrl, paramJson.toString(), header);
        log.info("{}请求返回：{}",method,responseStr);
        if (responseStr != null && !"".equals(responseStr)) {
            if (StringUtils.isValidJson(responseStr)) {
                JSONObject responseJson = JSONObject.fromObject(responseStr);
                if (responseJson.containsKey("code")) {
                    if (responseJson.getInt("code") == 0) {
                        return responseJson.getString("ticket");
                    }else{
                        log.warn("{}请求统一用户ticket接口返回code不等于0",method);
                    }
                }else{
                    log.warn("{}请求统一用户ticket接口返回不存在code",method);
                }

            }else{
                log.warn("{}请求统一用户ticket接口返回非JSON数据，数据：{}",method,responseStr);
            }
        }else{
            log.warn("{}请求统一用户ticket接口异常",method);
        }
        return resultStr;
    }

    @Override
    public String generatePortalToken(String portalUserId) {
        if (config == null) {
            config = new Config();
        }
        String resultStr = "";
        String method = String.format("调用%s.generatePortalToken(%s)",simpleName,portalUserId);
        String url =config.getGeneratePortalTokenUrl();
//        String url = "http://*************/cmic-portal-auth/token/createToken";
        url = url+ "?portalUserId="+portalUserId;
        Map<String,Object> header = new HashMap<>();
        header.put("appId", cmicProperties.getAppid());
        header.put("appSecret",cmicProperties.getAppSecret());
        header.put("source","1");
        header.put("functionId","sso_create_token");
        String responseStr = HttpUtils.sendPost(url, null, header);
        if (responseStr != null && !"".equals(responseStr)) {
            if (StringUtils.isValidJson(responseStr)) {
                JSONObject responseJson = JSONObject.fromObject(responseStr);
                if (responseJson.containsKey("code")) {
                    if (responseJson.getInt("code") == 0) {
                        resultStr  =  responseJson.getString("accessToken");
                    }else{
                        log.warn("{}请求portalToken接口返回code不等于0",method);
                    }
                }else{
                    log.warn("{}请求portalToken接口返回不存在code",method);
                }
            }else{
                log.warn("{}请求portalToken接口返回非JSON数据，数据：{}",method,responseStr);
            }
        }else{
            log.warn("{}请求portalToken接口异常",method);
        }
        return resultStr;
    }

    @Override
    public String validatePortalToken(String token,String source) {
        if (config == null) {
            config = new Config();
        }
        String resultStr = "";
        String method = String.format("调用%s.validatePortalToken(%s,%s)-->",simpleName,token,source);
//        String url = config.getAuthHost()+"/cmic-portal-auth/token/validateToken";
        Map<String,Object> header = new HashMap<>();
        header.put("appId", cmicProperties.getAppid());
        header.put("appSecret",cmicProperties.getAppSecret());
        header.put("source",source);
        header.put("functionId","sso_validate_token");
        header.put("Content-Type","application/json");
        JSONObject paramJson = new JSONObject();
        paramJson.put("accessToken",token);
        String responseStr = HttpUtils.sendPost(config.getValidatePortalTokenUrl(), paramJson.toString(), header);
        if (responseStr != null && !"".equals(responseStr)) {
            if (StringUtils.isValidJson(responseStr)) {
                JSONObject responseJson = JSONObject.fromObject(responseStr);
                if (responseJson.containsKey("code")) {
                    if (responseJson.getInt("code") == 0) {
                       resultStr = responseStr;
                    }else{
                        log.warn("{}请求portalToken校验接口返回code不等于0",method);
                    }
                }else{
                    log.warn("{}请求portalToken校验接口返回不存在code",method);
                }
            }else {
                log.warn("{}请求portalToken校验接口返回非JSON数据，数据：{}",method,responseStr);
            }
        }else{
            log.warn("{}请求portalToken校验接口异常",method);
        }

        return resultStr;
    }
    // todo 请求存在问题
    @Override
    public String generateToken(String portalUserId) {
//        String method = String.format("调用%s.generateToken(%s)-->",simpleName,portalUserId);
////        Map<String,Object> header = new HashMap<>();
//
////        header.put("appId", cmicProperties.getAppid());
////        header.put("appSecret",cmicProperties.getAppSecret());
////        header.put("source","1");
////        header.put("functionId","sso_create_token");
//        String reqTimestamp = DateUtils.formatTime(DateUtils.getCurrentTime(), "yyyyMMddHHmmssS");
//        String url = config.getServiceHost()+"/CMIC_SERVICE/service/cmicTokenSeravice?wsdl";
//        String head = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:ser=\"http://service.cmic.richinfo.com/\">";
//        head += "<soapenv:Header><msghead>";
//        head += "<appId>" + cmicProperties.getAppid() + "</appId>";
//        head += "<appSecret>" + cmicProperties.getAppSecret() + "</appSecret>";
//        head += "<reqTimestamp>" + reqTimestamp + "</reqTimestamp>";
//        head += "<functionId>sso_create_token</functionId>";
//        head += "</msghead></soapenv:Header>";
//        head += "<soapenv:Body>";
//        head += "<ser:createToken>";
//        head += "<userId>" + portalUserId + "</userId>";
//        head += "</ser:createToken>";
//        head += "</soapenv:Body>";
//        head += "</soapenv:Envelope>";
//
//        Map<String,Object> header = new HashMap<>();
//        header.put("Content-Type","application/soap+xml; charset=utf-8");
//        String s = HttpUtils.sendPost(url, head, header);
//        log.info("{}请求返回：{}",method,s);
        return null;
    }

    @Override
    public String validateTicket(String ticket) {
        if (config == null) {
            config = new Config();
        }
        String resultStr = "";
        String method = String.format("调用%s.validateTicket(%s)-->",simpleName,ticket);
//        String url = config.getAuthHost()+"/cmic-portal-auth/hr/validateTicket";
        Map<String,Object> header = new HashMap<>();
        header.put("appId", cmicProperties.getAppid());
        header.put("appSecret",cmicProperties.getAppSecret());
        header.put("functionId","validateHRTicket");
        header.put("Content-Type","application/json");
        JSONObject paramJson = new JSONObject();
        paramJson.put("ticket",ticket);
        String responseStr = HttpUtils.sendPost(config.validateTicketUrl, paramJson.toString(), header);
        log.info("{}验证统一用户ticket返回：{}",method,responseStr);
        if (responseStr != null && !"".equals(responseStr)) {
            if (StringUtils.isValidJson(responseStr)) {
                JSONObject responseJson = JSONObject.fromObject(responseStr);
                if (responseJson.containsKey("code")) {
                    if (responseJson.getInt("code") == 0) {
                        resultStr = responseStr;
                    }else{
                        log.warn("{}请求ticket校验接口返回code不等于0",method);
                    }
                }else{
                    log.warn("{}请求ticket校验接口返回不存在code",method);
                }
            }else {
                log.warn("{}请求ticket校验接口返回非JSON数据，数据：{}",method,responseStr);
            }
        }else{
            log.warn("{}请求ticket校验接口异常",method);
        }
        return resultStr;
    }

    //    public static void main(String[] args) {
//        String reqTimestamp = DateUtils.formatTime(DateUtils.getCurrentTime(), "yyyyMMddHHmmssS");
//        String url1 = "http://service.portal.cmic:30080/CMIC_SERVICE/service/cmicTokenSeravice";
//        String head = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:ser=\"http://service.cmic.richinfo.com/\">";
//        head += "<soapenv:Header><msghead>";
//        head += "<appId>140</appId>";
//        head += "<appSecret>x1HZisCbln38ftog1RgdjkriLSzCOgdX6Dlvr1Ww</appSecret>";
//        head += "<reqTimestamp>" + reqTimestamp + "</reqTimestamp>";
//        head += "<functionId>sso_create_token</functionId>";
//        head += "</msghead></soapenv:Header>";
//
//        head += "<soapenv:Body>";
//        head += "<ser:createToken>";
//        head += "<userId>dwzhengqihui</userId>";
//        head += "</ser:createToken>";
//
//
//        head += "</soapenv:Body>";
//        head += "</soapenv:Envelope>";
//
//        Map<String,Object> header = new HashMap<>();
//        header.put("Content-Type","application/xml; charset=utf-8");
//        header.put("Accept", "application/xml");
//        try {
//            // 目标 URL
//            URL url = new URL(url1);
//
//            // 打开连接
//            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
//
//            // 设置请求方法
//            connection.setRequestMethod("POST");
//
//            // 设置请求头
//            connection.setRequestProperty("Content-Type", "application/xml; utf-8");
//            connection.setRequestProperty("Accept", "application/xml");
//            connection.setDoOutput(true);
//
//            // XML 数据
////            String xmlData = "<request><name>John</name><age>30</age></request>";
//
//            // 发送 XML 数据
//            try (OutputStream os = connection.getOutputStream()) {
//                byte[] input = head.getBytes("utf-8");
//                os.write(input, 0, input.length);
//            }
//
//            // 获取响应状态码
//            int responseCode = connection.getResponseCode();
//            System.out.println("Response Code: " + responseCode);
//
//            // 读取响应数据
//            if (responseCode == HttpURLConnection.HTTP_OK) {
//                try (java.io.InputStream is = connection.getInputStream()) {
//                    byte[] response = new byte[is.available()];
//                    is.read(response);
//                    System.out.println("Response: " + new String(response));
//                }
//            } else {
//                System.out.println("Request failed with response code: " + responseCode);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
    public  class Config {
        /**
         * 验证ticket地址
         */
        private String validateTicketUrl;
        /**
         * 验证PortalToken
         */
        private String validatePortalTokenUrl;
        /**
         * 生成PortalToken地址
         */
        private String  generatePortalTokenUrl;
        /**
         * 生成ticket地址
         */
        private String generateTicketUrl;
       public Config(){
           List<String> types = new ArrayList<>();
           types.add("cmic-portal-auth服务配置");
           List<Map<String, Object>> baseDataByTypeNameList = iDataBaseService.getBaseDataByTypeName(types);
           log.info("查询到配置信息：{}", JSON.toJSONString(baseDataByTypeNameList));
           baseDataByTypeNameList.forEach(z ->{
               if ("校验ticket地址".equals(z.get("xsmc".toLowerCase()).toString())) {
                   this.validateTicketUrl = z.get("xsz".toLowerCase()).toString();
               }else if ("校验PortalToken地址".equals(z.get("xsmc".toLowerCase()).toString())){
                   this.validatePortalTokenUrl = z.get("xsz".toLowerCase()).toString();
               }else if ("生成PortalToken地址".equals(z.get("xsmc".toLowerCase()).toString())){
                   this.generatePortalTokenUrl = z.get("xsz".toLowerCase()).toString();
               }else if("生产ticket地址".equals(z.get("xsmc".toLowerCase()).toString())){
                   this.generateTicketUrl = z.get("xsz".toLowerCase()).toString();
               }

           });

//           log.info("测试获取到配置信息：{}",baseDataValue);
//           if ("test21".equals(environment)) {
//               this.authHost = "http://auth.portal.cmic:30080";
//               this.serviceHost = "http://service.portal.cmic:30080";
//           } else if ("test86".equals(environment)) {
//               this.authHost = "http://*************";
//               this.serviceHost = "http://*************";
//           } else if ("pro".equals(environment)) {
//               this.authHost = "http://**************";
//               this.serviceHost = "http://**************";
//           }
       }

        public String getValidateTicketUrl() {
            return validateTicketUrl;
        }

        public void setValidateTicketUrl(String validateTicketUrl) {
            this.validateTicketUrl = validateTicketUrl;
        }

        public String getValidatePortalTokenUrl() {
            return validatePortalTokenUrl;
        }

        public void setValidatePortalTokenUrl(String validatePortalTokenUrl) {
            this.validatePortalTokenUrl = validatePortalTokenUrl;
        }

        public String getGeneratePortalTokenUrl() {
            return generatePortalTokenUrl;
        }

        public void setGeneratePortalTokenUrl(String generatePortalTokenUrl) {
            this.generatePortalTokenUrl = generatePortalTokenUrl;
        }

        public String getGenerateTicketUrl() {
            return generateTicketUrl;
        }

        public void setGenerateTicketUrl(String generateTicketUrl) {
            this.generateTicketUrl = generateTicketUrl;
        }
    }
}
