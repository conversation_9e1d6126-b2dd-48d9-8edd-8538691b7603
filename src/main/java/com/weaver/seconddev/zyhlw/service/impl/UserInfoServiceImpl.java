package com.weaver.seconddev.zyhlw.service.impl;

import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.seconddev.zyhlw.service.IUserInfoService;
import com.weaver.teams.domain.user.SimpleEmployee;
import com.weaver.teams.security.context.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserInfoServiceImpl implements IUserInfoService {

    private final String simpleName= UserInfoServiceImpl.class.getSimpleName();


    @Override
    public WeaResult<SimpleEmployee> getUserInfo() {
        return UserContext.getCurrentUser();
    }
}
