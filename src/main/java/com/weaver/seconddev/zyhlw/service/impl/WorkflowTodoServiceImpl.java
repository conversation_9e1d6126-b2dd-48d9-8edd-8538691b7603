package com.weaver.seconddev.zyhlw.service.impl;

import com.alibaba.fastjson.JSON;
import com.weaver.common.hrm.remote.HrmRemotingService;
import com.weaver.framework.rpc.annotation.RpcReference;
import com.weaver.openapi.pojo.flow.res.FlowListResultVo;
import com.weaver.openapi.pojo.flow.res.vo.FlowList;
import com.weaver.openapi.pojo.user.res.vo.UserInfoResult;
import com.weaver.seconddev.zyhlw.domain.request.todo.TodoItemRequest;
import com.weaver.seconddev.zyhlw.domain.response.todo.TodoAuditResponse;
import com.weaver.seconddev.zyhlw.service.IPortalWaitService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.service.IWorkflowTodoService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.DateUtils;
import com.weaver.seconddev.zyhlw.util.ListUtils;
import com.weaver.seconddev.zyhlw.util.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 流程待办实现
 *
 * <AUTHOR>
 * @Date 2024-07-04 10:06
 */
@Slf4j
@Service
public class WorkflowTodoServiceImpl implements IWorkflowTodoService {
    @Resource
    private IOpenPlatformService openPlatformService;
    @Resource
    private CmicProperties cmicProperties;
    @Resource
    private IPortalWaitService iPortalWaitService;
    @RpcReference
    HrmRemotingService hrRemote;

    /**
     * 同步流程->待办数据到集团统一待办
     *
     * @return void
     * <AUTHOR>
     * @Date 10:09 2024/7/4
     * @Param []
     **/
    @Override
    public void syncWorkflowTodoJt() {
        // 获取所有用户
        List<UserInfoResult> allUsers = openPlatformService.findAllUsersV3();
        log.info("批量推送待办数据到统一待办服务, 查询到的用户数量: {}", allUsers.size());
        allUsers.forEach(e -> {
            try {
                // ,1-审批中,2-已退回,3-正常归档
                FlowListResultVo flowListResultVo = openPlatformService.getAllToDoWorkflowRequestList(e.getUserid(), null, null, null);
                if (!flowListResultVo.getData().isEmpty()) {
                    log.info("批量推送待办数据到统一待办服务, 待办数量: {}", flowListResultVo.getData().size());
                    List<TodoItemRequest> todoItemRequestList = new ArrayList<>();
                    // 组装批量流程
                    log.info("批量推送待办数据到统一待办服务, 开始组装，用户: {}", e.getUserid());
                    for (FlowList flowList : flowListResultVo.getData()) {
                        todoItemRequestList.addAll(assembleTodoData(flowList, e.getUserid()));

                    }
                    log.info("批量推送待办数据到统一待办服务, 组装结束待办数量: {}", todoItemRequestList.size());
//                    iPortalWaitService.pushTodoDataToJtBatch(todoItemRequestList, "同步流程");
                }
            } catch (Exception ex) {
                log.info("批量推送待办数据到统一待办服务异常，异常用户：{}", e.getUserid());
            }
        });
    }

    /**
     * 稽核待办数据接口
     * 1、查询所有用户
     * 2、遍历用户查询集团待办数据、查询ec用户待办数据
     * 3、判断判断数据是否存在
     * 3.1 集团存在，ec不存在，集团删除
     * 3.2 集团不存在，ec存在，集团新增
     * 3.3 集团存在，ec存在，不处理
     * 4、推送稽核后的数据
     *
     * @return void
     * <AUTHOR>
     * @Date 09:25 2024/7/8
     * @Param []
     **/
    @Override
    public void checkWorkflowTodoData() {
        log.info("稽核待办数据接口，开始");
        List<UserInfoResult> allUsers = openPlatformService.findAllUsersV3();
        List<TodoItemRequest> todoItemRequestList = new ArrayList<>();
        allUsers.forEach(e -> {
            // ec待办数据
            FlowListResultVo flowListResultVo = openPlatformService.getToDoWorkflowRequestList(e.getUserid(), null, null, null);
            List<String> ecIds = flowListResultVo.getData().isEmpty() ? new ArrayList<>() : flowListResultVo.getData().stream().map(flowList -> flowList.getRequestid() + "-" + e.getUserid()).collect(Collectors.toList());
            log.info("稽核待办数据接口，ec数据，{}用户待办数据条数:{}", e.getUserid(), ecIds.size());
            log.info("稽核待办数据接口，ec数据，：{}", JSON.toJSONString(ecIds));
            // 集团待办数据
            List<TodoAuditResponse.Item> auditTodoData = iPortalWaitService.auditUserTodoData(e.getUserid());
            List<String> jtIds = auditTodoData.isEmpty() ? new ArrayList<>() : auditTodoData.stream().map(TodoAuditResponse.Item::getOriginalId).collect(Collectors.toList());
            log.info("稽核待办数据接口，统一待办服务数据，{}用户待办数据条数:{}", e.getUserid(), jtIds.size());
            log.info("稽核待办数据接口，统一待办服务数据：{}", JSON.toJSONString(jtIds));
            // ec - 集团 的差集 (新增)
            List<String> ecDifference = ListUtils.getDifference(ecIds, jtIds);
            log.info("稽核待办数据接口，ec->统一待办服务的差集: {}", JSON.toJSONString(ecDifference));
            if (!ecDifference.isEmpty()) {
                List<FlowList> flowLists = flowListResultVo.getData().stream().filter(flowList -> ecDifference.contains(flowList.getRequestid() + "-" + e.getUserid())).collect(Collectors.toList());
                for (FlowList flowList : flowLists) {
                    todoItemRequestList.addAll(assembleTodoData(flowList, e.getUserid()));
                }
            }

            // 集团 - ec 的差集（删除）
            List<String> jtDifference = ListUtils.getDifference(jtIds, ecIds);
            log.info("稽核待办数据接口，统一待办服务->ec的差集: {}", JSON.toJSONString(jtDifference));
            if (!jtDifference.isEmpty()) {
                List<TodoAuditResponse.Item> todoList = auditTodoData.stream().filter(item -> jtDifference.contains(item.getOriginalId())).collect(Collectors.toList());
                for (TodoAuditResponse.Item item : todoList) {
                    todoItemRequestList.addAll(assembleTodoDataDel(item, e.getUserid()));
                }
            }
        });
        log.info("稽核待办数据接口，稽核数据总数:{}", todoItemRequestList.size());
        if (!todoItemRequestList.isEmpty()) {
//            iPortalWaitService.pushTodoDataToJtBatch(todoItemRequestList, "稽核待办数据接口");
        }

    }

    /**
     * 组装推送数据
     *
     * @param operatorId 操作人
     * <AUTHOR>
     * @Date 14:54 2024/7/4
     **/
    @Override
    public List<TodoItemRequest> assembleTodoData(FlowList flowList, Long operatorId) {
        List<TodoItemRequest> todoItemRequestList = new ArrayList<>();
        try {
            Map<?, ?> empInfo = hrRemote.getEmployee(operatorId);
            log.info("当前用户信息，empInfo: {}", JSON.toJSONString(empInfo));
            log.info("当前流程信息，flowList: {}", JSON.toJSONString(flowList));
            // 调用推送接口
            TodoItemRequest todoItemRequest = TodoItemRequest.builder()
                    .itemTitle(flowList.getRequestname())
                    .itemStatus("1")
                    .originalId(flowList.getRequestid() + "-" + operatorId)
                    .systemCode("ECOLOGY10")
                    .authType("1")
                    .createTime(DateUtils.formatTime(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"))
                    .creatorUserId(flowList.getCreatorId())
                    .creatorUserIdType(1)
                    .receiverUserId(String.valueOf(operatorId))
                    .receiverCnName(!empInfo.isEmpty() ? empInfo.get("username").toString() : "")
                    .receiverUserIdType("1")
                    .webUrl(cmicProperties.getEcUrl() + Constants.PC_WORKFLOW_URL + flowList.getRequestid())
                    .mobileUrl(cmicProperties.getEcUrl() + Constants.APP_WORKFLOW_URL + flowList.getRequestid())
                    .itemType(1)
                    .instId(flowList.getRequestid() + "-" + operatorId)
                    .build();
            todoItemRequestList.add(todoItemRequest);
        } catch (Exception ex) {
            log.info("组装推送数据报错，e: {}", ex.getMessage());
        }
        return todoItemRequestList;
    }

    /**
     * 组装删除推送数据
     *
     * @param operatorId 操作人
     * <AUTHOR>
     * @Date 14:54 2024/7/4
     **/
    @Override
    public List<TodoItemRequest> assembleTodoDataDel(TodoAuditResponse.Item item, Long operatorId) {
        List<TodoItemRequest> todoItemRequestList = new ArrayList<>();
        try {
            Map<?, ?> empInfo = hrRemote.getEmployee(operatorId);
            if (!empInfo.isEmpty()) {
                // 调用推送接口
                TodoItemRequest todoItemRequest = TodoItemRequest.builder()
                        .itemStatus("0")
                        .itemTitle(item.getItemTitle())
                        .originalId(item.getOriginalId())
                        .systemCode("ECOLOGY10")
                        .authType("1")
                        .createTime(DateUtils.formatTime(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"))
                        .creatorUserId(item.getCreatorUserId())
                        .creatorUserIdType(1)
                        .receiverUserId(String.valueOf(operatorId))
                        .receiverCnName(!empInfo.isEmpty() ? empInfo.get("username").toString() : "")
                        .receiverUserIdType("1")
                        .webUrl(item.getWebUrl())
                        .mobileUrl(item.getMobileUrl())
                        .itemType(1)
                        .instId(item.getOriginalId())
                        .build();
                todoItemRequestList.add(todoItemRequest);
            }

        } catch (Exception ex) {
            log.info("组装删除推送数据报错，e: {}", ex.getMessage());
        }
        return todoItemRequestList;
    }

    /**
     * 稽核待办数据(单个)
     *
     * @param userIds    要稽核的用户id集合
     * @param requestId  待办数据id
     * @param workflowId 工作流id
     * @return void
     * <AUTHOR>
     * @Date 15:39 2024/7/17
     **/
    @Override
    public void auditWorkflowData(List<Long> userIds, String requestId, String workflowId, String requestName) {
        List<TodoItemRequest> todoItemRequestList = new ArrayList<>();
        userIds.forEach(e -> {
            // ec待办数据
            FlowListResultVo flowListResultVo = openPlatformService.getToDoWorkflowRequestList(e, requestName, workflowId, null);
            log.info("稽核待办数据接口，ec待办数据：{}", JSON.toJSONString(flowListResultVo));
            List<String> ecIds = flowListResultVo.getData().isEmpty() ? new ArrayList<>() : flowListResultVo.getData().stream()
                    .filter(flowList -> requestId.equals(flowList.getRequestid()))
                    .map(flowList -> flowList.getRequestid() + '-' + e).collect(Collectors.toList());
            log.info("稽核待办数据接口，ec数据，：{}", JSON.toJSONString(ecIds));
            // 集团待办数据
            List<TodoAuditResponse.Item> auditTodoData = iPortalWaitService.auditUserTodoData(e);
            List<String> jtIds = auditTodoData.isEmpty() ? new ArrayList<>() : auditTodoData.stream()
                    .map(TodoAuditResponse.Item::getOriginalId)
                    .filter((requestId + "-" + e)::equals).collect(Collectors.toList());
            log.info("稽核待办数据接口，统一待办服务数据：{}", JSON.toJSONString(jtIds));
            if (!ecIds.isEmpty()) {
                List<FlowList> flowLists = flowListResultVo.getData().stream().filter(flowList -> ecIds.contains(flowList.getRequestid() + "-" + e)).collect(Collectors.toList());
                for (FlowList flowList : flowLists) {
                    todoItemRequestList.addAll(assembleTodoData(flowList, e));
                }
            }
            if (!jtIds.isEmpty()) {
                List<TodoAuditResponse.Item> todoList = auditTodoData.stream().filter(item -> jtIds.contains(item.getOriginalId())).collect(Collectors.toList());
                for (TodoAuditResponse.Item item : todoList) {
                    todoItemRequestList.addAll(assembleTodoDataDel(item, e));
                }
            }
        });
        log.info("稽核待办数据接口，稽核数据总数:{}", todoItemRequestList.size());
        if (!todoItemRequestList.isEmpty()) {
//            iPortalWaitService.pushTodoDataToJtBatch(todoItemRequestList, "稽核待办数据接口");
        }
    }

}
