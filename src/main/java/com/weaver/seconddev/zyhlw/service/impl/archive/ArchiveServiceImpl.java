package com.weaver.seconddev.zyhlw.service.impl.archive;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.openapi.pojo.user.res.vo.UserInfoResult;
import com.weaver.seconddev.zyhlw.domain.archive.SmsListRespVO;
import com.weaver.seconddev.zyhlw.service.IArchiveService;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.ConvertDataUtils;
import com.weaver.seconddev.zyhlw.util.StringUtils;

import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 档案服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-04
 */
@Service
@Slf4j
public class ArchiveServiceImpl implements IArchiveService {

    @Resource
    IDataSqlService dataSqlService;

    @Resource
    private CmicProperties cmicProperties;

    @Resource
    private IOpenPlatformService openPlatformService;

    @Override
    public SmsListRespVO smsList(String orderId, Integer pageNo, Integer pageSize) throws Exception {
        SmsListRespVO smsListRespVO = new SmsListRespVO();

        // 获取总条数
        String countSql = "select count(1) as totalSize from uf_datacenter_sms where orderid= ?";
        List<Map<String, Object>> countResult = ConvertDataUtils.convertListMapKeyToLowerCase(dataSqlService
                .ebuilderFromSql(countSql, 1, 1, SourceType.LOGIC, Collections.singletonList(orderId)));
        if (countResult.isEmpty()) {
            return smsListRespVO;
        }

        Long totalSize = MapUtil.getLong(countResult.get(0), "totalSize");
        smsListRespVO.setTotalSize(totalSize);

        // 获取列表
        String sql = "select * from uf_datacenter_sms where orderid= ?";
        List<Map<String, Object>> dataList = ConvertDataUtils.convertListMapKeyToLowerCase(dataSqlService
                .ebuilderFromSql(sql, pageNo, pageSize, SourceType.LOGIC, Collections.singletonList(orderId)));
        dataList.forEach(data -> {
            Map<String, Object> dataMap = new HashMap<>();

            String uid = MapUtil.getStr(dataMap, "receiveruserid");
            if (StringUtils.isNotBlank(uid)) {
                UserInfoResult userInfoResult = openPlatformService.getUser(Long.valueOf(uid));
                dataMap.put("fa_sdx", userInfoResult.getUsername());
            }
            dataMap.put("fa_ssj", MapUtil.getStr(data, "createtime"));
            dataMap.put("duan_xnr", MapUtil.getStr(data, "smsconent"));
            dataMap.put("fa_szt", MapUtil.getInt(data, "issend"));

            dataList.add(dataMap);

            smsListRespVO.setDatas(dataList);
        });
        return smsListRespVO;
    }

}
