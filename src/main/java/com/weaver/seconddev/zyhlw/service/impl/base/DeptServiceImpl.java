package com.weaver.seconddev.zyhlw.service.impl.base;

import com.alibaba.fastjson.JSON;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.seconddev.zyhlw.domain.portal.DepartmentModel;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IDeptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DeptServiceImpl implements IDeptService {

    @Resource
    IDataSqlService dataSqlService;

    /**
     * 根据id获取部门信息
     *
     * @param id 部门id
     * @return 部门信息
     */
    @Override
    public DepartmentModel getDeptById(String id, String tenantKey) {
        String sql = "select * from eteams.department  where id = %s and tenant_key = %s";
        Map<String, Object> data = dataSqlService.eBuilderFromSqlOne(sql, SourceType.LOGIC);
        return JSON.parseObject(JSON.toJSONString(data), DepartmentModel.class);
    }
}
