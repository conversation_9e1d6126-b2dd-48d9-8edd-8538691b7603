package com.weaver.seconddev.zyhlw.service.impl.base;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.google.common.collect.Lists;
import com.weaver.common.form.metadata.option.FieldOption;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.seconddev.zyhlw.domain.formdata.FormTableDTO;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IFormBaseService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 浏览框相关工具类
 * <p>
 *
 * <AUTHOR>
 * @time 2025年02月25 15:16:04
 * @since 1.0
 */
@Service
@Slf4j
public class FormBaseServiceImpl implements IFormBaseService {

    @Resource
    IDataSqlService dataSqlService;

    @Resource
    private CmicProperties cmicProperties;

    /**
     * 获取表单/流程浏览框数据
     * todo
     * <p>
     *
     * @param tableName 表名
     * @param dataKey   浏览框key
     * @param formType  浏览框所属：0：表单，1：流程
     * @return null
     * @description 。
     * <p>
     * <AUTHOR>
     * @time 2025年02月25 15:15:53
     * @since 1.0
     */
    @Override
    public List<FieldOption> getFormOptions(String tableName, String dataKey, Integer formType) {
        String sql = "elect fp.* from field_option fp left join form_field ff on fp.field_id=ff.id left join form_table ft on ft.form_id=ff.form_id where ft.table_name='%s' and ff.data_key='%s' and (fp.archive is null or fp.archive=0)";
        sql = String.format(sql, tableName, dataKey);
        List<Map<String, Object>> dataResult;
        if (formType == 1) {
            dataResult = dataSqlService.workflowFromSqlAll(sql, SourceType.LOGIC);
        } else {
            dataResult = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        }
        if (!dataResult.isEmpty()) {
            return convert(dataResult, FieldOption.class);
        }
        return Collections.emptyList();
    }

    /**
     * 获取表单/流程浏览框数据
     * <p>
     *
     * @param tableName 表名
     * @param dataKey   浏览框key
     * @param formType  浏览框所属：0：表单，1：流程
     * @return null
     * @description 。
     * <p>
     * <AUTHOR>
     * @time 2025年02月25 15:15:53
     * @since 1.0
     */
    @Override
    public Map<String, String> getFormOptionsValue(String tableName, String dataKey, Integer formType) {
        List<FieldOption> fieldOptions = getFormOptions(tableName, dataKey, formType);
        if (!fieldOptions.isEmpty()) {
            return fieldOptions.stream().collect(Collectors.toMap(FieldOption::getValueKey, FieldOption::getName));
        }
        return Collections.emptyMap();
    }

    @Override
    public FormTableDTO getTableNameByFormId(Long formId) {
        String sql = "select ft.* from form_table ft where ft.form_id = ? and ft.tenant_key = ? and ft.delete_type = 0";
        List<Map<String, Object>> dataResult = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC,
                Lists.newArrayList(formId.toString(), cmicProperties.getHostTenantKey()));
        if (!CollectionUtils.isEmpty(dataResult)) {
            return BeanUtil.mapToBean(dataResult.get(0), FormTableDTO.class, true, CopyOptions.create());
        }
        return null;
    }

    public static <T> List<T> convert(List<Map<String, Object>> mapList, Class<T> clazz) {
        return mapList.stream().map(map -> {
            try {
                T obj = clazz.getDeclaredConstructor().newInstance();
                BeanUtils.populate(obj, map); // 自动匹配同名字段
                return obj;
            } catch (Exception e) {
                throw new RuntimeException("转换失败", e);
            }
        }).collect(Collectors.toList());
    }

}