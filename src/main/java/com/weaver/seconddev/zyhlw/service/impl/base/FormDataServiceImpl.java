package com.weaver.seconddev.zyhlw.service.impl.base;


import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.seconddev.zyhlw.domain.formdata.FormDataListReqDTO;
import com.weaver.seconddev.zyhlw.domain.formdata.FormTableDTO;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IFormBaseService;
import com.weaver.seconddev.zyhlw.service.impl.FormDataService;
import com.weaver.seconddev.zyhlw.service.impl.export.converter.Converter;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.DataOperateUtil;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import com.weaver.seconddev.zyhlw.util.converter.ConverterUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.weaver.seconddev.zyhlw.util.DataOperateUtil.querySqlJoin;

/**
 * 表单数据相关操作接口实现类
 *
 * <AUTHOR>
 * @date 2025年05月06日 15:50
 */
@Service
@Slf4j
public class FormDataServiceImpl implements FormDataService {

    @Resource
    private IFormBaseService formBaseService;

    @Resource
    private IDataSqlService dataSqlService;

    @Resource
    private ConverterUtil converterUtil;

    @Resource
    private CmicProperties cmicProperties;

    @Override
    public WeaResult<JSONObject> getFormDataList(FormDataListReqDTO reqDTO) {
        log.info("-----获取表单数据接口-----, req: {}", JSON.toJSONString(reqDTO));
        if (reqDTO.getFormId() == null) {
            return WeaResult.fail("参数表单id不能为空");
        }
        try {
            //获取表单信息
            FormTableDTO formTable = formBaseService.getTableNameByFormId(Long.valueOf(reqDTO.getFormId()));
            if (formTable == null) {
                return WeaResult.fail("表单不存在,请重新核验表单id");
            }
            String tablename = formTable.getTableName();
            //明细表有值就用明细表查
            if (StringUtils.isNotEmpty(reqDTO.getDetail())) {
                tablename += "_dt" + reqDTO.getDetail();
            }
            log.info("mainTablename: {}", tablename);

            // 获取对应表单数据
            JSONObject resultJson = new JSONObject();
            // 使用优化后的 querySqlJoin 方法，返回 SQL 和参数
            DataOperateUtil.SqlQueryResult sqlResult = querySqlJoin(
                    tablename, reqDTO.getConditions(), reqDTO.getQueryFields(), cmicProperties.getHostTenantKey());
            List<Map<String, Object>> mainData = dataSqlService
                    .eBuilderFromSqlAll(sqlResult.getSql(), SourceType.LOGIC, sqlResult.getParams());
            List<Map<String, Object>> convertMainData = new ArrayList<>();
            //数据根据字段类型添加span后缀
            if (!CollectionUtils.isEmpty(mainData)) {
                //初始化，现将mainData的key转成小写
                mainData.forEach(map -> {
                    Map<String, Object> newMap = new HashMap<>();
                    map.forEach((key, value) -> {
                        newMap.put(key.toLowerCase(), value);
                    });
                    convertMainData.add(newMap);
                });

                Map<String, Converter> converterMap = converterUtil.getConverter(reqDTO.getFormId());
                if (!MapUtil.isEmpty(converterMap)) {
                    for (Map<String, Object> map : convertMainData) {
                        // 创建一个新的Map来存储需要添加的span条目
                        Map<String, Object> spanEntries = new HashMap<>();

                        // 遍历原始Map并收集需要添加的span条目
                        map.forEach((key, value) -> {
                            String filedName = key.toLowerCase();
                            if (converterMap.containsKey(filedName) &&
                                    converterMap.get(filedName) != null &&
                                    value != null) {
                                spanEntries.put(filedName + "span",
                                        converterMap.get(filedName).convert(value.toString()));
                            }
                        });

                        // 将span条目添加到原始Map
                        map.putAll(spanEntries);
                    }
                }
            }
            resultJson.put("message", "success");
            resultJson.put("data", convertMainData);
            resultJson.put("count", convertMainData.size());
            return WeaResult.success(resultJson);
        } catch (Exception e) {
            log.error("调用 {}.getFormDataList 执行异常", FormDataServiceImpl.class.getSimpleName(), e);
            return WeaResult.fail("系统异常!");
        }
    }
}
