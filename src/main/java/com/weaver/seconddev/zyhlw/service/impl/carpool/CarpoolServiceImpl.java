package com.weaver.seconddev.zyhlw.service.impl.carpool;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.weaver.openapi.pojo.user.res.vo.UserInfoResult;
import com.weaver.seconddev.zyhlw.service.ICarpoolService;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.ebuilder.EBuilderUtil;
import com.weaver.teams.domain.user.SimpleEmployee;
import com.weaver.teams.security.context.UserContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 拼车功能service实现类
 * 
 * <AUTHOR>
 * @date 2025-05-29 10:00:00
 */
@Slf4j
@Service
public class CarpoolServiceImpl implements ICarpoolService {

    @Resource
    private IOpenPlatformService openPlatformService;

    @Resource
    private IDataBaseService dataBaseService;

    @Resource
    private CmicProperties cmicProperties;

    private static final DateTimeFormatter DATE_TIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    @Override
    public Boolean clickLog(String billId) {
        SimpleEmployee currentUser = UserContext.getCurrentUser();
        UserInfoResult userInfoResult = openPlatformService.getUser(currentUser.getUid());
        Long departmentId = userInfoResult.getDepartment().get(0);
        String mobile = userInfoResult.getMobile();

        // 到期时间
        String clikTime = DATE_TIME_FORMAT.format(LocalDateTime.now());
        // 用户Id
        Long userId = currentUser.getUid();
        // uf_dian_log表单Id
        String formId = dataBaseService.getBaseValue("uf_dian_log", "objId");
        // 插入数据
        List<Map<String, Object>> dataList = new ArrayList<>();
        Map<String, Object> data = new HashMap<>();
        data.put("dian_jsj", clikTime);
        data.put("yong_h", userId);
        data.put("pin_cxx", billId);
        data.put("bu_m", departmentId);
        data.put("shou_jh", mobile);
        dataList.add(Collections.singletonMap("mainTable", data));

        EBuilderUtil.saveFormDataV2(cmicProperties.getOpenPlatformUrl(), currentUser.getUid(), formId, dataList,
                openPlatformService.getAccessToken());

        return true;
    }
}