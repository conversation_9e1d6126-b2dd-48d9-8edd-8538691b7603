package com.weaver.seconddev.zyhlw.service.impl.export;

import com.weaver.seconddev.zyhlw.service.impl.export.constant.TemplateConstants;
import com.weaver.seconddev.zyhlw.service.impl.export.service.ExportService;
import com.weaver.seconddev.zyhlw.service.impl.export.service.MultipleLayerExportServiceImpl;
import org.springframework.stereotype.Component;

/**
 * <h1>导出工厂</h1>
 *
 * <p>Description: </p>
 *
 * <p>Note: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Component
public class ExcelExporterFactory {

    public ExportService getExporter(String templateId) {
        if (TemplateConstants.ORDINARY_DOUBLE_LAYER_EXPORT_TEMPLATE.equals(templateId)) {
//            return new OrdinaryDoubleLayerExportServiceImpl();
        } else if(TemplateConstants.MULTIPLE_LAYER_EXPORT_TEMPLATE.equals(templateId)) {
            return new MultipleLayerExportServiceImpl();
        } else if(TemplateConstants.MULTIPLE_LAYER_EXPORT_TEMPLATE_V2.equals(templateId)){
//            return new MultipleLayerExportServiceImplV2();
        }
        throw new IllegalArgumentException("未找到相应的导出模板");
    }
}
