package com.weaver.seconddev.zyhlw.service.impl.export.commons;

import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
@Slf4j
public class ACommonsUtilsNew {

    // 私有静态实例，防止外部直接创建对象
    private static ACommonsUtilsNew instance;

    @Resource
    CmicProperties cmicProperties;

    @Resource
    IDataSqlService dataSqlService;

    // 私有构造方法，防止外部通过new创建对象实例
    private ACommonsUtilsNew() {
        // 初始化代码
    }

    // 提供公共的静态方法，返回唯一实例
    public static synchronized ACommonsUtilsNew getInstance() {
        if (instance == null) {
            instance = new ACommonsUtilsNew();
        }
        return instance;
    }



    /**
     * 下拉选择框的值和名字对
     *
     * @param fieldId 选择框字段的id
     * @return map的键值对关系：value选择框字段一个选项的值，name对应选择框字段一个选项的名称
     */
    public List<Map<String, String>> getSelectValueNamesByFieldId(Long fieldId) {
        List<Map<String, String>> selectValueNames = new ArrayList<>();
        if (!Objects.isNull(fieldId)) {
            String querySql = "SELECT fo.name AS selectname, fo.value_key AS selectvalue " +
                    "FROM field_option fo " +
                    "WHERE fo.field_id = %s " +
                    "    AND (fo.archive IS NULL " +
                    "        OR fo.archive = 0) " +
                    "    AND fo.delete_type = 0 " +
                    "    AND fo.tenant_key = '%s' " +
                    "ORDER BY fo.id";
            querySql = String.format(querySql, fieldId, cmicProperties.getHostTenantKey());
            List<Map<String, Object>> data = dataSqlService.eBuilderFromSqlAll(querySql, SourceType.LOGIC);
            data.forEach(rs -> {
                String selectValue = rs.get("selectvalue").toString();
                String selectName = rs.get("selectname").toString();
                if (StringUtils.isNotBlank(selectValue) && StringUtils.isNotBlank(selectName)) {
                    Map<String, String> valueNamePair = new HashMap<String, String>();
                    valueNamePair.put("value", selectValue);
                    valueNamePair.put("name", selectName);
                    selectValueNames.add(valueNamePair);
                }
            });
        }
        return selectValueNames;
    }


}
