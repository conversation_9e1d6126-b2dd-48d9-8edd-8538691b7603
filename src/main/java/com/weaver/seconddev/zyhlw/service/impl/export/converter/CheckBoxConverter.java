package com.weaver.seconddev.zyhlw.service.impl.export.converter;

import org.springframework.stereotype.Component;

/**
 * check框值转换器
 *
 * <AUTHOR>
 */
@Component
public class CheckBoxConverter implements Converter {
    private static final int EXPRESSION_COUNT = 2;

    public CheckBoxConverter() {
    }

    /**
     * 表达式的格式为{YES}|{NO}
     */
    @Override
    public String convert(String value, Object... args) {
        if (args.length == 0 || !(args[0] instanceof String)) {
            throw new IllegalArgumentException("CheckBox值转换时，参数校验异常");
        }
        String expression = (String) args[0];
        String[] doublePartExpression = expression.split("[}|{]");
        if (expression.length() != EXPRESSION_COUNT) {
            throw new RuntimeException("Check框表达式不正确");
        }
        String yes = doublePartExpression[0];
        String no = doublePartExpression[1];
        return "1".equals(value) ? yes.substring(1) : no.substring(0, no.length() - 1);
    }
}
