package com.weaver.seconddev.zyhlw.service.impl.export.converter;

import com.weaver.common.cache.base.BaseCache;
import com.weaver.seconddev.zyhlw.service.impl.export.converter.factory.RelateBrowserConverterFactory;
import com.weaver.seconddev.zyhlw.service.impl.export.converter.factory.SelectConverterFactory;
import com.weaver.seconddev.zyhlw.service.impl.export.converter.factory.ViewConverterFactory;
import com.weaver.seconddev.zyhlw.service.impl.export.entity.EcColumnInfo;
import com.weaver.seconddev.zyhlw.util.cache.CacheKey;
import com.weaver.seconddev.zyhlw.util.cache.CacheModuleKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <h1>类型转换</h1>
 *
 * <p>TODO涉及类型过多，没有统一方法，需要自己自定义添加</p>
 *
 * <p>Note: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Component
@Slf4j
public class ConverterBase {

    @Resource
    BaseCache baseCache;

    @Resource
    private SelectConverterFactory selectConverterFactory;

    @Resource
    private ViewConverterFactory viewConverterFactory;

    @Resource
    private RelateBrowserConverterFactory relateBrowserConverterFactory;

    /**
     * 多行行文本
     */
    private static final String HTML_TYPE_MULTI_TEXT = "TextArea";

    /**
     * 人员组织
     */
    private static final String HTML_TYPE_EMPLOYEE = "Employee";
    /**
     * 关联浏览
     */
    private static final String HTML_TYPE_RELATE_BROWSER = "RelateBrowser";
    /**
     * 关联流程
     */
    private static final String HTML_TYPE_WORKFLOW = "Workflow";
    /**
     * check复选框
     */
    private static final String HTML_TYPE_CHECK = "CheckBox";
    /**
     * 下拉单选框
     */
    private static final String HTML_TYPE_SELECT = "Select";
    /**
     * 部门选择
     */
    private static final String HTML_TYPE_DEPT = "Department";

    public Converter getConvert(EcColumnInfo columnInfo) {
        String htmlType = columnInfo.getHtmlType().toLowerCase();
        Long fieldId = columnInfo.getFieldId();
        try {
            if (htmlType.equals(HTML_TYPE_MULTI_TEXT.toLowerCase())) {
                return new RichTextConverter();
            } else if (htmlType.equals(HTML_TYPE_RELATE_BROWSER.toLowerCase())) {
                return relateBrowserConverterFactory.createRelateBrowserConverter(columnInfo);
            } else if (htmlType.equals(HTML_TYPE_CHECK.toLowerCase())) {
                return new CheckBoxConverter();
            } else if (htmlType.equals(HTML_TYPE_SELECT.toLowerCase())) {
                return selectConverterFactory.createSelectConverter(fieldId);
            } else if (htmlType.equals(HTML_TYPE_EMPLOYEE.toLowerCase())
                    || htmlType.equals(HTML_TYPE_WORKFLOW.toLowerCase())
                    || htmlType.equals(HTML_TYPE_DEPT.toLowerCase())) {
                return viewConverterFactory.createViewConverter(columnInfo);

            }
        } catch (Exception e) {
            log.error("转换器获取失败, htmlType:{}, fieldId:{}", htmlType, fieldId, e);
        }
        return null;
    }

    public Converter reflectConverter(String classFullName) throws ClassNotFoundException, InstantiationException, IllegalAccessException {
        String key = String.format("%s.%s", CacheKey.ASYNC_EXPORT_KEY + ":", classFullName);
        Converter converter = null;
        boolean containsKey = baseCache.containsKey(CacheModuleKey.ASYNC_EXPORT_MODULE, key);

        if (containsKey) {
            converter = (Converter) baseCache.get(CacheModuleKey.ASYNC_EXPORT_MODULE, key);
        }
        if (converter != null) {
            return converter;
        }
        Class<?> converterClass = Class.forName(classFullName);
        converter = (Converter) converterClass.newInstance();
        baseCache.set(CacheModuleKey.ASYNC_EXPORT_MODULE, key, converter);
        return converter;
    }
}
