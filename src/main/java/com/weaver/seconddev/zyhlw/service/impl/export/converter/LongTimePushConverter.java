package com.weaver.seconddev.zyhlw.service.impl.export.converter;

import org.springframework.stereotype.Component;

/**
 *
 *
 * <AUTHOR>
 */
@Component
public class LongTimePushConverter implements Converter {

    @Override
    public String convert(String value, Object... args) {
        if(value != null && value.equals("2099-01-01")){
            return "长期推进";
        }
        return value;
    }
}
