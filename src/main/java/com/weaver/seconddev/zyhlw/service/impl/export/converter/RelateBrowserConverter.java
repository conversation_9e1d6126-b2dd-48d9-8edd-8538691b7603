package com.weaver.seconddev.zyhlw.service.impl.export.converter;

import com.alibaba.fastjson.JSON;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.impl.export.commons.ACommonsUtilsNew;
import com.weaver.seconddev.zyhlw.service.impl.export.entity.EcColumnInfo;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 关联浏览转换器
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class RelateBrowserConverter implements Converter {


    private Map<String, String> relateBrowserData;

    //    @Resource
    CmicProperties cmicProperties;

    //    @Resource
    IDataSqlService dataSqlService;


    @Autowired
    public RelateBrowserConverter(CmicProperties cmicProperties, IDataSqlService dataSqlService) {
        this.cmicProperties = cmicProperties;
        this.dataSqlService = dataSqlService;
    }

    public void init(EcColumnInfo ecColumnInfo) {
        String browserType = ecColumnInfo.getBrowserType();
        this.relateBrowserData = getBrowserQueryMap(browserType);
}

    /**
     * 获取关联浏览的信息
     * @return Map集合，querySql - 浏览框查询sql、 nameKey - 浏览框关联字段
     */
    public Map<String, String> getBrowserQueryMap(String browserType){
        HashMap<String, String> map = new HashMap<>();
        String querySql = "select data_id,data_content from eteams.browser_data_cache where browser_type='%s' and tenant_key = '%s'";
        querySql = String.format(querySql, browserType, cmicProperties.getHostTenantKey());
        List<Map<String, Object>> browserData = dataSqlService.eBuilderFromSqlAll(querySql, SourceType.LOGIC);
        browserData.forEach(rs -> {
            String dataId = rs.getOrDefault("data_id", "").toString();
            String dataContent = rs.getOrDefault("data_content", "").toString();
            if(StringUtils.isNotBlank(dataId) && StringUtils.isNotBlank(dataContent)) {
                map.put(dataId, dataContent);

            }
        });
        return map;
    }

    @Override
    public String convert(String value, Object... args) {
        log.info("RelateBrowserConverter.convert value:{}, relateBrowserData:{}", value, JSON.toJSONString(relateBrowserData));
        if (StringUtils.isBlank(value)) {
            return "";
        }
        if (relateBrowserData == null) {
            return value;
        }
        StringBuilder names = new StringBuilder();
        for (String valueItem : value.split(",")) {
            String name = relateBrowserData.getOrDefault(valueItem, valueItem);
            if (StringUtils.isBlank(value)) {
                names = new StringBuilder(name);
            } else {
                names.append(",").append(name);
            }
        }
        // 清除前后的逗号
        if (names.length() > 0) {
            names.deleteCharAt(0);
        }
        return clearCommas(names.toString());
    }
    public static String clearCommas(String str) {
        if (str == null) {
            return null;
        }
        // 使用正则表达式替换前后的逗号
        return str.replaceAll("^,+|,+$", "");
    }

}
