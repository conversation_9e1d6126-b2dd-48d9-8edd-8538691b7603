package com.weaver.seconddev.zyhlw.service.impl.export.converter;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 富文本转换器
 *
 * <AUTHOR>
 */
@Component
public class RichTextConverter implements Converter {
    @Override
    public String convert(String value, Object... args) {
        String s = value.replaceAll("<.*?>", "|");
        String trim = s.replaceAll("\\\\n", "").replace("&nbsp;", "").trim();
        String[] list = trim.split("\\|");
        StringBuilder builder = new StringBuilder();
        boolean needToClean = false;
        for (String row : list) {
            if (!StringUtils.isBlank(row)) {
                builder.append(row).append("\r\n");
                needToClean = true;
            }
        }
        if (needToClean) {
            int start = builder.length() - 2;
            int end = builder.length();
            builder.delete(start, end);
        }
        return builder.toString();
    }
}
