package com.weaver.seconddev.zyhlw.service.impl.export.converter;

import com.alibaba.fastjson.JSON;
import com.weaver.seconddev.zyhlw.service.impl.export.commons.ACommonsUtilsNew;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 选择框值转换器
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SelectConverter implements Converter {

    private final ACommonsUtilsNew aCommonsUtilsNew;
    private List<Map<String, String>> selectValueNames;

    @Autowired
    public SelectConverter(ACommonsUtilsNew aCommonsUtilsNew) {
        this.aCommonsUtilsNew = aCommonsUtilsNew;
    }

    public void init(Long fieldId) {
        this.selectValueNames = aCommonsUtilsNew.getSelectValueNamesByFieldId(fieldId);
    }

    @Override
    public String convert(String value, Object... args) {
        log.info("SelectConverter.convert value:{}, selectValueNames:{}", value, JSON.toJSONString(selectValueNames));

        if (StringUtils.isBlank(value)) {
            return "";
        }
        if (selectValueNames == null) {
            return value;
        }
        String names = "";
        for (String valueItem : value.split(",")) {
            for (Map<String, String> selectValueName : selectValueNames) {
                String selectValue = selectValueName.get("value");
                String selectName = selectValueName.get("name");
                if (selectValue.equals(valueItem)) {
                    if (StringUtils.isBlank(names)) {
                        names = selectName;
                    } else {
                        names += "," + selectName;
                    }
                }
            }
        }
        return clearCommas(names);
    }

    public static String clearCommas(String str) {
        if (str == null) {
            return null;
        }
        // 使用正则表达式替换前后的逗号
        return str.replaceAll("^,+|,+$", "");
    }

}
