package com.weaver.seconddev.zyhlw.service.impl.export.converter;


import com.alibaba.fastjson.JSON;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.impl.export.commons.ACommonsUtilsNew;
import com.weaver.seconddev.zyhlw.service.impl.export.entity.EcColumnInfo;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 浏览框值转换器
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ViewConverter implements Converter {

    private Map<String, String> viewData;

//    @Resource
    CmicProperties cmicProperties;

//    @Resource
    IDataSqlService dataSqlService;


    @Autowired
    public ViewConverter(CmicProperties cmicProperties, IDataSqlService dataSqlService) {
        this.cmicProperties = cmicProperties;
        this.dataSqlService = dataSqlService;
    }

    public void init(EcColumnInfo ecColumnInfo) {
        String viewType = ecColumnInfo.getHtmlType();
        Map<String, String> browserDataMap = new HashMap<>();
        Map<String, Map<String, String>> browserQueryMap = getViewQueryMap();
        if (browserQueryMap.containsKey(String.valueOf(viewType))) {
            Map<String, String> browserQueryItem = browserQueryMap.get(String.valueOf(viewType));
            String querySql = browserQueryItem.get("querySql");
            String nameKey = browserQueryItem.get("nameKey");
            // 判断querySql是否存在 %s
            if(querySql.contains("%s")) {
                querySql = String.format(querySql, cmicProperties.getHostTenantKey());
            }
            List<Map<String, Object>> dataSource = dataSqlService.eBuilderFromSqlAll(querySql, SourceType.LOGIC);
            dataSource.forEach(rs -> browserDataMap.put(String.valueOf(rs.get("id")), rs.getOrDefault(nameKey, "").toString()));
            this.viewData = browserDataMap;
        }
    }

    public ViewConverter() {
    }

    /**
     * 获取非自定义浏览框的信息
     * @return Map集合，querySql - 浏览框查询sql、 nameKey - 浏览框关联字段
     */
    public Map<String, Map<String, String>> getViewQueryMap(){
        HashMap<String, Map<String, String>> map = new HashMap<>();
        String querySql = "select llklx, cxsql, llkxszd from uf_jmsjcxllkpzb";
        List<Map<String, Object>> browserQueryConfig = dataSqlService.eBuilderFromSqlAll(querySql, SourceType.LOGIC);
        for(Map<String, Object> queryConfig: browserQueryConfig){
            String type = queryConfig.get("llklx").toString();
            String sql = queryConfig.get("cxsql").toString();
            String nameKey = queryConfig.get("llkxszd").toString();
            HashMap<String, String> item = new HashMap<>();
            item.put("nameKey", nameKey);
            item.put("querySql", sql);
            map.put(type, item);
        }
        return map;
    }

    @Override
    public String convert(String value, Object... args) {
        log.info("ViewConverter.convert value:{}, viewData:{}", value, JSON.toJSONString(viewData));
        if (StringUtils.isBlank(value)) {
            return "";
        }
        if (viewData == null) {
            return value;
        }
        String names = "";
        for (String valueItem : value.split(",")) {
            String name = viewData.getOrDefault(valueItem, valueItem);
            if (StringUtils.isBlank(names)) {
                names = name;
            } else {
                names += "," + name;
            }
        }
        return clearCommas(names);
    }

    public static String clearCommas(String str) {
        if (str == null) {
            return null;
        }
        // 使用正则表达式替换前后的逗号
        return str.replaceAll("^,+|,+$", "");
    }
}
