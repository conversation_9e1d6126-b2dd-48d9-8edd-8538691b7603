package com.weaver.seconddev.zyhlw.service.impl.export.converter.factory;

import com.weaver.seconddev.zyhlw.service.impl.export.commons.ACommonsUtilsNew;
import com.weaver.seconddev.zyhlw.service.impl.export.converter.SelectConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <h1>select工厂</h1>
 *
 * <p>Description: </p>
 *
 * <p>Note: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Component
public class SelectConverterFactory {
    @Autowired
    private ACommonsUtilsNew aCommonsUtilsNew;

    public SelectConverter createSelectConverter(Long fieldId) {
        SelectConverter converter = new SelectConverter(aCommonsUtilsNew);
        converter.init(fieldId);
        return converter;
    }
}
