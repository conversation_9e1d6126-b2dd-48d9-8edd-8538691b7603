package com.weaver.seconddev.zyhlw.service.impl.export.converter.factory;

import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.impl.export.commons.ACommonsUtilsNew;
import com.weaver.seconddev.zyhlw.service.impl.export.converter.SelectConverter;
import com.weaver.seconddev.zyhlw.service.impl.export.converter.ViewConverter;
import com.weaver.seconddev.zyhlw.service.impl.export.entity.EcColumnInfo;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <h1>select工厂</h1>
 *
 * <p>Description: </p>
 *
 * <p>Note: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Component
public class ViewConverterFactory {
    @Resource
    CmicProperties cmicProperties;

    @Resource
    IDataSqlService dataSqlService;

    public ViewConverter createViewConverter(EcColumnInfo ecColumnInfo) {
        ViewConverter converter = new ViewConverter(cmicProperties, dataSqlService);
        converter.init(ecColumnInfo);
        return converter;
    }
}
