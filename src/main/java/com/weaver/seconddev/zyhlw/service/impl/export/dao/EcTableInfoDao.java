package com.weaver.seconddev.zyhlw.service.impl.export.dao;


import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.impl.export.entity.EcColumnInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 表单配置信息存取类
 *
 * <AUTHOR>
 */
@Component
public class EcTableInfoDao {

    @Resource
    private IDataSqlService dataSqlService;
    /**
     * 根据表ID加载表单配置信息
     * TODO 这个应该是有问题的。碰到单独处理 by sunzhenguang
     * @param tableId 表ID
     * @return 字段信息映射
     */
    public Map<String, EcColumnInfo> loadTableInfoByTableId(String tableId) {
        String queryColumnsInfoSql = "select a.id, a.browser_module browsermodule,a.browser_type browsertype,0 type, a.data_key AS fieldname, a.data_type AS fielddbtype, a.COMPONENT_KEY fieldhtmltype, cbi.CONDITION_SQL sqltext, '' linkname, sf.data_key detailtable from FORM_FIELD a left join form f on a.form_id = f.id left join eteams.custom_browser_info cbi on cbi.sign = a.data_type left join SUB_FORM sf on a.sub_form_id = sf.id where f.TARGET_ID = " + tableId;
        List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(queryColumnsInfoSql, SourceType.LOGIC);
        return processColumnInfoData(dataList, tableId);
    }

    /**
     * 根据表单ID加载表单配置信息
     * @param formId 表单ID
     * @return 字段信息映射
     */
    public Map<String, EcColumnInfo> loadTableInfoByFormId(String formId) {
        String queryColumnsInfoSql = "select a.id, a.browser_module browsermodule,a.browser_type browsertype,0 type, a.data_key AS fieldname, a.data_type AS fielddbtype, a.COMPONENT_KEY fieldhtmltype, cbi.CONDITION_SQL sqltext, '' linkname, sf.data_key detailtable from FORM_FIELD a left join form f on a.form_id = f.id left join eteams.custom_browser_info cbi on cbi.sign = a.data_type left join SUB_FORM sf on a.sub_form_id = sf.id where f.id = " + formId;
        List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(queryColumnsInfoSql, SourceType.LOGIC);
        return processColumnInfoData(dataList, formId);
    }

    /**
     * 处理字段信息数据
     * @param dataList 数据列表
     * @param tableOrFormId 表ID或表单ID
     * @return 字段信息映射
     */
    private Map<String, EcColumnInfo> processColumnInfoData(List<Map<String, Object>> dataList, String tableOrFormId) {
        Map<String, EcColumnInfo> result = new HashMap<>();
        dataList.forEach(rs -> {
            int selectType = (int) rs.getOrDefault("type", 0); // 浏览框类型，该字段e10已经没了。不再使用

            String htmlType = rs.getOrDefault("fieldhtmltype", "").toString();
            String detailTableName = rs.getOrDefault("detailtable", "").toString();
            String fieldName = rs.getOrDefault("fieldname", "").toString();
            Long fieldId = (Long) rs.getOrDefault("id", null);
            String fieldDbType = rs.getOrDefault("fielddbtype", "").toString();
            String sqlText = rs.getOrDefault("sqltext", "").toString();
            String showName = rs.getOrDefault("linkname", "").toString();
            String browsermodule = rs.getOrDefault("browsermodule", "").toString();
            String browsertype = rs.getOrDefault("browsertype", "").toString();
            // 字段名为空，说明是主表，不为空为明细表
            int whereIndex = sqlText.indexOf("where");
            if (whereIndex == -1) {
                whereIndex = sqlText.length();
            }
            sqlText = sqlText.substring(0, whereIndex);
            String prefix = "";
            if (!StringUtils.isBlank(detailTableName)) {
                int index = detailTableName.lastIndexOf("_");
                prefix = detailTableName.substring(index + 1) + "_";
            }
            String newFiledName = prefix + fieldName;
            result.put(newFiledName, new EcColumnInfo(htmlType, selectType, fieldName, fieldId, tableOrFormId, fieldDbType, sqlText, showName, browsermodule, browsertype));
        });
        return result;
    }

}
