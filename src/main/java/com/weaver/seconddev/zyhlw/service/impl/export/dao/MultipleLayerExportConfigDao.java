package com.weaver.seconddev.zyhlw.service.impl.export.dao;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.openapi.pojo.form.params.FormVo;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.impl.DataSqlServiceImpl;
import com.weaver.seconddev.zyhlw.service.impl.export.converter.ConverterBase;
import com.weaver.seconddev.zyhlw.service.impl.export.entity.EcColumnInfo;
import com.weaver.seconddev.zyhlw.service.impl.export.entity.odl.MultipleColumnConfig;
import com.weaver.seconddev.zyhlw.service.impl.export.entity.odl.MultipleLayerConfig;
import com.weaver.seconddev.zyhlw.service.impl.export.entity.odl.MultipleTitleConfig;
import com.weaver.seconddev.zyhlw.service.impl.export.service.TableColumnAnalyser;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 多层标题导出配置数据存取类
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class MultipleLayerExportConfigDao {
    private final String SIMPLE_NAME = MultipleLayerExportConfigDao.class.getSimpleName();


    @Resource
    CmicProperties cmicProperties;
    @Resource
    private IDataSqlService dataSqlService;
    @Resource
    private EcTableInfoDao ecTableInfoDao;
    @Resource
    private ConverterBase converterBase;

    public MultipleLayerConfig loadConfigByType(int type) {
        String method = String.format("调用%s.loadConfigByType-->", SIMPLE_NAME);
        try {
            String queryMainConfigSql = "select * from uf_san_cbtmbpz where type = %s and delete_type = 0";
            log.info("{}开始执行: {}", method, type);
            queryMainConfigSql = String.format(queryMainConfigSql, type);
            Map<String, Object> data = dataSqlService.eBuilderFromSqlOne(queryMainConfigSql, SourceType.LOGIC);
            if (!data.isEmpty()) {
                MultipleLayerConfig config = new MultipleLayerConfig(
                        (Long) data.getOrDefault("id", null),
                        data.getOrDefault("filename", "").toString(),
                        data.getOrDefault("tablename", "").toString(),
                        data.getOrDefault("tableid", "").toString(),
                        data.getOrDefault("sheetname", "").toString()
                );
                String queryDetailSql = String.format("select * from uf_san_cbtmbpz_dt1 where form_data_id = %s and delete_type=0 order by orderNum+0 asc", config.getId());
                List<Map<String, Object>> dataDt1List = dataSqlService.eBuilderFromSqlAll(queryDetailSql, SourceType.LOGIC);

                if (!dataDt1List.isEmpty()) {
                    Map<String, EcColumnInfo> ecColumnInfoMap = ecTableInfoDao.loadTableInfoByTableId(config.getTableId());
                    log.info("{}ecColumnInfoMap: {}", method, JSON.toJSONString(ecColumnInfoMap));

                    List<MultipleTitleConfig> titleConfigs = new ArrayList<>();
                    Map<String, List<MultipleColumnConfig>> columnConfigIndexQuote = new HashMap<>();
                    List<MultipleColumnConfig> columnConfigsOneLayer = new ArrayList<>();
                    dataDt1List.forEach(rs -> {
                        MultipleTitleConfig titleConfig = new MultipleTitleConfig(
                                (Long) rs.getOrDefault("id", null),
                                rs.getOrDefault("title", "").toString(),
                                rs.getOrDefault("titlecolor", "").toString(),
                                rs.getOrDefault("titlebackground", "").toString(),
                                rs.getOrDefault("titlerownum", "").toString(),
                                rs.getOrDefault("titleid", "").toString(),
                                rs.getOrDefault("suptitleid", "").toString(),
                                rs.getOrDefault("fieldtitlecolor", "").toString(),
                                rs.getOrDefault("fieldtitlebackground", "").toString()
                        );
                        titleConfigs.add(titleConfig);
                        columnConfigIndexQuote.put(titleConfig.getTitleId(), new ArrayList<>());
                    });

                    String queryDetail2TableSql = String.format("select * from uf_san_cbtmbpz_dt2 where form_data_id = %s and delete_type=0 order by orderNum+0 asc", config.getId());
                    List<Map<String, Object>> dataDt2List = dataSqlService.eBuilderFromSqlAll(queryDetail2TableSql, SourceType.LOGIC);
                    List<MultipleColumnConfig> columnConfigs = new ArrayList<>();
                    dataDt2List.forEach(rs -> {
                        String key = rs.getOrDefault("suptitleid", "").toString();
                        MultipleColumnConfig columnConfig = new MultipleColumnConfig(
                                (Long) rs.getOrDefault("id", null),
                                key,
                                rs.getOrDefault("fieldname", "").toString(),
                                rs.getOrDefault("fieldtitle", "").toString(),
                                rs.getOrDefault("fieldtitlecolor", "").toString(),
                                rs.getOrDefault("fieldtitlebackground", "").toString(),
                                rs.getOrDefault("specialconvert", "").toString(),
                                rs.getOrDefault("checkconvertexpression", "").toString()
                        );
                    EcColumnInfo ecColumnInfo = ecColumnInfoMap.get(columnConfig.getFieldname());
                    String specialConvert = columnConfig.getSpecialConvert();
                    if (specialConvert != null && !specialConvert.trim().isEmpty()) {
                        try {
                            ecColumnInfo.setConverter(converterBase.reflectConverter(columnConfig.getSpecialConvert()));
                        } catch (Exception e) {
                            log.info("{}字段：{},自定义解析器转换错误，错误类型: {},错误信息：{}", method, columnConfig.getFieldname(), e, e.getMessage());
                            ecColumnInfo.setConverter(converterBase.getConvert(ecColumnInfo));
                        }
                    } else {
                        ecColumnInfo.setConverter(converterBase.getConvert(ecColumnInfo));
                    }
                        columnConfig.setEcColumnInfo(ecColumnInfo);
                        log.info("{}ecColumnInfo: {}", method, JSON.toJSONString(ecColumnInfo));
                        columnConfigIndexQuote.getOrDefault(key, columnConfigsOneLayer).add(columnConfig);
                        columnConfigs.add(JSON.parseObject(JSON.toJSONString(columnConfig, SerializerFeature.DisableCircularReferenceDetect), MultipleColumnConfig.class));
                    });
                    config.setTitleConfigs(titleConfigs);
                    config.setColumnConfigs(columnConfigs);
                    config.setColumnConfigsOneLayer(columnConfigsOneLayer);
                    config.setColumnConfigIndexQuote(columnConfigIndexQuote);
                    config.initTreeTitleConfigs();
                }
                return config;
            }
        } catch (Exception e) {
            log.error("{}异常{}", method, e);
            log.info("{}异常：{}", e, e.getMessage());
        }
        return null;
    }

    public Set<String> loadDetailTableSet(String tableName) {
        Set<String> tableSet = new HashSet<>();
        String sql = "SELECT sf.data_key as detailtable FROM form_table f left join sub_form sf on f.form_id = sf.form_id WHERE f.TABLE_NAME='%s' and f.delete_type = 0  AND f.tenant_key = '%s' GROUP BY sf.DATA_KEY";
        sql = String.format(sql, tableName, cmicProperties.getHostTenantKey());
        List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        dataList.forEach(rs -> {
            String detailTable = rs.getOrDefault("detailtable", "").toString();
            if (!StringUtils.isBlank(detailTable)) {
                tableSet.add(detailTable);
            }
        });
        return tableSet;
    }

    public int loadDataSource(String[] idArray, MultipleLayerConfig config, List<Map<String, Object>> dataSource) {
        List<String> idList = new ArrayList<>();
        StringBuilder builder = new StringBuilder();
        for (int i = 1; i <= idArray.length; i++) {
            String id = idArray[i - 1];
            builder.append(id);
            if (i % 1000 == 0 || i == idArray.length) {
                idList.add(builder.toString());
                builder = new StringBuilder();
                continue;
            }
            builder.append(",");
        }
        Set<String> tableSet = loadDetailTableSet(config.getTableName());
        log.info("获取到的tableSet：{}", JSON.toJSONString(tableSet));
        for (String idString : idList) {
            List<Map<String, Object>> ds = loadDataSource(tableSet, config.getTableName(), idString);
            dataSource.addAll(ds);
        }
        return tableSet.size();
    }

    public static void main(String[] args) {
        List<Map<String, Object>> aa = new ArrayList<>();
        System.out.println(JSON.toJSONString(aa));
        aa.forEach(obj -> {
            Long mainId = (Long) obj.getOrDefault("form_data_id", null);
            System.out.println("22" + mainId);
            System.out.println((Long) obj.getOrDefault("form_data_id", null));
            System.out.println("22");

        });
    }

    public List<Map<String, Object>> loadDataSource(Set<String> tableSet, String tableName, String ids) {
        String querySql = "select * from " + tableName + " where id in (" + ids + ") and delete_type=0 order by id desc";
        List<Map<String, Object>> dataSource = dataSqlService.eBuilderFromSqlAll(querySql, SourceType.LOGIC);
        Map<String, Map<String, Object>> dataSourceIndex = dataSource.stream().collect(Collectors.toMap(k -> k.get("id").toString(), v -> v));

        for (String table : tableSet) {
            String index = table.substring(table.length() - 1);
            String queryDetail1Sql = "select * from " + table + " where form_data_id in (" + ids + ") and delete_type=0 order by id asc";
            List<Map<String, Object>> dataDetail1 = dataSqlService.eBuilderFromSqlAll(queryDetail1Sql, SourceType.LOGIC);
            log.info("查询子表：{}", JSON.toJSONString(dataDetail1));
            dataDetail1.forEach(rs -> {
                Long mainId = (Long) rs.getOrDefault("form_data_id", null);
                log.info("查询mainId：{}", mainId);
                if (mainId == null) {
                    return;
                }
                Map<String, Object> entity = dataSourceIndex.get(mainId.toString());
                String tableKey = "detail_" + index;
                log.info("tableKey:{}", tableKey);
                List<Map<String, Object>> detailDatas = (List<Map<String, Object>>) entity.getOrDefault(tableKey, new ArrayList<>(20));

                Map<String, Object> detailData = new HashMap<>();
                String[] columnName = rs.keySet().toArray(new String[0]);
                for (String column : columnName) {
                    String newColumn = column.toLowerCase();
                    String key = "dt" + index + "_" + newColumn;
                    String value = rs.getOrDefault(newColumn, "").toString();
                    detailData.put(key, value);
                }

                detailDatas.add(detailData);
                entity.put(tableKey, detailDatas);
            });

        }
        return dataSource;
    }
}
