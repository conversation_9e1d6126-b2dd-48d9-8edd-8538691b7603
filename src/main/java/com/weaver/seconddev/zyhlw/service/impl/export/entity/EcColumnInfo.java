package com.weaver.seconddev.zyhlw.service.impl.export.entity;


import com.weaver.seconddev.zyhlw.service.impl.export.converter.Converter;

import java.io.Serializable;

/**
 * EC字段信息
 *
 * <AUTHOR>
 */
public class EcColumnInfo implements Serializable {
    /**
     * 字段页面显示类型
     */
    private String htmlType;
    /**
     * 浏览框类型
     */
    private Integer selectType;
    /**
     * 字段名称
     */
    private String fieldName;
    /**
     * 字段ID
     */
    private Long fieldId;
    /**
     * 表单ID
     */
    private String billId;
    /**
     * 字段数据库类型
     */
    private String fieldDbType;

    private String sqlText;

    private String showName;
    private String browserModule;
    private String browserType;
    private Converter converter;

    public EcColumnInfo(String htmlType, Integer selectType, String fieldName, Long fieldId, String billId, String fieldDbType, String sqlText, String showName, String browserModule, String browserType) {
        this.htmlType = htmlType;
        this.selectType = selectType;
        this.fieldName = fieldName;
        this.fieldId = fieldId;
        this.billId = billId;
        this.fieldDbType = fieldDbType;
        this.sqlText = sqlText;
        this.showName = showName;
        this.browserModule = browserModule;
        this.browserType = browserType;
    }

    public String getFielddbtype() {
        return fieldDbType;
    }

    public void setFielddbtype(String fieldDbType) {
        this.fieldDbType = fieldDbType;
    }

    public String getHtmlType() {
        return htmlType;
    }

    public void setHtmlType(String htmlType) {
        this.htmlType = htmlType;
    }

    public Integer getSelectType() {
        return selectType;
    }

    public void setSelectType(Integer selectType) {
        this.selectType = selectType;
    }

    public String getBrowserModule() {
        return browserModule;
    }

    public void setBrowserModule(String browserModule) {
        this.browserModule = browserModule;
    }

    public String getBrowserType() {
        return browserType;
    }

    public void setBrowserType(String browserType) {
        this.browserType = browserType;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public Long getFieldId() {
        return fieldId;
    }

    public void setFieldId(Long fieldId) {
        this.fieldId = fieldId;
    }

    public String getBillId() {
        return billId;
    }

    public void setBillId(String billId) {
        this.billId = billId;
    }

    public String getFieldDbType() {
        return fieldDbType;
    }

    public void setFieldDbType(String fieldDbType) {
        this.fieldDbType = fieldDbType;
    }

    public String getSqlText() {
        return sqlText;
    }

    public void setSqlText(String sqlText) {
        this.sqlText = sqlText;
    }

    public String getShowName() {
        return showName;
    }

    public void setShowName(String showName) {
        this.showName = showName;
    }

    public Converter getConverter() {
        return converter;
    }

    public void setConverter(Converter converter) {
        this.converter = converter;
    }
}
