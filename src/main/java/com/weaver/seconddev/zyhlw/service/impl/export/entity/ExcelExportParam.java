package com.weaver.seconddev.zyhlw.service.impl.export.entity;

import com.weaver.teams.domain.user.SimpleEmployee;

import java.io.OutputStream;

/**
 * 导出参数
 */
public class ExcelExportParam {
    private String templateId;
    private String typeId;
    private String idListStr;
    private SimpleEmployee user;

    private OutputStream outputStream;

    public ExcelExportParam(String templateId, String typeId, String idListStr, SimpleEmployee user, OutputStream outputStream) {
        this.templateId = templateId;
        this.typeId = typeId;
        this.idListStr = idListStr;
        this.user = user;
        this.outputStream = outputStream;
    }

    public ExcelExportParam(String templateId, String typeId, String idListStr, SimpleEmployee user) {
        this.templateId = templateId;
        this.typeId = typeId;
        this.idListStr = idListStr;
        this.user = user;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public String getTypeId() {
        return typeId;
    }

    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }

    public String getIdListStr() {
        return idListStr;
    }

    public void setIdListStr(String idListStr) {
        this.idListStr = idListStr;
    }

    public SimpleEmployee getUser() {
        return user;
    }

    public void setUser(SimpleEmployee user) {
        this.user = user;
    }

    public OutputStream getOutputStream() {
        return outputStream;
    }

    public void setOutputStream(OutputStream outputStream) {
        this.outputStream = outputStream;
    }
}