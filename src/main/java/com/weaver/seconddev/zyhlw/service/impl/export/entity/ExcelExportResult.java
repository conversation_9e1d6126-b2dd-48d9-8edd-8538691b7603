package com.weaver.seconddev.zyhlw.service.impl.export.entity;

import com.weaver.seconddev.zyhlw.service.impl.export.exporter.ExcelExport;

/**
 * 执行结果
 *
 * <AUTHOR>
 * @date 2022/8/10
 */
public class ExcelExportResult {
    private byte[] data;
    private String fileName;

    private ExcelExport excelExporter;

    public ExcelExportResult(byte[] data, String fileName) {
        this.data = data;
        this.fileName = fileName;
    }

    public ExcelExportResult(ExcelExport excelExporter, String fileName) {
        this.excelExporter = excelExporter;
        this.fileName = fileName;
    }

    public byte[] getData() {
        return data;
    }

    public void setData(byte[] data) {
        this.data = data;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public ExcelExport getExcelExporter() {
        return excelExporter;
    }

    public void setExcelExporter(ExcelExport excelExporter) {
        this.excelExporter = excelExporter;
    }
}
