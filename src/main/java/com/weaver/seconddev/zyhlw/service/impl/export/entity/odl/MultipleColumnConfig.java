package com.weaver.seconddev.zyhlw.service.impl.export.entity.odl;

import com.weaver.seconddev.zyhlw.service.impl.export.entity.EcColumnInfo;

/**
 * <AUTHOR>
 * @date 2022/11/3
 */
public class MultipleColumnConfig {
    protected Long id;
    protected String supTitleId;
    protected String fieldTitle;
    protected String fieldTitleColor;
    protected String fieldTitleBackGround;
    protected String fieldname;
    protected String checkConvertExpression;
    protected String specialConvert;

    protected EcColumnInfo ecColumnInfo;

    public MultipleColumnConfig(Long id, String fieldTitle, String fieldTitleColor, String fieldTitleBackGround, String fieldname) {
        this.id = id;
        this.fieldTitle = fieldTitle;
        this.fieldTitleColor = fieldTitleColor;
        this.fieldTitleBackGround = fieldTitleBackGround;
        this.fieldname = fieldname;
    }

    public MultipleColumnConfig(Long id, String supTitleId, String fieldname, String fieldTitle, String fieldTitleColor, String fieldTitleBackGround, String specialConvert, String checkConvertExpression) {
        this.id = id;
        this.supTitleId = supTitleId;
        this.fieldname = fieldname;
        this.fieldTitle = fieldTitle;
        this.fieldTitleColor = fieldTitleColor;
        this.fieldTitleBackGround = fieldTitleBackGround;
        this.specialConvert = specialConvert;
        this.checkConvertExpression = checkConvertExpression;
    }


    public Long getId() { return id; }

    public void setId(Long id) { this.id = id; }

    public String getFieldTitle() { return fieldTitle; }

    public void setFieldTitle(String fieldTitle) { this.fieldTitle = fieldTitle; }

    public String getFieldTitleColor() { return fieldTitleColor; }

    public void setFieldTitleColor(String fieldTitleColor) { this.fieldTitleColor = fieldTitleColor; }

    public String getFieldTitleBackGround() { return fieldTitleBackGround; }

    public void setFieldTitleBackGround(String fieldTitleBackGround) { this.fieldTitleBackGround = fieldTitleBackGround; }

    public String getFieldname() { return fieldname; }

    public void setFieldname(String fieldname) { this.fieldname = fieldname; }

    public String getCheckConvertExpression() { return checkConvertExpression; }

    public void setCheckConvertExpression(String checkConvertExpression) { this.checkConvertExpression = checkConvertExpression; }

    public String getSpecialConvert() { return specialConvert; }

    public void setSpecialConvert(String specialConvert) { this.specialConvert = specialConvert; }

    public EcColumnInfo getEcColumnInfo() { return ecColumnInfo; }

    public void setEcColumnInfo(EcColumnInfo ecColumnInfo) { this.ecColumnInfo = ecColumnInfo; }

    public String getSupTitleId() {
        return supTitleId;
    }

    public void setSupTitleId(String supTitleId) {
        this.supTitleId = supTitleId;
    }
}
