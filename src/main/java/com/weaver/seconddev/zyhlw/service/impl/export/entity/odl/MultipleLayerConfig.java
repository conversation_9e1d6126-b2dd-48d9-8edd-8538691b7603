package com.weaver.seconddev.zyhlw.service.impl.export.entity.odl;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 多层模板配置实体
 *
 * <AUTHOR>
 */
public class MultipleLayerConfig {
    private Long id;
    private String fileName;
    private String tableName;
    private String tableId;
    private String sheetName;

    int treeLayerCount = 0;
    int columnCount = 0;

    private Map<String, List<MultipleColumnConfig>> columnConfigIndexQuote = new HashMap<>();
    private List<MultipleTitleConfig> titleConfigs = new ArrayList<>();
    private List<MultipleTitleConfig> treeTitleConfigs = new ArrayList<>();
    private List<MultipleColumnConfig> columnConfigs = new ArrayList<>();
    private List<MultipleColumnConfig> columnConfigsOneLayer = new ArrayList<>();

    public MultipleLayerConfig(Long id, String fileName, String tableName, String tableId, String sheetName) {
        this.id = id;
        this.fileName = fileName;
        this.tableName = tableName;
        this.tableId = tableId;
        this.sheetName = sheetName;
    }

    /**
     * 获取树形结构标题配置信息
     *
     * @param id        标题标识
     * @param treeLayer 树形结构层级
     * @return 返回树形结构标题配置
     */
    public List<MultipleTitleConfig> getTreeMultipleTitleConfig(String id, int treeLayer) {
        if (id == null || id.isEmpty()) {
            getTreeMultipleTitleConfig();
        }
        List<MultipleTitleConfig> treeGroup = new ArrayList<>();
        for (MultipleTitleConfig titleConfig : titleConfigs) {
            String titleId = titleConfig.getTitleId();
            String supTitleId = StringUtils.isNotBlank(titleConfig.getSupTitleId()) ? titleConfig.getSupTitleId() : "0";
            if (id != null && id.equals(supTitleId)) {
                int currentTreeLayer = treeLayer + 1;
                titleConfig.setTreeLayer(currentTreeLayer + titleConfig.getTreeLayer());
                if (currentTreeLayer >= treeLayerCount) {
                    treeLayerCount = currentTreeLayer + 1;
                }
                titleConfig.setColumnNum(columnCount);
                boolean isExistNextLayer = false;
                if (!titleId.isEmpty()) {
                    List<MultipleTitleConfig> treeMultipleTitleConfig = getTreeMultipleTitleConfig(titleId, currentTreeLayer);
                    if (!treeMultipleTitleConfig.isEmpty()) {
                        isExistNextLayer = true;
                    }
                    titleConfig.setSubTitleConfigs(treeMultipleTitleConfig);
                }
                titleConfig.setIsExistNextLayer(isExistNextLayer);
                if (!isExistNextLayer) {
                    List<MultipleColumnConfig> _columnConfigs = columnConfigIndexQuote.get(titleConfig.getTitleId());
                    if (_columnConfigs == null || _columnConfigs.isEmpty()) {
                        columnCount += 1;
                    } else {
                        columnCount += _columnConfigs.size();
                    }
                }
                titleConfig.setColumnCount(columnCount - titleConfig.getColumnNum());
                treeGroup.add(titleConfig);
            }
        }
        return treeGroup;
    }

    public List<MultipleTitleConfig> getTreeMultipleTitleConfig() {
        for (MultipleTitleConfig titleConfig : titleConfigs) {
            titleConfig.setTreeLayer(0);
            titleConfig.setColumnCount(0);
            titleConfig.setColumnNum(0);
        }
        this.treeLayerCount = 0;
        this.columnCount = 0;
        return getTreeMultipleTitleConfig("0", 0);
    }

    public void initTreeTitleConfigs() {
        this.treeTitleConfigs = this.getTreeMultipleTitleConfig();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getTableId() {
        return tableId;
    }

    public void setTableId(String tableId) {
        this.tableId = tableId;
    }

    public String getSheetName() {
        return sheetName;
    }

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }

    public List<MultipleTitleConfig> getTitleConfigs() {
        return titleConfigs;
    }

    public void setTitleConfigs(List<MultipleTitleConfig> titleConfigs) {
        this.titleConfigs = titleConfigs;
    }

    public List<MultipleColumnConfig> getColumnConfigs() {
        return columnConfigs;
    }

    public void setColumnConfigs(List<MultipleColumnConfig> columnConfigs) {
        this.columnConfigs = columnConfigs;
    }

    public Map<String, List<MultipleColumnConfig>> getColumnConfigIndexQuote() {
        return columnConfigIndexQuote;
    }

    public List<MultipleColumnConfig> getColumnConfigByIndex(String titleId) {
        return columnConfigIndexQuote.get(titleId);
    }

    public void setColumnConfigIndexQuote(Map<String, List<MultipleColumnConfig>> columnConfigIndexQuote) {
        this.columnConfigIndexQuote = columnConfigIndexQuote;
    }

    public int getTreeLayerCount() {
        return treeLayerCount;
    }

    public void setTreeLayerCount(int treeLayerCount) {
        this.treeLayerCount = treeLayerCount;
    }

    public int getColumnCount() {
        return columnCount;
    }

    public void setColumnCount(int columnCount) {
        this.columnCount = columnCount;
    }

    public List<MultipleTitleConfig> getTreeTitleConfigs() {
        return treeTitleConfigs;
    }

    public void setTreeTitleConfigs(List<MultipleTitleConfig> treeTitleConfigs) {
        this.treeTitleConfigs = treeTitleConfigs;
    }

    public List<MultipleColumnConfig> getColumnConfigsOneLayer() {
        return columnConfigsOneLayer;
    }

    public void setColumnConfigsOneLayer(List<MultipleColumnConfig> columnConfigsOneLayer) {
        this.columnConfigsOneLayer = columnConfigsOneLayer;
    }
}
