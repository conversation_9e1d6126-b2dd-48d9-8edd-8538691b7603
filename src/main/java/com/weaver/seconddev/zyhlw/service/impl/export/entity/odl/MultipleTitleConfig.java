package com.weaver.seconddev.zyhlw.service.impl.export.entity.odl;

import java.util.ArrayList;
import java.util.List;

public class MultipleTitleConfig {
    protected Long id;
    protected String title;
    protected String titleColor;
    protected String titleBackGround;
    protected String titleRownum;
    protected String titleId;
    protected String supTitleId;
    protected String fieldTitleColor;
    protected String fieldTitleBackGround;
    protected List<MultipleTitleConfig> subTitleConfigs = new ArrayList<>();
    protected List<MultipleColumnConfig> columnConfigs  = new ArrayList<>();;
    protected boolean isExistColumn = false;
    protected boolean isExistNextLayer = false;

    protected int treeLayer = 0;    // 标题树形结构层级
    protected int columnCount = 0;  // 标题所占列数
    protected int columnNum = 0;    // 当前标题列数
    protected int orderNum = 0;     // 标题显示顺序

    public MultipleTitleConfig(Long id, String title, String titleColor, String titleBackGround, String titleRownum, String titleId, String supTitleId, String fieldTitleColor, String fieldTitleBackGround) {
        this.id = id;
        this.title = title;
        this.titleColor = titleColor;
        this.titleBackGround = titleBackGround;
        this.titleRownum = titleRownum;
        this.titleId = titleId;
        this.supTitleId = supTitleId;
        this.fieldTitleColor = fieldTitleColor;
        this.fieldTitleBackGround = fieldTitleBackGround;
    }

    public Long getId() { return id; }

    public void setId(Long id) { this.id = id; }

    public String getTitle() { return title; }

    public void setTitle(String title) { this.title = title; }

    public String getTitleColor() { return titleColor; }

    public void setTitleColor(String titleColor) { this.titleColor = titleColor; }

    public String getTitleBackGround() { return titleBackGround; }

    public void setTitleBackGround(String titleBackGround) { this.titleBackGround = titleBackGround; }

    public String getTitleRownum() { return titleRownum; }

    public void setTitleRownum(String titleRownum) { this.titleRownum = titleRownum; }

    public String getTitleId() { return titleId; }

    public void setTitleId(String titleId) { this.titleId = titleId; }

    public String getSupTitleId() { return supTitleId; }

    public void setSupTitleId(String supTitleId) { this.supTitleId = supTitleId; }

    public String getFieldTitleColor() { return fieldTitleColor; }

    public void setFieldTitleColor(String fieldTitleColor) { this.fieldTitleColor = fieldTitleColor; }

    public String getFieldTitleBackGround() { return fieldTitleBackGround; }

    public void setFieldTitleBackGround(String fieldTitleBackGround) { this.fieldTitleBackGround = fieldTitleBackGround; }

    public List<MultipleColumnConfig> getColumnConfigs() {
        return columnConfigs;
    }

    public void setColumnConfigs(List<MultipleColumnConfig> columnConfigs) {
        this.columnConfigs = columnConfigs;
    }

    public boolean getIsExistColumn() { return isExistColumn; }

    public void setIsExistColumn(boolean isExistColumn) { this.isExistColumn = isExistColumn; }

    public List<MultipleTitleConfig> getSubTitleConfigs() { return subTitleConfigs; }

    public void setSubTitleConfigs(List<MultipleTitleConfig> subTitleConfigs) { this.subTitleConfigs = subTitleConfigs; }

    public int getTreeLayer() {
        return treeLayer;
    }

    public void setTreeLayer(int treeLayer) {
        this.treeLayer = treeLayer;
    }

    public int getColumnCount() {
        return columnCount;
    }

    public void setColumnCount(int columnCount) {
        this.columnCount = columnCount;
    }

    public int getColumnNum() {
        return columnNum;
    }

    public void setColumnNum(int columnNum) {
        this.columnNum = columnNum;
    }

    public int getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(int orderNum) {
        this.orderNum = orderNum;
    }

    public boolean getIsExistNextLayer() { return isExistNextLayer; }

    public void setIsExistNextLayer(boolean isExistNextLayer) { this.isExistNextLayer = isExistNextLayer; }
}
