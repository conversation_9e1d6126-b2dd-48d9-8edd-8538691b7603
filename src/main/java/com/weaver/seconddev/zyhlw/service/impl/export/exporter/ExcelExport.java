package com.weaver.seconddev.zyhlw.service.impl.export.exporter;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.stereotype.Service;

@Service
public class ExcelExport implements ExcelExporter  {
    private int dataCount = 0;
    private int currentDownNum = 0;

    private HSSFWorkbook workbook = new HSSFWorkbook();

    @Override
    public byte[] export(Object... args) throws Exception  {
        return workbook.getBytes();
    }

    public int getDataCount() {
        return dataCount;
    }

    public void setDataCount(int dataCount) {
        this.dataCount = dataCount;
    }

    public int getCurrentDownNum() {
        return currentDownNum;
    }

    public void setCurrentDownNum(int currentDownNum) {
        this.currentDownNum = currentDownNum;
    }

    public HSSFWorkbook getWorkbook() {
        return workbook;
    }

    public void setWorkbook(HSSFWorkbook workbook) {
        this.workbook = workbook;
    }
}
