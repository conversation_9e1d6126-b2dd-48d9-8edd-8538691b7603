package com.weaver.seconddev.zyhlw.service.impl.export.exporter;

import com.alibaba.fastjson.JSON;
import com.weaver.seconddev.zyhlw.service.impl.export.converter.Converter;
import com.weaver.seconddev.zyhlw.service.impl.export.entity.EcColumnInfo;
import com.weaver.seconddev.zyhlw.service.impl.export.entity.odl.MultipleColumnConfig;
import com.weaver.seconddev.zyhlw.service.impl.export.entity.odl.MultipleLayerConfig;
import com.weaver.seconddev.zyhlw.service.impl.export.entity.odl.MultipleTitleConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFPalette;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class MultipleLayerXlsExporter extends ExcelExport implements ExcelExporter {
    protected final HSSFWorkbook workbook = new HSSFWorkbook();
    private final MultipleLayerConfig config;
    private final List<Map<String, Object>> dataSource;
    private final int detailCount;
    private short index = 20;

    private int dataCount = 0;
    private int currentDownNum = 0;

    public MultipleLayerXlsExporter(MultipleLayerConfig config, List<Map<String, Object>> dataSource, int detailCount) {
        this.config = config;
        this.detailCount = detailCount;
        this.dataSource = dataSource;
    }

    @Override
    public byte[] export(Object... args) throws Exception {
        fillHeader();
        fillBody();
        super.setWorkbook(workbook);
        return workbook.getBytes();
    }

    protected void fillHeader() {
        long beginTimeMillis = System.currentTimeMillis();

        int numberOfSheets = workbook.getNumberOfSheets();
        String sheetName = getSheetName(config.getSheetName());
        HSSFSheet sheet;
        if (numberOfSheets == 0) {
            sheet = workbook.createSheet(sheetName);
        } else {
            sheet = workbook.getSheet(sheetName);
        }
        List<MultipleTitleConfig> treeTitleConfigs = config.getTreeTitleConfigs();
        List<MultipleColumnConfig> columnConfigsOneLayer = config.getColumnConfigsOneLayer();
        int treeLayerCount = config.getTreeLayerCount();
        for (int i = 0; i < treeLayerCount; i++) {
            sheet.createRow(i);
        }
        fillTreeHeader(treeTitleConfigs);
        fillHeader(columnConfigsOneLayer);
        log.info("填充多层标题表头耗时: " + (System.currentTimeMillis() - beginTimeMillis) + " ms");
    }

    protected void fillHeader(List<MultipleColumnConfig> columnConfigs) {
        int numberOfSheets = workbook.getNumberOfSheets();
        String sheetName = getSheetName(config.getSheetName());
        HSSFSheet sheet;
        if (numberOfSheets == 0) {
            sheet = workbook.createSheet(sheetName);
        } else {
            sheet = workbook.getSheet(sheetName);
        }
        int beginCol = config.getColumnCount();
        int treeLayerCount = config.getTreeLayerCount();
        Row row1 = getRow(sheet, 0);
        for (int i = 0; i < columnConfigs.size(); i++) {
            int currentColIndex = beginCol + i;
            MultipleColumnConfig columnConfig = columnConfigs.get(i);
            String fieldTitle = columnConfig.getFieldTitle();
            String fieldTitleColor = columnConfig.getFieldTitleColor();
            String fieldTitleBackGround = columnConfig.getFieldTitleBackGround();

            Font fieldTitleFont = getFont(workbook, "宋体", true, (short) 10);
            fieldTitleFont.setColor(getColor(workbook, fieldTitleColor).getIndex());
            CellStyle newFieldTitleStyle = getBorderStyle(workbook);
            newFieldTitleStyle.setFont(fieldTitleFont);
            newFieldTitleStyle.setFillForegroundColor(getColor(workbook, fieldTitleBackGround).getIndex());
            newFieldTitleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            Cell fieldTitleCell = row1.createCell(currentColIndex);
            fieldTitleCell.setCellValue(fieldTitle);
            fieldTitleCell.setCellStyle(newFieldTitleStyle);
            if (treeLayerCount > 0) {
                for (int rowIndex = 1; rowIndex < treeLayerCount; rowIndex++) {
                    Row row = getRow(sheet, rowIndex);
                    Cell emptyCell = row.createCell(currentColIndex);
                    emptyCell.setCellStyle(newFieldTitleStyle);
                }
                sheet.addMergedRegion(new CellRangeAddress(0, treeLayerCount - 1, currentColIndex, currentColIndex));
            }
        }
    }

    protected void fillTreeHeader(List<MultipleTitleConfig> titleConfigs) {
        int numberOfSheets = workbook.getNumberOfSheets();
        String sheetName = getSheetName(config.getSheetName());
        HSSFSheet sheet;
        if (numberOfSheets == 0) {
            sheet = workbook.createSheet(sheetName);
        } else {
            sheet = workbook.getSheet(sheetName);
        }
        for (MultipleTitleConfig titleConfig : titleConfigs) {
            int rowIndex = titleConfig.getTreeLayer() - 1;
            if (rowIndex > -1) {
                Row row = getRow(sheet, rowIndex);
                String titleFontColor = titleConfig.getTitleColor();
                String titleBackGround = titleConfig.getTitleBackGround();

                Font titleFont = getFont(workbook, "宋体", true, (short) 11);
                titleFont.setColor(getColor(workbook, titleFontColor).getIndex());
                CellStyle newTitleStyle = getBorderStyle(workbook);
                newTitleStyle.setFont(titleFont);
                newTitleStyle.setFillForegroundColor(getColor(workbook, titleBackGround).getIndex());
                newTitleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

                // 设置头标题
                String title = titleConfig.getTitle();
                int columnNum = titleConfig.getColumnNum();
                Cell titleCell = row.createCell(columnNum);
                titleCell.setCellValue(title);
                titleCell.setCellStyle(newTitleStyle);

                int columnCount = titleConfig.getColumnCount();
                for (int i = columnNum + 1; i < columnNum + columnCount; i++) {
                    Cell emptyCell = row.createCell(i);
                    emptyCell.setCellStyle(newTitleStyle);
                }
                if ((columnNum + columnCount - 1) > columnNum) {
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, columnNum, columnNum + columnCount - 1));
                }
                if (!titleConfig.getIsExistNextLayer()) {
                    int currentRowIndex = rowIndex + 1;
                    Row row1 = getRow(sheet, currentRowIndex);
                    String fieldTitleColorDefault = titleConfig.getFieldTitleColor();
                    String fieldTitleBackGroundDefault = titleConfig.getFieldTitleBackGround();
                    List<MultipleColumnConfig> columnConfigs = config.getColumnConfigByIndex(titleConfig.getTitleId());
                    if (columnConfigs != null) {
                        for (int i = 0; i < columnConfigs.size(); i++) {
                            int currentColIndex = columnNum + i;

                            MultipleColumnConfig columnConfig = columnConfigs.get(i);
                            String fieldTitle = columnConfig.getFieldTitle();
                            String fieldTitleColor = columnConfig.getFieldTitleColor();
                            String fieldTitleBackGround = columnConfig.getFieldTitleBackGround();

                            if (fieldTitleColor == null || fieldTitleColor.isEmpty()) {
                                fieldTitleColor = fieldTitleColorDefault;
                            }
                            if (fieldTitleBackGround == null || fieldTitleBackGround.isEmpty()) {
                                fieldTitleBackGround = fieldTitleBackGroundDefault;
                            }

                            Font fieldTitleFont = getFont(workbook, "宋体", true, (short) 10);
                            fieldTitleFont.setColor(getColor(workbook, fieldTitleColor).getIndex());
                            CellStyle newFieldTitleStyle = getBorderStyle(workbook);
                            newFieldTitleStyle.setFont(fieldTitleFont);
                            newFieldTitleStyle.setFillForegroundColor(getColor(workbook, fieldTitleBackGround).getIndex());
                            newFieldTitleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

                            Cell fieldTitleCell = row1.createCell(currentColIndex);
                            fieldTitleCell.setCellValue(fieldTitle);
                            fieldTitleCell.setCellStyle(newFieldTitleStyle);

                            int treeLayerCount = config.getTreeLayerCount();
                            for (int overRowIndex = currentRowIndex + 1; overRowIndex < treeLayerCount; overRowIndex++) {
                                Row overRow = getRow(sheet, overRowIndex);
                                Cell emptyCell = overRow.createCell(currentColIndex);
                                emptyCell.setCellStyle(newFieldTitleStyle);
                            }
                            if (currentRowIndex < treeLayerCount - 1) {
                                sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, treeLayerCount - 1, currentColIndex, currentColIndex));
                            }
                        }
                    }
                } else {
                    List<MultipleTitleConfig> subInterpretedMultipleTitleConfig = titleConfig.getSubTitleConfigs();
                    if (!subInterpretedMultipleTitleConfig.isEmpty()) {
                        fillTreeHeader(titleConfig.getSubTitleConfigs());
                    }
                }
            }
        }
    }

    private String getSheetName(String sheetName) {
        return StringUtils.isNotBlank(sheetName) ? sheetName : "sheet1";
    }

    protected void fillBody() {
        log.info("MultipleLayerXlsExporter.fillBody:" + JSON.toJSONString(config));
        long beginTimeMillis = System.currentTimeMillis();
        HSSFSheet sheet = workbook.getSheetAt(0);
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setWrapText(true);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);

        List<MultipleColumnConfig> columnConfigs = new ArrayList<>();
        for (MultipleTitleConfig titleConfig : config.getTitleConfigs()) {
            List<MultipleColumnConfig> _columnConfigs = config.getColumnConfigByIndex(titleConfig.getTitleId());
            if (_columnConfigs == null) {
                _columnConfigs = new ArrayList<>();
            }
            columnConfigs.addAll(_columnConfigs);
        }
        columnConfigs.addAll(config.getColumnConfigsOneLayer());
        int beginRowIndex = config.getTreeLayerCount();
        beginRowIndex = beginRowIndex == 0 ? 1 : beginRowIndex;
        this.setDataCount(dataSource.size());
        this.setCurrentDownNum(0);
        for (Map<String, Object> dataRow : dataSource) {
            List<Map<String, Object>>[] arrays = new ArrayList[detailCount + 1];
            int rowSpan = 1;
            for (int j = 1; j <= detailCount; j++) {
                List<Map<String, Object>> dataRowDetail = (List<Map<String, Object>>) dataRow.get("detail_" + j);
                arrays[j] = dataRowDetail;
                if (!Objects.isNull(dataRowDetail)) {
                    rowSpan = Math.max(rowSpan, dataRowDetail.size());
                }
            }
            for (int j = beginRowIndex, length = beginRowIndex + rowSpan; j < length; j++) {
                Row row = sheet.createRow(j);
                for (int k = 0, len = columnConfigs.size(); k < len; k++) {
                    Cell cell = row.createCell(k);
                    cell.setCellStyle(cellStyle);
                }
            }

            for (int k = 0, len = columnConfigs.size(); k < len; k++) {
                MultipleColumnConfig columnConfig = columnConfigs.get(k);
                EcColumnInfo ecColumnInfo = columnConfig.getEcColumnInfo();
                log.info("MultipleLayerXlsExporter.fillBody:{}", JSON.toJSONString(columnConfig));
                Converter converter = null;
                if (ecColumnInfo != null) {
                    converter = ecColumnInfo.getConverter();
                }
                String column = columnConfig.getFieldname();
                if (column.startsWith("dt")) {
                    int index = column.charAt(2) - '0';
                    List<Map<String, Object>> array = arrays[index];
                    if (!Objects.isNull(array)) {
                        for (int j = 0; j < array.size(); j++) {
                            Row row = sheet.getRow(beginRowIndex + j);
                            Cell cell = row.getCell(k);
                            Map<String, Object> detail = array.get(j);
                            String value = detail.getOrDefault(column, "").toString();
                            if (converter != null) {
                                value = converter.convert(value);
                            }
                            cell.setCellValue(value);
                        }
                    }
                } else {
                    Row row = getRow(sheet, beginRowIndex);
                    Cell cell = row.getCell(k);
                    String value = dataRow.getOrDefault(column, "").toString();
                    if (converter != null) {
                        value = converter.convert(value);
                    }
                    cell.setCellValue(value);
                }
            }

            // 合并单元格
            for (int k = 0, len = columnConfigs.size(); k < len; k++) {
                MultipleColumnConfig interpretedColumnConfig = columnConfigs.get(k);
                String column = interpretedColumnConfig.getFieldname();
                if (rowSpan > 1 && !column.startsWith("dt")) {
                    int lastRow = beginRowIndex + rowSpan - 1;
                    CellRangeAddress cellAddresses = new CellRangeAddress(beginRowIndex, lastRow, k, k);
                    sheet.addMergedRegion(cellAddresses);
                }
            }
            beginRowIndex += rowSpan;
            this.setCurrentDownNum(this.getCurrentDownNum() + 1);
        }
        resize(sheet);
        log.info("填充Excel内容耗时: " + (System.currentTimeMillis() - beginTimeMillis) + " ms");
    }

    public CellStyle getBorderStyle(HSSFWorkbook workbook) {
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setWrapText(true);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        return cellStyle;
    }

    private Font getFont(HSSFWorkbook workbook, String name, boolean bold, short size) {
        Font font = workbook.createFont();
        font.setFontName(name);
        font.setBold(bold);
        font.setFontHeightInPoints(size);
        return font;
    }

    private HSSFColor getColor(HSSFWorkbook workbook, String config) {
        String[] colorNumber = config.split(",");
        HSSFPalette customPalette = workbook.getCustomPalette();
        HSSFColor color = customPalette.findColor(
                Integer.valueOf(colorNumber[0]).byteValue(),
                Integer.valueOf(colorNumber[1]).byteValue(),
                Integer.valueOf(colorNumber[2]).byteValue()
        );
        if (color == null) {
            customPalette.setColorAtIndex(index,
                    Integer.valueOf(colorNumber[0]).byteValue(),
                    Integer.valueOf(colorNumber[1]).byteValue(),
                    Integer.valueOf(colorNumber[2]).byteValue()
            );
            return customPalette.getColor(index++);
        }
        return color;
    }

    private Row getRow(HSSFSheet sheet, int index) {
        int rowNum = sheet.getPhysicalNumberOfRows();
        return rowNum > index ? sheet.getRow(index) : sheet.createRow(index);
    }

    private void resize(HSSFSheet sheet) {
        Row r = getRow(sheet, 0);
        r.setHeight((short) (4 * 128 + 200));
        Map<Integer, Integer> maxWidth = new HashMap<>();
        int rowCount = sheet.getPhysicalNumberOfRows();
        int cellCount = 0;
        for (int i = config.getTreeLayerCount(); i < rowCount; i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                continue;
            }
            cellCount = row.getPhysicalNumberOfCells();
            for (int j = 0; j < cellCount; j++) {
                Cell cell = row.getCell(j);
                if (cell == null) {
                    continue;
                }
                int length = cell.getStringCellValue().getBytes().length * 256 + 200;
                //这里把宽度最大限制到15000
                if (length > 15000) {
                    length = 15000;
                }
                maxWidth.put(j, Math.max(length, maxWidth.getOrDefault(j, 2000)));
            }
        }
        for (int i = 0; i < cellCount; i++) {
            sheet.setColumnWidth(i, maxWidth.get(i));
        }
    }

    public HSSFWorkbook getWorkbook() {
        return workbook;
    }

    public int getDataCount() {
        return dataCount;
    }

    public void setDataCount(int dataCount) {
        this.dataCount = dataCount;
    }

    public int getCurrentDownNum() {
        return currentDownNum;
    }

    public void setCurrentDownNum(int currentDownNum) {
        this.currentDownNum = currentDownNum;
    }
}
