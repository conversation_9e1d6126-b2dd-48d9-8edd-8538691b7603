package com.weaver.seconddev.zyhlw.service.impl.export.service;


import com.weaver.seconddev.zyhlw.service.impl.export.entity.ExcelExportParam;
import com.weaver.seconddev.zyhlw.service.impl.export.entity.ExcelExportResult;

/**
 * 导出服务
 *
 * <AUTHOR>
 */
public interface ExportService {

    /**
     * 执行导出
     *
     * @param param 导出参数
     * @return 数据流
     * @throws Exception 导出过程出现的异常
     */
    ExcelExportResult doExport(ExcelExportParam param) throws Exception;

    /**
     * 设置异步操作
     *
     * @return 当前对象
     */
    ExportService async();

    /**
     * 是否为异步服务
     *
     * @return true-异步, false-同步
     */
    boolean isAsync();

    /**
     * 获取导出文件后缀
     *
     * @return .xls-03版Excel文件后缀, .xlsx-07版Excel文件后缀
     */
    String getFileSuffix();
}
