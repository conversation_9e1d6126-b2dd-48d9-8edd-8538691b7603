package com.weaver.seconddev.zyhlw.service.impl.export.service;

import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.impl.DataSqlServiceImpl;
import com.weaver.seconddev.zyhlw.service.impl.export.dao.MultipleLayerExportConfigDao;
import com.weaver.seconddev.zyhlw.service.impl.export.entity.ExcelExportParam;
import com.weaver.seconddev.zyhlw.service.impl.export.entity.ExcelExportResult;
import com.weaver.seconddev.zyhlw.service.impl.export.entity.odl.MultipleLayerConfig;
import com.weaver.seconddev.zyhlw.service.impl.export.exporter.MultipleLayerXlsExporter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 多层标题模板导出服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MultipleLayerExportServiceImpl implements ExportService {
    private boolean async = false;

    @Resource
    private MultipleLayerExportConfigDao multipleLayerExportConfigDao;

    @Override
    public ExcelExportResult doExport(ExcelExportParam param) {
        log.info("执行导出动作,{}", multipleLayerExportConfigDao);
        StopWatch stopWatch = new StopWatch();
        try {
            stopWatch.start("加载多层标题导出配置");
            MultipleLayerConfig config = multipleLayerExportConfigDao.loadConfigByType(Integer.parseInt(param.getTypeId()));
            stopWatch.stop();

            stopWatch.start("加载导出数据源");
            String ids = param.getIdListStr();
            String[] idArray = ids.split(",");
            List<Map<String, Object>> dataSource = new ArrayList<>(idArray.length);
            log.info("加载导出数据源,config:{}", JSON.toJSONString(config));
            int detailCount = multipleLayerExportConfigDao.loadDataSource(idArray, config, dataSource);
            stopWatch.stop();
            log.info("加载导出数据源,detailCount:{},config:{}, dataSource:{}", detailCount, JSON.toJSONString(config), JSON.toJSONString(dataSource));

            stopWatch.start("导出数据");
            MultipleLayerXlsExporter exporter = new MultipleLayerXlsExporter(config, dataSource, detailCount);
            byte[] export = exporter.export();
            stopWatch.stop();

            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            String dateStr = format.format(new Date());
            String fileName = config.getFileName() + "-" + param.getUser().getUsername() + "-" + dateStr + ".xls";
            exporter.getWorkbook().write(param.getOutputStream());
            return new ExcelExportResult(export, fileName);
        } catch (Exception e){
            e.printStackTrace();
            log.info("导出异常：" + e + "," + e.getMessage());
            return null;
        } finally {
            log.info("Excel导出结束");
            StopWatch.TaskInfo[] taskInfo = stopWatch.getTaskInfo();
            for (StopWatch.TaskInfo info : taskInfo) {
                log.info(info.getTaskName() + "花费时间: " + info.getTimeMillis() + "ms");
            }
        }
    }

    @Override
    public ExportService async() {
        this.async = true;
        return this;
    }

    @Override
    public boolean isAsync() {
        return this.async;
    }

    @Override
    public String getFileSuffix() {
        return ".xls";
    }


}
