package com.weaver.seconddev.zyhlw.service.impl.export.service;


import com.weaver.seconddev.zyhlw.service.impl.export.converter.CheckBoxConverter;
import com.weaver.seconddev.zyhlw.service.impl.export.converter.Converter;
import com.weaver.seconddev.zyhlw.service.impl.export.converter.RichTextConverter;
import com.weaver.seconddev.zyhlw.service.impl.export.dao.EcTableInfoDao;
import com.weaver.seconddev.zyhlw.service.impl.export.entity.EcColumnInfo;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/8/5
 */
@Component
public class TableColumnAnalyser {
    private static final String KEY = "SPECIAL_CONVERTER:";
    private final EcTableInfoDao ecTableInfoDao = new EcTableInfoDao();

    /**
     * 多行行文本
     */
    private static final int HTML_TYPE_MULTI_TEXT = 2;
    /**
     * 浏览框
     */
    private static final int HTML_TYPE_VIEW = 3;
    /**
     * check框
     */
    private static final int HTML_TYPE_CHECK = 4;
    /**
     * 选择框
     */
    private static final int HTML_TYPE_SELECT = 5;
    private static final int DB_TYPE_CUSTOMIZE = 161;
    private final String tableId;

    private Map<String, EcColumnInfo> ecColumnInfoMap;

    public TableColumnAnalyser(String tableId) {
        this.tableId = tableId;
    }


    public void loadTableInfo() {
        this.ecColumnInfoMap = ecTableInfoDao.loadTableInfoByTableId(tableId);
    }

    public EcColumnInfo getColumnInfo(String columnName) {
        return ecColumnInfoMap.get(columnName);
    }



    public Map<String, EcColumnInfo> getEcColumnInfoMap() {
        return ecColumnInfoMap;
    }
    public EcColumnInfo getEcColumnInfo(String fieldnameKey) {
        return ecColumnInfoMap.get(fieldnameKey);
    }

    public void setEcColumnInfoMap(Map<String, EcColumnInfo> ecColumnInfoMap) {
        this.ecColumnInfoMap = ecColumnInfoMap;
    }
}
