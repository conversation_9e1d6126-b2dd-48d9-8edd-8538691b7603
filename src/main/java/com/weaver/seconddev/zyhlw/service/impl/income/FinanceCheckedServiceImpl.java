package com.weaver.seconddev.zyhlw.service.impl.income;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.ebuilder.form.client.entity.obj.Obj;
import com.weaver.ebuilder.form.client.service.emobile.IEtFormDatasetService;
import com.weaver.framework.rpc.annotation.RpcReference;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.service.income.IFinanceCheckedService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.SQLUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.util.*;

@Service
@Slf4j
public class FinanceCheckedServiceImpl implements IFinanceCheckedService {
    private final String simpleName = FinanceCheckedServiceImpl.class.getSimpleName();

    @Resource
    CmicProperties cmicProperties;

    @Resource
    IDataSqlService dataSqlService;

    @Resource
    IDataBaseService dataBaseService;

    @Resource
    IOpenPlatformService iOpenPlatformService;


    @RpcReference(group = "ebuilderform")
    IEtFormDatasetService iEtFormDatasetService;

    @Resource
    IDataBaseService iDataBaseService;

    /**
     * 判断是否有在途的红冲或者作废的发票
     * @param hongCfpjeStr 红冲或者作废的发票号码
     * @return
     */
    @Override
    public Map<String, String> addFlow(String hongCfpjeStr) {
        String arrhczf = iDataBaseService.getBaseDataValue("发票红冲及作废流程", "发票红冲及作废流程节点");
        String tableNameChild = iDataBaseService.getBaseDataValue("发票红冲及作废流程表", "发票红冲及作废流程");  //红冲表，和作废表
        log.info("进入方法addFlow");

        Map<String, String> resultMap = new HashMap<>();
        String[] hong_cfpje = hongCfpjeStr.split(",");//发票号码
        String[] arr = arrhczf.split(","); //节点ID
        String[] biam = tableNameChild.split(","); //红冲表，和作废表

        resultMap.put("msg", "成功");
        resultMap.put("code", "0");
        Set<Integer> list = new HashSet<>();
        for (int i = 0; i < hong_cfpje.length; i++) {
            for (String value : biam) {
                String sql = "select t1.* from " + value + " t1," +
                        value + "_dt1 t2  where  t2.mainid=t1.id  and  t2." + (value.equals(biam[1]) ? "hong_cfpje" : "fa_phm") + " in(" + hong_cfpje[i] + ")";
                log.info("打印SQL:" + sql);
                List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);

                for (Map<String, Object> map : dataList) {
                    String sql2 = "select distinct userid,nodeid,usertype,agenttype,agentorbyagentid,isremark,showorder,id,groupid " +
                            "from workflow_currentoperator where (isremark in ('0','1','5','7','8','9','11') or (isremark='4' and viewtype=0))  " +
                            "and requestid=" + map.get("requestid").toString() + " order by isremark,groupid,showorder asc ,id asc"; //无返回值则为归档
                    log.info("addFlow打印SQL" + sql);
                    List<Map<String, Object>> dataList2 = dataSqlService.eBuilderFromSqlAll(sql2, SourceType.LOGIC);

                    for (Map<String, Object> map2 : dataList2) {
                        for (String s : arr) {
                            if (s.equals(map2.get("nodeid").toString())) {
                                list.add(i + 1);
                                log.info("错误行" + StringUtils.strip(list.toString(), "[]") + "：不能选择正在红冲或作废申请中的发票");
                                resultMap.put("msg", "错误行" + StringUtils.strip(list.toString(), "[]") + "：不能选择正在红冲或作废申请中的发票");
                                resultMap.put("code", "1");
                            }
                        }
                    }
                }
            }
        }
        return resultMap;
    }


    /**
     * 勾稽操作时，判断发票和应收单关联信息
     *
     * @param arrStr
     * @return
     */
    @Override
    public Map<String, Object> newSelverify(String arrStr) {
        log.info("进入方法NewSelverify方法");
        Map<String, Object> map3 = new HashMap<>(); //返回值
        map3.put("msg", "成功");
        map3.put("code", "0");
        try {
            String arr = URLDecoder.decode(arrStr, "utf-8");
            JSONArray jsonArray = JSONObject.parseArray(arr);
            log.info(JSON.toJSONString(jsonArray));
            Map<String, Map<String, String>> pxxk_NewMap = jsonArrayToMap(jsonArray);
            log.info("执行进入方法NewSelverify方法，获取pxxk_NewMap数据：" + JSONObject.toJSONString(pxxk_NewMap));
            for (String key : pxxk_NewMap.keySet()) {
                // 获取集合中发票信息
                Map<String, String> childMap = pxxk_NewMap.get(key);
                //应收冻结金额
                BigDecimal ysdo_jje = SQLUtil.parseBigDecimal(childMap.getOrDefault("do_jje", "0"));
                //应收未开票金额
                BigDecimal wkpje = SQLUtil.parseBigDecimal(childMap.getOrDefault("wkpje", "0"));
                //发票冻结金额
                BigDecimal fpdo_jje = SQLUtil.parseBigDecimal(childMap.getOrDefault("fpdo_jje", "0"));

                //发票未勾稽金额
                BigDecimal wei_gjje = SQLUtil.parseBigDecimal(childMap.getOrDefault("wei_gjje", "0"));
                //发票编号
                String fa_phm = childMap.getOrDefault("fa_phm", "");
                //该发票下面的应收编号
                String ying_scode = childMap.getOrDefault("ying_scode", "");
                //本次勾稽金额
                BigDecimal ben_gjje = SQLUtil.parseBigDecimal(childMap.getOrDefault("ben_gjje", "0"));
                if (ben_gjje.compareTo(wkpje.subtract(ysdo_jje)) > 0) {
                    //本次勾稽的金额不能大于关联应收未开票金额+冻结金额
                    String msg = "发票号码:" + fa_phm + "，关联的应收单:" + ying_scode
                            + "中，本次勾稽金额不能大于应收单未开票金额（其中应收单" + ysdo_jje + "元冻结中）";
                    map3.put("code", "1");
                    map3.put("msg", msg);
                } else if (wkpje.compareTo(wei_gjje.subtract(fpdo_jje)) > 0) {
                    //本次勾稽的金额不能大于关联发票未开票金额+冻结金额
                    if (ben_gjje.compareTo(wei_gjje.subtract(fpdo_jje)) > 0) {
                        String msg = "发票号码:" + fa_phm + "，关联的应收单:" + ying_scode
                                + "中，本次勾稽金额不能大于发票未勾稽金额（其中" + fpdo_jje + "元冻结中）";
                        map3.put("code", "1");
                        map3.put("msg", msg);

                    } else if (ben_gjje.compareTo(wkpje) > 0) {
                        String msg = "发票号码:" + fa_phm + "，关联的应收单:" + ying_scode
                                + "中，本次勾稽金额不能大于发票未开票金额";
                        map3.put("code", "1");
                        map3.put("msg", msg);

                    }
                }

            }

            //获取以应收的维度统计的数据
            Map<String, Map<String, String>> yingshou_NewMap = jsonArrayToYingShouMap(jsonArray);
            for (String key : yingshou_NewMap.keySet()) {
                // 获取集合中应收信息
                Map<String, String> childMap = yingshou_NewMap.get(key);
                //应收冻结金额
                BigDecimal ysdo_jje = SQLUtil.parseBigDecimal(childMap.getOrDefault("do_jje", "0"));
                //应收未开票金额
                BigDecimal wkpje = SQLUtil.parseBigDecimal(childMap.getOrDefault("wkpje", "0"));

                //应收编号
                String ying_scode = childMap.getOrDefault("ying_scode", "");

                //本次勾稽金额
                BigDecimal ben_gjje = SQLUtil.parseBigDecimal(childMap.getOrDefault("ben_gjje", "0"));
                if (ben_gjje.compareTo(wkpje.subtract(ysdo_jje)) > 0) {
                    //本次勾稽的金额不能大于关联应收未开票金额+冻结金额
                    String msg = "应收单:" + ying_scode + "中，本次勾稽金额不能大于应收单未开票金额（其中应收单" + ysdo_jje + "元冻结中）";
                    map3.put("code", "1");
                    map3.put("msg", msg);

                }
            }

        } catch (Exception e) {
            log.info("错误！" + e.getMessage());
        }
        return map3;
    }


    /**
     * 判断相关勾稽记录是否存在
     *
     * @param gouji_idStr
     * @return
     */
    @Override
    public Map<String, String> selFlow(String gouji_idStr) {
        log.info("进入SelFlow");
        Map<String, String> map = new HashMap<>();
        String[] gouji_id = gouji_idStr.split(",");

        List<Integer> list = new ArrayList();
        for (int g = 0; g < gouji_id.length; g++) {
            String detailSql = "select * from uf_fa_pgjb_dt1 where id in(" + gouji_id[g] + ")";
            List<Map<String, Object>> detailList = dataSqlService.eBuilderFromSqlAll(detailSql, SourceType.LOGIC);
            map.put("msg", "成功！");
            map.put("code", "0");
            for (Map<String, Object> detailMap : detailList) {
                if (detailMap.get("fa_gdlx").toString().equals("2")) {
                    list.add(g + 1);
                    map.put("msg", "错误行：" + StringUtils.strip(list.toString(), "[]") + "，该条勾稽记录已经被反勾稽或反勾稽审批中");
                    map.put("code", "1");
                }
            }
        }
        return map;
    }


    /**
     * 获取发票最大Id值
     * @return
     */
    @Override
    public Integer GetMaxId() {
        Integer maxId = 0;
        log.info("进入GetMaxId");
        Map<String, String> map = new HashMap<>();
        String sql = "select max(id) as id from uf_ke_hxxb";
        List<Map<String, Object>> detailList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        if (!detailList.isEmpty()) {
            String currentId = detailList.get(0).get("id").toString();
            maxId = Integer.valueOf(currentId + 1);
        }
        return maxId;
    }

    /**
     * 去重Key   相加value
     */
    public Map<String, Object> listmap(List<Map<String, Object>> list1) {
        Map<String, Object> mapAll = new HashMap<String, Object>();
        for (Map<String, Object> map : list1) {
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String name = entry.getKey();
                Object score = entry.getValue();
                Object scoreAll = mapAll.get(entry.getKey());
                if (scoreAll == null) {
                    mapAll.put(name, score);
                } else {
                    scoreAll = ((BigDecimal) scoreAll).add(((BigDecimal) score));
                    mapAll.put(name, scoreAll);
                }
            }
        }
        Map<String, Object> map = new HashMap<>();
        for (Map.Entry<String, Object> entry : mapAll.entrySet()) {
            //如果需要将map再做list，在这里处理
            map.put(entry.getKey(), entry.getValue());
        }
        log.info("打印Map:" + JSON.toJSONString(map));
        return map;
    }

    /**
     * @param jsonArray 以发票的维度统计获取每张发票对应的应收和勾稽判断
     * @return 以发票的维度统计获取每张发票对庆的应收和勾稽判断
     */
    private Map<String, Map<String, String>> jsonArrayToMap(JSONArray jsonArray) {
        Map<String, Map<String, String>> newMap = new HashMap<>();
        Map<String, Map<String, String>> pxxk_NewMap = new HashMap<>();

        log.info("执行jsonArrayToMap 方法，jsonArray：" + JSONObject.toJSONString(jsonArray));


        for (int i = 0; i < jsonArray.size(); i++) {
            //ying_sd:应收单ID，fa_psqd：发票号码ID，ben_gjje：本次勾稽金额

            JSONObject temp = jsonArray.getJSONObject(i);
            //发票编号ID
            String fa_psqd = temp.getString("fa_psqd");


            String ying_sd = temp.getString("ying_sd");
            //本次勾稽金额
            BigDecimal ben_gjje = new BigDecimal(temp.getString("ben_gjje"));
            Map<String, String> fapiao_Map = newMap.getOrDefault(fa_psqd, new HashMap<>());
            log.info("通过key:" + fa_psqd + "获取fapiao_Map：" + JSONObject.toJSONString(fapiao_Map));
            if (fapiao_Map.size() > 0) {

                //获取该发票之前已关联的勾稽金额
                BigDecimal old_ben_gjje = new BigDecimal(fapiao_Map.getOrDefault("ben_gjje", "0"));
                //计算新的勾稽金额
                BigDecimal new_ben_gjje = old_ben_gjje.add(ben_gjje);
                fapiao_Map.put("ben_gjje", new_ben_gjje.toPlainString());
                String old_ying_sd = fapiao_Map.getOrDefault("fa_ying_sd", "");
                if (ying_sd.equals("") == false) {
                    //如果之前关联的应收不为空，则把新应收加到后面
                    old_ying_sd = old_ying_sd.endsWith(",") ? old_ying_sd + ying_sd : old_ying_sd + "," + ying_sd;
                }
                fapiao_Map.put("fa_ying_sd", old_ying_sd);
                fapiao_Map.put("fa_psqd", fa_psqd);


            } else {
                fapiao_Map.put("fa_psqd", fa_psqd);
                fapiao_Map.put("fa_ying_sd", ying_sd);
                fapiao_Map.put("ben_gjje", ben_gjje.toPlainString());
            }
            //对生成的集合数据保存到newMap中
            log.info("填充数据后的fapiao_Map：" + JSONObject.toJSONString(fapiao_Map));
            newMap.put(fa_psqd, fapiao_Map);
        }

        log.info("以发票ID维度生成处理后的newMap：" + JSONObject.toJSONString(newMap));


        for (Map.Entry<String, Map<String, String>> entry : newMap.entrySet()) {
            Map<String, String> fapiao_newMap = new HashMap<>();
            String key = entry.getKey();
            Map<String, String> childMap = entry.getValue();
            //发票编号ID
            String fa_psqd = childMap.get("fa_psqd");
            String sql = "select * from uf_fa_pxxk where id=" + fa_psqd;
            List<Map<String, Object>> pxxlList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
            //通过ID获取发票信息
            Map<String, Object> pxxk_Map = pxxlList.size() > 0 ? pxxlList.get(0) : new HashMap<>();
            //发票号码
            String fa_phm = pxxk_Map.getOrDefault("fa_phm", "").toString();
            //发票冻结金额
            String fpdo_jje = pxxk_Map.getOrDefault("do_jje", "0").toString();
            //发票未勾稽金额
            String wei_gjje = pxxk_Map.getOrDefault("wei_gjje", "0").toString();

            //应收编号ID
            String fa_ying_sd = childMap.get("fa_ying_sd");
            //获取应收集合Map
            Map<String, String> ying_Map = getYingShouMap(fa_ying_sd);
            log.info("fa_ying_sd:" + fa_ying_sd + ",ying_Map：" + JSON.toJSONString(ying_Map));

            fapiao_newMap.put("fa_phm", fa_phm);
            fapiao_newMap.put("id", key);
            fapiao_newMap.put("fpdo_jje", fpdo_jje);
            fapiao_newMap.put("wei_gjje", wei_gjje);
            fapiao_newMap.put("fa_ying_sd", fa_ying_sd);
            fapiao_newMap.put("do_jje", ying_Map.get("do_jje"));
            fapiao_newMap.put("wkpje", ying_Map.get("wkpje"));
            fapiao_newMap.put("ying_scode", ying_Map.get("ying_scode"));
            //本次勾稽金额
            String ben_gjje = childMap.get("ben_gjje");
            fapiao_newMap.put("ben_gjje", ben_gjje);
            log.info("key:" + key + ",fapiao_newMap：" + JSON.toJSONString(fapiao_newMap));
            pxxk_NewMap.put(key, fapiao_newMap);

        }
        log.info("最终生成的pxxk_NewMap：" + JSONObject.toJSONString(pxxk_NewMap));
        return pxxk_NewMap;
    }

    /**
     * @param jsonArray
     * @return 以应收工单的维度获取每张应收单勾稽记录信息
     */
    private Map<String, Map<String, String>> jsonArrayToYingShouMap(JSONArray jsonArray) {
        Map<String, Map<String, String>> newMap = new HashMap<>();
        Map<String, Map<String, String>> yingshou_newList = new HashMap<>();

        log.info("执行jsonArrayToMap 方法，jsonArray：" + JSONObject.toJSONString(jsonArray));
        for (int i = 0; i < jsonArray.size(); i++) {
            //ying_sd:应收单ID，fa_psqd：发票号码ID，ben_gjje：本次勾稽金额
            Map<String, Object> map = new HashMap<>();
            JSONObject temp = jsonArray.getJSONObject(i);
            // 应收ID
            String ying_sd = temp.getString("ying_sd");
            //本次勾稽金额
            BigDecimal ben_gjje = new BigDecimal(temp.getString("ben_gjje"));
            Map<String, String> yingshou_Map = newMap.getOrDefault(ying_sd, new HashMap<>());
            log.info("通过key:" + ying_sd + "yingshou_Map：" + JSONObject.toJSONString(yingshou_Map));
            if (yingshou_Map.size() > 0) {

                //获取该发票之前已关联的勾稽金额
                BigDecimal old_ben_gjje = new BigDecimal(yingshou_Map.getOrDefault("ben_gjje", "0"));
                //计算新的勾稽金额
                BigDecimal new_ben_gjje = old_ben_gjje.add(ben_gjje);
                yingshou_Map.put("ben_gjje", new_ben_gjje.toPlainString());
                yingshou_Map.put("fa_ying_sd", ying_sd);

            } else {
                yingshou_Map.put("fa_ying_sd", ying_sd);
                yingshou_Map.put("ben_gjje", ben_gjje.toPlainString());
            }
            //对生成的集合数据保存到newMap中
            log.info("填充数据后的yingshou_Map：" + JSONObject.toJSONString(yingshou_Map));
            newMap.put(ying_sd, yingshou_Map);
        }

        log.info("数据处理后以应收维度统计的newMap：" + JSONObject.toJSONString(newMap));


        for (Map.Entry<String, Map<String, String>> entry : newMap.entrySet()) {

            Map<String, String> yingshou_newMap = new HashMap<>();
            // 获取集合中发票id
            String key = entry.getKey();
            Map<String, String> childMap = entry.getValue();

            //应收编号ID
            String fa_ying_sd = childMap.get("fa_ying_sd");
            //获取应收集合Map
            Map<String, String> ying_Map = getYingShouMap(fa_ying_sd);
            log.info("fa_ying_sd:" + fa_ying_sd + ",ying_Map：" + JSON.toJSONString(ying_Map));
            yingshou_newMap.put("id", key);

            yingshou_newMap.put("fa_ying_sd", fa_ying_sd);
            yingshou_newMap.put("do_jje", ying_Map.get("do_jje"));
            yingshou_newMap.put("wkpje", ying_Map.get("wkpje"));
            yingshou_newMap.put("ying_scode", ying_Map.get("ying_scode"));
            //本次勾稽金额
            String ben_gjje = childMap.get("ben_gjje");
            yingshou_newMap.put("ben_gjje", ben_gjje);
            log.info("key:" + key + ",yingshou_newMap：" + JSON.toJSONString(yingshou_newMap));
            yingshou_newList.put(key, yingshou_newMap);

        }
        log.info("最终生成的yingshou_newMap：" + JSONObject.toJSONString(yingshou_newList));
        return yingshou_newList;
    }

    /**
     * 通过传过来的应收ID获取应收工单Map信息
     *
     * @param fa_ying_sd
     * @return
     */
    private Map<String, String> getYingShouMap(String fa_ying_sd) {
        Map<String, String> newMap = new HashMap<>();
        List<String> list = new ArrayList<>();
        String[] ying_sd_group = fa_ying_sd.split(",");
        for (String ying_sd : ying_sd_group) {
            ying_sd = ying_sd.trim();
            if (ying_sd.equals("") == false && list.contains(ying_sd) == false) {
                list.add(ying_sd);
            }
        }

        String new_ying_sd = String.join(",", list);
        //应收冻结金额
        String do_jje = "0";
        //应收未开票金额
        String wkpje = "0";
        //应收工单编号
        String ying_scode = "";
        if (new_ying_sd.equals("") == false) {
            String sql = "select sum(nvl(do_jje,0)) as do_jje,sum(nvl(wkpje,0)) as wkpje from  uf_ying_slb where id in (" + new_ying_sd + ")";
            List<Map<String, Object>> slbList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
            Map<String, Object> ying_Map = slbList.size() > 0 ? slbList.get(0) : new HashMap<>();
            do_jje = ying_Map.getOrDefault("ying_Map", "0").toString();
            wkpje = ying_Map.getOrDefault("wkpje", "0").toString();

            String sql_Name = "select bao_zdbh from  uf_ying_slb where id in (" + new_ying_sd + ") group by bao_zdbh ";
            List<Map<String, Object>> ying_List = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);

            for (Map<String, Object> map : ying_List) {
                String bao_zdbh = map.getOrDefault("bao_zdbh", "").toString();
                if (bao_zdbh.equals("") == false) {
                    if (ying_scode.equals("") == false) {
                        ying_scode = ying_scode.endsWith(",") ? ying_scode + bao_zdbh : ying_scode + "," + bao_zdbh;
                    } else {
                        ying_scode = bao_zdbh;
                    }
                }
            }

        }
        newMap.put("do_jje", do_jje);
        newMap.put("wkpje", wkpje);
        newMap.put("ying_scode", ying_scode);
        return newMap;

    }

    /**
     * 通过ID判断合同欠费规则中的数据中的是否已发起设置欠费规则流程
     * @param ids
     * @return
     */
    @Override
    public Map<String,String> GetEnterpriseOverduePaymentRulesMap(String ids)
    {
        Map<String,String> resultMap = new HashMap<>();
        resultMap.put("code", "0");
        resultMap.put("msg", "合同欠费规则未发起合同欠费规则与经办人设置流程");
        String isExist="0";
        String[] split = ids.split(",");
        for(String id:split)
        {
            String sql = "select id from uf_he_tqfgzgl where shi_fyfqszqfgzlc='0' and  id ="+id;
            List<Map<String,Object>> list = dataSqlService.eBuilderFromSqlAll(sql,SourceType.LOGIC);
            if(!list.isEmpty())
            {
                isExist="1";
                resultMap.put("msg", "合同欠费规则已发起合同欠费规则与经办人设置流程");
                break;
            }

        }
        resultMap.put("isExist", isExist);
        return resultMap;

    }





}
