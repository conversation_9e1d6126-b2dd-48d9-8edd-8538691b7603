package com.weaver.seconddev.zyhlw.service.impl.income;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.income.IGetCheckFinanceService;
import com.weaver.seconddev.zyhlw.util.SQLUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Service
@Slf4j
public class GetCheckFinanceServieImpl  implements IGetCheckFinanceService {
    private static final String FA_PHM = "fa_phm";
    private static final String BAO_ZDBH = "bao_zdbh";
    private static final String FA_PSQD = "fa_psqd";
    private static final String FA_PKYJE = "fa_pkyje";

    private String simpleName = GetCheckFinanceServieImpl.class.getSimpleName();

    @Resource
    IDataSqlService dataSqlService;

    @Resource
    IDataBaseService iDataBaseService;


    /**
     * 校验是否同一个合同方法（根据应收id）
     *
     * @param ids
     * @param resultMap
     */
    //2021-8-21
    @Override
    public Map<String, Object> checkHeTong(String ids, Map<String, Object> resultMap) throws Exception {
        log.info("1、执行校验是否同一个合同方法");
        int pageSize = 1000;

        String sql = "select count(he_tbh) as he_tbh from  UF_YING_SLB where id in(" + ids + ")  group by he_tbh";
        List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        log.info("1.1、调用：checkGJZT方法，查询应收列表的 SQL=" + sql + ",返回的集合数据：" + JSONObject.toJSONString(dataList));
        if (dataList.size() > 1) {
            resultMap.put("msg", "必须为相同合同编号，请重新选择。");
            resultMap.put("code", 0);
        } else if (dataList.size() == 1) {
            resultMap.put("msg", "校验通过");
            resultMap.put("code", 1);
        } else {
            resultMap.put("msg", "未匹配到合同数据");
            resultMap.put("code", 0);
        }
        return resultMap;

    }

    /**
     * 校验应收单勾稽状态是否已勾稽方法（根据应收ID）
     *
     * @param ids
     * @param resultMap
     */
    @Override
    public Map<String, Object> checkGJZT(String ids, Map<String, Object> resultMap) throws Exception {
        log.info("2、执行校验应收单勾稽状态是否已勾稽方法");
        int pageSize = 1000;
        String sql = "select * from UF_YING_SLB where id in(" + ids + ")";

        List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        log.info("2.1、调用checkGJZT方法，查询应收列表的 SQL=" + sql + ",返回的集合数据：" + JSONObject.toJSONString(dataList));
        if (dataList.size() > 1) {
            String fpgjzt = dataList.get(0).getOrDefault("fpgjzt", "").toString();
            if (fpgjzt.equals("0")) {
                resultMap.put("msg", dataList.get(0).getOrDefault(BAO_ZDBH, "").toString());
                resultMap.put("code", 0);
            }

        }
        return resultMap;
    }

    /**
     * 校验发票状态是否正常方法（根据发票ID）
     *
     * @param ids
     * @param resultMap
     */
    @Override
    public Map<String, Object> checkFPZT(String ids, Map<String, Object> resultMap) throws Exception {
        log.info("3、执行校验发票状态是否正常方法");
        int pageSize = 1000;
        String sql = "select * from uf_fa_pxxk where id in(" + ids + ")";

        List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        log.info("3.1、调用checkFPZT方法，查询应收列表的 SQL=" + sql + ",返回的集合数据：" + JSONObject.toJSONString(dataList));
        if (dataList.size() > 1) {
            String fa_pzt = dataList.get(0).getOrDefault("fa_pzt", "").toString();
            if (fa_pzt.equals("0")) {
                resultMap.put("msg", "必须是正常的发票，请重新选择。");
                resultMap.put("code", 0);
            }

        }
        return resultMap;
    }

    /**
     * 校验日期
     *
     * @param ids
     * @param list
     */
    @Override
    public List<Map<String, Object>> checkdate(String ids, List<Map<String, Object>> list) throws Exception {
        log.info("15、执行校验发票状态是否正常方法");
        String sql = "select * from uf_fa_pxxk where id in(" + ids + ")";

        try {
            List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
            for (Map<String, Object> map : dataList) {
                String kai_prq = map.getOrDefault("kai_prq", "").toString();
                map.put("kai_prq", kai_prq);
                list.add(map);
            }

        } catch (Exception ex) {
            log.error("{}.checkdate()获取数据接口异常：{}", simpleName, ex.getMessage());

        }

        return list;
    }

    /*
     * 发票信息库勾稽按钮校验
     * 发票未开票金额
     * */
    @Override
    public Map<String, Object> checkfpwgjje(String ids, Map<String, Object> resultMap) throws Exception {
        log.info("16:发票信息库勾稽按钮校验");
        int pageSize = 1000;
        String sql = "select * from uf_fa_pxxk where id in(" + ids + ") and wei_gjje=0";
        List<String> list = new ArrayList<>();
        List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        log.info("3.1、调用checkFPZT方法，查询应收列表的 SQL=" + sql + ",返回的集合数据：" + JSONObject.toJSONString(dataList));
        if (!dataList.isEmpty()) {
            for (Map<String, Object> map : dataList) {
                list.add(map.getOrDefault(FA_PHM, "").toString());
            }
        }
        if (!list.isEmpty()) {
            resultMap.put(FA_PHM, StringUtils.strip(list.toString(), "[]"));
            resultMap.put("code", "0");
        }
        return resultMap;

    }

    /**
     * 验证发票反勾稽是否有重复勾稽
     */
    @Override
    public void checkGJJLFP2(String ids, Map<String, Object> resultMap) {
        log.info("12、执行验证勾稽记录数据是否正常方法(根据发票ID)");

        String[] ids2 = ids.split(",");
        List<Integer> list = new ArrayList<>();
        for (int id = 0; id < ids2.length; id++) {
            String sql = "select * from UF_FA_PGJB_DT1 where id in(" + ids2[id] + ")";
            List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
            log.info("12、查询发票勾稽明细 SQL=" + sql);
            for (Map<String, Object> map : dataList) {
                BigDecimal js;
                int fa_psqd = Integer.valueOf(map.get(FA_PSQD).toString());
                BigDecimal ben_cgjje = SQLUtil.parseBigDecimal(map.get("ben_cgjje").toString());

                String sql2 = "select FA_PSQD, (sum(case when fa_gdlx=0 then BEN_CGJJE else 0 end) - sum(case when fa_gdlx=1 then BEN_CGJJE else 0 end)) as gjze from UF_FA_PGJB_DT1 where FA_PSQD in (" + fa_psqd + ") group by FA_PSQD";
                List<Map<String, Object>> dataList2 = dataSqlService.eBuilderFromSqlAll(sql2, SourceType.LOGIC);
                for (Map<String, Object> map2 : dataList2) {
                    BigDecimal gjze = SQLUtil.parseBigDecimal(String.valueOf(map2.get("gjze")));
                    js = gjze.subtract(ben_cgjje);
                    int s = js.compareTo(BigDecimal.ZERO);  //返回0表示等于0，返回1表示大于0，返回-1表示小于0
                    log.info("打印12---" + ben_cgjje + "," + gjze + "," + s);
                    if (s < 0) {
                        list.add(id + 1);
                        resultMap.put("msg", "错误行：" + StringUtils.strip(list.toString(), "[]") + "，改条勾稽记录已经被反勾稽或反勾稽审批中");
                        resultMap.put("code", 0);
                    }
                }

            }
        }
    }

    /**
     * 查询发票信息库  勾稽金额是否有勾稽记录
     *
     * @param ids
     * @param resultMap
     */
    @Override
    public void checkGJJU(String ids, Map<String, Object> resultMap) { //18496
        log.info("4、执行校验勾稽记录勾稽金额是否有勾稽");
        String sql = "select * from uf_fa_pxxk where id in(" + ids + ")";
        List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        log.info("4、查询发票勾稽明细 SQL=" + sql);
        for (Map<String, Object> map : dataList) {
            BigDecimal yi_gjje = new BigDecimal(map.get("yi_gjje").toString());
            int s = yi_gjje.compareTo(BigDecimal.ZERO);  //返回0表示等于0，返回1表示大于0，返回-1表示小于0
            if (s > 0) {
                resultMap.put("msg", map.get(FA_PHM));
                resultMap.put("code", 0);
                break;
            }
        }
    }

    /**
     * 校验是否存在勾稽记录
     */
    @Override
    public void checkGJJU2(String ids, Map<String, Object> resultMap) { //18496
        log.info("13、执行校验勾稽记录是否反勾稽方法");

        String sql = "select * from uf_fa_pgjb_dt1 where fa_psqd  in(" + ids + ")";
        List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);

        log.info("4、查询发票勾稽明细 SQL=" + sql);
        for (Map<String, Object> map : dataList) {
            if (map.get("fa_gdlx").toString().equals("1")) {

                String sql2 = "select fa_phm from uf_fa_pxxk where id in(" + map.get(FA_PSQD) + ")";
                List<Map<String, Object>> dataList2 = dataSqlService.eBuilderFromSqlAll(sql2, SourceType.LOGIC);
                for (Map<String, Object> map2 : dataList) {
                    resultMap.put("msg", map2.get(FA_PHM).toString());
                    resultMap.put("code", 0);
                    break;
                }
            }
        }
    }


    /**
     * 验证勾稽记录数据是否正常方法(根据发票ID)
     *
     * @param ids
     * @param resultMap
     */
    @Override
    public void checkGJJLFP(String ids, Map<String, Object> resultMap) {
        log.info("5、执行验证勾稽记录数据是否正常方法(根据发票ID)");

        String[] ids2 = ids.split(",");
        List<String> list = new ArrayList<>();
        for (String s : ids2) {
            String sql = "select fa_psqd,nvl(isfinish,0) isfinish1 from uf_fa_pgjb_dt1 where fa_psqd in(" + s + ")";
            List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
            log.info("4、查询发票勾稽明细 SQL=" + sql);
            for (Map<String, Object> map : dataList) {
                if (map.get("isfinish1").toString().equals("0")) {
                    resultMap.put("msg", "该发票存在待同步的勾稽记录，请先处理。");

                    String sql2 = "select * from uf_fa_pxxk where id = " + map.get(FA_PSQD).toString();
                    List<Map<String, Object>> dataList2 = dataSqlService.eBuilderFromSqlAll(sql2, SourceType.LOGIC);
                    for (Map<String, Object> map2 : dataList) {
                        list.add(map2.get(FA_PHM).toString());
                        resultMap.put(FA_PHM, StringUtils.strip(list.toString(), "[]"));
                        resultMap.put("code", 0);
                        break;
                    }
                }
            }
        }
    }

    /**
     * 验证红冲发票可用金额大于0
     *
     * @param ids
     * @param resultMap
     */
    @Override
    public void checkGJJLFP3(String ids, Map<String, Object> resultMap) {
        log.info("14、执行验证勾稽记录数据是否正常方法(根据发票ID)");

        String sql = "select * from uf_fa_pgjb_dt1 where fa_psqd in(" + ids + ")";
        List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);

        log.info("4、查询发票勾稽明细 SQL=" + sql);
        for (Map<String, Object> map : dataList) {
            if (map.get(FA_PKYJE).toString().equals("0")) {
                resultMap.put("msg", "发票可用金额为0不可以被红冲。");
                resultMap.put("code", 0);
                break;
            }
        }
    }


    /**
     * 验证勾稽记录数据是否正常方法(根据应收ID)
     *
     * @param ids
     * @param resultMap
     */
    @Override
    public void checkGJJLYS(String ids, Map<String, Object> resultMap) {
        log.info("6、执行验证勾稽记录数据是否正常(根据应收ID)");
        String sql = "select * from uf_fa_pgjb_dt1 where ying_sd in(" + ids + ")";
        List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        log.info("4、查询发票勾稽明细 SQL=" + sql);
        for (Map<String, Object> map : dataList) {
            if (map.get("ying_synch").toString().equals("0")) {
                resultMap.put("msg", "该应收单存在待同步的勾稽记录，请先处理。");
                String sql2 = "select * from UF_YING_SLB where id = " + map.get("ying_sd").toString();
                List<Map<String, Object>> dataList2 = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
                for (Map<String, Object> map2 : dataList2) {

                    resultMap.put(BAO_ZDBH, map2.get(BAO_ZDBH).toString());
                    resultMap.put("code", 0);
                    break;
                }
            }
        }
    }

    /**
     * 验证勾稽记录数据是否正常(勾稽记录ID)
     *
     * @param ids
     * @param resultMap
     */
    @Override
    public void checkGJJLID(String ids, Map<String, Object> resultMap) {
        log.info("7、执行验证勾稽记录数据是否正常(根据勾稽明细ID)");

        String sql = "select * from uf_fa_pgjb_dt1 where id in(" + ids + ")";
        List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);

        log.info("4、查询发票勾稽明细 SQL=" + sql);
        for (Map<String, Object> map : dataList) {
            if (map.get("fa_gdlx").toString().equals("1")) {
                resultMap.put("msg", "该勾稽记录存在反勾稽记录，请选择未被反勾稽发票。");
                resultMap.put("code", 0);
                break;
            }
        }
    }

    /**
     * 验证发票号码是否唯一       方法(发票号码)
     *
     * @param fphm
     * @param resultMap
     */
    @Override
    public void checkFPHM(String fphm, Map<String, Object> resultMap) {
        log.info("8、执行验证发票号码是否唯一方法(发票号码)");

        String sql = "select  * from  UF_FA_PXXK  where FA_PHM  in(" + fphm + ")";
        List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        log.info("4、查询发票信息 SQL=" + sql);
        List<String> list = new ArrayList<>();
        for (Map<String, Object> map : dataList) {
            list.add(map.get(FA_PHM).toString());
        }
        if (!list.isEmpty()) {
            resultMap.put(FA_PHM, StringUtils.strip(list.toString(), "[]"));
            resultMap.put("msg", StringUtils.strip(list.toString(), "[]") + "发票号与现有发票重复，请检查后再提交。");
            resultMap.put("code", 0);
        }
    }

    /**
     * 验证红冲金额不能大于发票的已勾稽金额方法（JSON ）[{faId:1,jine:9.0},{faId:1,jine:9.0}]
     * 2021-6-21 修改  红冲发票金额 <= 发票可用金额
     *
     * @param parm
     * @param resultMap
     */
    @Override
    public void checkHCJE(String parm, Map<String, Object> resultMap) {
        log.info("9、执行验证红冲金额不能大于发票的已勾稽金额方法（JSON ）");
        JSONArray jsonArray = JSON.parseArray(parm);

        List<Integer> list = new ArrayList<>();
        List<String> list2 = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject item = jsonArray.getJSONObject(i);
            String sql = "select * from  UF_FA_PXXK  where id =" + item.getString("faId");
            List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
            for (Map<String, Object> map : dataList) {
                log.info("4、查询发票信息 SQL=" + sql + ",jine:");
                log.info(item.getString("jine") + "----" + map.get(FA_PKYJE).toString());
                //取消 发票的红冲金额不能大于已勾稽金额
                //修改  红冲发票金额<=发票可用金额
                BigDecimal jine = new BigDecimal(item.getString("jine"));
                BigDecimal fa_pkyje = new BigDecimal(map.get(FA_PKYJE).toString());
                BigDecimal do_jje = SQLUtil.parseBigDecimal(map.get("do_jje").toString());  //发票冻结金额
                fa_pkyje = fa_pkyje.subtract(do_jje);
                if (jine.compareTo(fa_pkyje) > 0) {
                    list.add(i + 1);
                    list2.add(String.valueOf(do_jje));
                    resultMap.put(FA_PHM, map.get(FA_PHM).toString());
                    resultMap.put("msg", "错误行：" + StringUtils.strip(list.toString(), "[]") + "，红冲金额不能大于发票可红冲金额（冻结中" + StringUtils.strip(list2.toString(), "[]") + "元）");
                    resultMap.put("code", 0);
                }
            }
        }
    }

    /**
     * 验证开票金额大于应收报账单未开票的总金额（JSON）["yingId":"1,1,1","jine":1]
     *
     * @param parm
     * @param resultMap
     */
    @Override
    public void checkKPJE(String parm, Map<String, Object> resultMap) {
        log.info("10、执行验证开票金额大于应收报账单未开票的总金额（JSON）");
        JSONArray jsonArray = JSON.parseArray(parm);
        BigDecimal jine = SQLUtil.parseBigDecimal("0");
        HashSet<String> id = new HashSet<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject item = jsonArray.getJSONObject(i);
            String[] idA = item.getString("yingId").split(",");
            id.addAll(Arrays.asList(idA));
            jine = jine.add(SQLUtil.parseBigDecimal(item.getString("jine")));
        }

        String sql = "select * from UF_YING_SLB where id in (" + String.join(",", id) + ")";
        List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        log.info("4、查询应收信息 SQL=" + sql);

        BigDecimal wkpje = SQLUtil.parseBigDecimal("0");

        for (Map<String, Object> map : dataList) {
            wkpje = wkpje.add(SQLUtil.parseBigDecimal(map.get("wkpje").toString()));
        }
        if (jine.compareTo(wkpje) > 0) {
            resultMap.put("msg", "金额不能大于应收单的未开票金额。");
            resultMap.put("code", 0);
        }
    }
    /*
     * 判断客户编号是否存在
     * ids : ID
     * */
    @Override
    public void ChenKhbhSel(String ids, Map<String, Object> resultMap) {
        log.info("17:客户编号查询方法");

        resultMap.put("code", "1");
        resultMap.put("msg", "成功！");
        String sql = "select * from uf_ke_hxxb where khxxbm in(" + spilt2(ids) + ")";
        List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        for (Map<String, Object> map : dataList) {
            resultMap.put("code", "0");
            resultMap.put("msg", "客户信息编码不存在，请核实");
        }
    }

    /**
     * 判断传过来的发票在发票作废和红冲流程中有在途的工单
     *
     * @param ids
     * @param resultMap
     */
    @Override
    public void ChenOrderOnWayByFPBH(String ids, Map<String, Object> resultMap) {
        log.info("18:判断传过来的发票在发票作废和红冲流程中有在途的工单，传过来的发票ID：" + ids);

        resultMap.put("code", "1");
        resultMap.put("msg", "成功！");

        //发票红冲流程表
        String tableName_red = iDataBaseService.getBaseDataValue("发票红冲申请流程表名称", "财务收入管理");
        String tableNameDetail_red = tableName_red + "_dt1";
        //发票作废流程表
        String tableName_cancel = iDataBaseService.getBaseDataValue("发票作废申请流程表名称", "财务收入管理");
        String tableNameDetail_cancel = tableName_cancel + "_dt1";

        if (ids.trim().equals("")) {
            resultMap.put("code", "0");
            resultMap.put("msg", "传过来的发票ID不能为空！");
        } else {

            String sql_red = "select requestid  from workflow_requestbase where currentnodetype in (1,2) " +
                    " and requestid in (select requestid from " + tableName_red + " where id in (select mainid from " + tableNameDetail_red + " where hong_cfpje in (" + ids + ")))";

            List<Map<String, Object>> dataList_Red = dataSqlService.eBuilderFromSqlAll(sql_red, SourceType.LOGIC);

            String sql_cancel = "select requestid  from workflow_requestbase where currentnodetype in (1,2) " +
                    " and requestid in (select requestid from " + tableName_cancel + " where id in (select mainid from " + tableNameDetail_cancel + " where fa_phm in (" + ids + ")))";
            List<Map<String, Object>> dataList_Cancel = dataSqlService.eBuilderFromSqlAll(sql_red, SourceType.LOGIC);


            if (!dataList_Red.isEmpty()) {
                resultMap.put("code", "0");
                resultMap.put("msg", "该发票在发票红冲流程中存在审批中的流程，不能对该发票进行作废操作！");
            } else if (!dataList_Cancel.isEmpty()) {
                resultMap.put("code", "0");
                resultMap.put("msg", "该发票在发票作废流程中存在审批中的流程，不能对该发票进行作废操作！");
            }
        }
    }

    /*
     * 字符串拼接 ‘’
     * */
    public String spilt2(String str) {
        StringBuilder sb = new StringBuilder();

        String[] temp = str.split(",");

        for (String s : temp) {
            if (!"".equals(s) && s != null) {

                sb.append("'").append(s).append("',");
            }

        }
        String result = sb.toString();
        String tp = result.substring(result.length() - 1);

        if (",".equals(tp)) {
            return result.substring(0, result.length() - 1);
        } else {
            return result;
        }

    }
}
