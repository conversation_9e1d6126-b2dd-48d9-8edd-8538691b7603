package com.weaver.seconddev.zyhlw.service.impl.income;

import com.alibaba.fastjson.JSONObject;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.seconddev.zyhlw.domain.request.eb.EbFormDataReq;
import com.weaver.seconddev.zyhlw.domain.response.form.EbFormDataResultVo;
import com.weaver.seconddev.zyhlw.domain.response.form.vo.DataJson;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.SQLUtil;
import com.weaver.seconddev.zyhlw.util.ebuilder.EBuilderUtil;
import com.weaver.teams.domain.user.SimpleEmployee;
import com.weaver.teams.security.context.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class IncomeServiceImpl {
    private String methodName = IncomeServiceImpl.class.getSimpleName();
    @Resource
    IDataSqlService dataSqlService;

    @Resource
    IDataBaseService iDataBaseService;

    @Resource
    IOpenPlatformService iOpenPlatformService;
    @Resource
    private CmicProperties cmicProperties;

    /**
     * 从勾稽列表中获取相关的数据，更新原应收单数据，用逗号分隔
     *
     * @param affectedYsIdArea 本次受影响的应收单id
     */
    public Map<String, Object> statisticYsInfo(String affectedYsIdArea) {
        log.info("============ 开始统计应收数据 ============");
        log.info("本次统计的应收单id:  " + affectedYsIdArea);

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("code", 200);
        resultMap.put("msg", "更新成功");
        try {

            String getAffectedYsSql = "select * from uf_ying_slb where id in (" + affectedYsIdArea + ")";
            List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(getAffectedYsSql, SourceType.LOGIC);
            BigDecimal lie_zje = new BigDecimal(0);
            Map<String, Object> dataMap = new HashMap<>();
            for (Map<String, Object> map : dataList) {
                int id = Integer.parseInt(map.get("id").toString());
//                lie_zje = SQLUtil.parseBigDecimal(map.get("lie_zje").toString());  //列帐金额
//                BigDecimal kai_pjebhs = SQLUtil.parseBigDecimal(map.get("kai_pjebhs").toString());  //开票金额
//                BigDecimal wei_gjje = lie_zje.subtract(kai_pjebhs);  //未开票金额=列帐金额-开票金额
//
                dataMap.put(String.valueOf(id), id);


                log.info("获取到的应收单信息:   " + dataMap);

                String statisticGjInfoSql = "select ying_sd, sum(case when fa_gdlx=0 then BEN_CGJJE else 0 end) - sum(case when fa_gdlx=1 then BEN_CGJJE else 0 end) as gou_jze from UF_FA_PGJB_DT1 where YING_SD in (" + Integer.valueOf(id) + ") group by ying_sd";
                List<Map<String, Object>> dataList2 = dataSqlService.eBuilderFromSqlAll(statisticGjInfoSql, SourceType.LOGIC);

                List<Map<String, Object>> newYsInfoList = new ArrayList<>();
                // 勾稽总额就是已开票金额
                for (Map<String, Object> map2 : dataList2) {
                    int ying_sd = Integer.parseInt(map2.get("ying_sd").toString());   //应收单ID
                    BigDecimal gou_jze = SQLUtil.parseBigDecimal(map2.get("gou_jze").toString()); // 统计的勾稽总额
                    if (dataMap.get(ying_sd) == null) {
                        continue;
                    }

                    log.info("【统计】 === 应收单号: " + ying_sd + "  列账金额:  " + lie_zje + "  勾稽总额: " + gou_jze);
                    int fpgjzt = 2; // 默认未勾稽
                    int kai_pzt = 0;
                    if (gou_jze.compareTo(lie_zje) == 0) {
                        fpgjzt = 0; // 已勾稽
                        kai_pzt = 1;
                    } else if (gou_jze.compareTo(SQLUtil.parseBigDecimal("0")) > 0) {
                        fpgjzt = 1; // 部分勾稽
                        kai_pzt = 2;
                    }

                    log.info("发票勾稽状态: " + fpgjzt);
                    Map<String, Object> newInfoMap = new HashMap<>();
                    JSONObject mewDataOjbect = new JSONObject();

                    mewDataOjbect.put("fpgjzt", fpgjzt); // 发票状态
                    mewDataOjbect.put("fpgjje", gou_jze.toString()); // 已勾稽金额

                    mewDataOjbect.put("wkpje", lie_zje.subtract(gou_jze).toString()); // 未开票金额
                    mewDataOjbect.put("kai_pjebhs", gou_jze.toString()); // 开票金额不含税
                    mewDataOjbect.put("kai_pzt", kai_pzt);
                    mewDataOjbect.put("id", ying_sd);
                    Map<String, Object> newDataMap = new HashMap<>();
                    newDataMap.put("mainTable", mewDataOjbect);

                    newYsInfoList.add(newDataMap);
                }
                log.info("新的应收单信息:  " + newYsInfoList);

                // 从表单配置表中获取表单ID（objId）
                String objId = iDataBaseService.getTableFormIdValue("uf_ying_slb");
                // 获取当前用户
                SimpleEmployee currentUser = UserContext.getCurrentUser();

                if (currentUser == null) {
                    String method = String.format("调用%s.statisticYsInfo(%s)-->获取当前currentUser为空！", methodName, affectedYsIdArea);
                    log.error(method);

                }
                String userId = String.valueOf(currentUser.getId());
                log.info("调用PortalController.token方法，获取currentUser信息：" + JSONObject.toJSONString(currentUser));


               // String updateYsInfoSql = "update uf_ying_slb set fpgjzt=?, fpgjje=?, wkpje=?, kai_pjebhs=?,kai_pzt=? where id=?";

                Map<String, Object> mainTableField = new HashMap<>(1);

                List<String> mainTableFields = new ArrayList<>();
                mainTableFields.add("id");
                mainTableField.put("mainTable", mainTableFields);


                EbFormDataReq builder = new EbFormDataReq.Builder()
                        .userId(userId)
                        .objId(objId)
                        .needAdd("false")
                        .updateType("updatePolicy")
                        .updateField(mainTableField)
                        .datas(newYsInfoList)
                        .build();
                EbFormDataResultVo resultVo = EBuilderUtil.updateFormDataV2(builder, iOpenPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());
                log.info("调用{}  更新数据返回结果：{}", methodName, JSONObject.toJSONString(resultVo));


            }
        } catch (Exception ex) {
            resultMap.put("code", 202);
            resultMap.put("msg", "更新失败,原因：" + ex.getMessage());
        }
        return resultMap;


    }

    /**
     * 统计本次受影响的发票数据，并更新
     *
     * @param invoiceIdArea 次受影响的发票id，用逗号分隔
     */
    public void statisticInvoiceInfo(String invoiceIdArea) {
        log.info("============ 开始统计发票数据 ============");
        log.info("本次统计的发票id:  " + invoiceIdArea);


        String statisticInvoiceInfoSql = "select FA_PSQD, (sum(case when fa_gdlx=0 then BEN_CGJJE else 0 end) - sum(case when fa_gdlx=1 then BEN_CGJJE else 0 end)) as gjze from UF_FA_PGJB_DT1 where FA_PSQD in (" + invoiceIdArea + ") group by FA_PSQD";

        List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(statisticInvoiceInfoSql, SourceType.LOGIC);
        List<Map<String, Object>> statisticInvoiceInfoList = new ArrayList<>();
        for (Map<String, Object> map : dataList) {
            int fa_psqd = Integer.parseInt(map.get("fa_psqd").toString());
            String gjze = map.get("gjze").toString();
            Map<String, Object> statisticInvoiceInfo = new HashMap<>();
            statisticInvoiceInfo.put("fa_psqd", fa_psqd);
            statisticInvoiceInfo.put("gjze", gjze);
            statisticInvoiceInfoList.add(statisticInvoiceInfo);
        }
        log.info("统计的发票数据有: " + statisticInvoiceInfoList);
        // 获取受影响的发票
        String getAffectedInvoiceSql = "select id, jin_e, fa_pzt, fa_pkyje, yi_gjje, wei_gjje, nvl(hong_cje,0) as hong_cje  from uf_fa_pxxk where id in (" + invoiceIdArea + ")";
        List<Map<String, Object>> dataList2 = dataSqlService.eBuilderFromSqlAll(getAffectedInvoiceSql, SourceType.LOGIC);
        List<Map<String, Object>> newInvoiceList = new ArrayList<>();
        for (Map<String, Object> map2 : dataList2) {
            int id = Integer.parseInt(map2.get("id").toString());
            log.info("发票id:   " + id);
            for (Map<String, Object> invoice : statisticInvoiceInfoList) {
                int invoiceId = (int) invoice.get("fa_psqd");
                if (id == invoiceId) {
                    log.info("发票数据:  " + invoice);
                    BigDecimal gjze = SQLUtil.parseBigDecimal(String.valueOf(invoice.get("gjze")));
                    BigDecimal jin_e = SQLUtil.parseBigDecimal(map2.get("jin_e").toString());
                    int fa_pzt = Integer.parseInt(map2.get("fa_pzt").toString());
                    BigDecimal hong_cje = SQLUtil.parseBigDecimal(map2.get("hong_cje").toString());
                    BigDecimal wei_gjje1 = SQLUtil.parseBigDecimal(map2.get("wei_gjje").toString());
//                    double ke_yje = fa_pzt == 1 ? 0 : jin_e - hong_cje;
//                    double wei_gjje = fa_pzt == 1 ? 0 : jin_e - gjze;
//                    BigDecimal ke_yje = fa_pzt == 1 ? 0 : ArithmeticUtil.sub(jin_e,hong_cje);
//                    BigDecimal wei_gjje = fa_pzt == 1 ? 0 : ArithmeticUtil.sub(jin_e,gjze);
                    BigDecimal ke_yje = fa_pzt == 1 ? BigDecimal.valueOf(0) : jin_e.subtract(hong_cje);
                    BigDecimal wei_gjje = fa_pzt == 1 ? BigDecimal.valueOf(0) : ke_yje.subtract(gjze);

                  /*  Integer hong_czt;
                    if (hong_cje.compareTo(jin_e) == 0) {
                        hong_czt = 1;
                    } else if (hong_cje.compareTo(BigDecimal.valueOf(0)) == 0) {
                        hong_czt = null;
                    } else {
                        hong_czt = 0;
                    }*/
                    Map<String, Object> row = new HashMap<>();
                    JSONObject rowOjbect = new JSONObject();
                    rowOjbect.put("fa_pkyje", ke_yje.toString()); // 发票可用金额为发票总金额-已勾稽总额，但是作废发票可用金额为0
                    rowOjbect.put("yi_gjje", gjze.toString());
                    rowOjbect.put("wei_gjje", wei_gjje.toString()); // 未勾稽金额等于发票的可用金额
                    rowOjbect.put("id", id);
                    row.put("mainTable", rowOjbect);
                    newInvoiceList.add(row);
                }
            }
        }
        log.info("更新的发票数据有:  " + newInvoiceList);

       // String updateInvoiceSql = "update uf_fa_pxxk set fa_pkyje=?,yi_gjje=?,wei_gjje=? where id=?";
        log.info("本次勾稽后发票的数据:  " + newInvoiceList);
        // updateNewInvoiceInfoRs.executeBatchSql(updateInvoiceSql, newInvoiceList);
        // 从表单配置表中获取表单ID（objId）
        String objId = iDataBaseService.getTableFormIdValue("uf_fa_pxxk");
        // 获取当前用户
        SimpleEmployee currentUser = UserContext.getCurrentUser();

        if (currentUser == null) {
            String method = String.format("调用%s.statisticYsInfo(%s)-->获取当前currentUser为空！", methodName, invoiceIdArea);
            log.error(method);

        }
        String userId = String.valueOf(currentUser.getId());
        log.info("调用PortalController.token方法，获取currentUser信息：" + JSONObject.toJSONString(currentUser));

        Map<String, Object> mainTableField = new HashMap<>(1);
        List<String> mainTableFields = new ArrayList<>();
        mainTableFields.add("id");
        mainTableField.put("mainTable", mainTableFields);
        EbFormDataReq builder = new EbFormDataReq.Builder()
                .userId(userId)
                .objId(objId)
                .needAdd("false")
                .updateType("updatePolicy")
                .updateField(mainTableField)
                .datas(newInvoiceList)
                .build();
        EbFormDataResultVo resultVo = EBuilderUtil.updateFormDataV2(builder, iOpenPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());
        log.info("调用{}  更新数据返回结果：{}", methodName, JSONObject.toJSONString(resultVo));

        Changestatus(invoiceIdArea);
    }


    /**
     * 反勾稽之后是否存在勾稽金额  如果不存在修改状态为1
     * isfinish   1
     * 是否完成状态  修改为1
     */
    public void Changestatus(String fa_psqd) {
        String statisticInvoiceInfoSql = "select FA_PSQD, (sum(case when fa_gdlx=0 then BEN_CGJJE else 0 end) - sum(case when fa_gdlx=1 then BEN_CGJJE else 0 end)) as gjze from UF_FA_PGJB_DT1 where FA_PSQD in (" + fa_psqd + ") group by FA_PSQD";
        List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(statisticInvoiceInfoSql, SourceType.LOGIC);
        List<Map<String, Object>> datas = new ArrayList<>();
        for (Map<String, Object> map : dataList) {
            String tempFa_psqd = map.get("FA_PSQD").toString();
            BigDecimal gjze = SQLUtil.parseBigDecimal(String.valueOf(map.get("gjze")));
            if (gjze.compareTo(new BigDecimal(0)) == 0) {
                // String sql = "update uf_fa_pgjb_dt1 set isfinish=1 where fa_psqd=" + tempFa_psqd;
                Map<String, Object> dataMap = new HashMap<>();
                JSONObject mainTableObject = new JSONObject();

                mainTableObject.put("isfinish", "1");
                mainTableObject.put("fa_psqd", tempFa_psqd);
                dataMap.put("mainTable", mainTableObject);
                datas.add(dataMap);
            }

        }

        Map<String, Object> mainTableField = new HashMap<>(1);
        List<String> mainTableFields = new ArrayList<>();
        mainTableFields.add("fa_psqd");
        mainTableField.put("mainTable", mainTableFields);
        // 从表单配置表中获取表单ID（objId）?? todo 更新明细表数据，这个方法要改，请查看API接口，重新编写一个接口
        String objId = iDataBaseService.getTableFormIdValue("uf_fa_pgjb_dt1");
        // 获取当前用户
        SimpleEmployee currentUser = UserContext.getCurrentUser();

        if (currentUser == null) {
            String method = String.format("调用%s.Changestatus(%s)-->获取当前currentUser为空！", methodName, fa_psqd);
            log.error(method);

        }
        String userId = String.valueOf(currentUser.getId());

        EbFormDataReq builder = new EbFormDataReq.Builder()
                .userId(userId)
                .objId(objId)
                .needAdd("false")
                .updateType("updatePolicy")
                .updateField(mainTableField)
                .datas(datas)
                .build();
        EbFormDataResultVo resultVo = EBuilderUtil.updateFormDataV2(builder, iOpenPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());

    }

    /*
     * 发票红冲归档后执行
     * invoiceIdArea  发票申请单ID
     * */
    public void Reddashed(String invoiceIdArea) {
        log.info("============ 红冲归档 ============");
        log.info("本次统计的发票id:  " + invoiceIdArea);

        String statisticInvoiceInfoSql = "select FA_PSQD, (sum(case when fa_gdlx=0 then BEN_CGJJE else 0 end) - sum(case when fa_gdlx=1 then BEN_CGJJE else 0 end)) as gjze from UF_FA_PGJB_DT1 where FA_PSQD in (" + invoiceIdArea + ") group by FA_PSQD";
        List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(statisticInvoiceInfoSql, SourceType.LOGIC);

        List<Map<String, Object>> statisticInvoiceInfoList = new ArrayList<>();
        for (Map<String, Object> map : dataList) {
            int fa_psqd = Integer.parseInt(map.get("fa_psqd").toString());
            String gjze = map.get("gjze").toString();
            Map<String, Object> statisticInvoiceInfo = new HashMap<>();
            statisticInvoiceInfo.put("fa_psqd", fa_psqd);
            statisticInvoiceInfo.put("gjze", gjze);
            statisticInvoiceInfoList.add(statisticInvoiceInfo);
        }
        log.info("统计的发票数据有: " + statisticInvoiceInfoList);
        // 获取受影响的发票
        String getAffectedInvoiceSql = "select id, jin_e, fa_pzt, fa_pkyje, yi_gjje, wei_gjje, nvl(hong_cje,0) as hong_cje  from uf_fa_pxxk where id in (" + invoiceIdArea + ")";
        List<Map<String, Object>> dataList2 = dataSqlService.eBuilderFromSqlAll(getAffectedInvoiceSql, SourceType.LOGIC);

        List<Map<String, Object>> newInvoiceList = new ArrayList<>();
        for (Map<String, Object> map2 : dataList2) {
            int id = Integer.parseInt(map2.get("id").toString());
            log.info("发票id:   " + id);
            for (Map<String, Object> invoice : statisticInvoiceInfoList) {
                int invoiceId = (int) invoice.get("fa_psqd");
                if (id == invoiceId) {
                    log.info("红冲发票数据:  " + invoice);
                    Map<String, Object> dataMap = new HashMap<>();
                    JSONObject mainTableObject = new JSONObject();

                    BigDecimal gjze = SQLUtil.parseBigDecimal(String.valueOf(invoice.get("gjze")));
                    BigDecimal jin_e = SQLUtil.parseBigDecimal(map2.get("jin_e").toString());
                    int fa_pzt = Integer.parseInt(map2.get("fa_pzt").toString());
                    BigDecimal hong_cje = SQLUtil.parseBigDecimal(map2.get("hong_cje").toString());
                    BigDecimal ke_yje = fa_pzt == 1 ? BigDecimal.valueOf(0) : jin_e.subtract(hong_cje);
                    BigDecimal wei_gjje = fa_pzt == 1 ? BigDecimal.valueOf(0) : jin_e.subtract(hong_cje);
                    Integer hong_czt;
                    if (hong_cje.compareTo(jin_e) == 0) {
                        hong_czt = 1;
                    } else if (hong_cje.compareTo(BigDecimal.valueOf(0)) == 0) {
                        hong_czt = null;
                    } else {
                        hong_czt = 0;
                    }

                    mainTableObject.put("fa_pkyje", ke_yje.toString()); // 发票可用金额为发票总金额-已勾稽总额，但是作废发票可用金额为0
                    mainTableObject.put("yi_gjje", gjze.toString());
                    mainTableObject.put("wei_gjje", wei_gjje.toString()); // 未勾稽金额等于发票的可用金额
                    mainTableObject.put("hong_czt", hong_czt);
                    mainTableObject.put("id", id);
                    dataMap.put("mainTable", mainTableObject);
                    newInvoiceList.add(dataMap);
                }
            }
        }
        log.info("红冲的发票数据有:  " + newInvoiceList);


        String updateInvoiceSql = "update uf_fa_pxxk set fa_pkyje=?,yi_gjje=?,wei_gjje=?,hong_czt=? where id=?";
        log.info("本次勾稽后发票的数据:  " + newInvoiceList);
        //updateNewInvoiceInfoRs.executeBatchSql(updateInvoiceSql, newInvoiceList);

        // 从表单配置表中获取表单ID（objId）
        String objId = iDataBaseService.getTableFormIdValue("uf_ying_slb");
        // 获取当前用户
        SimpleEmployee currentUser = UserContext.getCurrentUser();

        if (currentUser == null) {
            String method = String.format("调用%s.Reddashed(%s)-->获取当前currentUser为空！", methodName, invoiceIdArea);
            log.error(method);

        }
        String userId = String.valueOf(currentUser.getId());
        log.info("调用PortalController.token方法，获取currentUser信息：" + JSONObject.toJSONString(currentUser));


        String updateYsInfoSql = "update uf_ying_slb set fpgjzt=?, fpgjje=?, wkpje=?, kai_pjebhs=?,kai_pzt=? where id=?";

        Map<String, Object> mainTableField = new HashMap<>(1);
        List<String> mainTableFields = new ArrayList<>();
        mainTableFields.add("id");
        mainTableField.put("mainTable", mainTableFields);
        EbFormDataReq builder = new EbFormDataReq.Builder()
                .userId(userId)
                .objId(objId)
                .needAdd("false")
                .updateType("updatePolicy")
                .updateField(mainTableField)
                .datas(newInvoiceList)
                .build();
        EbFormDataResultVo resultVo = EBuilderUtil.updateFormDataV2(builder, iOpenPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());

        Changestatus(invoiceIdArea);

    }
}
