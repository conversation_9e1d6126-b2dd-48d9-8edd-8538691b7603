package com.weaver.seconddev.zyhlw.service.impl.income;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.seconddev.zyhlw.domain.request.eb.EbFormDataReq;
import com.weaver.seconddev.zyhlw.domain.response.form.EbFormDataResultVo;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.service.income.IInvoiceStatisticService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import com.weaver.seconddev.zyhlw.util.ebuilder.EBuilderUtil;
import com.weaver.teams.domain.user.SimpleEmployee;
import com.weaver.teams.security.context.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.Context;
import java.util.*;

@Service
@Slf4j
public class InvoiceStatisticImpl implements IInvoiceStatisticService {

    private String methodName = IncomeServiceImpl.class.getSimpleName();
    @Resource
    IDataSqlService dataSqlService;

    @Resource
    IDataBaseService iDataBaseService;

    @Resource
    IOpenPlatformService iOpenPlatformService;
    @Resource
    private CmicProperties cmicProperties;

    @Resource
    IncomeServiceImpl incomeService;


    /**
     * 更新发票勾稽记录的同步状态
     *
     * @param invoiceId
     * @param ysId
     * @return
     */
    @Override
    public Map<String, Object> updateByIRId(String invoiceId, String ysId) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if ("".equals(ysId) && "".equals(invoiceId)) {
                throw new Exception("应收单id、发票id不能为空!!");
            }
            if (ysId.length() > 0) {
                incomeService.statisticYsInfo(ysId);
            }
            if (invoiceId.length() > 0) {
                incomeService.statisticInvoiceInfo(invoiceId);
            }
            String selectDetailSql = "select id uf_fa_pgjb_dt1 set ying_synch=1 where ";
            if (ysId.length() > 0) {
                selectDetailSql += "ying_sd in (" + ysId + ")";
                if (invoiceId.length() > 0) {
                    selectDetailSql += " and  fa_psqd in (" + invoiceId + ")";
                }
            } else {
                selectDetailSql += "fa_psqd in (" + invoiceId + ")";
            }

            List<Map<String, Object>> detailList = dataSqlService.eBuilderFromSqlAll(selectDetailSql, SourceType.LOGIC);


            List<Map<String, Object>> datas = new ArrayList<>();
            for (Map<String, Object> detailMap : detailList) {

                String detailId = detailMap.get("id").toString();
                Map<String, Object> dataMap = new HashMap<>();
                JSONObject mainTableObject = new JSONObject();
                mainTableObject.put("ying_synch", "1");
                mainTableObject.put("id", detailId);
                dataMap.put("mainTable", mainTableObject);
                datas.add(dataMap);
            }

            // 从表单配置表中获取表单ID（objId）
            String objId = iDataBaseService.getTableFormIdValue("uf_fa_pgjb_dt1");
            // 获取当前用户
            SimpleEmployee currentUser = UserContext.getCurrentUser();

            if (currentUser == null) {
                String method = String.format("调用%s.updateByIRId(%s,%s)-->获取当前currentUser为空！", methodName, invoiceId, ysId);
                log.error(method);

            }
            String userId = String.valueOf(currentUser.getId());

            Map<String, Object> mainTableField = new HashMap<>();
            List<String> mainTableFields = new ArrayList<>();
            mainTableFields.add("id");
            mainTableField.put("mainTable", mainTableFields);


            EbFormDataReq builder = new EbFormDataReq.Builder()
                    .userId(userId)
                    .objId(objId)
                    .needAdd("false")
                    .updateType("updatePolicy")
                    .updateField(mainTableField)
                    .datas(datas)
                    .build();
            EbFormDataResultVo resultVo = EBuilderUtil.updateFormDataV2(builder, iOpenPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());
            log.info("调用{}  更新数据返回结果：{}", methodName, JSONObject.toJSONString(resultVo));


            resultMap.put("isSuccess", 1);
        } catch (Exception e) {
            resultMap.put("isSuccess", 0);
            e.printStackTrace();
            log.info("更新发票、应收单出错!  错误信息: " + e.getMessage());
        }
        return resultMap;
    }

    /**
     * 根据勾稽记录ID更新勾稽记录状态
     * @param gjId
     * @return
     */

    @Override
    public Map<String, Object> updateByGjId(String gjId) {
        String sql = "select ying_sd, fa_psqd,id from uf_fa_pgjb_dt1 where id in (" + gjId + ")";
        List<Map<String, Object>> detailList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);

        Set ysIdSet = new HashSet();
        Set invoiceIdSet = new HashSet();
        List<List> ysfpList = new ArrayList<>();
        List<Map<String, Object>> datas = new ArrayList<>();
        for (Map<String, Object> detailMap : detailList) {
            String ying_sd = detailMap.get("ying_sd").toString();
            String fa_psqd = detailMap.get("fa_psqd").toString();
            ysIdSet.add(ying_sd);
            invoiceIdSet.add(fa_psqd);

            List<Object> ysfp = new ArrayList<>();
            ysfp.add(ying_sd);
            ysfp.add(fa_psqd);
            ysfpList.add(ysfp);

            String detailId = detailMap.get("id").toString();
            Map<String, Object> dataMap = new HashMap<>();
            JSONObject mainTableObject = new JSONObject();
            mainTableObject.put("ying_synch", "1");
            mainTableObject.put("id", detailId);
            dataMap.put("mainTable", mainTableObject);
            datas.add(dataMap);


        }
        String ysIdArea = setToString(ysIdSet);
        String invoiceIdArea = setToString(invoiceIdSet);

        Map<String, Object> resultMap = new HashMap<>();
        try {
            incomeService.statisticYsInfo(ysIdArea);
            incomeService.statisticInvoiceInfo(invoiceIdArea);

            // String updateSql = "update uf_fa_pgjb_dt1 set ying_synch=1 where id in (" + gjId + ")";
            // 从表单配置表中获取表单ID（objId）
            String objId = iDataBaseService.getTableFormIdValue("uf_fa_pgjb_dt1");
            // 获取当前用户
            SimpleEmployee currentUser = UserContext.getCurrentUser();

            if (currentUser == null) {
                String method = String.format("调用%s.updateByIRId(%s)-->获取当前currentUser为空！", methodName, gjId);
                log.error(method);

            }
            String userId = String.valueOf(currentUser.getId());
            Map<String, Object> mainTableField = new HashMap<>();
            List<String> mainTableFields = new ArrayList<>();
            mainTableFields.add("id");
            mainTableField.put("mainTable", mainTableFields);


            EbFormDataReq builder = new EbFormDataReq.Builder()
                    .userId(userId)
                    .objId(objId)
                    .needAdd("false")
                    .updateType("updatePolicy")
                    .updateField(mainTableField)
                    .datas(datas)
                    .build();
            EbFormDataResultVo resultVo = EBuilderUtil.updateFormDataV2(builder, iOpenPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());
            log.info("调用{}  更新数据返回结果：{}", methodName, JSONObject.toJSONString(resultVo));

            resultMap.put("isSuccess", 1);
            resultMap.put("msg", "数据更新成功");

        } catch (Exception e) {
            resultMap.put("isSuccess", 0);
            resultMap.put("msg", "数据更新失败，原因："+e.getMessage());
            e.printStackTrace();
        }
        return resultMap;
    }

    @Override
    public Map<String, Object> getList(String ids) {
        log.info("1、开始执行/zyhlw/web/invoice/updateStatus");
        // String ids = Util.null2String(request.getParameter("ids"));
        log.info("获取传过来的ids：" + ids);

        StringBuffer sb = new StringBuffer();
        String[] idGroup = ids.split(",");
        for (String tempId : idGroup) {
            if (StringUtils.isNotNull(tempId) && tempId.equals("") == false) {
                sb.append(tempId);
                sb.append(",");
            }
        }
        String whereStr = sb.toString().trim();
        if (whereStr.endsWith(",")) {
            whereStr = whereStr.substring(0, whereStr.length() - 1);
        }
        if (whereStr.length() > 0) {
            whereStr = " gouji_id in (" + whereStr + ") or id in (" + whereStr + ")";
        } else {
            whereStr = " 1=0 ";
        }

        Map<String, Object> resultMap = new HashMap<>();
        try {

            String selectDetailSql = "select id from uf_fa_pgjb_dt1  where " + whereStr;
            log.info("执行: " + selectDetailSql);
            List<Map<String, Object>> detailList = dataSqlService.eBuilderFromSqlAll(selectDetailSql, SourceType.LOGIC);
            List<Map<String, Object>> datas = new ArrayList<>();
            for (Map<String, Object> detailMap : detailList) {

                String detailId = detailMap.get("id").toString();
                Map<String, Object> dataMap = new HashMap<>();
                JSONObject mainTableObject = new JSONObject();
                mainTableObject.put("ying_synch", "1");
                mainTableObject.put("isfinish", "1");
                mainTableObject.put("id", detailId);
                dataMap.put("mainTable", mainTableObject);
                datas.add(dataMap);
            }

            // 从表单配置表中获取表单ID（objId）
            String objId = iDataBaseService.getTableFormIdValue("uf_fa_pgjb_dt1");
            // 获取当前用户
            SimpleEmployee currentUser = UserContext.getCurrentUser();

            if (currentUser == null) {
                String method = String.format("调用%s.getList(%s)-->获取当前currentUser为空！", methodName, ids);
                log.error(method);

            }
            String userId = String.valueOf(currentUser.getId());

            Map<String, Object> mainTableField = new HashMap<>();
            List<String> mainTableFields = new ArrayList<>();
            mainTableFields.add("id");
            mainTableField.put("mainTable", mainTableFields);


            EbFormDataReq builder = new EbFormDataReq.Builder()
                    .userId(userId)
                    .objId(objId)
                    .needAdd("false")
                    .updateType("updatePolicy")
                    .updateField(mainTableField)
                    .datas(datas)
                    .build();
            EbFormDataResultVo resultVo = EBuilderUtil.updateFormDataV2(builder, iOpenPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());
            log.info("调用{}  更新数据返回结果：{}", methodName, JSONObject.toJSONString(resultVo));

            resultMap.put("isSuccess", 1);
            log.info("2、执行结果：" + JSONObject.toJSONString(resultMap));
        } catch (Exception ex) {
            resultMap.put("isSuccess", 0);
            log.info("执行失败：" + ex.toString());
        }
        return resultMap;
    }

    private String setToString(Set set) {
        return Arrays.toString(set.toArray()).replace("[", "").replace("]", "");
    }


}
