package com.weaver.seconddev.zyhlw.service.impl.invoice;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.seconddev.zyhlw.action.invoice.FinanceCheckedAction;
import com.weaver.seconddev.zyhlw.domain.request.eb.EbFormDataReq;
import com.weaver.seconddev.zyhlw.domain.response.form.EbFormDataResultVo;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IGetInvoiceInformationService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.ebuilder.EBuilderUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.apache.commons.lang.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @Description 发票模块功能
 * @date 2025/2/19 17:38
 */

@Slf4j
@Service
public class GetInvoiceInformationImpl implements IGetInvoiceInformationService {
    public static final String S = "执行成功 结果{}";
    public static final String S1 = "{}开始执行";
    public static final String S2 = "{}ids:{}";
    public static final String FA_PHM = "fa_phm";
    public static final String S3 = "系统内部错误：";
    private final String simpleName = GetInvoiceInformationImpl.class.getSimpleName();
    @Resource
    IDataSqlService dataSqlService;
    @Resource
    IDataBaseService iDataBaseService;

    @Resource
    IOpenPlatformService iOpenPlatformService;

    @Resource
    CmicProperties cmicProperties;

    /**
     * 根据发票ID列表获取发票信息。
     *
     * @param ids 以逗号分隔的发票ID字符串
     * @return 包含发票信息的结果对象
     */
    @Override
    public WeaResult<Object> getInvoiceListByIds(String ids) {
        String method = String.format("调用%s.getInvoiceListByIds-->", simpleName);
        log.info(S1, method);
        log.info(S2, method, ids);

        if (ids.isEmpty()) {
            return WeaResult.fail("ids参数不能为空");
        }

        try {

            //发票信息库信息
            String sql = "select * from uf_fa_pxxk where id in (" + ids + ") and is_delete = 0 and tenant_key = 't6il1ypj4w'";
            List<Map<String, Object>> invoiceInformation = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);

            invoiceInformation.forEach(item -> {
                String id = String.valueOf(item.get("id"));

                //发票勾稽明细1 信息

                String sql1 = "select distinct to_char(ying_sd) ying_sd from uf_fa_pgjb_dt1 where is_delete = 0 and tenant_key = 't6il1ypj4w' and to_char(fa_psqd) = " + id;
                List<Map<String, Object>> invoiceCheckingDetails1 = dataSqlService.eBuilderFromSqlAll(sql1, SourceType.LOGIC);

                //应收单数据集合
                List<Map<String, Object>> specialobjList = new ArrayList<>();
                List<Long> receivableId = new ArrayList<>();

                invoiceCheckingDetails1.forEach(item1 -> {
                    String ysd = String.valueOf(item1.get("ying_sd"));
                    receivableId.add(Long.parseLong(ysd));
                    //应收列表信息
                    String sql3 = "select id,bao_zdbh from uf_ying_slb where is_delete = 0 and tenant_key = 't6il1ypj4w' and id = " + ysd;
                    Map<String, Object> receivable = dataSqlService.eBuilderFromSqlOne(sql3, SourceType.LOGIC);

                    specialobjList.add(receivable);

                });

                item.put("yingId", receivableId);
                item.put("specialobjList", specialobjList);

            });
            log.info(S, JSONObject.toJSONString(invoiceInformation));

            return WeaResult.success(invoiceInformation);

        } catch (Exception e) {
            log.info("调用GetInvoiceInformationImpl.getInvoiceListByIds  执行异常", e);
            return WeaResult.fail(S3 + e.getMessage());
        }
    }


    /**
     * 根据发票ID列表获取发票勾稽信息。
     *
     * @param ids 以逗号分隔的发票勾稽ID字符串
     * @return 包含发票勾稽信息的结果对象
     */
    @Override
    public WeaResult<Object> getReverseCheckOfInvoicesByIds(String ids) {
        String method = String.format("调用%s.getReverseCheckOfInvoicesByIds-->", simpleName);
        log.info(S1, method);
        log.info(S2, method, ids);
        if (ids.isEmpty()) {
            return WeaResult.fail("ids参数不能为空");
        }
        try {

            //查询发票勾稽信息
            String sql = "select * from uf_fa_pgjb_dt1 where is_delete = 0 and tenant_key = 't6il1ypj4w' and id in (" + ids + ")";
            List<Map<String, Object>> invoiceCheckingDetails1 = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);

            invoiceCheckingDetails1.forEach(item -> {
                String ysd = String.valueOf(item.get("ying_sd"));
                String fp = String.valueOf(item.get("fa_psqd"));

                //查询应收单信息
                String sql2 = "select * from uf_ying_slb where is_delete = 0 and tenant_key = 't6il1ypj4w' and id =" + ysd;
                Map<String, Object> receivablesInformation = dataSqlService.eBuilderFromSqlOne(sql2, SourceType.LOGIC);

                //查询发票信息
                String sql3 = "select * from uf_fa_pxxk where is_delete = 0 and tenant_key = 't6il1ypj4w' and id =" + fp;
                Map<String, Object> invoiceInformation = dataSqlService.eBuilderFromSqlOne(sql3, SourceType.LOGIC);

                //应收编号
                String bzd = String.valueOf(receivablesInformation.get("bao_zdbh"));

                //发票号码
                String fph = String.valueOf(invoiceInformation.get(FA_PHM));

                item.put("bao_zdbh", bzd);
                item.put(FA_PHM, fph);

            });

            log.info(S, JSONObject.toJSONString(invoiceCheckingDetails1));
            return WeaResult.success(invoiceCheckingDetails1);

        } catch (Exception e) {
            log.info("调用GetInvoiceInformationImpl.getReverseCheckOfInvoicesByIds  执行异常", e);
            return WeaResult.fail(S3 + e.getMessage());
        }
    }

    /**
     * 根据发票ID列表获取应收单信息。
     *
     * @param ids 以逗号分隔的应收单ID字符串
     * @return 包含应收信息的结果对象
     */
    @Override
    public WeaResult<Object> getReceivablesInformationByIds(String ids) {
        String method = String.format("调用%s.getReceivablesInformationByIds-->", simpleName);
        log.info(S1, method);
        log.info(S2, method, ids);

        StringBuffer sb = new StringBuffer();
        String[] idGroup = ids.split(",");

        for (String tempId : idGroup) {
            if (StringUtils.isNotBlank(tempId)) {
                sb.append(tempId);
                sb.append(",");
            }
        }

        // 去除最后一个逗号
        if (sb.length() > 0) {
            sb.setLength(sb.length() - 1);
        }

        String condition = "";
        if (sb.length() > 0) {
            condition = " and id in (" + sb + ")";
        }


        try {

            //查询应收单信息
            String sql = "select * from UF_YING_SLB where is_delete = 0 and tenant_key = 't6il1ypj4w'" + condition;
            List<Map<String, Object>> receivablesInformation = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
            receivablesInformation.forEach(item -> {
                String kh = String.valueOf(item.get("ke_hmc"));

                //查询客户ID
                String sqlSelect = "select max(id) as id from uf_ke_hxxb where is_delete = 0 and tenant_key = 't6il1ypj4w' and ltrim(rtrim(mcqpf)) = " + kh.trim();
                Map<String, Object> customerId = dataSqlService.eBuilderFromSqlOne(sqlSelect, SourceType.LOGIC);

                item.put("customerId", customerId.get("id"));

            });

            log.info(S, JSONObject.toJSONString(receivablesInformation));
            return WeaResult.success(receivablesInformation);
        } catch (Exception e) {
            log.info("调用GetInvoiceInformationImpl.getReceivablesInformationByIds  执行异常", e);
            return WeaResult.fail(S3 + e.getMessage());
        }


    }

    /**
     * 根据发票号码查询发票红冲、作废信息
     *
     * @param invoiceNumber 以逗号分隔的发票ID字符串
     * @return 包含发票信息的结果对象
     */
    @Override
    public WeaResult<Object> getInvoiceCancelInformationByInvoiceNumber(String invoiceNumber) {
        String method = String.format("调用%s.getInvoiceCancelInformationByInvoiceNumber-->", simpleName);
        log.info(S1, method);
        log.info("{}invoiceNumber:{}", method, invoiceNumber);

        if (invoiceNumber.isEmpty()) {
            return WeaResult.fail("invoiceNumber参数不能为空");
        }

        try {
            //查询发票信息
            String nodeId = iDataBaseService.getBaseDataValue("发票红冲及作废流程节点", "发票红冲及作废流程");
            String tableName = iDataBaseService.getBaseDataValue("发票红冲及作废流程表", "发票红冲及作废流程");

            //发票号码
            String[] invoiceNumberArr = invoiceNumber.split(",");
            //节点ID
            String[] nodeIdArr = nodeId.split(",");
            //红冲表，和作废表
            String[] tableNameArr = tableName.split(",");
            Set<Integer> list = new HashSet<>();
            for (int i = 0; i < invoiceNumberArr.length; i++) {
                for (String value : tableNameArr) {
                    String sql = "select t1.id from " + value + " t1," + value + "_dt1 t2  where t1.is_delete = 0 and t1.tenant_key = 't6il1ypj4w' and t2.is_delete = 0 and t2.tenant_key = 't6il1ypj4w' and t2.FORM_DATA_ID = t1.id  " +
                            "and  t2." + (value.equals(tableNameArr[1]) ? "hong_cfpje" : FA_PHM) + " in(" + invoiceNumberArr[i] + ")";
                    List<Map<String, Object>> invoiceCancelInformation = dataSqlService.workflowFromSqlAll(sql, SourceType.LOGIC);

                    for (Map<String, Object> item : invoiceCancelInformation) {
                        String requestId = String.valueOf(item.get("requestid"));
                        String sql2 = "SELECT * FROM wfc_requestbase  WHERE status != '归档' AND requestid = " + requestId + " AND delete_type = 0 AND tenant_key = 't6il1ypj4w'";
                        Map<String, Object> workflowCurrentOperator = dataSqlService.workflowFromSqlOne(sql2, SourceType.LOGIC);
                        if (!workflowCurrentOperator.isEmpty()){
                            list.add(i + 1);
                        }
                    }
                }
            }

            if (!list.isEmpty()){
                return WeaResult.fail("错误行" + StringUtils.strip(list.toString(), "[]") + "：不能选择正在红冲或作废申请中的发票");
            }

            return WeaResult.success("校验成功");
        } catch (Exception e) {
            log.info("调用GetInvoiceInformationImpl.getInvoiceCancelInformationByInvoiceNumber  执行异常", e);
            return WeaResult.fail(S3 + e.getMessage());
        }
    }


    /**
     * 根据ID查询发票反勾稽信息
     *
     * @param ids 以逗号分隔的发票ID字符串
     * @return 包含发票信息的结果对象
     */
    @Override
    public WeaResult<Object> getInvoiceCancelInformationByIds(String ids) {
        String method = String.format("调用%s.getInvoiceCancelInformationByIds-->", ids);
        log.info(S1, method);
        log.info("{}invoiceNumber:{}", method, ids);
        if (ids.isEmpty()) {
            return WeaResult.fail("invoiceNumber参数不能为空");
        }

        String[] idsArr = ids.split(",");
        List<Integer> list = new ArrayList();

        try {
            for (int i = 0; i < idsArr.length; i++) {
                String sql = "select fa_gdlx from uf_fa_pgjb_dt1 where is_delete = 0 and tenant_key = 't6il1ypj4w' and id in(" + ids + ")";
                List<Map<String, Object>> invoiceCancelInformation = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);

                for (Map<String, Object> item : invoiceCancelInformation) {
                    if (item.get("fa_gdlx").equals("2")) {
                        list.add(i + 1);
                        return WeaResult.fail("错误行：" + org.apache.commons.lang.StringUtils.strip(list.toString(), "[]") + "，该条勾稽记录已经被反勾稽或反勾稽审批中");
                    }
                }
            }
            return WeaResult.success("校验成功");
        } catch (Exception e) {
            log.info("调用GetInvoiceInformationImpl.getInvoiceCancelInformationByIds  执行异常", e);
            return WeaResult.fail(S3 + e.getMessage());
        }
    }


    /**
     * 从勾稽列表中获取相关的数据，更新原应收单数据，用逗号分隔
     *
     * @param affectedYsIdArea 本次受影响的应收单id
     */
    @Override
    public void statisticYsInfo(String affectedYsIdArea, String userid) {
        String method = String.format("调用%s.statisticYsInfo-->", simpleName);
        String ysTableId = iDataBaseService.getBaseValue("uf_ying_slb", "objId");
        log.info(S1, method);
        try {
            String sql = "select * from uf_ying_slb where is_delete = 0 and tenant_key = 't6il1ypj4w' and id in (" + affectedYsIdArea + ")";
            List<Map<String, Object>> ysList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
            BigDecimal lie_zje;
            Map<Integer, Map<String, Object>> ysMap = new HashMap<>();
            for (Map<String, Object> item : ysList) {
                String id = String.valueOf(item.get("id"));

                //列帐金额
                lie_zje = FinanceCheckedAction.parseBigDecimal(String.valueOf(item.get("lie_zje")));
                //开票金额
                BigDecimal kai_pjebhs = FinanceCheckedAction.parseBigDecimal(String.valueOf(item.get("kai_pjebhs")));
                //未开票金额=列帐金额-开票金额
                BigDecimal wei_gjje = lie_zje.subtract(kai_pjebhs);
                Map<String, Object> ys = new HashMap<>();
                ys.put("id", id);
                ys.put("lie_zje", lie_zje.toString());
                ys.put("kai_pjebhs", kai_pjebhs.toString());
                ys.put("wei_gjje", wei_gjje.toString());
                ysMap.put(Integer.valueOf(id), ys);

                sql = "select ying_sd, sum(case when fa_gdlx=0 then BEN_CGJJE else 0 end) - sum(case when fa_gdlx=1 then BEN_CGJJE else 0 end) as gou_jze from UF_FA_PGJB_DT1 where is_delete = 0 and tenant_key = 't6il1ypj4w' and YING_SD in (" + Integer.valueOf(id) + ") group by ying_sd";
                List<Map<String, Object>> gouJzeList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
                List<Map<String, Object>> newYsInfoList = new ArrayList<>();

                for (Map<String, Object> item2 : gouJzeList) {
                    //应收单ID
                    int ying_sd = Integer.parseInt(item2.get("ying_sd").toString());
                    // 统计的勾稽总额
                    BigDecimal gou_jze = FinanceCheckedAction.parseBigDecimal(item2.get("gou_jze").toString());
                    if (ysMap.get(ying_sd) == null) {
                        continue;
                    }
                    log.info("【统计】 === 应收单号: " + ying_sd + "  列账金额:  " + lie_zje + "  勾稽总额: " + gou_jze);
                    // 默认未勾稽
                    int fpgjzt = 2;
                    int kai_pzt = 0;

                    if (gou_jze.compareTo(lie_zje) == 0) {
                        // 已勾稽
                        fpgjzt = 0;
                        kai_pzt = 1;
                    } else if (gou_jze.compareTo(FinanceCheckedAction.parseBigDecimal("0")) > 0) {
                        // 部分勾稽
                        fpgjzt = 1;
                        kai_pzt = 2;
                    }

                    log.info("发票勾稽状态: " + fpgjzt);
                    Map<String, Object> map = new HashMap<>(6);
                    map.put("fpgjzt", ying_sd);
                    map.put("fpgjje", gou_jze.toString());
                    map.put("wkpje", lie_zje.subtract(gou_jze).toString());
                    map.put("kai_pjebhs", gou_jze.toString());
                    map.put("kai_pzt", kai_pzt);
                    map.put("id", ying_sd);
                    Map<String, Object> dataS = new HashMap<>(1);
                    dataS.put("mainTable", map);
                    newYsInfoList.add(dataS);

                }
                log.info("【更新】 === " + JSON.toJSONString(newYsInfoList));
                //uf_ying_slb
                if (!newYsInfoList.isEmpty()) {
                    Map<String, Object> mainTableField = new HashMap<>(1);
                    List<String> mainTableFields = new ArrayList<>();
                    mainTableFields.add("id");
                    mainTableField.put("mainTable", mainTableFields);

                    EbFormDataReq builder = new EbFormDataReq.Builder()
                            .userId(userid)
                            .objId(ysTableId)
                            .needAdd("true")
                            .updateType("updatePolicy")
                            .updateField(mainTableField)
                            .datas(newYsInfoList)
                            .build();
                    EbFormDataResultVo ebFormDataResultVo = EBuilderUtil.updateFormDataV2(builder, iOpenPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());
                    log.info("调用{}  更新数据返回结果：{}", method, JSONObject.toJSONString(ebFormDataResultVo));
                }
            }
        } catch (Exception e) {
            log.info("调用GetInvoiceInformationImpl.statisticYsInfo  执行异常", e);
        }
    }

    @Override
    public void statisticInvoiceInfo(String invoiceIdArea, String userid) {
        String method = String.format("调用%s.statisticInvoiceInfo-->", simpleName);
        log.info(S1, method);
        log.info("本次统计的发票id:  " + invoiceIdArea);

        String fpTableId = iDataBaseService.getBaseValue("uf_fa_pxxk", "objId");
        String fpGjTableId = iDataBaseService.getBaseValue("uf_fa_pgjb", "objId");
        try {
            String sql = "select FA_PSQD, (sum(case when fa_gdlx=0 then BEN_CGJJE else 0 end) - sum(case when fa_gdlx=1 then BEN_CGJJE else 0 end)) as gjze from UF_FA_PGJB_DT1 where is_delete = 0 and tenant_key = 't6il1ypj4w' and FA_PSQD in (" + invoiceIdArea + ") group by FA_PSQD";
            List<Map<String, Object>> gouJzeList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
            List<Map<String, Object>> statisticInvoiceInfoList = new ArrayList<>();
            List<Map<String, Object>> newInvoiceList = new ArrayList<>();
            for (Map<String, Object> item : gouJzeList) {
                String fa_psqd = item.get("fa_psqd").toString();
                String gjze = item.get("gjze").toString();
                Map<String, Object> statisticInvoiceInfo = new HashMap<>();
                statisticInvoiceInfo.put("fa_psqd", fa_psqd);
                statisticInvoiceInfo.put("gjze", gjze);
                statisticInvoiceInfoList.add(statisticInvoiceInfo);
            }
            log.info("统计的发票数据有: " + statisticInvoiceInfoList);
            // 获取受影响的发票
            sql = "select id, jin_e, fa_pzt, fa_pkyje, yi_gjje, wei_gjje, nvl(hong_cje,0) as hong_cje  from uf_fa_pxxk where is_delete = 0 and tenant_key = 't6il1ypj4w' and id in (" + invoiceIdArea + ")";
            List<Map<String, Object>> affectedInvoiceList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
            for (Map<String, Object> item : affectedInvoiceList) {
                String id = item.get("id").toString();
                for (Map<String, Object> statisticInvoiceInfo : statisticInvoiceInfoList) {
                    String invoiceId = statisticInvoiceInfo.get("fa_psqd").toString();
                    if (id == invoiceId) {
                        log.info("发票数据:  " + statisticInvoiceInfo);
                        BigDecimal gjze = FinanceCheckedAction.parseBigDecimal(String.valueOf(statisticInvoiceInfo.get("gjze")));
                        BigDecimal jin_e = FinanceCheckedAction.parseBigDecimal(item.get("jin_e").toString());
                        int fa_pzt = Integer.parseInt(item.get("fa_pzt").toString());
                        BigDecimal hong_cje = FinanceCheckedAction.parseBigDecimal(item.get("hong_cje").toString());

                        BigDecimal ke_yje = fa_pzt == 1 ? BigDecimal.valueOf(0) : jin_e.subtract(hong_cje);
                        BigDecimal wei_gjje = fa_pzt == 1 ? BigDecimal.valueOf(0) : ke_yje.subtract(gjze);

                        // 发票可用金额为发票总金额-已勾稽总额，但是作废发票可用金额为0
                        Map<String, Object> row = new HashMap<>(4);
                        row.put("fa_pkyje", ke_yje.toString());
                        row.put("yi_gjje", gjze.toString());
                        row.put("wei_gjje", wei_gjje.toString());
                        row.put("id", id);
                        Map<String, Object> dataS = new HashMap<>(1);
                        dataS.put("mainTable", row);
                        newInvoiceList.add(dataS);
                    }
                }
            }
            log.info("【更新】 newInvoiceList === " + JSON.toJSONString(newInvoiceList));
            if (!newInvoiceList.isEmpty()) {
                Map<String, Object> mainTableField = new HashMap<>(1);
                List<String> mainTableFields = new ArrayList<>();
                mainTableFields.add("id");
                mainTableField.put("mainTable", mainTableFields);
                EbFormDataReq builder = new EbFormDataReq.Builder()
                        .userId(userid)
                        .objId(fpTableId)
                        .needAdd("true")
                        .updateType("updatePolicy")
                        .updateField(mainTableField)
                        .datas(newInvoiceList)
                        .build();
                EbFormDataResultVo ebFormDataResultVo = EBuilderUtil.updateFormDataV2(builder, iOpenPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());
                log.info("调用{}  更新数据返回结果：{}", method, JSONObject.toJSONString(ebFormDataResultVo));

            }
            //UF_FA_PGJB_DT1
            changeStatus(invoiceIdArea, userid, fpGjTableId);

        } catch (Exception e) {
            log.info("调用GetInvoiceInformationImpl.getInvoiceCancelInformationByIds  执行异常", e);
        }
    }

    /**
     * 反勾稽之后是否存在勾稽金额  如果不存在修改状态为1
     * isfinish   1
     * 是否完成状态  修改为1
     */
    public void changeStatus(String fa_psqd, String userid,String fpGjTableId) {
        String sql = "select FA_PSQD, (sum(case when fa_gdlx=0 then BEN_CGJJE else 0 end) - sum(case when fa_gdlx=1 then BEN_CGJJE else 0 end)) as gjze from UF_FA_PGJB_DT1 where is_delete = 0 and tenant_key = 't6il1ypj4w' and FA_PSQD in (" + fa_psqd + ") group by FA_PSQD";
        List<Map<String, Object>> gouJzeList = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        List<Map<String, Object>> newInvoiceList = new ArrayList<>();

        for (Map<String, Object> statisticInvoiceInfoRs : gouJzeList) {

            String tempFa_psqd = statisticInvoiceInfoRs.get("FA_PSQD").toString();

            BigDecimal gjze = FinanceCheckedAction.parseBigDecimal(String.valueOf(statisticInvoiceInfoRs.get("gjze")));

            if (gjze.compareTo(new BigDecimal(0)) == 0) {
                Map<String, Object> map = new HashMap<>(2);
                map.put("isfinish", 1);
                map.put("fa_psqd", tempFa_psqd);
                Map<String, Object> dataS = new HashMap<>(1);
                dataS.put("mainTable", map);
                newInvoiceList.add(dataS);
            }
        }

        log.info("【更新】 newInvoiceList === " + JSON.toJSONString(newInvoiceList));
        if (!newInvoiceList.isEmpty()) {
            Map<String, Object> mainTableField = new HashMap<>(1);
            List<String> mainTableFields = new ArrayList<>();
            mainTableFields.add("fa_psqd");
            mainTableField.put("mainTable", mainTableFields);

            EbFormDataReq builder = new EbFormDataReq.Builder()
                    .userId(userid)
                    .objId(fpGjTableId)
                    .needAdd("true")
                    .updateType("updatePolicy")
                    .updateField(mainTableField)
                    .datas(newInvoiceList)
                    .build();
            EbFormDataResultVo ebFormDataResultVo = EBuilderUtil.updateFormDataV2(builder, iOpenPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());
            log.info("调用changeStatus更新数据返回结果：{}", JSONObject.toJSONString(ebFormDataResultVo));
        }
    }
}
