package com.weaver.seconddev.zyhlw.service.impl.ledgermanagement;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.ebuilder.form.client.service.emobile.IEtFormDatasetService;
import com.weaver.framework.rpc.annotation.RpcReference;
import com.weaver.seconddev.zyhlw.service.*;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.teams.domain.department.SimpleDepartment;
import com.weaver.teams.domain.user.SimpleEmployee;
import com.weaver.teams.security.context.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <h1>DepartmentLedgerServiceImpl 责任部门台账</h1>
 *
 * <p>Description: </p>
 *
 * <p>Note: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@Service
public class DepartmentLedgerServiceImpl implements IDepartmentLedgerService {

    private final String SIMPLE_NAME = DepartmentLedgerServiceImpl.class.getSimpleName();

    @RpcReference(group = "ebuilderform")
    IEtFormDatasetService iEtFormDatasetService;

    @Resource
    CmicProperties cmicProperties;

    @Resource
    IDataSqlService dataSqlService;

    @Resource
    IFormBaseService formBaseService;


    @Override
    public WeaResult<Object> getManagementSum(String condition1, String condition2) {
        String method = String.format("调用%s.getManagementSum-->", SIMPLE_NAME);
        log.info("{}开始执行责任部门台账管理部门侧接口数据, condition1: {}, condition2: {}", method, condition1, condition2);
        JSONObject jsonObject = new JSONObject();
        //获取当前用户
        SimpleEmployee currentUser = UserContext.getCurrentUser();
        log.info("{}用户信息:{}", method, JSON.toJSONString(currentUser));
        SimpleDepartment currentDepart = currentUser.getDepartment();
        Long departmentId = currentDepart.getId();
        String department = getDepartment(departmentId);
        String[] fruits = department.split("/");
        String departmentId2 = fruits[0];   //当前用户一级部门
        departmentId2 = "'%" + departmentId2 + "%'";
        //条件模块
        String and = " and ";
        String feng_xdj = "feng_xdj = ";    //风险等级
        String feng_xly = "feng_xly = ";    //风险领域
        String ze_rbm = "ze_rbm LIKE ";     //管理部门
        //条件为空
        if (condition1.isEmpty() && condition2.isEmpty()) {
            List<Map<String, Object>> sumList = RiskInformationBaseByDj(ze_rbm + departmentId2);
            jsonObject.put(this.getName(ze_rbm) + "sum", sumList);
        }
        //领域条件
        if (!condition1.isEmpty() && condition2.isEmpty()) {
            List<Map<String, Object>> sumList = RiskInformationBaseByDj(feng_xly + condition1 + and + ze_rbm + departmentId2);
            jsonObject.put(this.getName(ze_rbm) + "sum",sumList );
        }
        //等级条件
        if (condition1.isEmpty() && !condition2.isEmpty()) {
            List<Map<String, Object>> list = RiskInformationBaseByDj(feng_xdj + condition2 + and + ze_rbm + departmentId2);
            jsonObject.put(this.getName(ze_rbm) + "sum", list);
        }

        //等级条件 与 领域条件
        if (!condition1.isEmpty() && !condition2.isEmpty()) {
            List<Map<String, Object>> list = RiskInformationBaseByDj(feng_xly + condition1 + and + feng_xdj + condition2 + and + ze_rbm + departmentId2);
            jsonObject.put(this.getName(ze_rbm) + "sum", list);
        }

        return WeaResult.success(jsonObject);
    }

    @Override
    public WeaResult<Object> getExecutiveArmSum(String condition1, String condition2) {
        String method = String.format("调用%s.getExecutiveArmSum-->", SIMPLE_NAME);
        log.info("{}开始执行责任部门台账执行部门侧接口数据, condition1: {}, condition2: {}", method, condition1, condition2);
        JSONObject jsonObject = new JSONObject();
        //获取当前用户
        SimpleEmployee currentUser = UserContext.getCurrentUser();
        SimpleDepartment currentDepart = currentUser.getDepartment();
        Long departmentId = currentDepart.getId();
        String department = this.getDepartment(departmentId);
        String[] fruits = department.split("/");
        String departmentId2 = fruits[0];   //当前用户一级部门
        departmentId2 = "'%" + departmentId2 + "%'";
        //条件模块
        String and = " and ";
        String feng_xdj = "feng_xdj = ";    //风险等级
        String feng_xly = "feng_xly = ";    //风险领域
        String shi_ybm = "shi_ybm LIKE ";     //管理部门
        //条件为空
        if (condition1.isEmpty() && condition2.isEmpty()) {
            List<Map<String, Object>> sumList = RiskInformationBaseByDj(shi_ybm + departmentId2);
            jsonObject.put(this.getName(shi_ybm) + "sum", sumList);
        }
        //领域条件
        if (!condition1.isEmpty() && condition2.isEmpty()) {
            List<Map<String, Object>> sumList = RiskInformationBaseByDj(feng_xly + condition1 + and + shi_ybm + departmentId2);
            jsonObject.put(this.getName(shi_ybm) + "sum",sumList );
        }
        //等级条件
        if (condition1.isEmpty() && !condition2.isEmpty()) {
            List<Map<String, Object>> list = RiskInformationBaseByDj(feng_xdj + condition2 + and + shi_ybm + departmentId2);
            jsonObject.put(this.getName(shi_ybm) + "sum", list);
        }

        //等级条件 与 领域条件
        if (!condition1.isEmpty() && !condition2.isEmpty()) {
            List<Map<String, Object>> list = RiskInformationBaseByDj(feng_xly + condition1 + and + feng_xdj + condition2 + and + shi_ybm + departmentId2);
            jsonObject.put(this.getName(shi_ybm) + "sum", list);
        }

        return WeaResult.success(jsonObject);
    }

    @Override
    public WeaResult<Object> ResponsibleDepartmentLedger(String condition1, String condition2, String condition3) {
        String method = String.format("调用%s.ResponsibleDepartmentLedger-->", SIMPLE_NAME);
        log.info("{}开始执行责任部门台账页面数据接口, condition1: {}, condition2: {}", method, condition1, condition2);
        JSONObject jsonObject = new JSONObject();
        //获取当前用户
        SimpleEmployee currentUser = UserContext.getCurrentUser();
        log.info("{}用户信息:{}", method, JSON.toJSONString(currentUser));
        SimpleDepartment currentDepart = currentUser.getDepartment();
        Long departmentId = currentDepart.getId();
        String department = this.getDepartment(departmentId);
        String[] fruits = department.split("/");
        String departmentId2 = fruits[0];   //当前用户一级部门
        departmentId2 = "'%" + departmentId2 + "%'";
//        logger.info("当前用户：" + userid + "||" + "当前用户一级部门：" + departmentId2);

        //条件模块
        String and = " and ";
        String feng_xdj = "k.feng_xdj = ";    //风险等级
        String feng_xly = "k.feng_xly = ";    //风险领域

        if(condition3.equals("0")){
            condition3 = "k.ze_rbm LIKE ";
        }else if(condition3.equals("1")){
            condition3 = "k.shi_ybm LIKE ";
        }
        //条件为空
        if(!condition3.isEmpty()){
            if (condition1.isEmpty() && condition2.isEmpty()) {
                List<Map<String, Object>> list = RiskInformationBase(condition3 + departmentId2);
                jsonObject.put(this.getName(condition3) + "data", list);
            }

            //领域条件
            if (!condition1.isEmpty() && condition2.isEmpty()) {
                List<Map<String, Object>> list = RiskInformationBase(feng_xly + condition1 + and + condition3 + departmentId2);
                jsonObject.put(this.getName(condition3) + "data", list);
            }

            //等级条件
            if (condition1.isEmpty() && !condition2.isEmpty()) {
                List<Map<String, Object>> list = RiskInformationBase(feng_xdj + condition2 + and + condition3 + departmentId2);
                jsonObject.put(this.getName(condition3) + "data", list);
            }

            //等级条件 与 领域条件
            if (!condition1.isEmpty() && !condition2.isEmpty()) {
                List<Map<String, Object>> list = RiskInformationBase(feng_xly + condition1 + and + feng_xdj + condition2 + and + condition3 + departmentId2);
                jsonObject.put(this.getName(condition3) + "data", list);
            }
        }
        return WeaResult.success(jsonObject);
    }

    private String getDepartment(Long departmentId) {
        String sql = "SELECT d.id AS id, d.fullname AS departmentname, d.parent AS supdepid, d.rank FROM eteams.department d WHERE d.type = 'department' AND d.virtualid = 1 AND d.delete_type = 0 AND d.tenant_key = '%s' START WITH d.id = %s CONNECT BY PRIOR d.parent = d.id ORDER BY d.rank asc";
        sql = String.format(sql, cmicProperties.getHostTenantKey(), departmentId);
        List<Map<String, Object>> dataResult = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        // 用/分割部门id，返回字符串
        return dataResult.stream().map(map -> map.get("id").toString()).collect(Collectors.joining("/"));
    }

    private List<Map<String, Object>> RiskInformationBaseByDj(String condition) {
        String sql = "select count(CASE WHEN d.feng_xdj= '0' THEN 1 END) a,count(CASE WHEN d.feng_xdj = '1' THEN 1 END) b,count(CASE WHEN d.feng_xdj='2' THEN 1 END)c from uf_feng_xxxk  d where " + condition + " and feng_xzt != 1 and delete_type=0";
        List<Map<String, Object>> data = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        List<Map<String, Object>> list = new ArrayList<>();
        HashMap<String, Object> map1 = new HashMap<>();
        HashMap<String, Object> map2 = new HashMap<>();
        HashMap<String, Object> map3 = new HashMap<>();

        if (!data.isEmpty()) {
            data.forEach(e -> {
                map1.put("value", e.getOrDefault("a", 0));
                map1.put("name", "A级风险点");
                list.add(map1);
                map2.put("value", e.getOrDefault("b", 0));
                map2.put("name", "B级风险点");
                list.add(map2);
                map3.put("value", e.getOrDefault("c", 0));
                map3.put("name", "C级风险点");
                list.add(map3);
            });
        }
        return list;
    }

    private String getName(String name) {
        if (name.equals("0")) {
            name = "A级风险点";
        }
        if (name.equals("1")) {
            name = "B级风险点";
        }
        if (name.equals("2")) {
            name = "C级风险点";
        }
        if (name.equals("ze_rbm LIKE ")) {
            name = "ze_rbm_";
        }
        if (name.equals("shi_ybm LIKE ")) {
            name = "shi_ybm_";
        }
        return name;
    }

    private List<Map<String, Object>> RiskInformationBase(String condition) {
        Map<String, String> converter = formBaseService.getFormOptionsValue("uf_feng_xxxk", "feng_xdj", 0);
        String sql = "select k.ffeng_xbh,k.feng_xmc,k.feng_xly,k.feng_xdj,k.shi_ssj,k.ze_rbm,k.shi_ybm,k.id,ly.feng_xly feng_xly_span,d.fullname ze_rbm_span from uf_feng_xxxk k left join uf_feng_xly ly on ly.id=to_char(k.feng_xly) left join eteams.department d on d.id=k.ze_rbm where feng_xzt != 1 and k.delete_type=0 " + condition;
        log.info("条件查询风险信息库sql：{}", sql);
        List<Map<String, Object>> data = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        data.forEach(e -> {
            e.put("feng_xdj_span", converter.getOrDefault(e.get("feng_xdj").toString(), ""));
        });
        return data;
    }
}
