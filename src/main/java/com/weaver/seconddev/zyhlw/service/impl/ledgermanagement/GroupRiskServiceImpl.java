package com.weaver.seconddev.zyhlw.service.impl.ledgermanagement;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.seconddev.zyhlw.domain.portal.DepartmentModel;
import com.weaver.seconddev.zyhlw.domain.request.eb.EbFormDataReq;
import com.weaver.seconddev.zyhlw.domain.response.form.EbFormDataResultVo;
import com.weaver.seconddev.zyhlw.service.*;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import com.weaver.seconddev.zyhlw.util.ebuilder.EBuilderUtil;
import com.weaver.teams.domain.user.SimpleEmployee;
import com.weaver.teams.security.context.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <h1>GroupRiskServiceImpl 风险统计执行</h1>
 *
 * <p>Description: </p>
 *
 * <p>Note: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@Service
public class GroupRiskServiceImpl implements IGroupRiskService {

    private final String SIMPLE_NAME = GroupRiskServiceImpl.class.getSimpleName();


    @Resource
    CmicProperties cmicProperties;

    @Resource
    IDataSqlService dataSqlService;
    @Resource
    IDataBaseService dataBaseService;
    @Resource
    IOpenPlatformService openPlatformService;

    @Override
    public WeaResult<Object> execute() {
        String method = String.format("调用%s.execute-->", SIMPLE_NAME);
        log.info("{}开始执行", method);
        try {
            List<Map<String, Object>> fxbhList = this.getFxbhList();
            log.info("{}获取到的fxbList: {}", method, JSON.toJSONString(fxbhList));
            fxbhList.forEach(this::updateState);
            List<Map<String, Object>> jtList = this.getJTList();
            jtList.forEach(item -> {
                String id = item.getOrDefault("id", "").toString();
                String uid = item.getOrDefault("creator", "").toString();
                List<String> mainId = this.getMainId(id);
                if (mainId.isEmpty()) {
                    this.updateRiskInformationBase("1", id, uid);
                } else {
                    this.updateRiskInformationBase("0", id, uid);
                }
            });

            log.info("{}执行成功", method);
        } catch (Exception e) {
            log.info("{}异常：{}", method, e.getMessage());
        }
        return WeaResult.success(null);
    }

    private List<Map<String, Object>> getFxbhList() {
        String sql = "select id,creator from uf_feng_xxxk where feng_xzt != 1 and delete_type = 0";
        return dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
    }

    /**
     * 更新互联网风险点状态
     *
     * @param data
     */
    private void updateState(Map<String, Object> data) {
        String mainId = data.get("id").toString();
        List<String> jtList = this.getJTByMainId(mainId);
        int jtCount = jtList.size();
        log.info("更新互联网风险点状态mainId:{}", mainId);
        log.info("更新互联网风险点状态jtCount:{}", jtCount);
        String state;
        if (this.isSplit(mainId).size() > 1) {
            //拆分
            log.info("更新互联网风险点状态-拆分");
            state = "2";
        } else if (jtCount == 1) {
            //保留
            log.info("更新互联网风险点状态-保留");
            state = "0";
        } else if (jtCount > 1) {
            //合并
            log.info("更新互联网风险点状态-合并");
            state = "1";
        } else {
            //个性化
            log.info("更新互联网风险点状态-个性化");
            state = "3";
        }
        log.info("更新互联网风险点状态-state：{}", state);
        this.updateGroupManual(state, mainId, data.get("creator").toString());
    }

    /**
     * 获取集团风险列表
     *
     * @param mainId
     * @return
     */
    private List<String> getJTByMainId(String mainId) {
        String sql = "select id from uf_ji_tscfxb where id in ( select to_char(dt3.dui_yjtscfxbh) from uf_feng_xxxk_dt3 dt3 inner join uf_feng_xxxk x on dt3.form_data_id = %s and x.id = dt3.form_data_id and x.feng_xzt != 1 and dt3.delete_type = 0 and x.delete_type = 0 )";
        sql = String.format(sql, mainId);
        List<Map<String, Object>> data = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        return data.stream().map(map -> map.get("id"))
                .filter(Objects::nonNull)
                .map(Object::toString)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    //查询是否拆分
    private List<String> isSplit(String mainId) {
        String sql = "select id from uf_feng_xxxk where feng_xzt != 1 and id in( select to_char(form_data_id) form_data_id from uf_feng_xxxk_dt3 where to_char(dui_yjtscfxbh) in( select to_char(dui_yjtscfxbh) dui_yjtscfxbh from uf_feng_xxxk_dt3 where form_data_id = %s and delete_type=0) and delete_type=0 )";
        sql = String.format(sql, mainId);
        List<Map<String, Object>> data = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        return data.stream().map(map -> map.get("id"))
                .filter(Objects::nonNull)
                .map(Object::toString)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    /**
     * 更新对应的风险信息库集的团映射关系字段
     *
     * @param yu_jtscdysgx
     * @param id
     */
    private void updateGroupManual(String yu_jtscdysgx, String id, String uid) {
        log.info("更新对应的风险信息库集的团映射关系字段,yu_jtscdysgx:{}, id:{}, uid:{}",yu_jtscdysgx, id, uid);
        if (!id.isEmpty()) {
            String objId = dataBaseService.getBaseValue( "uf_feng_xxxk", "objId");
            List<Map<String, Object>> datas = new ArrayList<>();
            Map<String, Object> dataMap = new HashMap<>(2);
            JSONObject mainTableObject = new JSONObject();
            mainTableObject.put("yu_jtscdysgx", yu_jtscdysgx);
            mainTableObject.put("id", id);
            dataMap.put("mainTable", mainTableObject);
            datas.add(dataMap);
            Map<String, Object> mainTableField = new HashMap<>(1);
            List<String> mainTableFields = new ArrayList<>();
            mainTableFields.add("userid");
            mainTableField.put("mainTable", mainTableFields);
            EbFormDataReq builder = new EbFormDataReq.Builder()
                    .userId(uid)
                    .objId(objId)
                    .needAdd("false")
                    .updateType("updatePolicy")
                    .updateField(mainTableField)
                    .datas(datas)
                    .build();
            EbFormDataResultVo resultVo = EBuilderUtil.updateFormDataV2(builder, openPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());
            if (resultVo.getData().getDataJson().get(0).getStatus()) {
                log.info("更新对应的风险信息库集的团映射关系字段成功");
            } else {
                log.info("更新对应的风险信息库集的团映射关系字段失败,{}", JSON.toJSONString(resultVo));
            }
        }
    }
    private List<Map<String, Object>> getJTList() {
        String sql = "select id,creator from uf_ji_tscfxb where delete_type = 0";
        return dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
    }

    /**
     * 根据风险编号查出风险信息库id
     *
     * @param fxbh
     * @return
     */
    private List<String> getMainId(String fxbh) {
        String sql = "select id from uf_feng_xxxk where id in (select form_data_id from  uf_feng_xxxk_dt3 " +
                "where to_char(dui_yjtscfxbh) in (" + fxbh + ") group by form_data_id) and feng_xzt != 1 and delete_type=0";
        List<Map<String, Object>> data = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        return data.stream().map(map -> map.get("id"))
                .filter(Objects::nonNull)
                .map(Object::toString)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    /**
     * 更新对应的集团手册的映射关系字段
     *
     * @param ying_sgxzt
     * @param id
     */
    private void updateRiskInformationBase(String ying_sgxzt, String id, String uid) {
        if (!id.isEmpty()) {
            String objId = dataBaseService.getBaseValue( "uf_ji_tscfxb", "objId");
            List<Map<String, Object>> datas = new ArrayList<>();
            Map<String, Object> dataMap = new HashMap<>(2);
            JSONObject mainTableObject = new JSONObject();
            mainTableObject.put("ying_sgxzt", ying_sgxzt);
            mainTableObject.put("id", id);
            dataMap.put("mainTable", mainTableObject);
            datas.add(dataMap);
            Map<String, Object> mainTableField = new HashMap<>(1);
            List<String> mainTableFields = new ArrayList<>();
            mainTableFields.add("userid");
            mainTableField.put("mainTable", mainTableFields);
            EbFormDataReq builder = new EbFormDataReq.Builder()
                    .userId(uid)
                    .objId(objId)
                    .needAdd("false")
                    .updateType("updatePolicy")
                    .updateField(mainTableField)
                    .datas(datas)
                    .build();
            EbFormDataResultVo resultVo = EBuilderUtil.updateFormDataV2(builder, openPlatformService.getAccessToken(), cmicProperties.getOpenPlatformUrl());
            if (resultVo.getData().getDataJson().get(0).getStatus()) {
                log.info("执行更新对应的集团手册的映射关系字段成功");
            } else {
                log.info("执行更新对应的集团手册的映射关系字段失败,{}", JSON.toJSONString(resultVo));
            }
        }
    }
}
