package com.weaver.seconddev.zyhlw.service.impl.ledgermanagement;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.common.cache.base.BaseCache;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.eteams.file.client.file.FileData;
import com.weaver.eteams.file.client.file.FileObj;
import com.weaver.eteams.file.client.param.RemoteUploadParam;
import com.weaver.file.ud.api.FileDownloadService;
import com.weaver.file.ud.api.FileUploadService;
import com.weaver.seconddev.zyhlw.domain.portal.DepartmentModel;
import com.weaver.seconddev.zyhlw.service.*;
import com.weaver.seconddev.zyhlw.service.impl.export.ExcelExporterFactory;
import com.weaver.seconddev.zyhlw.service.impl.export.entity.ExcelExportParam;
import com.weaver.seconddev.zyhlw.service.impl.export.entity.ExcelExportResult;
import com.weaver.seconddev.zyhlw.service.impl.export.service.ExportService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import com.weaver.seconddev.zyhlw.util.cache.CacheKey;
import com.weaver.seconddev.zyhlw.util.cache.CacheModuleKey;
import com.weaver.teams.domain.EntityType;
import com.weaver.teams.domain.user.SimpleEmployee;
import com.weaver.teams.security.context.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.*;


/**
 * <h1>ResponsiblePersonLedgerServiceImpl 责任人台账</h1>
 *
 * <p>Description: </p>
 *
 * <p>Note: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@Service
public class ResponsiblePersonLedgerServiceImpl implements IResponsiblePersonLedgerService {

    private final String SIMPLE_NAME = ResponsiblePersonLedgerServiceImpl.class.getSimpleName();

    private static final ConcurrentHashMap<String, Future<?>> EXECUTE_FUTURES = new ConcurrentHashMap<>();
    private static final ThreadPoolExecutor POOL = new ThreadPoolExecutor(5, 10, 10, TimeUnit.MINUTES, new ArrayBlockingQueue<>(10), r -> {
        Thread thread = new Thread(r);
        thread.setName(CacheKey.ASYNC_EXPORT);
        thread.setDaemon(false);
        return thread;
    });

    @Resource
    CmicProperties cmicProperties;

    @Resource
    IDataSqlService dataSqlService;

    @Resource
    IDeptService deptService;
    @Resource
    BaseCache baseCache;

    @Resource
    private FileUploadService fileUploadService;

    @Resource
    private ExportService exportService;

    @Autowired
    private FileDownloadService fileDownloadService;


    @Override
    public WeaResult<Object> HuoQuStandingBook(String ly, String dj, String gettype) {
        String method = String.format("调用%s.HuoQuStandingBook-->", SIMPLE_NAME);
        log.info("{}开始执行, ly: {}, dj: {}, gettype: {}", method, ly, dj, gettype);
        JSONObject jsonObject = new JSONObject();
        SimpleEmployee currentUser = UserContext.getCurrentUser();
        String uid = StringUtils.null2String(currentUser.getUid());
        log.info("{}用户id为:{}", method, uid);
        log.info("{}接口入参:领域:{};等级:{};用户:{}", method, ly, dj, uid);

        try {
            List<String> fields;
            List<Map<String, Object>> preventions;
            List<Map<String, Object>> directs;
            if (ly.trim().isEmpty() && dj.trim().isEmpty()) {
                if (gettype.trim().isEmpty()) {
                    //获取领域数据
                    fields = this.getFields();
                    jsonObject.put("field", fields);
                    //直接责任人数据
                    directs = this.getDirects(uid, ly, dj, "1");
                    jsonObject.put("option1", directs);
                    //防控责任人数据
                    preventions = this.getDirects(uid, ly, dj, "2");
                    jsonObject.put("option2", preventions);
                } else {
                    List<Map<String, Object>> DirectRiskDetails;
                    if (gettype.equals("1")) {
                        DirectRiskDetails = this.getRiskDetails(uid, ly, dj, gettype);
                        jsonObject.put("Direct", DirectRiskDetails);
                    } else {
                        DirectRiskDetails = this.getRiskDetails(uid, ly, dj, gettype);
                        jsonObject.put("Prevention", DirectRiskDetails);
                    }
                }
            } else {
                fields = this.getFields();
                jsonObject.put("field", fields);
                directs = this.getDirects(uid, ly, dj, "1");
                jsonObject.put("option1", directs);
                preventions = this.getDirects(uid, ly, dj, "2");
                jsonObject.put("option2", preventions);
                List<Map<String, Object>> DirectRiskDetails = this.getRiskDetails(uid, ly, dj, "1");
                jsonObject.put("Direct", DirectRiskDetails);
                List<Map<String, Object>> PreventionRiskDetails = this.getRiskDetails(uid, ly, dj, "2");
                jsonObject.put("Prevention", PreventionRiskDetails);
            }

            return WeaResult.success(jsonObject);
        } catch (Exception var16) {
            log.info("{}报错原因:{}", method, var16.getMessage());
            return WeaResult.fail(var16.getMessage());

        }
    }

    @Override
    public void downloadAsyncExportFile(String key, HttpServletResponse response) {
        long beginTimeMillis = System.currentTimeMillis();
        String method = String.format("调用%s.downloadAsyncExportFile-->", SIMPLE_NAME);
        try {
            log.info("{}开始执行, key: {}", method, key);
            String cacheKey = CacheKey.ASYNC_EXPORT_KEY + ":" + key;
            log.info("{}缓存key, key: {}", method, cacheKey);
            boolean containsKey = baseCache.containsKey(CacheModuleKey.ASYNC_EXPORT_MODULE, cacheKey);
            log.info("{}containsKey: {}", method, containsKey);

            if (!containsKey) {
                throw new Exception("导出失败");
            }
            Object cacheObject = baseCache.get(CacheModuleKey.ASYNC_EXPORT_MODULE, cacheKey);
            if (cacheObject != null) {
                FileObj fileObj = JSON.parseObject(JSON.toJSONString(cacheObject), FileObj.class);
                String fileName = fileObj.getName();
                // 下载附件文件
                FileData fileData = fileDownloadService.downloadFile(fileObj.getFileid());
                log.info("{}文件下载成功, fileData: {}", method, JSON.toJSONString(fileData));
                String fileSuffix = fileName.substring(fileName.lastIndexOf("."));
                Workbook workbook;
                try (InputStream inputStream = fileData.getInputStream()) {
                    // 在这里进行读取操作
                    if (".xls".equals(fileSuffix)) {
                        workbook = new HSSFWorkbook(inputStream);
                    } else if (".xlsx".equals(fileSuffix)) {
                        workbook = new XSSFWorkbook(inputStream);
                    } else {
                        throw new Exception("文件导出后缀不正确");
                    }
                    workbook.write(response.getOutputStream());
                    response.setContentType("application/vnd.ms-excel; charset=utf-8");
                    response.setHeader("Content-Type", "application/vnd.ms-excel; charset=utf-8");
                    response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));

                }
            } else {
                throw new Exception("导出异常");
            }
        } catch (Exception e) {
            log.info("异常：{}", e.getMessage());
        } finally {
            log.info("{}接口执行总耗时间:{}", method, (System.currentTimeMillis() - beginTimeMillis) + " ms");
        }
    }

    @Override
    public WeaResult<Object> queryAsyncExportProgress(String key) {
        String method = String.format("调用%s.queryAsyncExportProgress-->", SIMPLE_NAME);
        long beginTimeMillis = System.currentTimeMillis();
        boolean isDone;
        try {
            log.info("{}开始执行, key:{}", method, key);
            if (StringUtils.isBlank(key)) {
                throw new IllegalArgumentException("查询参数缺失");
            }
            Future<?> future = EXECUTE_FUTURES.get(key);
            isDone = future.isDone();
            log.info("{}future.isDone(): {}", method, isDone);
            if (isDone) {
                String cacheKey = CacheKey.ASYNC_EXPORT_KEY + ":" + key;
                log.info("{}缓存key, key: {}", method, cacheKey);
                boolean containsKey = baseCache.containsKey(CacheModuleKey.ASYNC_EXPORT_MODULE, cacheKey);
                log.info("{}containsKey: {}", method, containsKey);
                if (containsKey) {
                    Object cacheObject = baseCache.get(CacheModuleKey.ASYNC_EXPORT_MODULE, cacheKey);
                    if (cacheObject == null) {
                        isDone = false;
                    }
                }
            }

        } catch (Exception e) {
            log.info("{}异常：{}", method, e.getMessage());
            isDone = false;
        } finally {
            log.info("{}接口执行总耗时间:{}", method, (System.currentTimeMillis() - beginTimeMillis) + " ms");
        }
        return isDone ? WeaResult.success(isDone) : WeaResult.fail("导出失败");
    }

    @Override
    public WeaResult<Object> asyncExport(String templateId, String type, String idList, HttpServletResponse response) {
        long beginTimeMillis = System.currentTimeMillis();
        String method = String.format("调用%s.asyncExport-->", SIMPLE_NAME);
        try {
            log.info("{}开始执行,templateId:{},type:{},idList:{}", method, templateId, type, idList);
            SimpleEmployee user = UserContext.getCurrentUser();
            if (StringUtils.isBlank(type) || StringUtils.isBlank(idList)) {
                return WeaResult.fail("导出参数缺失");
            }
            final String key = UUID.randomUUID().toString().replace("-", "");
            // 查询数据源
            Future<?> submit = POOL.submit(() -> {
                try {
                    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                    ExcelExportResult exportResult = exportService.async().doExport(
                            new ExcelExportParam(templateId, type, idList, user, byteArrayOutputStream));
                    log.info("{},打印exportResult: {}", method, JSON.toJSONString(exportResult));
                    String fileName = exportResult.getFileName();
                    // 上传excel
                    RemoteUploadParam remoteUploadParam = new RemoteUploadParam(fileName, String.valueOf(System.currentTimeMillis()), "ebuilder");
                    FileObj fileObj = fileUploadService.uploadLocalFile(byteArrayOutputStream.toByteArray(), user.getUid(), null, remoteUploadParam);
                    log.info("{},打印上传实体fileObj: {}", method, JSON.toJSONString(fileObj));
                    String cacheKey = CacheKey.ASYNC_EXPORT_KEY + ":" + key;
                    boolean success = baseCache.set(CacheModuleKey.ASYNC_EXPORT_MODULE, cacheKey, fileObj);
                    log.info("{}cacheKey写入缓存完成: {}, cache:{}", method, success, JSON.toJSONString(fileObj));
                } catch (Exception e) {
                    log.error("{}异步导出任务{}异常：{}", method, key, e);
                }
            });
            EXECUTE_FUTURES.put(key, submit);
            log.info("{}接口执行总耗时间:{}", method, (System.currentTimeMillis() - beginTimeMillis) + " ms");
            return WeaResult.success(key);
        } catch (Exception e) {
            log.info("{}异常：{}", method, e.getMessage());
            return WeaResult.fail(e.getMessage());
        }
    }

    @Override
    public void export(String templateId, String type, String idList, HttpServletResponse response) {
        String method = String.format("调用%s.export-->", SIMPLE_NAME);
        SimpleEmployee currentUser = UserContext.getCurrentUser();

        try {
            log.info("{}开始执行", method);
            log.info("{}ConfigrableExcelExportApi入参templateId: {}; type:  {}; idList：{}", method, templateId, type, idList);
            if (StringUtils.isBlank(type) || StringUtils.isBlank(idList)) {
                throw new IllegalArgumentException("导出参数缺失");
            }
            ExcelExportResult exportResult = exportService.async().doExport(new ExcelExportParam(templateId, type, idList, currentUser, response.getOutputStream()));
            response.setContentType("application/vnd.ms-excel; charset=utf-8");
            response.setHeader("Content-Type", "application/vnd.ms-excel; charset=utf-8");
            response.setHeader("Content-Disposition", "attachment; filename="
                    + URLEncoder.encode(exportResult.getFileName(), "UTF-8"));
            byte[] data = exportResult.getData();
            try (OutputStream outputStream = response.getOutputStream()) {
                outputStream.write(data);
                outputStream.flush();
            } catch (IOException e) {
                log.error("写入响应输出流时发生异常: {}", e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new IllegalArgumentException("导出异常:" + e.getMessage());
        }
    }

    private List<String> getFields() {
        List<String> fields = new ArrayList<>();
        String sql = "select  c.feng_xly  from  uf_feng_xly  c,(select  to_char(feng_xly) feng_xly  from  uf_feng_xxxk  where feng_xzt ='0' and delete_type=0  group  by  to_char(feng_xly)) e  where  e.feng_xly =  c.id and c.delete_type=0";
        List<Map<String, Object>> dataResult = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        if (!dataResult.isEmpty()) {
            dataResult.forEach(map -> fields.add(map.get("feng_xly").toString()));
        }
        return fields;
    }

    private List<Map<String, Object>> getDirects(String user, String lyType, String djType, String gettype) {
        ArrayList<Map<String, Object>> maps = new ArrayList<>();
        HashMap<String, Object> map = new HashMap<>();
        HashMap<String, Object> map1 = new HashMap<>();
        HashMap<String, Object> map2 = new HashMap<>();
        String sql;
        String sql2;
        if (gettype.equals("1")) {
            sql2 = "select  d.feng_xdj  from  uf_feng_xxxk  d  where (INSTR(d.zhi_jzrr, %s) > 0  or  id  in  (select form_data_id from uf_feng_xxxk_dt2 where INSTR(zhi_xbmzjzrr,%s)>0))  and  d.feng_xzt= 0 and d.delete_type=0";
            if (!lyType.trim().isEmpty()) {
                String id = this.getLyName(lyType);
                sql2 = sql2 + "  and  to_char(d.feng_xly)=" + id;
            }
            sql = "select count(CASE WHEN feng_xdj= '0' THEN 1 END) a,count(CASE WHEN feng_xdj = '1' THEN 1 END) b,count(CASE WHEN feng_xdj='2' THEN 1 END)c from (" + sql2 + ")";
        } else {
            sql2 = "select  d.feng_xdj  from  uf_feng_xxxk  d  where (INSTR(d.zhu_zbmfkzrr, %s) > 0  or  id  in  (select mainid from uf_feng_xxxk_dt2 where INSTR(zhi_xbmfkzrr,%s)>0))  and  d.feng_xzt= 0 and d.delete_type=0";
            if (!lyType.trim().isEmpty()) {
                String id = this.getLyName(lyType);
                sql2 = sql2 + "  and  to_char(d.feng_xly)=" + id;
            }
            sql = "select count(CASE WHEN feng_xdj= '0' THEN 1 END) a,count(CASE WHEN feng_xdj = '1' THEN 1 END) b,count(CASE WHEN feng_xdj='2' THEN 1 END)c from (" + sql2 + ")";
        }
        sql = String.format(sql, user, user);
        log.info("sql语句为:{}", sql);
        Map<String, Object> data = dataSqlService.eBuilderFromSqlOne(sql, SourceType.LOGIC);

        map.put("name", "A级风险点");
        map.put("value", data.getOrDefault("a", 0));
        map1.put("name", "B级风险点");
        map1.put("value", data.getOrDefault("b", 0));
        map2.put("name", "C级风险点");
        map2.put("value", data.getOrDefault("c", 0));

        if (djType.trim().isEmpty()) {
            log.info("添加全部参数");
            maps.add(map);
            maps.add(map1);
            maps.add(map2);
        } else if (djType.equals("0")) {
            log.info("a添加部分参数");
            maps.add(map);
        } else if (djType.equals("1")) {
            log.info("b添加部分参数");
            maps.add(map1);
        } else if (djType.equals("2")) {
            log.info("c添加部分参数");
            maps.add(map2);
        }

        log.info("直接风险点数据:{}", maps);
        return maps;
    }

    private String getLyName(String s) {
        String sql = "select id from uf_feng_xly where feng_xly = '%s' and delete_type=0";
        sql = String.format(sql, s);
        Map<String, Object> data = dataSqlService.eBuilderFromSqlOne(sql, SourceType.LOGIC);

        String id = data.get("id").toString();

        log.info("领域id为:{}", id);
        return id;
    }

    private List<Map<String, Object>> getRiskDetails(String user, String lyType, String djType, String gettype) {
        String sql;
        if (gettype.equals("1")) {
            sql = "select  d.id,d.ffeng_xbh,d.feng_xmc,d.feng_xly,d.feng_xdj,d.shi_ssj,d.ze_rbm,d.zhu_zbmfkzrr  from  uf_feng_xxxk  d  where (INSTR(d.zhi_jzrr, %s) > 0  or  id  in  (select form_data_id from uf_feng_xxxk_dt2 where INSTR(zhi_xbmzjzrr,%s)>0))  and  d.feng_xzt= 0 and d.delete_type=0";
            if (!lyType.trim().isEmpty()) {
                String id = this.getLyName(lyType);
                log.info("获取的领域id为:{}", id);
                sql = sql + "  and  to_char(d.feng_xly)=" + id;
            }
        } else {
            sql = "select  d.id,d.ffeng_xbh,d.feng_xmc,d.feng_xly,d.feng_xdj,d.shi_ssj,d.ze_rbm,d.zhu_zbmfkzrr  from  uf_feng_xxxk  d  where (INSTR(d.zhu_zbmfkzrr, %s) > 0  or  id  in  (select form_data_id from uf_feng_xxxk_dt2 where INSTR(zhi_xbmfkzrr,%s)>0))  and  d.feng_xzt= 0 and d.delete_type=0";
            if (!lyType.trim().isEmpty()) {
                String id = this.getLyName(lyType);
                log.info("获取的领域id为:{}", id);

                sql = sql + " and to_char(d.feng_xly)=" + id;
            }
        }
        if (djType.equals("0")) {
            sql = sql + " and d.feng_xdj = 0";
        } else if (djType.equals("1")) {
            sql = sql + " and d.feng_xdj = 1";
        } else if (djType.equals("2")) {
            sql = sql + " and d.feng_xdj = 2";
        }
        sql = String.format(sql, user, user);
        log.info("sql语句为:{}", sql);
        List<Map<String, Object>> data = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
        if (!data.isEmpty()) {
            data.forEach(e -> {
                String feng_xly = e.get("feng_xly").toString();
                feng_xly = this.lyTranslation(feng_xly);
                String feng_xdj = e.get("feng_xdj").toString();
                feng_xdj = this.djTranslation(feng_xdj);
                String ze_rbm = e.get("ze_rbm").toString();
                ze_rbm = this.bmTranslation(ze_rbm);
                e.put("feng_xly", feng_xly);
                e.put("feng_xdj", feng_xdj);
                e.put("ze_rbm", ze_rbm);
            });
        }

        log.info("获取到的风险详细数据为:{}", JSON.toJSONString(data));
        return data;
    }

    private String lyTranslation(String ly) {
        String sql = "select  feng_xly from  uf_feng_xly where id =" + ly;
        Map<String, Object> data = dataSqlService.eBuilderFromSqlOne(sql, SourceType.LOGIC);
        return data.getOrDefault("feng_xly", "").toString();
    }

    private String djTranslation(String dj) {
        if (dj.equals("0")) {
            return "A级";
        } else if (dj.equals("1")) {
            return "B级";
        } else {
            return dj.equals("2") ? "C级" : "";
        }
    }

    private String bmTranslation(String bm) {
        DepartmentModel departmentModel = deptService.getDeptById(bm, cmicProperties.getHostTenantKey());
        return departmentModel.getFullname();
    }

}
