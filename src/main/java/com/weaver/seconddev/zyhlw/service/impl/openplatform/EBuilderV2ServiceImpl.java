package com.weaver.seconddev.zyhlw.service.impl.openplatform;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.weaver.seconddev.zyhlw.domain.response.todo.ebuilder.EbGetDataV2Response;
import com.weaver.seconddev.zyhlw.service.IEBuilderV2Service;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import groovy.util.logging.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * EBuilderV2版本，开发平台相关操作
 * <AUTHOR>
 */
@lombok.extern.slf4j.Slf4j
@Service
@Slf4j
@JsonIgnoreProperties(ignoreUnknown = true)
public class EBuilderV2ServiceImpl implements IEBuilderV2Service {

    @Resource
    CmicProperties cmicProperties;
    @Resource
    private IOpenPlatformService openPlatformService;

    /**
     * eb获取分页数据v2
     *
     * @param objId 表单id
     * @param userid 用户id
     * @param pageNo 当前页
     * @param pageSize 每页数 最大支持1000
     * @param fieldNoFindIgnore 传入字段不存在是否忽略(默认忽略; n/false不忽略)
     * @return tb每页数据
     */
    @Override
    public EbGetDataV2Response getDataV2(String objId, Long userid, Long pageNo, Long pageSize, String fieldNoFindIgnore) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Accept", "application/json");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("access_token", openPlatformService.getAccessToken());
        paramMap.put("userid", userid);
        JSONObject dataJsonObject = new JSONObject();
        JSONObject headerObject = new JSONObject();
        headerObject.put("objId", objId);
        JSONObject pageInfoObject = new JSONObject();
        pageInfoObject.put("pageNo", pageNo);
        pageInfoObject.put("pageSize", pageSize);
        JSONObject operationinfoObject = new JSONObject();
        operationinfoObject.put("fieldNoFindIgnore", fieldNoFindIgnore);
        dataJsonObject.put("header", headerObject);
        dataJsonObject.put("pageInfo", pageInfoObject);
        dataJsonObject.put("operationinfo", operationinfoObject);
        paramMap.put("datajson", dataJsonObject);
        String host = cmicProperties.getOpenPlatformUrl() + "/api/ebuilder/form/dataset/v2/getAllData";
        log.info("eb获取分页数据v2，url:{}, params:{}", host, JSONObject.toJSONString(paramMap));

        String resJson = HttpRequest.post(host).body(JSONObject.toJSONString(paramMap)).headerMap(headers, true).execute().body();
        log.info("eb获取分页数据v2，resJson:{}", resJson);
        EbGetDataV2Response entity = new EbGetDataV2Response();
        ObjectMapper mapper = new ObjectMapper();
        try {
            entity = mapper.readValue(resJson, EbGetDataV2Response.class);
            return entity;
        } catch (JsonProcessingException e) {
            log.info("eb获取分页数据v2异常:{}", e.getMessage());
            log.error(e.getMessage());
            return entity;
        }

    }

}
