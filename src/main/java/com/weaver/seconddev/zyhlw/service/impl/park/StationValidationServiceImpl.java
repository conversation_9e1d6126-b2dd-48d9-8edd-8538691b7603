package com.weaver.seconddev.zyhlw.service.impl.park;


import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.openapi.pojo.dept.res.DeptVo;
import com.weaver.openapi.pojo.user.res.vo.UserInfoResult;
import com.weaver.openapi.service.DeptService;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.service.IStationValidationService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import com.weaver.seconddev.zyhlw.util.constant.WorkingSpaceConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * 园区行政服务管理系统 - 工位管理模块服务接口实现类
 *
 * <AUTHOR>
 * @date 2025年04月30日 16:05
 */
@Service
@Slf4j
public class StationValidationServiceImpl implements IStationValidationService {

    @Resource
    private IOpenPlatformService openPlatformService;

    @Resource
    private CmicProperties cmicProperties;

    @Resource
    private IDataSqlService dataSqlService;


    @Override
    public Boolean isSameDept(Long userId, Long targetDepartmentId) {
        log.info("判断部门是否相同: userId={}, targetDepartmentId={}", userId, targetDepartmentId);

        // 先查询当前用户部门
        UserInfoResult userInfoResult = openPlatformService.getUser(userId);
        if (userInfoResult == null || CollectionUtils.isEmpty(userInfoResult.getDepartment())) {
            log.info("用户信息为空或部门信息为空，返回false");
            return false;
        }
        log.info("当前用户部门列表: {}", userInfoResult.getDepartment());
        Long departmentId = userInfoResult.getDepartment().get(0);
        log.info("当前用户部门为: {}", departmentId);

        try {
            // 查找当前用户的部门
            DeptVo deptVo = getDeptV2(openPlatformService.getAccessToken(),
                    departmentId, cmicProperties.getOpenPlatformUrl());
            if (deptVo.getDepartment().getParent() == null) {
                return departmentId.equals(targetDepartmentId);
            }

            String parentDepartmentId = deptVo.getDepartment().getParent().getId();
            log.info("当前用户的上级部门为: {}", parentDepartmentId);

            // 判断目标部
            boolean result = targetDepartmentId.toString().equals(parentDepartmentId);
            log.info("目标部门{}是否与当期用户上级部门一致: {}", targetDepartmentId, result);
            return result;
        } catch (Exception e) {
            log.error("判断部门是否相同出错", e);
            return false;
        }
    }

    /**
     * 用于测试的包装方法，便于在测试中模拟
     *
     * @param accessToken     访问令牌
     * @param departmentId    部门ID
     * @param openPlatformUrl 开放平台URL
     * @return 部门信息
     */
    protected DeptVo getDeptV2(String accessToken, Long departmentId, String openPlatformUrl) {
        return DeptService.getDeptV2(accessToken, departmentId, openPlatformUrl, null);
    }

    @Override
    public List<Map<String, String>> getStationInfoByIds(String ids) {
        log.info("-------根据ID列表获取工位信息开始--------");
        log.info("工位ID列表: {}", ids);
        return queryStationInfo("id", ids);
    }

    @Override
    public List<Map<String, String>> getStationInfoByStationNums(String stationNums) {
        log.info("-------根据工位编号获取工位信息开始--------");
        log.info("工位编号列表: {}", stationNums);
        return queryStationInfo("gong_wbh", stationNums);
    }

    /**
     * 查询工位信息的通用方法
     *
     * @param fieldName 查询字段名称（id或gong_wbh）
     * @param values    查询字段值，以逗号分隔
     * @return 工位信息列表
     */
    private List<Map<String, String>> queryStationInfo(String fieldName, String values) {
        try {
            // 将逗号分隔的列表转换为数组
            String[] valueArray = values.split(",");

            // 构建查询SQL，使用参数化查询防止SQL注入
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("select * from ").append(WorkingSpaceConstants.SEAT_INFO_TABLE).append(" where ").append(fieldName).append(" in (");

            // 构建占位符列表
            List<String> placeholders = new ArrayList<>();
            for (int i = 0; i < valueArray.length; i++) {
                placeholders.add("?");
            }
            sqlBuilder.append(String.join(",", placeholders));
            sqlBuilder.append(")");

            // 构建参数列表
            List<String> params = new ArrayList<>(Arrays.asList(valueArray));

            // 执行查询
            List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(sqlBuilder.toString(), SourceType.LOGIC, params);
            log.info("查询到的工位信息数量: {}", dataList.size());

            // 处理结果集
            List<Map<String, String>> stationInfoList = new ArrayList<>();
            for (Map<String, Object> data : dataList) {
                Map<String, String> row = new HashMap<>();
                row.put("id", StringUtils.null2String(data.get("ID")));
                row.put("gong_wbh", StringUtils.null2String(data.get("GONG_WBH")));
                row.put("ban_gl", StringUtils.null2String(data.get("BAN_GL")));
                row.put("bei_z", StringUtils.null2String(data.get("BEI_Z")));
                row.put("qu_y", StringUtils.null2String(data.get("QU_Y")));
                row.put("gong_wzt", StringUtils.null2String(data.get("GONG_WZT")));
                row.put("shi_yr", StringUtils.null2String(data.get("SHI_YR")));
                row.put("lian_xfs", StringUtils.null2String(data.get("LIAN_XFS")));
                row.put("suo_sdw", StringUtils.null2String(data.get("SUO_SDW")));
                row.put("gong_wgsbm", StringUtils.null2String(data.get("GONG_WGSBM")));
                row.put("ren_ylx", StringUtils.null2String(data.get("REN_YLX")));
                stationInfoList.add(row);
            }

            log.info("-------查询工位信息结束--------");
            return stationInfoList;

        } catch (Exception e) {
            log.error("查询工位信息异常: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<Map<String, String>> getPeopleInfoByIds(String ids) {
        log.info("-------根据人员ID获取人员信息开始--------");
        log.info("人员ID列表: {}", ids);

        try {
            // 将逗号分隔的ID列表转换为数组
            String[] idArray = ids.split(",");

            // 构建查询SQL，使用参数化查询防止SQL注入
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("select * from ").append(WorkingSpaceConstants.HRM_INFO_TABLE).append(" where id in (");

            // 构建占位符列表
            List<String> placeholders = new ArrayList<>();
            for (int i = 0; i < idArray.length; i++) {
                placeholders.add("?");
            }
            sqlBuilder.append(String.join(",", placeholders));
            sqlBuilder.append(")");

            // 构建参数列表
            List<String> params = new ArrayList<>(Arrays.asList(idArray));

            // 执行查询
            List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(sqlBuilder.toString(), SourceType.LOGIC, params);
            log.info("查询到的人员信息数量: {}", dataList.size());

            // 处理结果集
            List<Map<String, String>> peopleInfoList = new ArrayList<>();
            for (Map<String, Object> data : dataList) {
                Map<String, String> row = new HashMap<>();
                row.put("id", StringUtils.null2String(data.get("ID")));
                row.put("xing_m", StringUtils.null2String(data.get("XING_M")));
                row.put("bu_mksssdw", StringUtils.null2String(data.get("BU_MKSSSDW")));
                row.put("bu_m", StringUtils.null2String(data.get("BU_M")));
                row.put("ren_ylx", StringUtils.null2String(data.get("REN_YLX")));
                row.put("shi_fsqgw", StringUtils.null2String(data.get("SHI_FSQGW")));
                row.put("ren_yzt", StringUtils.null2String(data.get("REN_YZT")));
                row.put("gong_wsqbm", StringUtils.null2String(data.get("GONG_WSQBM")));
                peopleInfoList.add(row);
            }

            log.info("-------根据人员ID获取人员信息结束--------");
            return peopleInfoList;

        } catch (Exception e) {
            log.error("根据人员ID获取人员信息异常: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }
}
