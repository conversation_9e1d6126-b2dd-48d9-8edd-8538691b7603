package com.weaver.seconddev.zyhlw.service.impl.park;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.ebuilder.form.client.service.emobile.IEtFormDatasetService;
import com.weaver.framework.rpc.annotation.RpcReference;
import com.weaver.seconddev.zyhlw.domain.park.GetFloorInfoRespVO;
import com.weaver.seconddev.zyhlw.domain.park.GetLeaderSeatsRespVO;
import com.weaver.seconddev.zyhlw.domain.park.GetMeetingInformationRespVO;
import com.weaver.seconddev.zyhlw.domain.park.GetPlanInfoRespVO;
import com.weaver.seconddev.zyhlw.domain.park.GetSeatsRespVO;
import com.weaver.seconddev.zyhlw.domain.park.SyncStationPeopleInfoRespVO;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.service.IWorkingSpaceService;
import com.weaver.seconddev.zyhlw.service.impl.export.converter.Converter;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import com.weaver.seconddev.zyhlw.util.constant.WorkingSpaceConstants;
import com.weaver.seconddev.zyhlw.util.converter.ConverterUtil;
import com.weaver.seconddev.zyhlw.util.ebuilder.EBuilderUtil;
import com.weaver.seconddev.zyhlw.util.enums.DsLogicGroupIdEnum;
import com.weaver.seconddev.zyhlw.util.park.WorkingSpaceUtil;
import com.weaver.teams.domain.user.SimpleEmployee;
import com.weaver.teams.security.context.UserContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 园区行政服务管理系统 - 工位管理模块服务接口实现类
 *
 * <AUTHOR>
 * @date 2025年04月25日 16:01
 */
@Service
@Slf4j
@SuppressWarnings("SpellCheckingInspection")
public class WorkingSpaceServiceImpl implements IWorkingSpaceService {

    @Resource
    IDataSqlService dataSqlService;

    @Resource
    private CmicProperties cmicProperties;

    @Resource
    private WorkingSpaceUtil workingSpaceUtil;

    @Resource
    private ConverterUtil converterUtil;

    @Resource
    private IOpenPlatformService openPlatformService;

    @RpcReference(group = "ebuilderform")
    IEtFormDatasetService etFormDatasetService;

    // 人员状态列表 0在职 1离职 2删除
    private static final List<String> REN_YZT_LIST = Arrays.asList("0", "1", "2");

    @Override
    public Boolean checkIsAdmin(String userId, String roleId) {
        if (StringUtils.isEmpty(userId) || StringUtils.isEmpty(roleId)) {
            return false;
        }
        String querySql = "SELECT hrmrolemembers.id AS id\n"
                + "FROM auth_user_role hrmrolemembers\n"
                + "WHERE hrmrolemembers.role_id = ?\n"
                + "\tAND hrmrolemembers.user_id = ?\n"
                + "\tAND hrmrolemembers.delete_type = 0\n"
                + "\tAND hrmrolemembers.tenant_key = ?";
        Map<String, Object> data = dataSqlService.executeCommonSqlOne(querySql, SourceType.LOGIC,
                DsLogicGroupIdEnum.HRM.getGroupId(), Arrays.asList(roleId, userId, cmicProperties.getHostTenantKey()));
        String id = StringUtils.null2String(data.get("id".toUpperCase()), "");
        return StringUtils.isNotEmpty(id);
    }

    @Override
    public SyncStationPeopleInfoRespVO syncStationPeopleInfo(Boolean async) {
        SyncStationPeopleInfoRespVO.SyncStationPeopleInfoRespVOBuilder respVOBuilder = SyncStationPeopleInfoRespVO
                .builder();
        log.info("-------同步人员信息工位数据开始--------");

        try {
            // 工位信息数据
            String stationInfoSql = "select * from " + WorkingSpaceConstants.SEAT_INFO_TABLE;
            List<Map<String, Object>> stationInfoList = dataSqlService.eBuilderFromSqlAll(stationInfoSql,
                    SourceType.LOGIC);
            log.info("获取工位信息数据，共{}条", stationInfoList.size());

            // 工位人员信息数据
            String stationPeopleSql = "select * from " + WorkingSpaceConstants.HRM_INFO_TABLE;
            List<Map<String, Object>> stationPeopleList = dataSqlService.eBuilderFromSqlAll(stationPeopleSql,
                    SourceType.LOGIC);
            log.info("获取工位人员信息数据，共{}条", stationPeopleList.size());

            // 需要修改的数据集合
            List<Map<String, Object>> peopleUpdateDataList = new ArrayList<>();
            List<Map<String, Object>> stationUpdateDataList = new ArrayList<>();
            List<String> stationPeopleIds = new ArrayList<>();

            // 创建工位使用者ID的集合和映射，用于快速查找
            Set<String> stationUserIds = new HashSet<>();
            Map<String, Map<String, Object>> userStationMap = new HashMap<>();

            // 预处理工位信息，收集工位使用者ID和映射关系
            for (Map<String, Object> station : stationInfoList) {
                String shiYr = StringUtils.null2String(station.get("shi_yr"));
                if (StringUtils.isNotEmpty(shiYr)) {
                    stationUserIds.add(shiYr);
                    userStationMap.put(shiYr, station);
                }
            }

            // 处理人员工位信息
            for (Map<String, Object> stationPeople : stationPeopleList) {
                String id = StringUtils.null2String(stationPeople.get("id"));
                stationPeopleIds.add(id);

                String gongWsqbm = StringUtils.null2String(stationPeople.get("gong_wsqbm"));

                // 如果该人员是工位使用者，直接从映射中获取对应工位信息
                if (stationUserIds.contains(id)) {
                    Map<String, Object> station = userStationMap.get(id);
                    String gongWbh = StringUtils.null2String(station.get("gong_wbh"));

                    // 构建人员信息更新数据
                    Map<String, Object> mainTable = new HashMap<>();
                    mainTable.put("id", id);
                    mainTable.put("shi_fsqgw", "0");
                    mainTable.put("gong_wsqbm", gongWsqbm);
                    mainTable.put("gong_wbh", gongWbh);

                    Map<String, Object> dataItem = new HashMap<>();
                    dataItem.put("mainTable", mainTable);
                    peopleUpdateDataList.add(dataItem);
                }
            }

            // 创建人员ID集合，用于快速检查人员是否存在
            Set<String> peopleIds = new HashSet<>();
            for (Map<String, Object> people : stationPeopleList) {
                peopleIds.add(StringUtils.null2String(people.get("id")));
            }

            // 处理工位信息
            for (Map<String, Object> station : stationInfoList) {
                String stationId = StringUtils.null2String(station.get("id"));
                String shiYr = StringUtils.null2String(station.get("shi_yr"));

                // 如果工位有使用者，但该使用者不在人员列表中，需要清空工位信息
                if (StringUtils.isNotEmpty(shiYr) && !peopleIds.contains(shiYr)) {
                    // 确定工位状态
                    String gongWzt = StringUtils.null2String(station.get("gong_wzt"));
                    String gongWgsbm = StringUtils.null2String(station.get("gong_wgsbm"));
                    String state = "1".equals(gongWzt) || "0".equals(gongWzt) ? gongWzt
                            : (StringUtils.isEmpty(gongWgsbm) ? "0" : "2");

                    // 构建工位信息更新数据
                    Map<String, Object> mainTable = new HashMap<>();
                    mainTable.put("id", stationId);
                    mainTable.put("shi_yr", "");
                    mainTable.put("lian_xfs", "");
                    mainTable.put("suo_sdw", "");
                    mainTable.put("ren_ylx", "");
                    mainTable.put("gong_wzt", state);

                    Map<String, Object> dataItem = new HashMap<>();
                    dataItem.put("mainTable", mainTable);
                    stationUpdateDataList.add(dataItem);
                }
            }

            if (!CollectionUtils.isEmpty(peopleUpdateDataList) || !CollectionUtils.isEmpty(stationUpdateDataList)) {
                log.info("需要更新的工位人员信息数据,size: {}", peopleUpdateDataList.size());
                log.info("需要更新的工位信息数据,size:: {}", stationUpdateDataList.size());
                // 使用API调用更新数据
                SimpleEmployee currentUser = UserContext.getCurrentUser();
                String userId = String.valueOf(currentUser.getUid());

                if (Boolean.TRUE.equals(async)) {
                    CompletableFuture.runAsync(() -> processUpdate(
                            peopleUpdateDataList, stationPeopleIds, userId, stationUpdateDataList));
                    return respVOBuilder.message("异步更新已启动,请稍后查看更新结果").code("1").build();
                } else {
                    processUpdate(
                            peopleUpdateDataList, stationPeopleIds, userId, stationUpdateDataList);
                    return respVOBuilder.message("更新成功").code("1").build();
                }
            } else {
                return respVOBuilder.message("无需更新").code("0").build();
            }
        } catch (Exception e) {
            log.error("同步人员信息工位数据异常: {}", e.getMessage(), e);
            return respVOBuilder.message("更新失败，错误：" + e.getMessage()).code("0").build();
        }
    }

    private void processUpdate(List<Map<String, Object>> peopleUpdateDataList, List<String> stationPeopleIds,
            String userId, List<Map<String, Object>> stationUpdateDataList) {
        try {
            // 1. 清空人员信息表工位数据
            if (!peopleUpdateDataList.isEmpty()) {
                log.info("将人员信息表工位数据清空");

                // 获取ObjId
                String hrmInfoTableObjId = EBuilderUtil.getFormObjId(etFormDatasetService,
                        cmicProperties.getOpenPlatformUrl(), userId, openPlatformService.getAccessToken(),
                        WorkingSpaceConstants.HRM_INFO_TABLE, "objId");
                log.info("获取到的人员信息表ObjId: {}", hrmInfoTableObjId);

                // 构建清空数据的请求
                List<Map<String, Object>> clearDataList = getClearDataList();

                // 使用通用方法分批处理清空操作
                String clearError = workingSpaceUtil.batchProcessData(clearDataList, userId, hrmInfoTableObjId,
                        "清空人员信息表工位数据", Lists.newArrayList("ren_yzt"));
                if (clearError != null) {
                    log.error("清空人员信息表工位数据失败，错误：{}", clearError);
                    return;
                }

                // 2. 异步更新人员信息表工位数据
                log.info("开始更新人员信息表工位数据");

                // 使用异步方法处理更新人员信息表工位数据
                workingSpaceUtil.batchProcessData(peopleUpdateDataList, userId, hrmInfoTableObjId, "更新人员信息表工位数据", null);
            }

            // 3. 异步更新工位信息表人员数据
            if (!stationUpdateDataList.isEmpty()) {
                log.info("开始更新工位信息表人员数据");
                // 获取ObjId
                String seatInfoTableObjId = EBuilderUtil.getFormObjId(etFormDatasetService,
                        cmicProperties.getOpenPlatformUrl(), userId, openPlatformService.getAccessToken(),
                        WorkingSpaceConstants.SEAT_INFO_TABLE, "objId");
                log.info("获取到的工位信息表ObjId: {}", seatInfoTableObjId);

                // 使用异步方法处理更新工位信息表人员数据
                workingSpaceUtil.batchProcessData(stationUpdateDataList, userId, seatInfoTableObjId, "更新工位信息表人员数据",
                        null);
            }
        } catch (Exception e) {
            log.error("同步人员信息工位数据异常: {}", e.getMessage(), e);
        }
    }

    private static List<Map<String, Object>> getClearDataList() {
        List<Map<String, Object>> clearDataList = new ArrayList<>();
        for (String status : REN_YZT_LIST) {
            Map<String, Object> clearMainTable = new HashMap<>();
            clearMainTable.put("ren_yzt", status);
            clearMainTable.put("shi_fsqgw", "1");
            clearMainTable.put("gong_wsqbm", "");
            clearMainTable.put("gong_wbh", "");

            Map<String, Object> clearDataItem = new HashMap<>();
            clearDataItem.put("mainTable", clearMainTable);
            clearDataList.add(clearDataItem);
        }
        return clearDataList;
    }

    @Override
    public GetSeatsRespVO getSeats(String building, String floor, String state, String hrmType) {
        log.info("-------获取工位信息列表开始--------");

        try {
            // 防止SQL注入，使用预编译SQL和参数化查询
            StringBuilder sqlBuilder = new StringBuilder();
            List<String> params = new ArrayList<>();

            // 基础查询SQL
            sqlBuilder.append(buildSeatsQuerySql());
            // 添加过滤条件
            if (StringUtils.isNotEmpty(building)) {
                String[] buildingArr = building.split(",");
                sqlBuilder.append("AND a.ban_gl in (")
                        .append(String.join(",", Collections.nCopies(buildingArr.length, "?"))).append(")");
                params.addAll(Arrays.asList(buildingArr));
            }

            if (StringUtils.isNotEmpty(floor)) {
                String[] floorArr = floor.split(",");
                sqlBuilder.append("AND a.bei_z in (")
                        .append(String.join(",", Collections.nCopies(floorArr.length, "?"))).append(")");
                params.addAll(Arrays.asList(floorArr));
            }

            if (StringUtils.isNotEmpty(state)) {
                String[] stateArr = state.split(",");
                sqlBuilder.append("AND a.gong_wzt in (")
                        .append(String.join(",", Collections.nCopies(stateArr.length, "?"))).append(")");
                params.addAll(Arrays.asList(stateArr));
            }

            if (StringUtils.isNotEmpty(hrmType)) {
                sqlBuilder.append("AND a.ren_ylx = ?");
                params.add(hrmType);
            }

            // 执行查询
            List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(sqlBuilder.toString(),
                    SourceType.LOGIC, params);

            // 处理结果集
            List<Map<String, Object>> resultList = new ArrayList<>();
            for (Map<String, Object> data : dataList) {
                Map<String, Object> resultItem = new HashMap<>();
                for (Map.Entry<String, Object> entry : data.entrySet()) {
                    String key = entry.getKey().toLowerCase();
                    String value = StringUtils.null2String(entry.getValue());
                    resultItem.put(key, value);
                }
                resultList.add(resultItem);
            }

            // 获取选项数据
            Map<String, List<Map<String, Object>>> options = new HashMap<>();
            List<String> selectFields = new ArrayList<>();
            selectFields.add("ban_gl");
            selectFields.add("bei_z");
            selectFields.add("qu_y");
            selectFields.add("gong_wzt");
            selectFields.add("ren_ylx");

            for (String field : selectFields) {
                options.put(field, workingSpaceUtil.getSelectBoxOptions(WorkingSpaceConstants.SEAT_INFO_TABLE, field));
            }

            log.info("-------获取工位信息列表结束--------");
            return GetSeatsRespVO.builder().data(resultList).options(options).build();

        } catch (Exception e) {
            log.error("获取工位信息列表异常: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public GetPlanInfoRespVO getPlanInfo(String floorId) {
        log.info("-------获取工位参数配置表开始--------");

        try {
            // 构建查询SQL，使用参数化查询防止SQL注入
            StringBuilder sqlBuilder = new StringBuilder();
            List<String> params = new ArrayList<>();

            sqlBuilder.append("SELECT * FROM ").append(WorkingSpaceConstants.SEAT_PLAN_INFO_TABLE).append(" WHERE 1=1");

            // 添加楼层过滤条件
            if (StringUtils.isNotEmpty(floorId)) {
                sqlBuilder.append(" AND lou_cid = ?");
                params.add(floorId);
            }

            // 执行查询
            List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(sqlBuilder.toString(),
                    SourceType.LOGIC, params);

            // 处理结果集
            List<Map<String, Object>> resultList = new ArrayList<>();
            for (Map<String, Object> data : dataList) {
                Map<String, Object> resultItem = new HashMap<>();
                for (Map.Entry<String, Object> entry : data.entrySet()) {
                    String key = entry.getKey().toLowerCase();
                    String value = StringUtils.null2String(entry.getValue());
                    resultItem.put(key, value);
                }
                resultList.add(resultItem);
            }

            // 获取选项数据
            Map<String, List<Map<String, Object>>> options = new HashMap<>();
            List<String> selectFields = new ArrayList<>();
            selectFields.add("yuan_slx");

            for (String field : selectFields) {
                options.put(field,
                        workingSpaceUtil.getSelectBoxOptions(WorkingSpaceConstants.SEAT_PLAN_INFO_TABLE, field));
            }

            log.info("-------获取工位参数配置表结束--------");
            return GetPlanInfoRespVO.builder().data(resultList).options(options).build();

        } catch (Exception e) {
            log.error("获取工位参数配置表异常: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public GetFloorInfoRespVO getFloorInfo(String building, String floor) {
        log.info("-------获取工位楼层信息开始--------");

        try {
            // 构建查询SQL，使用参数化查询防止SQL注入
            StringBuilder sqlBuilder = new StringBuilder();
            List<String> params = new ArrayList<>();

            sqlBuilder.append("SELECT * FROM ").append(WorkingSpaceConstants.SEAT_FLOOR_INFO).append(" WHERE 1=1");

            // 添加楼宇过滤条件
            if (StringUtils.isNotEmpty(building)) {
                sqlBuilder.append(" AND ban_gl IN (?)");
                params.add(building);
            }

            // 添加楼层过滤条件
            if (StringUtils.isNotEmpty(floor)) {
                sqlBuilder.append(" AND lou_c IN (?)");
                params.add(floor);
            }

            // 执行查询
            List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(sqlBuilder.toString(),
                    SourceType.LOGIC, params);

            // 处理结果集
            List<Map<String, Object>> resultList = new ArrayList<>();
            for (Map<String, Object> data : dataList) {
                Map<String, Object> resultItem = new HashMap<>();
                for (Map.Entry<String, Object> entry : data.entrySet()) {
                    String key = entry.getKey().toLowerCase();
                    String value = StringUtils.null2String(entry.getValue());
                    resultItem.put(key, value);
                }
                resultList.add(resultItem);
            }

            // 获取选项数据
            Map<String, List<Map<String, Object>>> options = new HashMap<>();
            List<String> selectFields = new ArrayList<>();
            selectFields.add("ban_gl");
            selectFields.add("lou_c");

            for (String field : selectFields) {
                options.put(field, workingSpaceUtil.getSelectBoxOptions(WorkingSpaceConstants.SEAT_FLOOR_INFO, field));
            }

            log.info("-------获取工位楼层信息结束--------");
            return GetFloorInfoRespVO.builder().data(resultList).options(options).build();

        } catch (Exception e) {
            log.error("获取工位楼层信息异常: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public GetLeaderSeatsRespVO getLeaderSeats(String building, String floor) {
        log.info("-------获取领导办公室信息开始--------");
        try {
            // 构建查询SQL，使用参数化查询防止SQL注入
            StringBuilder sqlBuilder = new StringBuilder();
            List<String> params = new ArrayList<>();

            sqlBuilder.append("SELECT * FROM ").append(WorkingSpaceConstants.LEADER_SEAT_INFO_TABLE)
                    .append(" WHERE 1=1");

            // 添加楼宇过滤条件
            if (StringUtils.isNotEmpty(building)) {
                sqlBuilder.append(" AND ban_gl = ?");
                params.add(building);
            }

            // 添加楼层过滤条件
            if (StringUtils.isNotEmpty(floor)) {
                sqlBuilder.append(" AND lou_c = ?");
                params.add(floor);
            }

            // 执行查询
            List<Map<String, Object>> dataList = dataSqlService.eBuilderFromSqlAll(sqlBuilder.toString(),
                    SourceType.LOGIC, params);

            // 定义需要特殊处理的字段
            List<String> depFields = new ArrayList<>();
            depFields.add("bu_m");

            List<String> hrmFields = new ArrayList<>();
            hrmFields.add("xing_m");

            List<String> zwFields = new ArrayList<>();
            zwFields.add("zhi_w");

            // 处理结果集
            List<Map<String, Object>> resultList = new ArrayList<>();
            for (Map<String, Object> data : dataList) {
                Map<String, Object> resultItem = new HashMap<>();
                for (Map.Entry<String, Object> entry : data.entrySet()) {
                    String key = entry.getKey().toLowerCase();
                    String value = StringUtils.null2String(entry.getValue());
                    resultItem.put(key, value);

                    // 处理特殊字段
                    if (StringUtils.isNotEmpty(value)) {
                        if (depFields.contains(key)) {
                            // 部门字段，获取部门全称
                            resultItem.put(key + "span", workingSpaceUtil.getDepartmentFullNameById(value));
                        } else if (hrmFields.contains(key)) {
                            // 人员字段，获取人员姓名
                            resultItem.put(key + "span", workingSpaceUtil.getLastnameById2(value));
                        } else if (zwFields.contains(key)) {
                            // 职务字段，获取职务名称
                            resultItem.put(key + "span", workingSpaceUtil.getJobnameById(value));
                        }
                    }
                }
                resultList.add(resultItem);
            }

            // 获取选项数据
            Map<String, List<Map<String, Object>>> options = new HashMap<>();
            List<String> selectFields = new ArrayList<>();
            selectFields.add("ban_gl");
            selectFields.add("lou_c");

            for (String field : selectFields) {
                options.put(field,
                        workingSpaceUtil.getSelectBoxOptions(WorkingSpaceConstants.LEADER_SEAT_INFO_TABLE, field));
            }

            log.info("-------获取领导办公室信息结束--------");
            return GetLeaderSeatsRespVO.builder().data(resultList).options(options).build();

        } catch (Exception e) {
            log.error("获取领导办公室信息异常: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 构建工位信息查询SQL
     *
     * @return SQL查询字符串
     */
    private String buildSeatsQuerySql() {

        return "SELECT \n" +
                "    a.gong_wbh AS gong_wbh, \n" +
                "    dao_qsj AS dao_qsj, \n" +
                "    a.gong_wgsbm AS gong_wgsbm, \n" +
                "    a.suo_sdw AS suo_sdw, \n" +
                "    a.gong_wzt AS gong_wzt,\n" +
                "    a.qu_y AS qu_y, \n" +
                "    a.shi_yr AS shi_yr, \n" +
                "    a.ban_gl AS ban_gl, \n" +
                "    a.zuo_h AS zuo_h, \n" +
                "    a.bei_z AS bei_z,\n" +
                "    a.id AS id, \n" +
                "    a.ren_ylx AS ren_ylx, \n" +
                "    a.lian_xfs AS lian_xfs, \n" +
                "    c.xing_m AS shi_yrspan,\n" +
                "    b.cd || CASE \n" +
                "        WHEN b.pd IS NOT NULL THEN '/' || b.pd\n" +
                "        ELSE NULL\n" +
                "    END AS gong_wgsbmspan\n" +
                "FROM " +
                WorkingSpaceConstants.HRM_INFO_TABLE +
                " c\n" +
                "RIGHT JOIN " +
                WorkingSpaceConstants.SEAT_INFO_TABLE +
                " a ON c.id = a.shi_yr \n" +
                "LEFT JOIN (\n" +
                "    SELECT \n" +
                "        b.id AS departid, \n" +
                "        a.fullname AS pd, \n" +
                "        b.fullname AS cd\n" +
                "    FROM eteams.department b\n" +
                "    LEFT JOIN eteams.department a ON b.parent = a.id\n" +
                "    AND b.type = 'department'\n" +
                "    AND b.virtualid = 1\n" +
                "    AND b.delete_type = 0\n" +
                "    AND b.tenant_key = '" +
                cmicProperties.getHostTenantKey() +
                "'\n" +
                "    AND a.type = 'department'\n" +
                "    AND a.virtualid = 1\n" +
                "    AND a.delete_type = 0\n" +
                "    AND a.tenant_key = '" +
                cmicProperties.getHostTenantKey() +
                "' \n" +
                ") b ON a.gong_wgsbm = ltrim(rtrim(b.departid)) \n" +
                "WHERE 1 = 1\n" +
                "    AND a.gong_wbh IS NOT NULL ";
    }

    @Override
    public GetMeetingInformationRespVO getMeetingInformation(String building, String floor, String id,
            String meetingRoomsName) {
        log.info("-------获取会议室信息开始--------");

        try {
            // 防止SQL注入，使用预编译SQL和参数化查询
            StringBuilder sqlBuilder = new StringBuilder();
            List<String> params = new ArrayList<>();

            // 基础查询SQL
            sqlBuilder.append(
                    "select id, hui_ysmc, rong_nrs, tou_ysb, hui_yslx, coalesce(yu_dzt, 0) as yu_dzt, zhuang_t, ")
                    .append("coalesce(mian_j, '0.00㎡') as mian_j from ")
                    .append(WorkingSpaceConstants.MEETING_ROOM_TABLE).append(" where 1=1");

            // 添加过滤条件
            if (StringUtils.isNotEmpty(building)) {
                sqlBuilder.append(" and ban_gl = ?");
                params.add(building);
            }

            if (StringUtils.isNotEmpty(floor)) {
                sqlBuilder.append(" and lou_c = ?");
                params.add(floor);
            }

            if (StringUtils.isNotEmpty(id)) {
                sqlBuilder.append(" and id = ?");
                params.add(id);
            }

            if (StringUtils.isNotEmpty(meetingRoomsName)) {
                sqlBuilder.append(" and hui_ysmc = ?");
                params.add(meetingRoomsName);
            }

            // 执行查询
            List<Map<String, Object>> dataList;
            if (params.isEmpty()) {
                dataList = dataSqlService.eBuilderFromSqlAll(sqlBuilder.toString(), SourceType.LOGIC);
            } else {
                dataList = dataSqlService.eBuilderFromSqlAll(sqlBuilder.toString(), SourceType.LOGIC, params);
            }

            // 获取转换器
            Converter touYsbConverter = converterUtil.getConverter(WorkingSpaceConstants.MEETING_ROOM_TABLE, "tou_ysb");
            Converter huiYslxConverter = converterUtil.getConverter(WorkingSpaceConstants.MEETING_ROOM_TABLE,
                    "hui_yslx");
            Converter yuDztConverter = converterUtil.getConverter(WorkingSpaceConstants.MEETING_ROOM_TABLE, "yu_dzt");
            Converter zhuangTConverter = converterUtil.getConverter(WorkingSpaceConstants.MEETING_ROOM_TABLE,
                    "zhuang_t");

            // 处理结果集
            List<Map<String, Object>> resultList = new ArrayList<>();
            for (Map<String, Object> data : dataList) {
                Map<String, Object> resultItem = new HashMap<>();
                for (Map.Entry<String, Object> entry : data.entrySet()) {
                    String key = entry.getKey().toLowerCase();
                    String value = StringUtils.null2String(entry.getValue());
                    resultItem.put(key, value);

                    // 处理特殊字段的转换
                    if ("tou_ysb".equals(key) && touYsbConverter != null) {
                        resultItem.put(key + "_span", touYsbConverter.convert(value));
                    } else if ("hui_yslx".equals(key) && huiYslxConverter != null) {
                        resultItem.put(key + "_span", huiYslxConverter.convert(value));
                    } else if ("yu_dzt".equals(key) && yuDztConverter != null) {
                        resultItem.put(key + "_span", yuDztConverter.convert(value));
                    } else if ("zhuang_t".equals(key) && zhuangTConverter != null) {
                        resultItem.put(key + "_span", zhuangTConverter.convert(value));
                    }
                }
                resultList.add(resultItem);
            }

            // 获取选项数据
            Map<String, List<Map<String, Object>>> options = new HashMap<>();
            List<String> selectFields = new ArrayList<>();
            selectFields.add("ban_gl");
            selectFields.add("lou_c");

            for (String field : selectFields) {
                options.put(field,
                        workingSpaceUtil.getSelectBoxOptions(WorkingSpaceConstants.MEETING_ROOM_TABLE, field));
            }

            log.info("-------获取会议室信息结束--------");
            return GetMeetingInformationRespVO.builder().data(resultList).options(options).build();

        } catch (Exception e) {
            log.error("获取会议室信息异常: {}", e.getMessage(), e);
            throw e;
        }
    }
}
