package com.weaver.seconddev.zyhlw.service.impl.partystyle;

import com.weaver.openapi.pojo.dept.res.DeptVo;
import com.weaver.openapi.service.DeptService;
import com.weaver.seconddev.zyhlw.domain.portal.DepartmentModel;
import com.weaver.seconddev.zyhlw.service.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.weaver.ebuilder.datasource.api.enums.SourceType.LOGIC;

/**
 * @date 2025-05-20
 */
@Service
@Slf4j
public class SupervisionServiceImpl implements SupervisionService {

    @Resource
    IPortalService portalService;
    @Resource
    IDataSqlService dataSqlService;
    @Resource
    private IHrmDepartmentService hrService;
    @Resource
    private IOpenPlatformService openPlatformService;
    @Resource
    CmicProperties cmicProperties;

    @Override
    public List<Map<String, String>> getBlockChart(String year, String dep) {
        try {
            // 构建条件查询的SQL（根据年份和部门）
            StringBuilder sqlBuilder = new StringBuilder("SELECT a.jian_dsxlx AS jian_dsxlx_id, b.jian_dsxlx, COUNT(a.jian_dsxlx) AS count ")
                    .append(" FROM uf_jian_djh a LEFT JOIN uf_jian_dsxlx b ON a.jian_dsxlx = b.id WHERE b.jian_dsxlx IS NOT NULL and (a.shi_fzs = 0 or a.shi_fzs is null)");
            if (!year.isEmpty()) {
                sqlBuilder.append(" AND a.ji_hkzsj LIKE ").append("'").append(year).append("%'");
            }
            String supDepartment = "";
            if (!dep.isEmpty()) {
                List<String> departIdList = portalService.getDepartIdList(dep);
                supDepartment = String.join(",", departIdList);
                sqlBuilder.append(" AND a.qian_tbm IN (").append(supDepartment).append(")");
            }
            sqlBuilder.append(" GROUP BY a.jian_dsxlx, b.jian_dsxlx");

            log.info("监督计划方块图数据sqlBuilder: {}", sqlBuilder);
            List<Map<String, Object>> supervisionSqlDatas = dataSqlService.ebuilderFromSql(sqlBuilder.toString(), 1, 100, LOGIC);


            // 查询所有事项类型，并初始化计数为0
            List<Map<String, String>> typeDatas = new ArrayList<>();
            List<Map<String, Object>> maps = dataSqlService.ebuilderFromSql("SELECT id, jian_dsxlx FROM uf_jian_dsxlx", 1, 100, LOGIC);
            for (Map<String, Object> map : maps) {
                Map<String, String> map1 = new HashMap<>();
                map1.put("id", String.valueOf(map.get("id")));
                map1.put("count", "0");
                map.put("jian_dsxlx", StringUtils.null2String(map.get("jian_dsxlx")));
                typeDatas.add(map1);
                // 直接在循环中累加计数，避免额外遍历
                for (Map<String, Object> supervisionMap : supervisionSqlDatas) {
                    if (map.get("id").equals(StringUtils.null2String(supervisionMap.get("jian_dsxlx_id")))) {
                        int count1 = Integer.parseInt(map1.get("count"));
                        int count2 = Integer.parseInt(StringUtils.null2String(supervisionMap.get("count"), "0"));
                        map.put("count", String.valueOf(count1 + count2));
                        break; // 一旦找到匹配项就跳出内层循环
                    }
                }
            }

            // 监督计划全查询并添加到typeDatas中
            String allCountSql = "SELECT COUNT(*) AS count, 0 AS id, '全年监督计划' AS jian_dsxlx FROM uf_jian_djh a LEFT JOIN uf_jian_dsxlx b ON a.jian_dsxlx = b.id WHERE b.jian_dsxlx IS NOT NULL and (a.shi_fzs = 0 or a.shi_fzs is null)";
            if (!year.isEmpty()) {
                allCountSql += " AND a.ji_hkzsj LIKE '" + year + "%'";
            }
            if (!supDepartment.isEmpty()) {
                allCountSql += " AND a.qian_tbm IN (" + supDepartment + ")";
            }
            List<Map<String, Object>> maps1 = dataSqlService.ebuilderFromSql(allCountSql, 1, 100, LOGIC);
            Map<String, Object> stringObjectMap = maps1.stream().findFirst().orElse(Collections.emptyMap());
            Map<String, String> convertedMap = stringObjectMap.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> String.valueOf(entry.getValue()) // 确保所有值转为 String
                    ));
            typeDatas.add(0, convertedMap);
            return typeDatas;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<Map<String, String>> getBlockChart2(String year, String dep) {
        log.info("开始执行监督计划方块图数据操作");
        try {
            // 构建条件查询的SQL（根据年份和部门）
            StringBuilder sqlBuilder = new StringBuilder("SELECT a.jian_dsxlx AS jian_dsxlx_id, b.jian_dsxlx, COUNT(a.jian_dsxlx) AS count ")
                    .append(" FROM uf_jian_djh a LEFT JOIN uf_jian_dsxlx b ON a.jian_dsxlx = b.id WHERE b.jian_dsxlx IS NOT NULL and (a.shi_fzs = 0 or a.shi_fzs is null)");
            if (!year.isEmpty()) {
                sqlBuilder.append(" AND a.ji_hkzsj LIKE ").append("'").append(year).append("%'");
            }
            String supDepartment = "";
            if (!dep.isEmpty()) {
                List<String> departIdList = portalService.getDepartIdList(dep);
                supDepartment = String.join(",", departIdList);
                sqlBuilder.append(" AND a.qian_tbm IN (").append(supDepartment).append(")");
            }
            sqlBuilder.append(" GROUP BY a.jian_dsxlx, b.jian_dsxlx");

            log.info("sqlBuilder: {}", sqlBuilder);
            List<Map<String, Object>> supervisionSqlDatas = dataSqlService.ebuilderFromSql(sqlBuilder.toString(), 1, 100, LOGIC);

            // 查询所有事项类型，并初始化计数为0
            List<Map<String, String>> typeDatas = new ArrayList<>();
            List<Map<String, Object>> maps = dataSqlService.ebuilderFromSql("SELECT id, jian_dsxlx FROM uf_jian_dsxlx", 1, 100, LOGIC);
            for (Map<String, Object> map : maps) {
                Map<String, String> map1 = new HashMap<>();
                map1.put("id", String.valueOf(map.get("id")));
                map1.put("count", "0");
                map.put("jian_dsxlx", StringUtils.null2String(map.get("jian_dsxlx")));
                typeDatas.add(map1);
                // 直接在循环中累加计数，避免额外遍历
                for (Map<String, Object> supervisionMap : supervisionSqlDatas) {
                    if (map.get("id").equals(StringUtils.null2String(supervisionMap.get("jian_dsxlx_id")))) {
                        int count1 = Integer.parseInt(map1.get("count"));
                        int count2 = Integer.parseInt(StringUtils.null2String(supervisionMap.get("count"), "0"));
                        map.put("count", String.valueOf(count1 + count2));
                        break; // 一旦找到匹配项就跳出内层循环
                    }
                }
            }

            // 监督计划全查询并添加到typeDatas中
            String allCountSql = "SELECT COUNT(*) AS count, 0 AS id, '全年监督计划' AS jian_dsxlx FROM uf_jian_djh a LEFT JOIN uf_jian_dsxlx b ON a.jian_dsxlx = b.id WHERE b.jian_dsxlx IS NOT NULL and (a.shi_fzs = 0 or a.shi_fzs is null)";
            if (!year.isEmpty()) {
                allCountSql += " AND a.ji_hkzsj LIKE '" + year + "%'";
            }
            if (!supDepartment.isEmpty()) {
                allCountSql += " AND a.qian_tbm IN (" + supDepartment + ")";
            }
            List<Map<String, Object>> maps1 = dataSqlService.ebuilderFromSql(allCountSql, 1, 100, LOGIC);
            Map<String, Object> stringObjectMap = maps1.stream().findFirst().orElse(Collections.emptyMap());
            Map<String, String> convertedMap = stringObjectMap.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> String.valueOf(entry.getValue()) // 确保所有值转为 String
                    ));
            typeDatas.add(0, convertedMap);
            return typeDatas;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<Map<String, Object>> getType() {
        log.info("开始执行监督计划类型数据操作");
        String sql = "select id,jian_dsxlx from uf_jian_dsxlx";
        List<Map<String, Object>> querySqlDatas;
        try {
            querySqlDatas = dataSqlService.ebuilderFromSql(sql, 1, 100, LOGIC);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return querySqlDatas;
    }

    @Override
    public List<Map<String, Object>> getPieChart(String year, String dep, String supervisionType) {
        log.info("开始执行监督计划饼状图数据操作");
        try {

            StringBuilder sqlBuilder = new StringBuilder("select SUM(CASE WHEN dang_qzt = 0 THEN 1 ELSE 0 END) AS count1,SUM(CASE WHEN dang_qzt = 1 THEN 1 ELSE 0 END) AS count2,SUM(CASE WHEN dang_qzt = 2 THEN 1 ELSE 0 END) AS count3 from uf_jian_djh where (shi_fzs = 0 or shi_fzs is null)");
            if (!year.isEmpty()) {
                sqlBuilder.append(" and ji_hkzsj like '").append(year).append("%'");
            }
            if (!dep.isEmpty()) {
                List<String> departIdList = portalService.getDepartIdList(dep);
                String supDepartment = String.join(",", departIdList);
                sqlBuilder.append(" AND a.qian_tbm IN (").append(supDepartment).append(")");
            }
            if (!supervisionType.isEmpty()) {
                sqlBuilder.append(" and jian_dsxlx =").append(supervisionType);
            }
            log.info("监督计划饼状图数据sqlBuilder:{}", sqlBuilder);
            List<Map<String, Object>> maps = dataSqlService.ebuilderFromSql(sqlBuilder.toString(), 1, 100, LOGIC);
            List<Map<String, Object>> pieChartList = new ArrayList<>();
            for (Map<String, Object> map : maps) {
                Map<String, Object> map1 = new HashMap<>();
                map1.put("count1", StringUtils.null2String(map.get("count1"), "0"));
                map1.put("count2", StringUtils.null2String(map.get("count2"), "0"));
                map1.put("count3", StringUtils.null2String(map.get("count3"), "0"));
                pieChartList.add(map1);
            }
            return pieChartList;
        } catch (Exception e) {
            log.error("监督计划饼状图数据异常:", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<Map<String, Object>> getColumnarChart(String year, String dep, String supervisionType) {
        log.info("开始执行监督计划柱状图数据操作");
        try {
            StringBuilder sqlBuilder = new StringBuilder("select qian_tbm ,count(qian_tbm) as count from uf_jian_djh where (shi_fzs = 0 or shi_fzs is null)");
            if (!year.isEmpty()) {
                sqlBuilder.append(" and ji_hkzsj like '").append(year).append("%'");
            }
            if (!dep.isEmpty()) {
                List<String> departIdList = portalService.getDepartIdList(dep);
                String supDepartment = String.join(",", departIdList);
                sqlBuilder.append(" AND a.qian_tbm IN (").append(supDepartment).append(")");
            }
            if (!supervisionType.isEmpty()) {
                sqlBuilder.append(" and jian_dsxlx =").append(supervisionType);
            }
            sqlBuilder.append(" group by qian_tbm");
            log.info("监督计划柱状图数据sqlBuilder:{}", sqlBuilder);
            List<Map<String, Object>> columnarList = new ArrayList<>();
            Map<String, Integer> countMap = new HashMap<>();
            List<String> supDepartmentList = new ArrayList<>();
            List<DepartmentModel> allDepartmentList = hrService.getAllDepartmentList();
            List<Map<String, Object>> maps = dataSqlService.ebuilderFromSql(sqlBuilder.toString(), 1, 100, LOGIC);
            for (Map<String, Object> map : maps) {
                Map<String, Object> map1 = new HashMap<>();
                String qian_tbm = (String) map.get("qian_tbm");
                int count = StringUtils.parseToInt(map.get("count1"), 0);
                String supDepartment = portalService.getDepartIdBySubSector(allDepartmentList, qian_tbm, 2);
                if (!countMap.containsKey(supDepartment)) {
                    countMap.put(supDepartment, count);
                    supDepartmentList.add(supDepartment);
                } else {
                    count = countMap.get(supDepartment) + 1;
                    countMap.put(supDepartment, count);
                }
            }

            for (String supDepartment : supDepartmentList) {
                Map<String, Object> map1 = new HashMap<>();
                DeptVo deptVo = DeptService.getDeptV2(openPlatformService.getAccessToken(),
                        Long.parseLong(supDepartment), cmicProperties.getOpenPlatformUrl(), null);
                map1.put("id", supDepartment);
                map1.put("name", deptVo.getDepartment().getName());
                map1.put("count", countMap.get(supDepartment));
                columnarList.add(map1);
            }
            return columnarList;
        } catch (Exception e) {
            log.error("<UNK>:", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<Map<String, Object>> getNumericalDiagram(String year, String dep, String supervisionType) {
        log.info("开始执行监督计划数线图数据操作");
        try {
            StringBuilder sqlBuilder = new StringBuilder("SELECT");
            for (int i = 1; i <= 12; i++) {
                String countAlias = "count" + i;
                String monthPattern = String.format("%%-%02d-%%", i);
                sqlBuilder.append(String.format(" SUM(CASE WHEN ji_hkzsj LIKE '%s' THEN 1 ELSE 0 END) AS %s,", monthPattern, countAlias));
            }
            // 去除最后一个逗号
            sqlBuilder.setLength(sqlBuilder.length() - 1);
            // 添加 FROM 子句
            sqlBuilder.append(" FROM uf_jian_djh where (shi_fzs = 0 or shi_fzs is null)");
            //条件判断拼接
            if (!year.isEmpty()) {
                sqlBuilder.append(" and ji_hkzsj like '").append(year).append("%'");
            }
            if (!dep.isEmpty()) {
                List<String> departIdList = portalService.getDepartIdList(dep);
                String supDepartment = String.join(",", departIdList);
                sqlBuilder.append(" AND a.qian_tbm IN (").append(supDepartment).append(")");
            }
            if (!supervisionType.isEmpty()) {
                sqlBuilder.append(" and jian_dsxlx =").append(supervisionType);
            }
            log.info("监督计划数线图数据 sqlBuilder:{}", sqlBuilder);
            List<Map<String, Object>> maps = dataSqlService.ebuilderFromSql(sqlBuilder.toString(), 1, 100, LOGIC);
            List<Map<String, Object>> list = new ArrayList<>();
            for (Map<String, Object> map : maps) {
                for (int i = 1; i <= 12; i++) {
                    Map<String, Object> map1 = new LinkedHashMap<>();
                    map1.put("name", getMonthName(i));
                    map1.put("count", StringUtils.null2String(map.get("count" + i), "0"));
                    list.add(map1);
                }
            }

            return list;
        } catch (Exception e) {
            log.error("监督计划数线图数据 异常报错:", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public Map<String, Object> getProjectById(String projectId) {
        log.info("开始根据id查询计划任务数据");
        try {
            String sql = "select * from uf_jian_djh where (shi_fzs = 0 or shi_fzs is null) and id =" + projectId;
            List<Map<String, Object>> maps = dataSqlService.ebuilderFromSql(sql, 1, 100, LOGIC);
            Map<String, Object> convertedMap = maps.stream().findFirst().orElse(Collections.emptyMap());
            log.info("<UNK>:{}", convertedMap);
            return convertedMap;
        } catch (Exception e) {
            log.error("<UNK>id<UNK>:", e);
            throw new RuntimeException(e);
        }
    }


    // 获取月份名称的辅助方法
    private String getMonthName(int monthNumber) {
        String[] monthNames = {"一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"};
        return monthNames[monthNumber - 1];
    }
}
