package com.weaver.seconddev.zyhlw.service.impl.partystyle;

import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.seconddev.zyhlw.domain.portal.DepartmentModel;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IHrmDepartmentService;
import com.weaver.seconddev.zyhlw.service.OaSystemOfficialService;
import com.weaver.seconddev.zyhlw.service.SupervisionTemplateService;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @date 2025-02-24
 */
@Slf4j
@Service
public class SupervisionTemplateServiceImpl implements SupervisionTemplateService {

    @Resource
    private IDataSqlService dataSqlService;
    @Resource
    private IHrmDepartmentService hrService;
    @Resource
    private OaSystemOfficialService oaSystemOfficialService;

    @Override
    public Map<String, Object> supervisionTemplateStatistics(String year, String quarter, String departmentId) {
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Object> map = new HashMap<>();
            String sql = "select count(*) as num from uf_suo_sly";
            List<Map<String, Object>> maps = dataSqlService.ebuilderFromSql(sql, 1, 100, SourceType.LOGIC);
            for (Map<String, Object> objectMap : maps) {
                String num = StringUtils.null2String(objectMap.get("num"), "0");
                if (num.isEmpty()) {
                    objectMap.put("field", 0);
                } else {
                    objectMap.put("field", num);
                }
            }

            String builder = "select sum(a.chang_jds) as chang_jds, sum(a.guan_kds) as guan_kds, COUNT(*) AS total_rows from uf_jian_dmbwtlb a  where is_delete = 0";
            if (!departmentId.isEmpty()) {
                builder += " and a.gui_sbm = " + departmentId;
            }
            if (!year.isEmpty()) {
                builder += sqlYearsStatementSplicing(quarter, year);
            }

            log.info("SQL语句为: {}", builder);

            List<Map<String, Object>> maps1 = dataSqlService.ebuilderFromSql(builder, 1, 100, SourceType.LOGIC);
            for (Map<String, Object> recordSet : maps1) {
                String controlPoints = StringUtils.null2String(recordSet.get("guan_kds"));
                String scenePoints = StringUtils.null2String(recordSet.get("chang_jds"));
                String totalRows = StringUtils.null2String(recordSet.get("total_rows"));
                if (controlPoints.isEmpty()) {
                    map.put("controlPoints", 0);
                } else {
                    map.put("controlPoints", controlPoints);
                }
                if (scenePoints.isEmpty()) {
                    map.put("scenePoints", 0);
                } else {
                    map.put("scenePoints", scenePoints);
                }


                map.put("totalRows", totalRows);
                log.info("返回的data为: " + map);
            }

            result.put("data", map);
            result.put("code", "1");
            result.put("msg", "操作成功");

        } catch (Exception e) {
            String errorMsg = "执行异常：" + e + "@" + e.getMessage();
            log.error("监督模板问题列表错误异常：{}", errorMsg);
            result.put("code", -1);
            result.put("msg", errorMsg);
        }
        return result;
    }

    @Override
    public Map<String, Object> moduleProportion(String dep, String quarter, String parameter, String type) {
        Map<String, Object> map = new HashMap<>();
        try {
            String sqlDepStatementSplicing = "";
            if (!dep.isEmpty()) {
                sqlDepStatementSplicing = " where a.is_delete = 0 and b.is_delete = 0 and a.gui_sbm = " + dep;
            }
            List<String> depsList = new ArrayList<>();
            //查询各部门问题点数量
            List<Map<String, Object>> problemsList = new ArrayList<>();
            //查询各问题模块管控点数量
            List<Map<String, Object>> questionList = new ArrayList<>();//领list组
            List<DepartmentModel> allDepartmentList = hrService.getAllDepartmentList();
            List<Map<String, Object>> fieldList = new ArrayList<>();//领list组
            switch (type) {
                case "1": {
                    //按问题点来输出占比
                    //查询各所属领域数量

                    String sql = "select b.id as id, b.suo_sly as field,count(*) as num from uf_jian_dmbwtlb a join uf_suo_sly b on a.suo_sly = b.id " + sqlDepStatementSplicing;
                    if (!parameter.isEmpty()) {
                        sql += sqlYearsStatementSplicing(quarter, parameter);
                    }
                    sql += " Group by b.suo_sly ,b.id";
                    List<Map<String, Object>> maps2 = dataSqlService.ebuilderFromSql(sql, 1, 100, SourceType.LOGIC);
                    for (Map<String, Object> recordSet : maps2) {
                        Map<String, Object> fieldMap = new HashMap<>();//领域map组
                        String num = StringUtils.null2String(recordSet.get("num"), "0");
                        String field = StringUtils.null2String(recordSet.get("field"));
                        String id = StringUtils.null2String(recordSet.get("id"));
                        fieldMap.put("name", field);
                        fieldMap.put("id", id);
                        if (num.isEmpty()) {
                            fieldMap.put("value", 0);
                        } else {
                            fieldMap.put("value", num);
                        }
                        fieldList.add(fieldMap);
                    }

                    String sql1 = "select b.id as id, b.wen_tmk as question,count(*) as num from uf_jian_dmbwtlb a join uf_wtmk b on a.wen_tmk = b.id " + sqlDepStatementSplicing;
                    if (!parameter.isEmpty()) {
                        sql1 += sqlYearsStatementSplicing(quarter, parameter);
                    }
                    sql1 += " Group by b.wen_tmk,b.id";
                    List<Map<String, Object>> maps1 = dataSqlService.ebuilderFromSql(sql1, 1, 100, SourceType.LOGIC);
                    for (Map<String, Object> recordSet1 : maps1) {
                        Map<String, Object> questionMap = new HashMap<>();//问题模块map组
                        String question = StringUtils.null2String(recordSet1.get("question"));
                        String num = StringUtils.null2String(recordSet1.get("num"), "0");
                        String id = StringUtils.null2String(recordSet1.get("id"));
                        questionMap.put("name", question);
                        if (num.isEmpty()) {
                            questionMap.put("value", 0);
                        } else {
                            questionMap.put("value", num);
                        }
                        questionMap.put("id", id);
                        questionList.add(questionMap);
                    }
                    //年份季度条件
                    String condition = "";
                    if (!parameter.isEmpty()) {
                        condition = sqlYearsStatementSplicing2(quarter, parameter);
                    }
                    String sql2 = "select a1.id, a.num from uf_zhu_ztbmpx a1 left join (select gui_sbm,count(*) as num from uf_jian_dmbwtlb " + condition + " group by gui_sbm) a on b.id = a.gui_sbm" + sqlDepStatementSplicing + " order by a1.bu_mxh asc";
                    List<Map<String, Object>> maps = dataSqlService.ebuilderFromSql(sql2, 1, 1000, SourceType.LOGIC);

                    for (Map<String, Object> recordSet2 : maps) {
                        Map<String, Object> problemsMap = new HashMap<>();
                        String zeRbm = StringUtils.null2String(recordSet2.get("id"));
                        String num = StringUtils.null2String(recordSet2.get("num"), "0");
                        String departmentName = allDepartmentList.stream()
                                .filter(e -> StringUtils.null2String(e.getId()).equals(zeRbm))
                                .map(DepartmentModel::getName).findFirst().orElse("");
                        problemsMap.put("name", departmentName);
                        depsList.add(zeRbm);
                        if (num.isEmpty()) {
                            problemsMap.put("value", 0);
                        } else {
                            problemsMap.put("value", num);
                        }

                        problemsList.add(problemsMap);
                    }
                    break;
                }
                case "2": {
                    //按场景点来输出占比
                    String sql = "select b.id as id, b.suo_sly as field, count(*) as num from uf_jian_dmbwtlb a " + "join uf_suo_sly b on a.suo_sly = b.id " + "join uf_jian_dmbwtlb_dt1 c on a.id = c.FORM_DATA_ID " + sqlDepStatementSplicing;
                    if (!parameter.isEmpty()) {
                        sql += sqlYearsStatementSplicing(quarter, parameter);
                    }
                    sql += " Group by b.suo_sly,b.id";
                    List<Map<String, Object>> maps2 = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
                    for (Map<String, Object> recordSet : maps2) {
                        Map<String, Object> fieldMap = new HashMap<>();//领域map组
                        String num = StringUtils.null2String(recordSet.get("num"), "0");
                        String field = StringUtils.null2String(recordSet.get("field"));
                        String id = StringUtils.null2String(recordSet.get("id"));
                        fieldMap.put("name", field);
                        fieldMap.put("id", id);
                        if (num.isEmpty()) {
                            fieldMap.put("value", 0);
                        } else {
                            fieldMap.put("value", num);
                        }
                        fieldMap.put("value", num);
                        fieldList.add(fieldMap);
                    }


                    String sql1 = "select b.id as id, b.wen_tmk as question,count(*) as num from uf_jian_dmbwtlb a " + "join uf_wtmk b on a.wen_tmk = b.id join uf_jian_dmbwtlb_dt1 c on a.id = c.FORM_DATA_ID " + sqlDepStatementSplicing;

                    if (!parameter.isEmpty()) {
                        sql1 += sqlYearsStatementSplicing(quarter, parameter);
                    }

                    sql1 += " Group by b.wen_tmk,b.id";
                    List<Map<String, Object>> maps1 = dataSqlService.eBuilderFromSqlAll(sql1, SourceType.LOGIC);
                    for (Map<String, Object> recordSet1 : maps1) {
                        Map<String, Object> questionMap = new HashMap<>();//问题模块map组
                        String question = StringUtils.null2String(recordSet1.get("question"));
                        String num = StringUtils.null2String(recordSet1.get("num"), "0");
                        String id = StringUtils.null2String(recordSet1.get("id"));
                        questionMap.put("name", question);
                        if (num.isEmpty()) {
                            questionMap.put("value", 0);
                        } else {
                            questionMap.put("value", num);
                        }
                        questionMap.put("id", id);
                        questionList.add(questionMap);
                    }
                    //年份季度条件
                    String condition = "";
                    if (!parameter.isEmpty()) {
                        condition = sqlYearsStatementSplicing2(quarter, parameter);
                    }
                    String sql2 = "select a1.id, a.num from uf_zhu_ztbmpx a1 left join (select gui_sbm,sum(chang_jds) as num from uf_jian_dmbwtlb " + condition + " group by gui_sbm) a on b.id = a.gui_sbm " + sqlDepStatementSplicing + " order by a1.bu_mxh asc";
                    List<Map<String, Object>> maps = dataSqlService.eBuilderFromSqlAll(sql2, SourceType.LOGIC);
                    for (Map<String, Object> map1 : maps) {
                        Map<String, Object> problemsMap = new HashMap<>();
                        String zeRbm = StringUtils.null2String(map1.get("id"));
                        String num = StringUtils.null2String(map1.get("num"), "0");
                        String departmentName = allDepartmentList.stream()
                                .filter(e -> StringUtils.null2String(e.getId()).equals(zeRbm))
                                .map(DepartmentModel::getName).findFirst().orElse("");
                        problemsMap.put("name", departmentName);
                        depsList.add(zeRbm);

                        if (num.isEmpty()) {
                            problemsMap.put("value", 0);
                        } else {
                            problemsMap.put("value", num);
                        }
                        problemsList.add(problemsMap);
                    }
                    break;
                }
                case "3": {
                    //按管控点来输出占比
                    String sql = "select b.id as id, b.suo_sly as field, count(*) as num from uf_jian_dmbwtlb a " + "join uf_suo_sly b on a.suo_sly = b.id " + "join uf_jian_dmbwtlb_dt2 c on a.id = c.FORM_DATA_ID " + sqlDepStatementSplicing;
                    if (!parameter.isEmpty()) {
                        sql += sqlYearsStatementSplicing(quarter, parameter);
                    }
                    sql += " Group by b.suo_sly,b.id";
                    List<Map<String, Object>> maps2 = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC);
                    for (Map<String, Object> recordSet : maps2) {
                        Map<String, Object> fieldMap = new HashMap<>();//领域map组
                        String num = StringUtils.null2String(recordSet.get("num"), "0");
                        String field = StringUtils.null2String(recordSet.get("field"));
                        String id = StringUtils.null2String(recordSet.get("id"));
                        fieldMap.put("name", field);
                        fieldMap.put("id", id);
                        if (num.isEmpty()) {
                            fieldMap.put("value", 0);
                        } else {
                            fieldMap.put("value", num);
                        }
                        fieldList.add(fieldMap);
                    }

                    String sql1 = "select b.id as id, b.wen_tmk as question,count(*) as num from uf_jian_dmbwtlb a " + "join uf_wtmk b on a.wen_tmk = b.id " + "join uf_jian_dmbwtlb_dt2 c on a.id = c.FORM_DATA_ID " + sqlDepStatementSplicing;

                    if (!parameter.isEmpty()) {
                        sql1 += sqlYearsStatementSplicing(quarter, parameter);
                    }
                    sql1 += " Group by b.wen_tmk,b.id";

                    List<Map<String, Object>> maps1 = dataSqlService.eBuilderFromSqlAll(sql1, SourceType.LOGIC);
                    for (Map<String, Object> recordSet1 : maps1) {
                        Map<String, Object> questionMap = new HashMap<>();//问题模块map组
                        String question = StringUtils.null2String(recordSet1.get("question"));
                        String num = StringUtils.null2String(recordSet1.get("num"), "0");
                        String id = StringUtils.null2String(recordSet1.get("id"));
                        questionMap.put("name", question);
                        questionMap.put("id", id);
                        if (num.isEmpty()) {
                            questionMap.put("value", 0);
                        } else {
                            questionMap.put("value", num);
                        }
                        questionList.add(questionMap);
                    }

                    log.info("问题模块list: {}", questionList);
                    map.put("questionModule", questionList);


                    //年份季度条件
                    String condition = "";
                    if (!parameter.isEmpty()) {
                        condition = sqlYearsStatementSplicing2(quarter, parameter);
                    }
                    String sql2 = "select a1.id, a.num from uf_zhu_ztbmpx a1 left join (select gui_sbm,sum(guan_kds) as num from uf_jian_dmbwtlb " + condition + " group by gui_sbm) a on b.id = a.gui_sbm " + sqlDepStatementSplicing + " order by a1.bu_mxh asc";

                    List<Map<String, Object>> mapList = dataSqlService.eBuilderFromSqlAll(sql2, SourceType.LOGIC);
                    for (Map<String, Object> recordSet2 : mapList) {
                        Map<String, Object> problemsMap = new HashMap<>();
                        String zeRbm = StringUtils.null2String(recordSet2.get("id"));
                        String num = StringUtils.null2String(recordSet2.get("num"), "0");
                        String departmentName = allDepartmentList.stream()
                                .filter(e -> StringUtils.null2String(e.getId()).equals(zeRbm))
                                .map(DepartmentModel::getName).findFirst().orElse("");
                        problemsMap.put("name", departmentName);
                        depsList.add(zeRbm);
                        if (num.isEmpty()) {
                            problemsMap.put("value", 0);
                        } else {
                            problemsMap.put("value", num);
                        }
                        problemsList.add(problemsMap);
                    }
                    log.info("查询结果depsList: {}", depsList);

                    map.put("problems", problemsList);
                    map.put("ids", depsList);

                    break;
                }
            }
            log.info("questionModule:{}", questionList);
            log.info("problems:{}", problemsList);
            log.info("field:{}", fieldList);
            log.info("ids:{}", depsList);
            map.put("questionModule", questionList);
            map.put("problems", problemsList);
            map.put("field", fieldList);
            map.put("ids", depsList);
        } catch (Exception e) {
            String errorMsg = "执行异常：" + e + "@" + e.getMessage();
            log.info(errorMsg);
        }
        log.info("map: {}", map);
        return map;
    }


    @Override
    public Map<String, Object> getScenePointChanges(String year, String quarter, String dep) {
        String sql = "SELECT SUM(CASE WHEN a.lei_x = 1 THEN 1 ELSE 0 END) AS change,"
                + " SUM(CASE WHEN a.lei_x = 0 THEN 1 ELSE 0 END) AS newAdd"
                + " FROM uf_jian_dmbwtlb_dt1 a"
                + " join uf_jian_dmbwtlb b on a.FORM_DATA_ID = b.id where a.is_delete = 0 and b.is_delete = 0";
        String databaseName = oaSystemOfficialService.getBaseDataValue("监督模版（场景点）变更流程", "监督模版（场景点）变更流程表");
        String delSql = "SELECT COUNT(*) " + databaseName + "_dt5 a JOIN " + databaseName + " b ON a.FORM_DATA_ID = b.id JOIN wfc_requestBase c ON b.id = c.requestId " +
                "WHERE b.delete_type = 0 AND c.flowStatus = 3";
        try {
            if (!dep.isEmpty()) {
                sql += " and b.gui_sbm = " + dep;
                delSql += " and b.gui_sbm = " + dep;
            }
            if (!year.isEmpty()) {
                sql += sqlYearsStatementSplicing(quarter, year);
                delSql += getWorkFlowArchive(quarter, year, delSql);
            }
            log.info("sql: {}, delSql: {}", sql, delSql);

            String newAdd = "0";
            String change = "0";

            List<Map<String, Object>> maps = dataSqlService.ebuilderFromSql(sql, 1, 1000, SourceType.LOGIC);

            for (Map<String, Object> recordSet : maps) {
                newAdd = StringUtils.null2String(recordSet.get("newAdd".toUpperCase()), "0");
                change = StringUtils.null2String(recordSet.get("change".toUpperCase()), "0");
            }

            log.info("newAdd{}; change{}", newAdd, change);

            String del = "0";
            List<Map<String, Object>> maps1 = dataSqlService.workflowFromSqlAll(delSql, SourceType.LOGIC);
            for (Map<String, Object> rs : maps1) {
                del = StringUtils.null2String(rs.get("count"), "0");
            }
            log.info("del{}", del);
            Map<String, Object> map = new HashMap<>();
            map.put("del", del);
            map.put("newAdd", newAdd);
            map.put("change", change);

            log.info("查询结果map: {}", map);

            return map;
        } catch (Exception e) {
            log.error("执行异常：{}@{}", e, e.getMessage());
        }
        return Collections.emptyMap();
    }

    @Override
    public String supervisionTemplateCondition(String guiSbm, String startTime, String endTime, String suoSly, String wenTmk, String controlPoints, String scenePoints, String kind) {
        StringBuilder sqlCondition = new StringBuilder(" and ");
        boolean hasCondition = false;

        if (!guiSbm.isEmpty()) {
            sqlCondition.append(" t1.gui_sbm = '").append(guiSbm).append("'");
            hasCondition = true;
        }
        if (!startTime.isEmpty() && !endTime.isEmpty()) {
            if (hasCondition) sqlCondition.append(" AND");
            sqlCondition.append(" t1.geng_xsj BETWEEN '").append(startTime).append("' AND '").append(endTime).append("'");
            hasCondition = true;
        }
        if (!suoSly.isEmpty()) {
            if (hasCondition) sqlCondition.append(" AND");
            sqlCondition.append(" t1.suo_sly = ").append(suoSly);
            hasCondition = true;
        }
        if (!wenTmk.isEmpty()) {
            if (hasCondition) sqlCondition.append(" AND");
            sqlCondition.append(" t1.wen_tmk = ").append(wenTmk);
            hasCondition = true;
        }

        if ("1".equals(scenePoints)) {
            if (hasCondition) sqlCondition.append(" AND");
            sqlCondition.append(" d1.chang_jdms is not null");
            hasCondition = true;
        }

        if ("1".equals(controlPoints)) {
            if (hasCondition) sqlCondition.append(" AND");
            sqlCondition.append(" d1.guan_kdms is not null");
            hasCondition = true;
        }
        if (!kind.isEmpty()) {
            if (hasCondition) sqlCondition.append(" AND");
            sqlCondition.append(" d1.lei_x = ").append(kind);
        }

        if (!hasCondition) {
            sqlCondition.setLength(0);
        }
        log.info("sqlCondition: {}", sqlCondition);
        return sqlCondition.toString();
    }

    public String getWorkFlowArchive(String quarter, String parameter, String builder) {
        String s = "";
        if (!builder.contains("where")) {
            s += " where";
        } else {
            s += " and";
        }

        switch (quarter) {
            case "1":
                s += " c.lastoperatedatetime between '" + parameter + "-01-01 00:00:00' and '" + parameter + "-03-31 23:59:59";
                break;
            case "2":
                s += " c.lastoperatedatetime between '" + parameter + "-04-01 00:00:00' and '" + parameter + "-06-30 23:59:59'";
                break;
            case "3":
                s += " c.lastoperatedatetime between '" + parameter + "-07-01 00:00:00' and '" + parameter + "-09-30 23:59:59'";
                break;
            case "4":
                s += " c.lastoperatedatetime between '" + parameter + "-10-01 00:00:00' and '" + parameter + "-12-31 23:59:59'";
                break;
            default:
                s += " c.lastoperatedatetime between '" + parameter + "-01-01 00:00:00' and '" + parameter + "-12-31 23:59:59'";
                break;
        }
        return s;
    }

    /**
     * 拼接年份、季度查询语句
     *
     * @param quarter   季度
     * @param parameter 年份
     * @return s 需要拼接的sql段
     */
    public String sqlYearsStatementSplicing(String quarter, String parameter) {
        String s = " and";

        switch (quarter) {
            case "1":
                s += " a.geng_xsj between '" + parameter + "-01-01' and '" + parameter + "-03-31'";
                break;
            case "2":
                s += " a.geng_xsj between '" + parameter + "-04-01' and '" + parameter + "-06-30'";
                break;
            case "3":
                s += " a.geng_xsj between '" + parameter + "-07-01' and '" + parameter + "-09-30'";
                break;
            case "4":
                s += " a.geng_xsj between '" + parameter + "-10-01' and '" + parameter + "-12-31'";
                break;
            default:
                s += " a.geng_xsj between '" + parameter + "-01-01' and '" + parameter + "-12-31'";
                break;
        }
        return s;
    }

    /**
     * 拼接年份、季度查询语句
     *
     * @param quarter   季度
     * @param parameter 年份
     * @return s 需要拼接的sql段
     */
    public String sqlYearsStatementSplicing2(String quarter, String parameter) {
        String s = " where is_delete = 0 and ";

        switch (quarter) {
            case "1":
                s += " geng_xsj between '" + parameter + "-01-01' and '" + parameter + "-03-31'";
                break;
            case "2":
                s += " geng_xsj between '" + parameter + "-04-01' and '" + parameter + "-06-30'";
                break;
            case "3":
                s += " geng_xsj between '" + parameter + "-07-01' and '" + parameter + "-09-30'";
                break;
            case "4":
                s += " geng_xsj between '" + parameter + "-10-01' and '" + parameter + "-12-31'";
                break;
            default:
                s += " geng_xsj between '" + parameter + "-01-01' and '" + parameter + "-12-31'";
                break;
        }
        return s;
    }

}
