package com.weaver.seconddev.zyhlw.service.impl.partystyle;

import com.alibaba.fastjson.JSONObject;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.seconddev.zyhlw.domain.portal.DepartmentModel;
import com.weaver.seconddev.zyhlw.service.*;
import com.weaver.seconddev.zyhlw.util.DateUtils;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @date 2025-02-18
 */
@Service
@Slf4j
public class TalkManagerServiceImpl implements TalkManagerService {

    private static final String simpleName = TalkManagerServiceImpl.class.getSimpleName();

    @Resource
    private IDataSqlService dataSqlService;
    @Resource
    private IPortalService portalService;
    @Resource
    private IHrmDepartmentService hrService;

    @Override
    public List<Map<String, Object>> getCarTrackApi(String year, String quarter) {
        String methodName = String.format("调用%s.getCarTrackApi()-->进入查询getCarTrackApi接口", simpleName);
        log.info("{};year:{},quarter:{}", methodName, year, quarter);
        String sql = "select shi_sxs,fa_qbm,ren_c,shi_szl,id,shi_j from uf_tan_hlb where is_delete = 0 and shi_j like '" + year + "%'";
        try {
            List<Map<String, Object>> maps = dataSqlService.ebuilderFromSql(sql, 1, 100, SourceType.LOGIC);
            if (!maps.isEmpty()) {
                List<Integer> list = new ArrayList<>();
                //根据季度进行筛选
                if (quarter != null && !quarter.isEmpty()) {
                    if (quarter.equals("0")) {
                        list.add(1);
                        list.add(2);
                        list.add(3);
                    }
                    if (quarter.equals("1")) {
                        list.add(4);
                        list.add(5);
                        list.add(6);
                    }
                    if (quarter.equals("2")) {
                        list.add(7);
                        list.add(8);
                        list.add(9);
                    }
                    if (quarter.equals("3")) {
                        list.add(10);
                        list.add(11);
                        list.add(12);
                    }
                    maps.removeIf(map -> !list.contains(DateUtils.parseString(map.get("shi_j").toString(), DateUtils.yyyy_MM_dd_HH_mm).getMonth().getValue()));
                }
                for (Map<String, Object> map : maps) {
                    map.remove("shi_j");
                }
                log.info("{} 查询成功{}", methodName, maps);
                return maps;
            }
        } catch (Exception e) {
            log.info("{}查询失败{},{}", methodName, e, e.getMessage());
        }
        return Collections.emptyList();
    }

    @Override
    public List<Map<String, Object>> getCountInfo2(String year, String quarter, String departmentId) {
        String methodName = String.format("调用%s.getCountInfo2()-->进入查询getCountInfo2接口", simpleName);
        log.info("{};year:{},quarter:{},departmentId:{}", methodName, year, quarter, departmentId);
        List<String> list2 = new ArrayList<>();
        list2.add("共展开廉洁谈话");
        list2.add("共展开责任约谈");
        list2.add("运用第一形态");
        List<Map<String, Object>> list = new ArrayList<>();
        for (int i = 0; i < list2.size(); i++) {
            StringBuilder sql = new StringBuilder("select sum(ren_c) as count FROM uf_tan_hlb where shi_szl = " + i);
            sql.append(getCondition("", year, quarter, String.valueOf(departmentId)));
            try {
                List<Map<String, Object>> maps = dataSqlService.ebuilderFromSql(sql.toString(), 1, 100, SourceType.LOGIC);
                String count = maps.stream().filter(map -> map.get("count") == null || map.get("count").equals(""))
                        .map(map -> map.get("count").toString()).findFirst().orElse("0");
                Map<String, Object> map = new HashMap<>();
                map.put("id", i);
                map.put("name", list2.get(i));
                map.put("value", count);
                list.add(map);
            } catch (Exception e) {
                log.info("{} 查询失败廉洁谈话数量{},{}", methodName, e, e.getMessage());
            }
        }
        log.info("{} 查询谈话管理统计数据成功{}", methodName, list);
        return list;
    }

    @Override
    public List<Map<String, Object>> getCountInfo3(String year, String quarter, String departmentId, String type) {
        String methodName = String.format("调用%s.getCountInfo3()-->进入查询getCountInfo3接口", simpleName);
        log.info("{};year:{},quarter:{},departmentId:{},type:{}", methodName, year, quarter, departmentId, type);
        List<Map<String, Object>> list = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        switch (type) {
            case "1":
                sql.append("SELECT SUM(CASE WHEN INSTR(','||shi_sxs||',', ',2,') > 0 THEN ren_c ELSE 0 END) AS count1,SUM(CASE WHEN INSTR(','||shi_sxs||',', ',3,') > 0 THEN ren_c ELSE 0 END) AS count2,SUM(CASE WHEN INSTR(','||shi_sxs||',', ',4,') > 0 THEN ren_c ELSE 0 END) AS count3,SUM(CASE WHEN INSTR(','||shi_sxs||',', ',5,') > 0 THEN ren_c ELSE 0 END) AS count4 FROM uf_tan_hlb WHERE shi_szl ='").append(type).append("'");
                break;
            case "2":
                sql.append("SELECT SUM(CASE WHEN INSTR(','||shi_sxs||',', ',8,') > 0 THEN ren_c ELSE 0 END) AS count1,SUM(CASE WHEN INSTR(','||shi_sxs||',', ',10,') > 0 THEN ren_c ELSE 0 END) AS count2,SUM(CASE WHEN INSTR(','||shi_sxs||',', ',16,') > 0 THEN ren_c ELSE 0 END) AS count3,").append(getOtherForms()).append(" AS count4 FROM uf_tan_hlb WHERE shi_szl ='").append(type).append("'");
                break;
            default:
                sql.append("SELECT SUM(CASE WHEN INSTR(','||shi_sxs||',', ',0,') > 0 THEN ren_c ELSE 0 END) AS count1,SUM(CASE WHEN INSTR(','||shi_sxs||',', ',1,') > 0 THEN ren_c ELSE 0 END) AS count2 FROM uf_tan_hlb WHERE shi_szl ='").append("0").append("'");
                break;
        }
        sql.append(getCondition("", year, quarter, departmentId));
        log.info("查询谈话分类sql语句:{}", sql);
        try {
            List<Map<String, Object>> maps = dataSqlService.ebuilderFromSql(sql.toString(), 1, 100, SourceType.LOGIC);
            for (Map<String, Object> map : maps) {
                switch (type) {
                    case "1": {
                        Map<String, Object> map1 = new HashMap<>();
                        map1.put("id", "2");
                        map1.put("name", "主体责任集体约谈");
                        map1.put("value", StringUtils.null2String(map.get("count1"), "0"));
                        list.add(map1);

                        Map<String, Object> map2 = new HashMap<>();
                        map2.put("id", "3");
                        map2.put("name", "主体责任个人约谈");
                        map2.put("value", StringUtils.null2String(map.get("count2"), "0"));
                        list.add(map2);

                        Map<String, Object> map3 = new HashMap<>();
                        map3.put("id", "4");
                        map3.put("name", "监督责任集体约谈");
                        map3.put("value", StringUtils.null2String(map.get("count3"), "0"));
                        list.add(map3);

                        Map<String, Object> map4 = new HashMap<>();
                        map4.put("id", "5");
                        map4.put("name", "监督责任个人约谈");
                        map4.put("value", StringUtils.null2String(map.get("count4"), "0"));
                        list.add(map4);

                        break;
                    }
                    case "2": {
                        Map<String, Object> map1 = new HashMap<>();
                        map1.put("id", "8");
                        map1.put("name", "提醒谈话");
                        map1.put("value", StringUtils.null2String(map.get("count1"), "0"));
                        list.add(map1);

                        Map<String, Object> map2 = new HashMap<>();
                        map2.put("id", "10");
                        map2.put("name", "批评教育");
                        map2.put("value", StringUtils.null2String(map.get("count2"), "0"));
                        list.add(map2);

                        Map<String, Object> map3 = new HashMap<>();
                        map3.put("id", "16");
                        map3.put("name", "召开民主生活会（组织生活会）批评帮助");
                        map3.put("value", StringUtils.null2String(map.get("count3"), "0"));
                        list.add(map3);

                        Map<String, Object> map4 = new HashMap<>();
                        map4.put("id1", "8");
                        map4.put("id2", "10");
                        map4.put("id3", "16");
                        map4.put("name", "其他形式");
                        map4.put("value", StringUtils.null2String(map.get("count4"), "0"));
                        list.add(map4);

                        break;
                    }
                    default: {
                        Map<String, Object> map1 = new HashMap<>();
                        map1.put("id", "0");
                        map1.put("name", "任前廉洁谈话");
                        map1.put("value", StringUtils.null2String(map.get("count1"), "0"));
                        list.add(map1);

                        Map<String, Object> map2 = new HashMap<>();
                        map2.put("id", "1");
                        map2.put("name", "日常廉洁谈话");
                        map2.put("value", StringUtils.null2String(map.get("count2"), "0"));
                        list.add(map2);

                        break;
                    }
                }
            }
            log.info("{} 请求获取统计数据获得数据：{}", methodName, JSONObject.toJSONString(list));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return list;
    }

    @Override
    public Map<String, Object> getCountInfo5(String year, String quarter, String departmentId, String shi_szl) {
        String methodName = String.format("调用%s.getCountInfo5()-->进入查询getCountInfo5接口", simpleName);
        log.info("{} year:{},quarter:{},departmentId:{},shi_szl:{}", methodName, year, quarter, departmentId, shi_szl);
        Map<String, Object> map = new HashMap<>();
        String condition = getCondition(shi_szl, year, quarter, String.valueOf(departmentId));
        String sql = "select shi_sdx,shi_sdXbm,shi_szl,shi_sxs from uf_tan_hlb where 1=1" + condition;
        try {
            List<Map<String, Object>> maps = dataSqlService.ebuilderFromSql(sql, 1, 100, SourceType.LOGIC);
            List<DepartmentModel> allDepartmentList = hrService.getAllDepartmentList();
            List<String> xAxisIdList = new ArrayList<>();
            List<Map<String, Object>> seriesList = dataKind(shi_szl);       //数据结构集合
            for (Map<String, Object> map1 : maps) {
                String shi_sdx = StringUtils.null2String(map1.get("shi_sdx"));
                String kind = StringUtils.null2String(map1.get("shi_szl"));
                String shi_sxs = StringUtils.null2String(map1.get("shi_sxs"));
                String[] shi_sdxStr = shi_sdx.split(",");
                for (String shi_sdxStr1 : shi_sdxStr) {
                    String supDepId = portalService.getDepartIdBySubSector(allDepartmentList, shi_sdxStr1, 3);
                    if (!departmentId.isEmpty() && !departmentId.equals(supDepId)) {
                        continue;
                    }
                    if (!xAxisIdList.contains(supDepId)) {
                        xAxisIdList.add(supDepId);
                        for (Map<String, Object> stringObjectMap : seriesList) {
                            List<Integer> valueList = (List<Integer>) stringObjectMap.get("value");
                            valueList.add(0);
                            stringObjectMap.put("value", valueList);
                        }
                        int index = xAxisIdList.indexOf(supDepId);
                        for (Map<String, Object> stringObjectMap : seriesList) {
                            List<Integer> valueList = (List<Integer>) stringObjectMap.get("value");
                            if (shi_szl.isEmpty()) {
                                if (stringObjectMap.get("id").equals(kind)) {
                                    valueList.set(index, valueList.get(index) + 1);
                                    stringObjectMap.put("value", valueList);
                                }
                            } else {
                                String[] shi_sxsStr = shi_sxs.split(",");
                                List<String> shi_sxsList = Arrays.asList(String.valueOf(stringObjectMap.get("shi_sxs_id")).split(","));
                                for (String shi_sxsKey : shi_sxsStr) {
                                    if (shi_sxsList.contains(shi_sxsKey)) {
                                        valueList.set(index, valueList.get(index) + 1);
                                        stringObjectMap.put("value", valueList);
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            List<Map<String, Object>> seriesList2 = this.dataKind(shi_szl);
            List<String> xAxisIdList2 = this.getDepartmentIds();
            List<String> xAxisDataList2 = getDepartmentNames(allDepartmentList);
            //创建新的数据集合按柱状图排序配置组合
            for (String deptId : xAxisIdList2) {
                for (int j = 0; j < seriesList.size(); j++) {
                    List<Integer> valueList2 = (List<Integer>) seriesList2.get(j).get("value");
                    if (xAxisIdList.contains(deptId)) {
                        List<Integer> valueList = (List<Integer>) seriesList.get(j).get("value");
                        int index = xAxisIdList.indexOf(deptId);
                        valueList2.add(valueList.get(index));
                    } else {
                        valueList2.add(0);
                    }
                }
            }

            map.put("series", seriesList2);
            map.put("xAxisId", xAxisIdList2);
            map.put("xAxisData", xAxisDataList2);
            return map;
        } catch (Exception e) {

            log.info("{} 请求获取统计数据异常：{};", methodName, e.getMessage(), e);
        }
        return Collections.emptyMap();
    }

    @Override
    public String conversationCondition(String shiSzl, String startTime, String endTime, String department, String shiSxs, String shapeType) {
        StringBuilder sqlCondition = new StringBuilder("1=1");
        if (!shiSzl.isEmpty()) {
            sqlCondition.append(" and t1.shi_szl = '").append(shiSzl).append("'");
        }
        if (!startTime.isEmpty() && !endTime.isEmpty()) {
            sqlCondition.append(" and t1.shi_j >= '").append(startTime).append("'");
            endTime = addOneMonth(endTime);
            sqlCondition.append(" and t1.shi_j < '").append(endTime).append("'");
        }

        //实施对象部门包含
        if (!department.isEmpty()) {
            List<String> departIdList = portalService.getDepartIdList(department);
            String subordinateDepartment = String.join(",", departIdList);
            log.info("下级部门：{}", subordinateDepartment);
            StringBuilder condition = new StringBuilder();
            String[] parts = subordinateDepartment.split(",");
            int[] array = new int[parts.length];
            for (int k = 0; k < parts.length; k++) {
                array[k] = Integer.parseInt(parts[k]);
                condition.append("INSTR(','||t1.shi_sdxbm||',', ',").append(array[k]).append(",') > 0");
                if (k != parts.length - 1) {
                    condition.append(" or ");
                }
            }
            sqlCondition.append(" and (").append(condition).append(")");
        }

        //实施形态包含
        if (!shiSxs.isEmpty() && shapeType.equals("0")) {
            int[] numbers = convertToArray(shiSxs);
            StringBuilder condition = new StringBuilder();
            for (int i = 0; i < numbers.length; i++) {
                condition.append("INSTR(','||t1.shi_sxs||',', ',").append(numbers[i]).append(",') > 0");
                if (i != numbers.length - 1) {
                    condition.append(" or ");
                }
            }
            sqlCondition.append(" and (").append(condition).append(")");
        }
        //实施形态不包含
        if (!shiSxs.isEmpty() && shapeType.equals("1")) {
            int[] numbers = convertToArray(shiSxs);
            StringBuilder condition = new StringBuilder();
            for (int i = 0; i < numbers.length; i++) {
                condition.append("INSTR(','||t1.shi_sxs||',', ',").append(numbers[i]).append(",') = 0");
                if (i != numbers.length - 1) {
                    condition.append(" or ");
                }
            }
            sqlCondition.append(" and (").append(condition).append(")");
        }

        log.info("generateSqlConditionSql：{}", sqlCondition);
        return sqlCondition.toString();
    }

    public static int[] convertToArray(String numbersString) {
        String[] numberStrings = numbersString.split(",");
        int[] numbers = new int[numberStrings.length];
        for (int i = 0; i < numberStrings.length; i++) {
            numbers[i] = Integer.parseInt(numberStrings[i]);
        }
        return numbers;
    }

    public static String addOneMonth(String dateString) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        try {
            Date date = sdf.parse(dateString);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.MONTH, 1);
            Date newDate = calendar.getTime();
            return sdf.format(newDate);
        } catch (Exception e) {
            log.error("addOneMonth异常的原因：{}", String.valueOf(e));
        }
        return null;
    }

    private ArrayList<String> getDepartmentIds() {
        ArrayList<String> departments = new ArrayList<>();
        String sql = "select bu_mmc from uf_zhu_ztBMpx order by bu_mxh asc";
        try {
            List<Map<String, Object>> maps = dataSqlService.ebuilderFromSql(sql, 1, 100, SourceType.LOGIC);
            for (Map<String, Object> map : maps) {
                String departmentName = StringUtils.null2String(map.get("id"));
                departments.add(departmentName);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        log.info("部门id数据为{}", departments);
        return departments;
    }

    /**
     * 获取柱状图部门name排序配置信息数组
     *
     * @param allDepartmentList 部门集合
     */
    private ArrayList<String> getDepartmentNames(List<DepartmentModel> allDepartmentList) {
        ArrayList<String> departments = new ArrayList<>();
        String sql = "select bu_mmc from uf_zhu_ztBMpx order by bu_mxh asc";
        try {
            List<Map<String, Object>> maps = dataSqlService.ebuilderFromSql(sql, 1, 100, SourceType.LOGIC);
            for (Map<String, Object> map : maps) {
                String id = StringUtils.null2String(map.get("bu_mmc"));
                String departmentName = allDepartmentList.stream().filter(e -> StringUtils.null2String(e.getId()).equals(id)).map(DepartmentModel::getName).findFirst().orElse("");
                departments.add(departmentName);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return departments;
    }

    /**
     * 返回数据组装
     *
     * @param shi_szl 实施种类 0-廉洁谈话 1-责任约谈 2-运用第一形态
     */
    private List<Map<String, Object>> dataKind(String shi_szl) {
        List<Map<String, Object>> seriesList = new ArrayList<>();   //数据集合
        if (shi_szl.isEmpty()) {
            Map<String, Object> seriesMap1 = new HashMap<>();
            seriesMap1.put("id", "0");
            seriesMap1.put("name", "廉洁谈话");
            seriesMap1.put("value", new ArrayList<Integer>());
            seriesList.add(seriesMap1);

            Map<String, Object> seriesMap2 = new HashMap<>();
            seriesMap2.put("id", "1");
            seriesMap2.put("name", "责任约谈");
            seriesMap2.put("value", new ArrayList<Integer>());
            seriesList.add(seriesMap2);

            Map<String, Object> seriesMap3 = new HashMap<>();
            seriesMap3.put("id", "2");
            seriesMap3.put("name", "运用第一形态");
            seriesMap3.put("value", new ArrayList<Integer>());
            seriesList.add(seriesMap3);

        } else if (shi_szl.equals("0")) {
            Map<String, Object> seriesMap1 = new HashMap<>();
            seriesMap1.put("id", "0");
            seriesMap1.put("name", "廉洁谈话");
            seriesMap1.put("shi_sxs_id", "0");
            seriesMap1.put("shi_sxs", "任前廉洁谈话");
            seriesMap1.put("value", new ArrayList<Integer>());
            seriesList.add(seriesMap1);
            Map<String, Object> seriesMap2 = new HashMap<>();
            seriesMap2.put("id", "0");
            seriesMap2.put("name", "廉洁谈话");
            seriesMap2.put("shi_sxs_id", "1");
            seriesMap2.put("shi_sxs", "日常廉洁谈话");
            seriesMap2.put("value", new ArrayList<Integer>());
            seriesList.add(seriesMap2);
        } else if (shi_szl.equals("1")) {
            Map<String, Object> seriesMap3 = new HashMap<>();
            seriesMap3.put("id", "1");
            seriesMap3.put("name", "责任约谈");
            seriesMap3.put("shi_sxs_id", "2");
            seriesMap3.put("shi_sxs", "主体责任集体约谈");
            seriesMap3.put("value", new ArrayList<Integer>());
            seriesList.add(seriesMap3);

            Map<String, Object> seriesMap4 = new HashMap<>();
            seriesMap4.put("id", "1");
            seriesMap4.put("name", "责任约谈");
            seriesMap4.put("shi_sxs_id", "3");
            seriesMap4.put("shi_sxs", "主体责任个人约谈");
            seriesMap4.put("value", new ArrayList<Integer>());
            seriesList.add(seriesMap4);

            Map<String, Object> seriesMap5 = new HashMap<>();
            seriesMap5.put("id", "1");
            seriesMap5.put("name", "责任约谈");
            seriesMap5.put("shi_sxs_id", "4");
            seriesMap5.put("shi_sxs", "监督责任集体约谈");
            seriesMap5.put("value", new ArrayList<Integer>());
            seriesList.add(seriesMap5);

            Map<String, Object> seriesMap6 = new HashMap<>();
            seriesMap6.put("id", "1");
            seriesMap6.put("name", "责任约谈");
            seriesMap6.put("shi_sxs_id", "5");
            seriesMap6.put("shi_sxs", "监督责任个人约谈");
            seriesMap6.put("value", new ArrayList<Integer>());
            seriesList.add(seriesMap6);
        } else if (shi_szl.equals("2")) {
            Map<String, Object> seriesMap7 = new HashMap<>();
            seriesMap7.put("id", "2");
            seriesMap7.put("name", "运用第一形态");
            seriesMap7.put("shi_sxs_id", "8");
            seriesMap7.put("shi_sxs", "提醒谈话");
            seriesMap7.put("value", new ArrayList<Integer>());
            seriesList.add(seriesMap7);

            Map<String, Object> seriesMap8 = new HashMap<>();
            seriesMap8.put("id", "2");
            seriesMap8.put("name", "运用第一形态");
            seriesMap8.put("shi_sxs_id", "10");
            seriesMap8.put("shi_sxs", "批评教育");
            seriesMap8.put("value", new ArrayList<Integer>());
            seriesList.add(seriesMap8);

            Map<String, Object> seriesMap9 = new HashMap<>();
            seriesMap9.put("id", "2");
            seriesMap9.put("name", "运用第一形态");
            seriesMap9.put("shi_sxs_id", "16");
            seriesMap9.put("shi_sxs", "召开民主生活会（组织生活会）批评帮助");
            seriesMap9.put("value", new ArrayList<Integer>());
            seriesList.add(seriesMap9);

            Map<String, Object> seriesMap10 = new HashMap<>();
            seriesMap10.put("id", "2");
            seriesMap10.put("name", "运用第一形态");
            seriesMap10.put("shi_sxs_id", "6,7,9,11,12,13,14,15,17");
            seriesMap10.put("shi_sxs", "其他形式");
            seriesMap10.put("value", new ArrayList<Integer>());
            seriesList.add(seriesMap10);
        }
        return seriesList;
    }

    private String getOtherForms() {
        String[] shi_sxs = "6,7,9,11,12,13,14,15,17".split(",");
        StringBuilder str = new StringBuilder();
        for (String shiSx : shi_sxs) {
            if (str.length() == 0) {
                str = new StringBuilder("SUM(CASE WHEN INSTR(','||shi_sxs||',', '," + shiSx + ",') > 0 THEN ren_c ELSE 0 END)");
            } else {
                str.append(" + SUM(CASE WHEN INSTR(','||shi_sxs||',', ',").append(shiSx).append(",') > 0 THEN ren_c ELSE 0 END)");
            }
        }
        return str.toString();
    }

    private String getCondition(String shi_szl, String year, String quarter, String department) {
        String condition = "";
        if (!shi_szl.isEmpty()) {
            condition += " and shi_szl = " + shi_szl;
        }
        if (!year.isEmpty()) {
            condition += " and shi_j like '" + year + "%'";
        }

        if (!quarter.isEmpty()) {
            List<Map<String, Object>> list = acquisitionQuarter(Integer.parseInt(year));
            condition += " and shi_j > '" + list.get(Integer.parseInt(quarter)).get("starTime") + "'";
            condition += " and shi_j < '" + list.get(Integer.parseInt(quarter)).get("endTime") + "'";
        }

        if (!department.isEmpty()) {
            List<String> departIdList = portalService.getDepartIdList(department);
            log.info("{} 请求获取统计数据，获取到部门ID集合：{}", simpleName, departIdList);
            //获取所有的下级部门
            String subordinateDepartment = String.join(",", departIdList);
            String[] parts = subordinateDepartment.split(",");
            String[] array = new String[parts.length];
            StringBuilder shi_sdXbmCondition = new StringBuilder();
            for (int k = 0; k < parts.length; k++) {
                array[k] = parts[k];
                shi_sdXbmCondition.append("INSTR(','||shi_sdXbm||',', ',").append(array[k]).append(",') > 0");
                if (k != parts.length - 1) {
                    shi_sdXbmCondition.append(" or ");
                }
            }
            condition += " and (" + shi_sdXbmCondition + ")";
        }

        return condition;
    }

    private List<Map<String, Object>> acquisitionQuarter(int year) {
        List<Map<String, Object>> list = new ArrayList<>();
        for (int quarter = 1; quarter <= 4; quarter++) {
            Map<String, Object> map = new HashMap<>();
            YearMonth startMonth = getStartMonthOfQuarter(year, quarter);
            YearMonth endMonth = getEndMonthOfQuarter(year, quarter);
            map.put("starTime", startMonth.format(DateTimeFormatter.ofPattern("yyyy-MM")));
            endMonth = endMonth.plusMonths(1);
            String formattedEndMonth = endMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            map.put("endTime", formattedEndMonth);
            list.add(map);
        }
        return list;
    }

    private YearMonth getStartMonthOfQuarter(int year, int quarter) {
        int startMonth = (quarter - 1) * 3 + 1;
        return YearMonth.of(year, startMonth);
    }

    private YearMonth getEndMonthOfQuarter(int year, int quarter) {
        int startMonth = (quarter - 1) * 3 + 1;
        int endMonth = startMonth + 2;
        return YearMonth.of(year, endMonth);
    }
}
