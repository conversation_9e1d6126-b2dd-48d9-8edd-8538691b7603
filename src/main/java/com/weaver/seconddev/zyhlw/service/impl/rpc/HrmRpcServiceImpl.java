package com.weaver.seconddev.zyhlw.service.impl.rpc;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.common.hrm.domain.organization.HrmOrgEmpCondition;
import com.weaver.common.hrm.dto.syncdata.HrmSyncDataConfig;
import com.weaver.common.hrm.remote.HrmRemoteIntegrationSyncDataService;
import com.weaver.framework.rpc.annotation.RpcReference;
import com.weaver.seconddev.zyhlw.service.IHrmPpcService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <h1>HrmRpcImpl</h1>
 *
 * <p>
 * 用户相关rpc二次封装
 * </p>
 *
 * <p>Note: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Service
@Slf4j
public class HrmRpcServiceImpl implements IHrmPpcService {
    private final String SIMPLE_NAME = HrmRpcServiceImpl.class.getSimpleName();

    @RpcReference
    HrmRemoteIntegrationSyncDataService hrmRemoteIntegrationSyncDataService;
    @Resource
    CmicProperties cmicProperties;

    /**
     * getEmployeeDataAllList
     * <p>
     *
     * @param condition  HrmOrgEmpCondition 参数说明 https://weapp.eteams.cn/build/techdoc/wdoc/index.html#/public/doc/83286b59-09c6-4c4e-ba6a-276f2437dcee
     *                   租户必传
     * @param dataConfig browserIdAndName 系统字段浏览按钮返回 id+name内容，和其他内容，内容类似浏览按钮回显的内容; 额外提供{full_code}Info内容
     * @return 用户信息
     * @description rpc获取全部人员信息，支持指定用户指定参数
     * <p>
     * <AUTHOR>
     * @time 2025年02月07 16:18:58
     * @since 1.0
     */
    @Override
    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> queryEmployeeDataAllList(HrmOrgEmpCondition condition, HrmSyncDataConfig dataConfig) {
        String method = String.format("调用%s.queryEmployeeDataAllList()-->", SIMPLE_NAME);

        List<Map<String, Object>> result = new ArrayList<>();
        int pageSize = 100;
        int pageNumber = 1;
        while (true) {
            try {
                Map<String, Object> resultVo = getEmployeeDataAllMap(condition, dataConfig, pageNumber, pageSize);
                if (resultVo == null || resultVo.isEmpty()) {
                    log.info("{}查询，当前页：{}，返回结果为空", method, pageNumber);
                    break;
                }
                List<Map<String, Object>> pageDataList = (List<Map<String, Object>>) resultVo.get("data");
                if (pageDataList == null || pageDataList.isEmpty()) {
                    break;
                }
                result.addAll(pageDataList);
                long total = (long) resultVo.get("total");

                if (result.size() >= total) {
                    break;
                }

                pageNumber++;
            } catch (Exception e) {
                log.error("{}查询，当前页：{}，发生异常", method, pageNumber, e);
                throw new RuntimeException(e);
            }
        }
        return result;
    }

    /**
     * 人员全信息查询接口
     * <p>
     * fieldList  要查询的字段，需要可以往里面加 参数说明(必须使用full_code) https://weapp.eteams.cn/build/techdoc/wdoc/index.html#/public/doc/60aafd88-bf14-4695-bfe8-53c630f405d2
     *            如账号id: employee.user_id
     * @param condition  HrmOrgEmpCondition 参数说明 https://weapp.eteams.cn/build/techdoc/wdoc/index.html#/public/doc/83286b59-09c6-4c4e-ba6a-276f2437dcee
     *                   租户必传
     * @param dataConfig browserIdAndName 系统字段浏览按钮返回 id+name内容，和其他内容，内容类似浏览按钮回显的内容; 额外提供{full_code}Info内容
     * @param current    当前页
     * @param pageSize   每页数，最大1000
     * @return 用户信息
     * @description rpc获取全部人员信息，支持指定用户指定参数
     * <p>
     * <AUTHOR>
     * @time 2025年02月07 16:18:58
     * @since 1.0
     */
    @Override
    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> getEmployeeDataAllList(HrmOrgEmpCondition condition, HrmSyncDataConfig dataConfig, Integer current, Integer pageSize) {
        String method = String.format("调用%s.getEmployeeDataAllList-->", SIMPLE_NAME);
        List<String> fieldList = new ArrayList<>();
        fieldList.add("employee.username");
        fieldList.add("employee.superior");
        fieldList.add("employee.position");
        fieldList.add("employee.grade");
        fieldList.add("employee.job_call");
        fieldList.add("employee.mobile");
        condition.setTenantKey(cmicProperties.getHostTenantKey());
        log.info("{}获取人员全信息, condition：{}, fieldList：{}, dataConfig：{}, current：{}, pageSize：{}", method, JSON.toJSONString(condition), JSON.toJSONString(fieldList), JSON.toJSONString(dataConfig), current, pageSize);
        WeaResult<Map<String, Object>> result = hrmRemoteIntegrationSyncDataService.pageEmployeeData(condition, fieldList, dataConfig, current, pageSize);
        log.info("{}获取人员全信息响应：{}",method, JSON.toJSONString(result));
        if(result.getCode() == 200) {
            return (List<Map<String, Object>>) result.getData().get("data");
        }
        return Collections.emptyList();
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> getEmployeeDataAllMap(HrmOrgEmpCondition condition, HrmSyncDataConfig dataConfig, Integer current, Integer pageSize) {
        String method = String.format("调用%s.getEmployeeDataAllMap-->", SIMPLE_NAME);
        List<String> fieldList = new ArrayList<>();
        fieldList.add("employee.username");
        fieldList.add("employee.superior");
        fieldList.add("employee.position");
        fieldList.add("employee.grade");
        fieldList.add("employee.job_call");
        fieldList.add("employee.mobile");
        condition.setTenantKey(cmicProperties.getHostTenantKey());
        log.info("{}获取人员全信息, condition：{}, fieldList：{}, dataConfig：{}, current：{}, pageSize：{}", method, JSON.toJSONString(condition), JSON.toJSONString(fieldList), JSON.toJSONString(dataConfig), current, pageSize);
        WeaResult<Map<String, Object>> result = hrmRemoteIntegrationSyncDataService.pageEmployeeData(condition, fieldList, dataConfig, current, pageSize);
        log.info("{}获取人员全信息响应：{}",method, JSON.toJSONString(result));
        if(result.getCode() == 200) {
            return result.getData();
        }
        return Collections.EMPTY_MAP;
    }
}
