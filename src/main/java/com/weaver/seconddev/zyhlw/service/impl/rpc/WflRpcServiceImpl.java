package com.weaver.seconddev.zyhlw.service.impl.rpc;

import com.alibaba.fastjson.JSON;
import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.common.hrm.domain.organization.HrmOrgEmpCondition;
import com.weaver.common.hrm.dto.syncdata.HrmSyncDataConfig;
import com.weaver.common.hrm.remote.HrmRemoteIntegrationSyncDataService;
import com.weaver.framework.rpc.annotation.RpcReference;
import com.weaver.seconddev.zyhlw.service.IHrmPpcService;
import com.weaver.seconddev.zyhlw.service.IWflPpcService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.teams.domain.user.SimpleEmployee;
import com.weaver.workflow.common.entity.list.api.RequestListConditionApiEntity;
import com.weaver.workflow.common.entity.list.api.publicapi.RequestListInfoPAEntity;
import com.weaver.workflow.list.api.rest.publicapi.WflRequestListRest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <h1>HrmRpcImpl</h1>
 *
 * <p>
 * 用户相关rpc二次封装
 * </p>
 *
 * <p>Note: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Service
@Slf4j
public class WflRpcServiceImpl implements IWflPpcService {
    private final String SIMPLE_NAME = WflRpcServiceImpl.class.getSimpleName();

    @RpcReference(group = "workflow")
    WflRequestListRest wflRequestListRest;

    @Override
    public List<RequestListInfoPAEntity> getToDoWorkflowRequestAllList(SimpleEmployee employee, RequestListConditionApiEntity conditionEntity) {
        String method = String.format("调用%s.getToDoWorkflowRequestAllList()-->", SIMPLE_NAME);

        List<RequestListInfoPAEntity> result = new ArrayList<>();
        int pageSize = 1000;
        int pageNumber = 1;
        while (true) {
            try {
                List<RequestListInfoPAEntity> resultVo = getToDoWorkflowRequestList(employee, conditionEntity, pageNumber, pageSize);
                if (resultVo == null || resultVo.isEmpty()) {
                    log.info("{}查询，当前页：{}，返回结果为空", method, pageNumber);
                    break;
                }
                result.addAll(resultVo);
                pageNumber++;
            } catch (Exception e) {
                log.error("{}查询，当前页：{}，发生异常", method, pageNumber, e);
                throw new RuntimeException(e);
            }
        }
        return result;

    }

    @Override
    public List<RequestListInfoPAEntity> getToDoWorkflowRequestList(SimpleEmployee employee, RequestListConditionApiEntity conditionEntity, int pageNo, int pageSize) {

        WeaResult<List<RequestListInfoPAEntity>> todoResult = wflRequestListRest.getToDoWorkflowRequestList(employee, conditionEntity, pageNo, pageSize);
        if(todoResult.getCode() == 200) {
            return todoResult.getData();
        }
        return Collections.emptyList();
    }
}
