package com.weaver.seconddev.zyhlw.service.income;

import java.util.Map;


public interface IFinanceCheckedService {

    Map<String, String> addFlow(String hongCfpjeStr);

    Map<String, Object> newSelverify(String arrStr);

    Map<String, String> selFlow(String gouji_idStr);

    Integer GetMaxId();

    /**
     * 通过ID判断合同欠费规则中的数据中的是否已发起设置欠费规则流程
     * @param ids
     * @return
     */
    Map<String,String> GetEnterpriseOverduePaymentRulesMap(String ids);


}
