package com.weaver.seconddev.zyhlw.service.income;

import java.util.List;
import java.util.Map;

public interface IGetCheckFinanceService {

    /**
     * 校验是否同一个合同方法（根据应收id）
     *
     * @param ids
     * @param resultMap
     */
    //2021-8-21
    Map<String, Object> checkHeTong(String ids, Map<String, Object> resultMap)  throws Exception;

    /**
     * 校验应收单勾稽状态是否已勾稽方法（根据应收ID）
     *
     * @param ids
     * @param resultMap
     */
     Map<String, Object> checkGJZT(String ids, Map<String, Object> resultMap) throws Exception;

    /**
     * 校验发票状态是否正常方法（根据发票ID）
     *
     * @param ids
     * @param resultMap
     */
     Map<String, Object> checkFPZT(String ids, Map<String, Object> resultMap) throws Exception;

    /**
     * 校验日期
     *
     * @param ids
     * @param list
     */
    List<Map<String, Object>> checkdate(String ids, List<Map<String, Object>> list) throws Exception;

    /*
     * 发票信息库勾稽按钮校验
     * 发票未开票金额
     * */

     Map<String, Object> checkfpwgjje(String ids, Map<String, Object> resultMap) throws Exception;

    /**
     * 验证发票反勾稽是否有重复勾稽
     */

    void checkGJJLFP2(String ids, Map<String, Object> resultMap);

    /**
     * 查询发票信息库  勾稽金额是否有勾稽记录
     *
     * @param ids
     * @param resultMap
     */
     void checkGJJU(String ids, Map<String, Object> resultMap);

    /**
     * 校验是否存在勾稽记录
     */
    void checkGJJU2(String ids, Map<String, Object> resultMap);

    /**
     * 验证勾稽记录数据是否正常方法(根据发票ID)
     *
     * @param ids
     * @param resultMap
     */
    void checkGJJLFP(String ids, Map<String, Object> resultMap);

    /**
     * 验证红冲发票可用金额大于0
     *
     * @param ids
     * @param resultMap
     */

    void checkGJJLFP3(String ids, Map<String, Object> resultMap);

    /**
     * 验证勾稽记录数据是否正常方法(根据应收ID)
     *
     * @param ids
     * @param resultMap
     */
    void checkGJJLYS(String ids, Map<String, Object> resultMap);

    /**
     * 验证勾稽记录数据是否正常(勾稽记录ID)
     *
     * @param ids
     * @param resultMap
     */
    void checkGJJLID(String ids, Map<String, Object> resultMap);

    /**
     * 验证发票号码是否唯一       方法(发票号码)
     *
     * @param fphm
     * @param resultMap
     */
    void checkFPHM(String fphm, Map<String, Object> resultMap);

    /**
     * 验证红冲金额不能大于发票的已勾稽金额方法（JSON ）[{faId:1,jine:9.0},{faId:1,jine:9.0}]
     * 2021-6-21 修改  红冲发票金额 <= 发票可用金额
     *
     * @param parm
     * @param resultMap
     */

    void checkHCJE(String parm, Map<String, Object> resultMap);

    /**
     * 验证开票金额大于应收报账单未开票的总金额（JSON）["yingId":"1,1,1","jine":1]
     *
     * @param parm
     * @param resultMap
     */
    void checkKPJE(String parm, Map<String, Object> resultMap);

    /*
     * 判断客户编号是否存在
     * ids : ID
     * */

    void ChenKhbhSel(String ids, Map<String, Object> resultMap);

    /**
     * 判断传过来的发票在发票作废和红冲流程中有在途的工单
     *
     * @param ids
     * @param resultMap
     */
    void ChenOrderOnWayByFPBH(String ids, Map<String, Object> resultMap);


}
