package com.weaver.seconddev.zyhlw.service.income;

import java.util.Map;

public interface IInvoiceStatisticService {

    /**
     * 更新发票的同步状态
     *
     * @param invoiceId
     * @param ysId
     * @return
     */
    public Map<String, Object> updateByIRId(String invoiceId, String ysId);

    public Map<String, Object> updateByGjId(String gjId);

    public Map<String, Object> getList(String ids);
}
