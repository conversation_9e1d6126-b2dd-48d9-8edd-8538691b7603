package com.weaver.seconddev.zyhlw.util;


import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;


@Component
@RefreshScope
@Getter
@Setter
public class CmicProperties {
    /** 企业corpId */
    @Value("${corpid}")
    private String corpid;
    /** 默认应用appKey */
    @Value("${default.app.key}")
    private String defaultAppKey;
    /** 默认应用appSecret */
    @Value("${default.app.secret}")
    private String defaultAppSecret;
    /** 开放平台地址 */
    @Value("${open.platform.url}")
    private String openPlatformUrl;
    /** 待办数量V1 */
    @Value("${portal.wait.getBenchDaiCount}")
    private String getBenchDaiCountUrl;
    /** 查询所有待办（无分页） */
    @Value("${portal.wait.item2.query.list}")
    private String waitItem2QueryListUrl;
    /** 查询待办 */
    @Value("${portal.wait.item1.query.list}")
    private String waitItem1QueryListUrl;
    /** 待办数量V2*/
    @Value("${portal.wait.item2.query.alltotal}")
    private String waitItem2QueryAlltotal;
    /**待办置灰接口V1*/
    @Value("${portal.wait.todo.setgray}")
    private String waitTodoSetGray;
    /**待办置灰接口V2*/
    @Value("${portal.wait.item2.change.setgray}")
    private String waitItem2ChangeSetGray;
    /**获取集团待办*/
    @Value("${portal.wait.tdunify.getTdUnifyList}")
    private String waitTdunifyGetTdUnifyList;
    /**SIM快捷认证地址*/
    @Value("${simQuickAuth.url}")
    private String simQuickAuthUrl;
    /**portal 应用appid*/
    @Value("${portal.appid}")
    private String appid;
    /**portal 应用密钥*/
    @Value("${portal.appSecret}")
    private String appSecret;
    /**同步人员接口地址*/
    @Value("${portal.user.sync.emp.url}")
    private String  syncEmpUrl;
    /**同步组织结构地址*/
    @Value("${portal.user.sync.org.url}")
    private String syncOrgUrl;
    /** 云桌面token认证地址 */
    @Value("${ldap.url}")
    private  String ldapUrl;
    /** 云桌面单点应用app_key */
    @Value("${ldap.app.key}")
    private String ldapAppKey;
    /** 云桌面单点应用app_security */
    @Value("${ldap.app.security}")
    private String ldapAppSecurity;
    /** 云桌面单点重定向地址 */
    @Value("${ldap.redirect.uri}")
    private String ldapRedirectUri;
    /** SIM快捷认证应用app_key */
    @Value("${simQuickAuth.app.key}")
    private String simQuickAuthAppKey;
    /** SIM快捷认证应用app_security */
    @Value("${simQuickAuth.app.security}")
    private String simQuickAuthAppSecurity;
    /** 免登链接 */
    @Value("${singleSignon.url}")
    private String singleSignonUrl;
    /** SIM快捷认证应用单点重定向 */
    @Value("${simQuickAuth.redirect.uri}")
    private String simQuickAuthRedirectUri;
    /** 更新待办缓存数据接口 */
    @Value("${portal.wait.start.get.sys.data}")
    private String startGetSysDataUrl;
    /** portal统一认证 获取apptoken */
    @Value("${portal.apptoken.getToken}")
    private String portalApptokenGetToken;
    /** 集团统一待办 事项新增或更新接口 */
    @Value("${cmic.portal.wait.data}")
    private String cmicPortalWaitData;
    /** 集团统一待办 事项快捷更新接口 */
    @Value("${cmic.portal.wait.quick}")
    private String cmicPortalWaitQuick;
    /** 集团统一待办 事项新增或更新接口(批量) */
    @Value("${cmic.portal.wait.datalist}")
    private String cmicPortalWaitDatalist;
    /** 集团统一待办 业务系统稽核接口 */
    @Value("${cmic.portal.wait.audit}")
    private String cmicPortalWaitAudit;
    /** portal统一认证 统一待办appId */
    @Value("${cmic.portal.apptoken.target.appId}")
    private String cmicPortalApptokenTargetAppId;
    /** e10域名地址 */
    @Value("${e10.url}")
    private String ecUrl;
    /** 主租户key */
    @Value("${host_tenant_key}")
    private String hostTenantKey;
    /** 统一待办在EB的appid */
    @Value("${todo.appid}")
    private String todoAppid;

}
