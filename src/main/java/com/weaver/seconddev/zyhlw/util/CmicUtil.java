package com.weaver.seconddev.zyhlw.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.weaver.ebuilder.common.util.StringUtil;
import com.weaver.seconddev.zyhlw.util.http.HttpUtils;
import com.weaver.seconddev.zyhlw.util.http.MyHostnameVerifier;
import com.weaver.seconddev.zyhlw.util.http.MyMiTM;
import com.weaver.seconddev.zyhlw.util.sso.ReturnBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.InputStreamRequestEntity;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.RequestEntity;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

@Slf4j
public class CmicUtil {
    private final static String simpleName = CmicUtil.class.getSimpleName();
    //后续需要改为从配置中读取 todo
    static String appId = "140";
    static String appSecret = "x1HZisCbln38ftog1RgdjkriLSzCOgdX6Dlvr1Ww";
    private static final String APP_ID = "appId";

    private static final String APP_SECRET = "appSecret";
    private static final String CONTENT_TYPE = "application/json; charset=utf-8";
    private static final String SSO_CREATE_TOKEN = "sso_create_token";
    private static final String SSO_VALIDATE_TOKEN = "sso_validate_token";

    /**
     * 同步人员数据
     *
     * @param startDate  开始时间 yyyy-MM-dd HH:mm:ss
     * @param functionId 执行方法  query_all_emps
     * @param endDate    结束时间 yyyy-MM-dd HH:mm:ss
     * @return
     * @throws Exception
     */
    public static JSONArray syncEmp(String startDate, String functionId, String endDate) throws Exception {
        String methodName = "调用" + simpleName + ".syncEmp()";
        JSONArray dataList = new JSONArray();
        String url = "http://userability.portal.cmic:30080/cmic-portal-user-ability/sync/emp";
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> param = new HashMap<>();
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        param.put("isValid", null);
        param.put("userType", "all");
        String paramStr = objectMapper.writeValueAsString(param);
        Map<String, Object> header = new HashMap<>();
        header.put("appId", "140");
        header.put("appSecret", "x1HZisCbln38ftog1RgdjkriLSzCOgdX6Dlvr1Ww");
        header.put("functionId", functionId);
        header.put("source", "");
        header.put("Content-Type", "application/json; charset=utf-8");
        header.put("Connection", "Keep-Alive");
        header.put("Accept-Charset", "UTF-8");
        header.put("content-type", "application/json");
        String hrDataStr = HttpUtils.sendPost(url, paramStr, header);
        if (StringUtils.isValidJson(hrDataStr)) {
            JSONObject jsonObject = JSON.parseObject(hrDataStr);
            if (jsonObject.getInteger("code") == 0) {
                dataList = jsonObject.getJSONArray("employeeResult");
            } else {
                log.error("{} 请求cmic-portal-user-ability服务code非0，返回：{}", methodName, hrDataStr);
            }
        } else {
            log.error("{} 请求cmic-portal-user-ability服务返回非JSON格式数据，返回：{}", methodName, hrDataStr);
        }
        return dataList;
    }

    /**
     * 同步组织结构
     *
     * @param reqTimestamp 时间戳
     * @param functionId   调用方法 query_all_orgs
     * @param recordDate   记录日期
     * @return
     * @throws Exception
     */
    public static JSONArray syncOrg(String reqTimestamp, String functionId, String recordDate) throws Exception {
        JSONArray dataList = new JSONArray();
        String methodName = "调用" + simpleName + ".syncOrg()";
        String url = "http://userability.portal.cmic:30080/cmic-portal-user-ability/sync/org";
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> param = new HashMap<>();
        param.put("isOpen", null);
        param.put("orgType", "all");
        String paramStr = objectMapper.writeValueAsString(param);
        Map<String, Object> header = new HashMap<>();
        header.put("appId", "140");
        header.put("appSecret", "x1HZisCbln38ftog1RgdjkriLSzCOgdX6Dlvr1Ww");
        header.put("functionId", functionId);
        header.put("source", "");
        header.put("Content-Type", "application/json; charset=utf-8");
        header.put("Connection", "Keep-Alive");
        header.put("Accept-Charset", "UTF-8");
        header.put("content-type", "application/json");
        String hrDataStr = HttpUtils.sendPost(url, paramStr, header);
        if (StringUtils.isValidJson(hrDataStr)) {
            JSONObject jsonObject = JSON.parseObject(hrDataStr);
            if (jsonObject.getInteger("code") == 0) {
                dataList = jsonObject.getJSONArray("orgResult");
            } else {
                log.error("{} 请求cmic-portal-user-ability服务code非0，返回：{}", methodName, hrDataStr);
            }
        } else {
            log.error("{} 请求cmic-portal-user-ability服务返回非JSON格式数据，返回：{}", methodName, hrDataStr);
        }
        return dataList;
    }


    /**
     * HttpURLConnection方式发送查验操作(支持https)
     *
     * @param spec       路径
     * @param appId      应用凭证
     * @param appSecret  应用密钥
     * @param functionId 访问方法
     * @param param      参数
     * @return 返回结果
     */
    public static String httpSenPost(String spec, String appId, String appSecret, String functionId, String source, String param, String contentType) {
        log.info("[httpSenPost通用工具类-级别|通知] 请求地址:{},appId:{},appSecret:{},functionId:{},source:{},param:{},contentType:{}",
                spec, appId, appSecret, functionId, source, param, contentType);

        //	返回信息
        StringBuilder result = new StringBuilder();
        //	Connection对象
        HttpURLConnection conn = null;
        if (contentType == null || "".equals(contentType)) {
            contentType = "application/x-www-form-urlencoded; charset=utf-8";
        }
        try {
            //	将Map转换为json格式数据
            trustAllHttpsCertificates();
            HttpsURLConnection.setDefaultHostnameVerifier(new MyHostnameVerifier());
            URL url = new URL(spec);
            conn = (HttpURLConnection) url.openConnection();
            //	需要输出
            conn.setDoOutput(true);
            //	需要输入
            conn.setDoInput(true);
            //	不允许缓存
            conn.setUseCaches(false);
            //	设置POST方式连接
            conn.setRequestMethod("POST");
            //	设置连接主机超时(单位：毫秒)
            conn.setConnectTimeout(120 * 1000);
            //	设置从主机读取数据超时
            conn.setReadTimeout(120 * 1000);
            //	设置请求属性
            conn.setRequestProperty("Content-Type", contentType);
            //	维持长连接
            conn.setRequestProperty("Connection", "Keep-Alive");
            //	设置字符集
            conn.setRequestProperty("Accept-Charset", "UTF-8");
//			//	设置请求属性
//		    conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded; charset=utf-8");
//		    //	维持长连接
//		    conn.setRequestProperty("contentType", "UTF-8");
//		    //	设置接收类型
//		    conn.setRequestProperty("Accept", CONTENT_TYPE_NOT_CHARSET);
            //	设置应用凭证
            if (appId != null && !"".equals(appId)) {
                conn.setRequestProperty(APP_ID, appId);
            }
            //	设置应用密钥
            if (appSecret != null && !"".equals(appSecret)) {
                conn.setRequestProperty(APP_SECRET, appSecret);
            }
            //	设置调用方法
            if (functionId != null && !"".equals(functionId)) {
                conn.setRequestProperty("functionId", functionId);
            }
            //	判断验证客户端(1、PC端，2、移动端)
            if (source != null && ("1".equals(source) || "2".equals(source))) {
                conn.setRequestProperty("source", source);
            }
            //	连接,也可以不用明文connect，使用下面的httpConn.getOutputStream()会自动connect
            conn.connect();
            //	建立输入流，向指向的URL传入参数
            DataOutputStream dos = new DataOutputStream(conn.getOutputStream());
            dos.write(param.getBytes(StandardCharsets.UTF_8));
            //	清空输入流
            dos.flush();
            //	关闭输入流
            dos.close();
            //	获得响应状态
            int resultCode = conn.getResponseCode();
            //	判断是否正确响应
            if (HttpURLConnection.HTTP_OK == resultCode) {
                //	获取响应信息
                String readLine;
                BufferedReader responseReader = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8));
                while ((readLine = responseReader.readLine()) != null) {
                    result.append(readLine);
                }
                responseReader.close();
            } else {
                result = new StringBuilder("{\"code\":\"" + resultCode + "\",\"msg\":\"页面信息返回异常\"}");
            }
            log.info("[httpSenPost通用工具类-级别|通知] 请求结果 resultCode:{}, result:{}", resultCode, result);
        } catch (Exception e) {
            log.error("[httpSenPost通用工具类-级别|异常] 系统发生异常！", e);
            result = new StringBuilder("{\"code\":\"-1\",\"msg\":\"" + e.getMessage() + "\"}");
        } finally {
            //	切断连接
            conn.disconnect();
        }
        return result.toString();
    }

    /**
     * 设置新的SSL验证
     */
    public static void trustAllHttpsCertificates() throws Exception {
        TrustManager[] trustAllCerts = new TrustManager[1];
        TrustManager tm = new MyMiTM();
        trustAllCerts[0] = tm;
        SSLContext sc = SSLContext.getInstance("SSL");
        sc.init(null, trustAllCerts, null);
        HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
    }


    /** 调用接口方法 end **/

    /** 发送Http请求方法 begin **/

    /**
     * 使用HttpClient发送请求(只支持http)
     *
     * @param path
     * @param head
     * @return 结果集
     */
    public static String clientSendPost(String path, String head) throws IOException {
        log.info("[使用HttpClient发送请求-级别|通知] 通用工具类发送请求，path：" + path + "，head：" + head);
        byte[] requestBytes;
        String soapRequestInfo = head;
        requestBytes = soapRequestInfo.getBytes(StandardCharsets.UTF_8);
        HttpClient httpClient = new HttpClient();
        //设置超时时间  add by dengks on 20220624
        httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(120 * 1000);  //设置超时时长为2分钟
        httpClient.getHttpConnectionManager().getParams().setSoTimeout(120 * 1000);

        PostMethod postMethod = new PostMethod(path);


        InputStream inputStream = new ByteArrayInputStream(requestBytes, 0, requestBytes.length);
        RequestEntity requestEntity = new InputStreamRequestEntity(inputStream, requestBytes.length, "application/soap+xml; charset=utf-8");
        postMethod.setRequestEntity(requestEntity);
        httpClient.executeMethod(postMethod);
        InputStream soapResponseStream = postMethod.getResponseBodyAsStream();
        InputStreamReader inputStreamReader = new InputStreamReader(soapResponseStream, StandardCharsets.UTF_8);
        BufferedReader bufferedReader = new BufferedReader(inputStreamReader);

        String responseLine = "";
        String soapResponseInfo = "";
        while ((responseLine = bufferedReader.readLine()) != null) {
            soapResponseInfo = soapResponseInfo + responseLine;
        }
        log.info("[使用HttpClient发送请求-级别|通知] 通用工具类请求结果：" + soapResponseInfo);
        return soapResponseInfo;
    }


    /**
     * 创建请求头
     *
     * @param appId        应用程序账号
     * @param appSecret    应用程序密钥
     * @param reqTimestamp 时间戳(年月日时分秒毫秒)
     * @param functionId   执行方法名
     * @param param        请求参
     * @return 请求头参数
     */
    public static String createRequestHead(String appId, String appSecret, String reqTimestamp, String functionId, String param) {
        String head = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:ser=\"http://service.cmic.richinfo.com/\">";
        head += "<soapenv:Header><msghead>";
        head += "<appId>" + appId + "</appId>";
        head += "<appSecret>" + appSecret + "</appSecret>";
        head += "<reqTimestamp>" + reqTimestamp + "</reqTimestamp>";
        head += "<functionId>" + functionId + "</functionId>";
        head += "</msghead></soapenv:Header>";
        head += "<soapenv:Body>";
        if ("queryAllPortalUsersByNew".equals(functionId)) {    //	全量查询人员
            head += "<ser:queryAllPortalUsersByNew/>";
        } else if ("queryPortalUsersAfterDateByNew".equals(functionId)) {    //	增量查询人员
            head += "<ser:queryPortalUsersAfterDateByNew>";
            head += "<recordDate>" + param + "</recordDate>";
            head += "</ser:queryPortalUsersAfterDateByNew>";
        } else if ("queryAllOrgsForAll".equals(functionId)) {    //	全量查询组织机构
            head += "<ser:queryAllOrgsForAll/>";
        } else if ("queryOrgsAfterDateForAll".equals(functionId)) {    //	增量查询组织机构
            head += "<ser:queryOrgsAfterDateForAll>";
            head += "<recordDate>" + param + "</recordDate>";
            head += "</ser:queryOrgsAfterDateForAll>";
        } else if (SSO_CREATE_TOKEN.equals(functionId)) {    //	统一认证-生成凭证
            head += "<ser:createToken>";
            head += "<userId>" + param + "</userId>";
            head += "</ser:createToken>";
        } else if ("sso_refresh_token".equals(functionId)) {    //	统一认证-更新凭证
            head += "<ser:refreshToken>";
            head += "<userId>" + param + "</userId>";
            head += "</ser:refreshToken>";
        } else if (SSO_VALIDATE_TOKEN.equals(functionId)) {    //	统一认证-单点验证
            head += "<ser:validateToken>";
            head += "<accessToken>" + param + "</accessToken>";
            head += "</ser:validateToken>";
        } else if ("sso_validateTokenForPhone".equals(functionId)) {    //	统一认证-单点验证(返回手机号码)
            head += "<ser:validateTokenForPhone>";
            head += "<accessToken>" + param + "</accessToken>";
            head += "</ser:validateTokenForPhone>";
        } else {    //	其它
            return "";
        }
        head += "</soapenv:Body>";
        head += "</soapenv:Envelope>";
        return head;
    }


    /** 创建请求头方法 end */
    /** 解析返回参方法 begin */
    /**
     * 解析返回值
     *
     * @param sb 返回值字符串
     * @return 结果集
     */
    public static ReturnBean XMLReader(String sb) throws DocumentException {
        log.info("执行XMLReader方法：返回值：" + sb);
        ReturnBean returnBean = new ReturnBean();
        Document document = DocumentHelper.parseText(sb);
        Element node = document.getRootElement();
        return listNodes(node, returnBean);
    }

    /**
     * 解析返回值
     *
     * @param node       节点
     * @param returnBean 节点值
     * @return 结果集
     * @throws UnsupportedEncodingException
     */
    @SuppressWarnings("unchecked")
    public static ReturnBean listNodes(Element node, ReturnBean returnBean) {
        if (node.getName().equals("code")) {    //	接口调用成功或失败标识
            returnBean.setCode(StringUtils.parseToInt(node.getTextTrim(), -1));
        } else if (node.getName().equals("msg")) {    //	错误信息或登录凭证
            returnBean.setMsg(StringUtil.vString(node.getTextTrim(), ""));
        } else if (node.getName().equals("successNum")) {    //	记录数
            returnBean.setSuccessNum(StringUtils.parseToInt(node.getTextTrim(), 0));
        } else if (node.getName().equals("faultcode")) {    //	故障代码
            returnBean.setFaultcode(StringUtil.vString(node.getTextTrim(), ""));
        } else if (node.getName().equals("faultstring")) {    //	故障内容
            returnBean.setFaultstring(StringUtil.vString(node.getTextTrim(), ""));
        } else if (node.getName().equals("employeeList") || node.getName().equals("organizationList")) {
            Map<String, String> map = new HashMap<String, String>();
            // 当前节点下面子节点迭代器
            Iterator<Element> it = node.elementIterator();
            // 遍历
            while (it.hasNext()) {
                // 获取某个子节点对象
                Element e = it.next();
                // 获取当前节点的所有属性节点
                map.put(StringUtil.vString(e.getName(), ""), StringUtil.vString(e.getTextTrim(), ""));
            }
            returnBean.getResultList().add(map);
        } else {
            // 当前节点下面子节点迭代器
            Iterator<Element> it = node.elementIterator();
            // 遍历
            while (it.hasNext()) {
                // 获取某个子节点对象
                Element e = it.next();
                // 对子节点进行遍历
                listNodes(e, returnBean);
            }
        }
        return returnBean;
    }


}
