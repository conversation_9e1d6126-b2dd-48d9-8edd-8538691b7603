package com.weaver.seconddev.zyhlw.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ConvertDataUtils {

    /***
     * 将list<map>中map的key转为大写
     * 
     * @param list
     * @return
     */
    public static List<Map<String, Object>> convertListMapKeyToUpperCase(List<Map<String, Object>> list) {
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> result = new ArrayList<>();
        for (Map<String, Object> map : list) {
            result.add(convertMapKeyToUpperCase(map));
        }
        return result;
    }

    /***
     * 将map中key转为大写
     * 
     * @param map
     * @return
     */
    public static Map<String, Object> convertMapKeyToUpperCase(Map<String, Object> map) {
        if (map == null || map.isEmpty()) {
            return new HashMap<>();
        }
        Map<String, Object> result = new HashMap<>();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            result.put(entry.getKey().toUpperCase(), entry.getValue());
        }
        return result;
    }

    /***
     * 将list<map>中map的key转为小写
     */
    public static List<Map<String, Object>> convertListMapKeyToLowerCase(List<Map<String, Object>> list) {
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> result = new ArrayList<>();
        for (Map<String, Object> map : list) {
            result.add(convertMapKeyToLowerCase(map));
        }
        return result;
    }

    /***
     * 将map中key转为小写
     */
    public static Map<String, Object> convertMapKeyToLowerCase(Map<String, Object> map) {
        if (map == null || map.isEmpty()) {
            return new HashMap<>();
        }
        Map<String, Object> result = new HashMap<>();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            result.put(entry.getKey().toLowerCase(), entry.getValue());
        }
        return result;
    }
}
