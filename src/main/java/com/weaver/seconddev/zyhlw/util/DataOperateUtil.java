package com.weaver.seconddev.zyhlw.util;

import com.weaver.seconddev.zyhlw.domain.formdata.ConditionDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

import static com.weaver.seconddev.zyhlw.util.constant.FormSqlQueryType.*;

@Slf4j
public class DataOperateUtil {

    // 用于验证字段名的正则表达式，只允许字母、数字、下划线和点号
    private static final Pattern FIELD_NAME_PATTERN = Pattern.compile("^[a-zA-Z0-9_.]+$");

    /**
     * 根据查询条件拼接sql，使用参数化查询防止SQL注入
     *
     * @param tablename   表单名称
     * @param conditions  查询条件 ，格式：List<ConditionDTO>
     * @param queryFields 查询字段
     * @param tenantKey   租户key
     * @return 包含SQL语句和参数的对象
     * conditions - 查询条件 - 属性： fieldname（字段名）、value（字段值）、queryType（查询类型）
     */
    public static SqlQueryResult querySqlJoin(String tablename, List<ConditionDTO> conditions,
                                              String queryFields, String tenantKey) {
        if (queryFields == null || queryFields.isEmpty()) {
            queryFields = "*";
        }
        
        // 验证表名和字段名，防止SQL注入
        if (isValidFieldName(tablename)) {
            log.error("Invalid table name: {}", tablename);
            throw new IllegalArgumentException("Invalid table name");
        }
        
        if (!queryFields.equals("*")) {
            for (String field : queryFields.split(",")) {
                String trimmedField = field.trim();
                if (isValidFieldName(trimmedField)) {
                    log.error("Invalid query field: {}", trimmedField);
                    throw new IllegalArgumentException("Invalid query field");
                }
            }
        }
        
        String querySql = "select " + queryFields + " from " + tablename + " where tenant_key = ? and delete_type=0 ";
        StringBuilder querySqlBuilder = new StringBuilder(querySql);
        List<String> params = new ArrayList<>();
        params.add(tenantKey); // tenant_key 参数占位，实际执行时会被替换
        
        handleSql(conditions, querySqlBuilder, params);

        StringBuilder sqlOrder = new StringBuilder();
        for (ConditionDTO condition : conditions) {
            String fieldname = condition.getFieldname();
            String queryType = StringUtils.null2String(condition.getQueryType());
            
            // 验证字段名
            if (isValidFieldName(fieldname)) {
                log.error("Invalid field name in order clause: {}", fieldname);
                throw new IllegalArgumentException("Invalid field name in order clause");
            }
            
            switch (queryType) {
                case QUERY_SQL_ORDER_ASC:
                    if (sqlOrder.toString().isEmpty()) {
                        sqlOrder = new StringBuilder(" order by " + fieldname + " asc");
                    } else {
                        sqlOrder.append(", ").append(fieldname).append(" asc");
                    }
                    break;
                case QUERY_SQL_ORDER_DESC:
                    if (sqlOrder.toString().isEmpty()) {
                        sqlOrder = new StringBuilder(" order by " + fieldname + " desc");
                    } else {
                        sqlOrder.append(", ").append(fieldname).append(" desc");
                    }
                    break;
            }
        }
        querySqlBuilder.append(sqlOrder);
        
        return new SqlQueryResult(querySqlBuilder.toString(), params);
    }

    /**
     * 处理SQL条件部分，使用参数化查询防止SQL注入
     */
    private static void handleSql(List<ConditionDTO> conditions, StringBuilder querySqlBuilder, List<String> params) {
        for (ConditionDTO condition : conditions) {
            String fieldname = condition.getFieldname();
            String value = StringUtils.null2String(condition.getValue());
            String queryType = StringUtils.null2String(condition.getQueryType());
            
            // 验证字段名
            if (isValidFieldName(fieldname)) {
                log.error("Invalid field name: {}", fieldname);
                throw new IllegalArgumentException("Invalid field name");
            }
            
            switch (queryType) {
                case QUERY_SQL_LIKE:
                    querySqlBuilder.append(" ").append(QUERY_SQL_AND).append(" ").append(fieldname).append(" like ?");
                    params.add("%" + value + "%");
                    break;
                case QUERY_SQL_IN:
                    String[] values = value.split(",");
                    if (values.length > 0) {
                        querySqlBuilder.append(" ").append(QUERY_SQL_AND).append(" ").append(fieldname).append(" in (");
                        for (int i = 0; i < values.length; i++) {
                            if (i > 0) {
                                querySqlBuilder.append(",");
                            }
                            querySqlBuilder.append("?");
                            params.add(values[i]);
                        }
                        querySqlBuilder.append(")");
                    }
                    break;
                case QUERY_SQL_DATE_RANGE:
                    String startValue = StringUtils.null2String(condition.getStartValue());
                    String endValue = StringUtils.null2String(condition.getEndValue());
                    if (!startValue.isEmpty()) {
                        querySqlBuilder.append(" ").append(QUERY_SQL_AND).append(" ").append(fieldname).append(">=?");
                        params.add(startValue);
                    }
                    if (!endValue.isEmpty()) {
                        querySqlBuilder.append(" ").append(QUERY_SQL_AND).append(" ").append(fieldname).append("<=?");
                        params.add(endValue);
                    }
                    break;
                case QUERY_SQL_NOT:
                    querySqlBuilder.append(" ").append(QUERY_SQL_AND).append(" ").append(fieldname).append(" <> ?");
                    params.add(value);
                    break;
                case QUERY_SQL_NOT_AND_NULL:
                    querySqlBuilder.append(" ").append(QUERY_SQL_AND).append(" (").append(fieldname).append(" <> ? ")
                            .append(QUERY_SQL_OR).append(" ").append(fieldname).append(" is null)");
                    params.add(value);
                    break;
                case QUERY_SQL_IS_NULL:
                    if (!value.isEmpty()) {
                        querySqlBuilder.append(" ").append(QUERY_SQL_AND).append(" (").append(fieldname).append(" = ? ")
                                .append(QUERY_SQL_OR).append(" ").append(fieldname).append(" is null)");
                        params.add(value);
                    } else {
                        querySqlBuilder.append(" ").append(QUERY_SQL_AND).append(" ").append(fieldname).append(" is null ");
                    }
                    break;
                case QUERY_SQL_IS_NOT_NULL:
                    if (!value.isEmpty()) {
                        querySqlBuilder.append(" ").append(QUERY_SQL_AND).append(" (").append(fieldname).append(" = ? ")
                                .append(QUERY_SQL_OR).append(" ").append(fieldname).append(" is not null)");
                        params.add(value);
                    } else {
                        querySqlBuilder.append(" ").append(QUERY_SQL_AND).append(" ").append(fieldname).append(" is not null ");
                    }
                    break;
                default:
                    if (!value.isEmpty()) {
                        querySqlBuilder.append(" ").append(QUERY_SQL_AND).append(" ").append(fieldname).append("=?");
                        params.add(value);
                    }
            }
        }
    }
    
    /**
     * 验证字段名是否合法，防止SQL注入
     * 
     * @param fieldName 字段名
     * @return 是否合法
     */
    private static boolean isValidFieldName(String fieldName) {
        return fieldName == null || !FIELD_NAME_PATTERN.matcher(fieldName).matches();
    }
    
    /**
     * SQL查询结果类，包含SQL语句和参数
     */
    @Data
    public static class SqlQueryResult {
        private final String sql;
        private final List<String> params;
        
        public SqlQueryResult(String sql, List<String> params) {
            this.sql = sql;
            this.params = params;
        }
    }
}
