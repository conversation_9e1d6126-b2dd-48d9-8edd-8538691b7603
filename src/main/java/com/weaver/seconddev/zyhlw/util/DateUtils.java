package com.weaver.seconddev.zyhlw.util;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024-07-01 17:58
 */
public class DateUtils {
    private DateUtils() {

    }

    public static final String yyyy_MM_dd_HH_mm_ss = "yyyy-MM-dd HH:mm:ss";
    public static final String yyyy_MM_dd_HH_mm = "yyyy-MM-dd HH:mm";

    public static final String yyyy_MM_dd = "yyyy-MM-dd";

    public static final String yyyyMMddHHmmssSSS = "yyyyMMddHHmmssSSS";

    public static final String yyyyMMddHHmmss = "yyyyMMddHHmmss";

    /**
     * 获取当前日期时间，并转换为yyyyMMddHHmmssSSS格式的字符串
     */
    public static String getCurrentTimestamp() {
        Date currentDate = new Date();
        return new SimpleDateFormat(yyyyMMddHHmmssSSS).format(currentDate);
    }

    /**
     * 获取当前时间
     **/
    public static LocalDateTime getCurrentTime() {
        return LocalDateTime.now();
    }

    /**
     * 格式化时间为字符串
     **/
    public static String formatTime(LocalDateTime dateTime, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return dateTime.format(formatter);
    }

    /**
     * 解析字符串为 LocalDateTime
     **/
    public static LocalDateTime parseString(String dateTimeStr, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return LocalDateTime.parse(dateTimeStr, formatter);
    }

    /**
     * 计算两个 LocalDateTime 之间的时间差
     **/
    public static long calculateTimeDifference(LocalDateTime startDateTime, LocalDateTime endDateTime, ChronoUnit unit) {
        return unit.between(startDateTime, endDateTime);
    }

    /**
     * 将 LocalDateTime 转换为指定时区的时间
     **/
    public static ZonedDateTime convertToTimeZone(LocalDateTime dateTime, ZoneId zoneId) {
        return dateTime.atZone(zoneId);
    }
}
