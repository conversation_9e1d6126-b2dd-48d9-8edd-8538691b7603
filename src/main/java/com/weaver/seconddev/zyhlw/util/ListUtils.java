package com.weaver.seconddev.zyhlw.util;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description list工具类
 * @Date 2024-07-18 10:07
 */
public class ListUtils {

    private ListUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 取list1 list2差集
     *
     * <AUTHOR>
     * @Date 10:07 2024/7/18
     * @Param [list1, list2]
     * @return java.util.List<java.lang.String>
     **/
    public static List<String> getDifference(List<String> list1, List<String> list2) {
        Set<String> set2 = new HashSet<>(list2);

        // 使用并行流来加速过滤操作
        return list1.parallelStream()
                .filter(item -> !set2.contains(item))
                .collect(Collectors.toList());
    }
}
