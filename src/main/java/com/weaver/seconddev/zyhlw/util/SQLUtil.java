package com.weaver.seconddev.zyhlw.util;

import java.math.BigDecimal;
import java.util.Map;

public class SQLUtil {
    /**
     * SQL语句拼接
     * 通过字段列表拼接
     *
     * @param colMap    字段列表
     * @param tableName 表名称
     * @return sql
     */
    public static String productInsertSQL(Map<String, String> colMap, String tableName) {
        StringBuilder sql = new StringBuilder();
        sql.append("insert into ").append(tableName);

        StringBuilder keys = new StringBuilder("(");
        StringBuilder values = new StringBuilder("(");

        for (Map.Entry<String, String> col : colMap.entrySet()) {
            keys.append(col.getKey()).append(",");
            values.append("'").append(col.getValue()).append("'").append(",");
        }

        keys.replace(keys.length() - 1, keys.length(), ")");
        values.replace(values.length() - 1, values.length(), ")");

        sql.append(keys).append("values").append(values);
        return sql.toString();
    }

    /**
     * 将字符串转换成BigDecimal类型
     * 如果是空字符串返回 0
     *
     * @param s 字符串
     * @return 成功返回BigDecimal值
     */
    public static BigDecimal parseBigDecimal(String s) {
        try {
            if (null == s || "".equals(s)) {
                return BigDecimal.valueOf(0);
            } else {
                return new BigDecimal(s);
            }
        } catch (Exception e) {
            return BigDecimal.valueOf(0);
        }
    }

    /**
     * 封装sql语句in语法字符串入参
     * 封装前：a,b,c
     * 封装后：'a','b','c'
     *
     * <AUTHOR>
     * @Date 17:21 2023/11/21
     * @Param [values]
     * @return java.lang.String
     **/
    public static String InCondition(String values) {
        String[] arr = values.split(",");
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < arr.length; i++) {
            String idValue=arr[i].trim();
            if(idValue.equals("")==false) {
                sb.append("'").append(idValue).append("'");
                if (i != arr.length - 1) {
                    sb.append(",");
                }
            }
        }
        return sb.toString();
    }
}
