package com.weaver.seconddev.zyhlw.util;

import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.seconddev.zyhlw.domain.response.form.EbFormDataResultVo;
import com.weaver.seconddev.zyhlw.util.ebuilder.EBuilderUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工位短信工具类
 *
 * <AUTHOR>
 * @date 2025年05月20日 16:30
 */
@Slf4j
public class SmsGongUtil {
    /**
     * 短信表名
     */
    public static final String SMS_TABLE_NAME = "uf_gong_sms";

    /**
     * 是否发送 - 未发送
     */
    public static final String SMS_IS_SEND_NO = "0";

    /**
     * 发送类型 - 短信
     */
    public static final String SMS_SEND_TYPE_SMS = "1";

    /**
     * 默认安全级别
     */
    public static final String DEFAULT_SEC_LEVEL = "10";

    /**
     * 日期格式化器
     */
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    /**
     * 时间格式化器
     */
    public static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    /**
     * 日期时间格式化器
     */
    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    /**
     * 保存短信数据
     *
     * @param openPlatformUrl 开放平台URL
     * @param userId          用户ID
     * @param smsTableObjId   短信表单对象ID
     * @param saveDataList    保存数据列表
     * @param accessToken     访问令牌
     * @return 保存结果
     */
    public static WeaResult<Map<String, Object>> saveSmsData(
            String openPlatformUrl, Long userId, String smsTableObjId,
            List<Map<String, Object>> saveDataList, String accessToken) {

        Map<String, Object> result = new HashMap<>();

        if (CollectionUtils.isEmpty(saveDataList)) {
            log.warn("保存短信数据列表为空");
            result.put("code", 0);
            result.put("msg", "没有需要发送的短信数据");
            return WeaResult.success(result);
        }

        try {
            // 保存短信数据
            EbFormDataResultVo saveResult = EBuilderUtil.saveFormDataV2(
                    openPlatformUrl,
                    userId,
                    smsTableObjId,
                    saveDataList,
                    accessToken);

            // 检查保存结果
            if (saveResult == null || saveResult.getData() == null ||
                    saveResult.getData().getDataJson() == null ||
                    saveResult.getData().getDataJson().isEmpty() ||
                    !saveResult.getData().getDataJson().get(0).getStatus()) {
                String errorMsg = saveResult != null && saveResult.getData() != null &&
                        saveResult.getData().getDataJson() != null &&
                        !saveResult.getData().getDataJson().isEmpty() ?
                        saveResult.getData().getDataJson().get(0).getMessage() : "保存短信数据失败";
                return WeaResult.fail(errorMsg);
            }

            log.info("保存短信数据成功");
            result.put("code", 0);
            result.put("msg", "发送成功");
            return WeaResult.success(result);
        } catch (Exception e) {
            log.error("保存短信数据失败", e);
            return WeaResult.fail("发送失败: " + e.getMessage());
        }
    }
}
