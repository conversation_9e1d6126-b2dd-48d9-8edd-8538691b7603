package com.weaver.seconddev.zyhlw.util;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.weaver.seconddev.zyhlw.domain.request.todo.TodoAuditRequest;
import com.weaver.seconddev.zyhlw.domain.request.todo.TodoItemQuickRequest;
import com.weaver.seconddev.zyhlw.domain.request.todo.TodoItemRequest;
import com.weaver.seconddev.zyhlw.domain.response.todo.TodoAuditResponse;
import com.weaver.seconddev.zyhlw.domain.response.todo.TodoItemQuickResponse;
import com.weaver.seconddev.zyhlw.domain.response.todo.TodoItemResponse;
import com.weaver.seconddev.zyhlw.util.http.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class TodoUtil {
    private static final String SIMPLENAME=TodoUtil.class.getSimpleName();
    /**
     * 查询待办V1
     * @param url   地址，可通过获取：CmicProperties.getWaitItem1QueryListUrl()
     * @param portalUserId 用户名
     * @param startTimeStr 开始日期
     * @param endTimeStr 结束日期
     * @param level  未知参数
     * @param isRefresh 是否刷新
     * @param title 标题
     * @param type 类型
     * @param sysName 系统名称
     * @param pageIndex 当前页
     * @param pageSize  每页大小
     * @return
     */
    public static List<Map<String,Object>> getTodoDataV1(String url,String portalUserId, String startTimeStr, String endTimeStr, int level, int isRefresh, String title, String type, String sysName, int pageIndex, int pageSize){
        String methodStr="调用"+SIMPLENAME+".getTodoDataV1()";
        String param="portalUserId="+portalUserId+"&level="+level+"&isRefresh="+(isRefresh <= 1?0:isRefresh)+"&pageIndex="+pageIndex+"&pageSize="+pageSize;
        if (startTimeStr !=null && !"".equals(startTimeStr)) {
            param += "&startTimeStr="+startTimeStr;
        }
        if (endTimeStr != null && !"".equals(endTimeStr)) {
            param += "&endTimeStr="+endTimeStr;
        }
        if (title != null && !"".equals(title)) {
            param += "&title="+title;
        }
        if (type != null && !"".equals(type)) {
            param += "&type="+type;
        }
        if (sysName != null && !"".equals(sysName)) {
            param += "&sysName="+sysName;
        }
        String jsonStr = HttpUtils.sendPost(url, param);
        List<Map<String,Object>> dataList = new ArrayList<>();
        if (StringUtils.isValidJson(jsonStr)) {
            JSONObject jsonObject = JSON.parseObject(jsonStr);
            String pageModelkey="pageModel";
            if (jsonObject.containsKey(pageModelkey)) {
                JSONObject pageModel = jsonObject.getJSONObject(pageModelkey);
                String itemsKey="items";
                if (pageModel.containsKey(itemsKey)) {
                    JSONArray items = pageModel.getJSONArray(itemsKey);
                    for (int i = 0; i < items.size(); i++) {
                        JSONObject item = items.getJSONObject(i);
                        dataList.add(item);
                    }
                }else{
                    log.warn("{} 请求待办服务返回缺少｛｝数据，返回｛｝",methodStr,itemsKey,jsonStr);
                }
            }else {
                log.warn("{} 请求待办服务返回缺少｛｝数据，返回｛｝",methodStr,pageModelkey,jsonStr);
            }
        }else {
            log.error("{} 请求待办服务返回非JSON格式数据，返回---{}",methodStr,jsonStr);
        }
        return dataList;
    }
    /**
     * 查询待办V2（无分页）
     * @param portalUserId 用户
     * @param itemType  用户系统编码类型，1、待办，2、待阅，3、已办，4、已阅，5、我的创建、6、草稿
     * @param systemCode  系统编码,非必填，输入编码值后只查询单个系统
     * @param resultType  查询结果itemList的集合类型，默认item，todo 对应SystemTodo
     * @param title 标题
     * @param url 地址，可通过获取：CmicProperties.getWaitItem2QueryListUrl()
     * @return
     */
    public static List<Map<String,Object>> getTodoDataV2(String portalUserId,Integer itemType,String systemCode,String resultType,String title,String url){
        String param ="portalUserId="+portalUserId+"&itemType="+itemType+"&resultType"+resultType;
        if (systemCode != null && !"".equals(systemCode)) {
            param = param+"&systemCode="+systemCode;
        }
        if (title != null && !"".equals(title)) {
            param = param+"&title="+title;
        }
        List<Map<String,Object>> dataList = new ArrayList<>();
        String json = HttpUtils.sendGet(url, param);
        if (StringUtils.isValidJson(json)) {
            JSONObject jsonObject = JSON.parseObject(json);
            String codeKey="code";
            if (jsonObject.getInteger(codeKey)==0) {
                JSONArray itemList = jsonObject.getJSONArray("itemList");
                for (int i = 0; i < itemList.size(); i++) {
                    JSONObject object = itemList.getJSONObject(i);
                    dataList.add(object);
                }
            }else {
                log.error("调用TodoUtil.getAllTodo 请求待办服务code非0，返回---"+json);
            }
        }else{
            log.error("调用TodoUtil.getAllTodo 请求待办服务返回非JSON格式数据，返回---"+json);
        }
        return  dataList;
    }

    /**
     * 查询各业务系统的待办数量--旧
     * @param portalUserId
     * @param url 地址 可通过获取：CmicProperties.getGetBenchDaiCountUrl()
     * @return
     * @throws Exception
     */
    public static Map<String,Object> getTodoTotalV1(String portalUserId,String url)throws Exception{
//        String url = "http://wait.portal.cmic:30080/cmic_portal_wait/allaccount/getBenchDaiCount.do";
        String param = "portalUserId="+portalUserId;
        Map<String,Object> dataMap = new HashMap<>(16);
        String json = HttpUtils.sendGet(url, param);
        if (StringUtils.isValidJson(json)) {
            JSONObject jsonObject = JSON.parseObject(json);
            dataMap = jsonObject;
        }else{
            log.error("调用TodoUtil.getTodoTotalV1 请求待办服务返回非JSON格式数据，返回：{}",json);
        }
        return dataMap;
    }

    /**
     * 查询各业务系统的待办数量--新
     * @param portalUserId 用户名
     * @param itemType  用户类型，1、待办，2、待阅，3、已办，4、已阅，5、我的创建、6、草稿
     * @param systemCode  系统编码,非必填，输入编码值后只查询单个系统
     * @param title 标题
     * @param url 地址 可通过获取：CmicProperties.getWaitItem2QueryAlltotal()
     * @return
     */
    public static Map<String,Object> getTodoTotalV2(String portalUserId,Integer itemType,String systemCode,String title,String url)throws Exception{
//        String url =  "http://wait.portal.cmic:30080/cmic_portal_wait/item2/query/alltotal";
        String param = "portalUserId="+portalUserId+"&itemType="+itemType;
        if (systemCode != null && !"".equals(systemCode)) {
            param = param+"&systemCode="+systemCode;
        }
        if (title != null && !"".equals(title)) {
            param = param+"&title="+title;
        }
        Map<String,Object> dataMap = new HashMap<>(16);
        String json =   HttpUtils.sendGet(url, param);
        if (StringUtils.isValidJson(json)) {
            JSONObject jsonObject = JSON.parseObject(json);
            String codeKey="code";
            if (jsonObject.getInteger(codeKey)==0) {
                dataMap = jsonObject.getJSONObject("systemTotal");
            }else{
                log.error("调用TodoUtil.getTodoTotalV2 请求待办服务code非0，返回：{}",json);
            }
        }else{
            log.error("调用TodoUtil.getTodoTotalV2 请求待办服务返回非JSON格式数据，返回：{}",json);
        }
        return dataMap;
    }
    /**
     * 置灰接口--旧
     * @param portalUserId 用户
     * @param todoId  待办唯一标识
     * @param todoType 业务系统名称
     * @param url 地址 可通过获取：CmicProperties.getWaitTodoSetGray()
     * @return
     */
    public static Map<String,Object> setGrayV1(String portalUserId,String todoId,String todoType,String url)throws Exception{

        String param = "portalUserId="+portalUserId+"&todoId="+todoId+"&todoType="+todoType;
        String json = HttpUtils.sendPost(url, param);
        Map<String, Object> dataMap = new HashMap<>(16);
        if (StringUtils.isValidJson(json)) {
            JSONObject jsonObject = JSON.parseObject(json);
            String codekey="code";
            if (jsonObject.getInteger(codekey)==0) {

            }else{
                log.error("调用TodoUtil.setGrayV1 请求待办服务code非0，返回：{}",json);
            }
            dataMap = jsonObject;
        }else{
            log.error("调用TodoUtil.setGrayV1 请求待办服务返回非JSON格式数据，返回：{}",json);
        }
        return dataMap;
    }
    /**
     * 置灰接口--新
     * @param portalUserId 用户
     * @param uniqueId 唯一标识
     * @param gray  设置1或Y
     * @param url 地址 可通过获取：CmicProperties.getWaitItem2ChangeSetGray()
     * @return
     */
    public static Map<String,Object> setGrayV2(String portalUserId, String uniqueId, String gray,String url)throws Exception{
        JSONObject paramJson = new JSONObject();
        paramJson.put("portalUserId",portalUserId);
        paramJson.put("uniqueId",uniqueId);
        paramJson.put("gray",gray);
        Map<String,Object> header = new HashMap<>(16);
        header.put("Content-Type","application/json");
        header.put("Accept","application/json");
        Map<String,Object> dataMap = new HashMap<>(16);
        String json = HttpUtils.sendPost(url,paramJson.toString(),header);
        if (StringUtils.isValidJson(json)) {
            JSONObject jsonObject = JSON.parseObject(json);
            String codeKey="code";
            if (jsonObject.getInteger(codeKey)==0) {

            }else{
                log.error("调用TodoUtil.setGrayV2 请求待办服务code非0，返回：{}",json);
            }
            dataMap = jsonObject;
        }else{
            log.error("调用TodoUtil.setGrayV2 请求待办服务返回非JSON格式数据，返回：{}",json);
        }
        return dataMap;
    }

    /**
     * 集团待办
     * @param portalUserId
     * @param receiverUid
     * @param itemType
     * @param companyEmail
     * @param pageIndex
     * @param pageSize
     * @param url 地址，可通过获取：CmicProperties.getWaitTdunifyGetTdUnifyList()
     * @param title  标题
     * @return
     */
    public static Map<String,Object> getTdUnifyList(String portalUserId, String receiverUid, String itemType, String companyEmail, String pageIndex, String pageSize,String url,String title)throws Exception{

        String paramstr = "";
        if (receiverUid == null || "".equals(receiverUid)) {
            receiverUid = "1234567";
        }

        if (companyEmail == null || "".equals(companyEmail)) {
            companyEmail = portalUserId + "@cmic.cmcc";
        }
        paramstr += "portalUserId=" + portalUserId;
        paramstr += "&receiverUid=" + receiverUid;
        paramstr += "&itemType=" + itemType;
        paramstr += "&companyEmail=" + companyEmail;
        paramstr += "&pageIndex=" + pageIndex;
        paramstr += "&pageSize=" + pageSize;
        if (title != null || !"".equals(title)) {
            paramstr += "&title="+ title;
        }
        Map<String,Object> dataMap = new HashMap<>(16);
        String json =   HttpUtils.sendGet(url, paramstr);
        if (StringUtils.isValidJson(json)) {
            JSONObject jsonObject = JSON.parseObject(json);
            String codeKey="code";
            if (jsonObject.getInteger(codeKey)==0) {
                dataMap = jsonObject;
            }else{
                log.error("调用TodoUtil.getTdUnifyList 请求待办服务code非0，返回：{}",json);
            }
        }else{
            log.error("调用TodoUtil.getTdUnifyList 请求待办服务返回非JSON格式数据，返回：{}",json);
        }
        return dataMap;
    }
    /**
     *  更新待办缓存接口
     * @param url   地址，可通过获取：CmicProperties.getStartGetSysDataUrl()
     * @param portalUserId 用户
     */
    public static void startGetSysData(String url,String portalUserId)throws Exception{
//        String url = "http://wait.portal.cmic:30080/cmic_portal_wait/allaccount/startGetSysData.do";
        String param = "portalUserId="+portalUserId;
        String json = HttpUtils.sendGet(url, param);
        log.info("调用TodoUtil.startGetSysData  请求待办服务接口返回：{}",json);
    }

    /**
     * 根据置灰状态拼接html字符串
     * @return
     */
    public static String getHtmlStrByGray(String gray,String val){
        if (val == null || "".equals(val)) {
            return val;
        }
        StringBuilder stringBuilder =new StringBuilder();
        stringBuilder.append("<div");
        String yKey="Y";
        if (yKey.equals(gray.toUpperCase())) {
            stringBuilder.append(" class='is_gray_y'");
        }
        stringBuilder.append(">");
        stringBuilder.append(val);
        stringBuilder.append("</div>");
        return  stringBuilder.toString();
    }

    /**
     * 流程-待办数据推送集团
     *
     * <AUTHOR>
     * @Date 16:11 2024/7/1
     * @param  url 请求路径 http://wait.portal.cmic:30080/cmic_portal_wait/item/data
     * @param todoItemRequest 更新数据
     * @param token apptoken
     * @return TodoItemResponse
     **/
    public static TodoItemResponse syncSysDataTodo(String url, TodoItemRequest todoItemRequest, String token) {
        Map<String, Object> header = new HashMap<>(16);
        header.put("Content-Type", "application/json");
        header.put("Accept", "application/json");
        header.put("Authorization", "Bearer " + token);
        TodoItemResponse todoItemResponse = new TodoItemResponse();
        try {
            String json = HttpUtils.sendPost(url, JSON.toJSONString(todoItemRequest), header);
            todoItemResponse = JSON.parseObject(json, TodoItemResponse.class);
            log.info("调用TodoUtil.syncSysDataTodo成功：{}", json);
        } catch (Exception e) {
            log.info("调用TodoUtil.syncSysDataTodo失败，e：{}", e.getMessage());
            todoItemResponse.setCode(500);
            todoItemResponse.setMsg(e.getMessage());
        }
        return todoItemResponse;
    }

    /**
     * 流程-待办数据推送集团(批量)
     *
     * <AUTHOR>
     * @Date 16:11 2024/7/1
     * @param  url 请求路径 http://wait.portal.cmic:30080/cmic_portal_wait/item/datalist
     * @param todoItemRequestList 更新数据
     * @return TodoItemResponse
     **/
    public static TodoItemResponse syncSysDataTodoBatch(String url, List<TodoItemRequest> todoItemRequestList, String token) {
        // 分页推送待办数据
        Map<String, Object> header = new HashMap<>(16);
        header.put("Content-Type", "application/json");
        header.put("Accept", "application/json");
        header.put("Authorization", "Bearer " + token);
        JSONObject paramJson = new JSONObject();
        paramJson.put("itemSize", todoItemRequestList.size());
        paramJson.put("itemList", todoItemRequestList);
        TodoItemResponse todoItemResponse = new TodoItemResponse();
        try {
            String json = HttpUtils.sendPost(url, paramJson.toString(), header);
            todoItemResponse = JSON.parseObject(json, TodoItemResponse.class);
            log.info("调用TodoUtil.syncSysDataTodoBatch成功：{}", json);
        } catch (Exception e) {
            log.info("调用TodoUtil.syncSysDataTodoBatch失败，e：{}", e.getMessage());
            todoItemResponse.setCode(500);
            todoItemResponse.setMsg(e.getMessage());
        }
        return todoItemResponse;
    }

    /**
     * 流程-待办数据推送集团，便捷变更，如下两种情况使用
     * （1）当事项的类型ItemType需要变更，但其它内容不做变更。
     * （2）当事项需要撤回或者删除时
     *
     * <AUTHOR>
     * @Date 16:11 2024/7/1
     * @param  url 请求路径 http://wait.portal.cmic:30080/cmic_portal_wait/item/quick
     * @param todoItemQuickRequest 更新数据
     * @return TodoItemResponse
     **/
    public static TodoItemQuickResponse syncSysDataTodoQuick(String url, TodoItemQuickRequest todoItemQuickRequest, String token) throws Exception {
        Map<String, Object> header = new HashMap<>(16);
        header.put("Content-Type", "application/json");
        header.put("Accept", "application/json");
        header.put("Authorization", "Bearer " + token);
        TodoItemQuickResponse todoItemQuickResponse = new TodoItemQuickResponse();
        try {
            String json = HttpUtils.sendPost(url, JSON.toJSONString(todoItemQuickRequest), header);
            todoItemQuickResponse = JSON.parseObject(json, TodoItemQuickResponse.class);
            log.info("调用TodoUtil.syncSysDataTodoQuick成功：{}", json);
        } catch (Exception e) {
            log.info("调用TodoUtil.syncSysDataTodoQuick失败，e：{}", e.getMessage());
            todoItemQuickResponse.setCode(500);
            todoItemQuickResponse.setMsg(e.getMessage());
        }
        return todoItemQuickResponse;
    }

    /**
     * 统一待办 业务方稽核接口
     *
     * <AUTHOR>
     * @Date 16:11 2024/7/1
     * @param  url 请求路径 http://wait.portal.cmic:30080/cmic_portal_wait/audit/app
     * @param todoAuditRequest 更新数据
     * @return TodoItemResponse
     **/
    public static TodoAuditResponse auditSysDataTodo(String url, TodoAuditRequest todoAuditRequest, String token) {
        Map<String, Object> header = new HashMap<>(2);
        header.put("Content-Type", "application/json");
        header.put("Accept", "application/json");
        header.put("Authorization", "Bearer " + token);
        TodoAuditResponse todoAuditResponse = new TodoAuditResponse();
        try {
            String json = HttpUtils.sendPost(url, JSON.toJSONString(todoAuditRequest), header);
            todoAuditResponse = JSON.parseObject(json, TodoAuditResponse.class);
            log.info("调用TodoUtil.auditSysDataTodo成功：{}", json);
        } catch (Exception e) {
            log.info("调用TodoUtil.auditSysDataTodo失败，e：{}", e.getMessage());
            todoAuditResponse.setCode(500);
            todoAuditResponse.setMessage(e.getMessage());
        }
        return todoAuditResponse;
    }
}
