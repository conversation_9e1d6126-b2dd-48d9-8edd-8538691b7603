package com.weaver.seconddev.zyhlw.util.cache;

import com.weaver.common.cache.base.BaseCache;
import com.weaver.common.cache.base.ModuleCacheInterface;
import com.weaver.common.cache.base.exception.IllegalCacheAccessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Component
public class CacheKey implements ModuleCacheInterface {
    @Resource
    private BaseCache baseCache;
    //要求变量名和变量值必须完全保持一致，注册时会执行检测，如果不一致，系统启动时会抛出异常
    public  static final String ACCESS_TOKEN_KEY = "ACCESS_TOKEN_KEY";

    public  static final String TODO_SERVICE_KEY = "TODO_SERVICE_KEY";
    public  static final String ASYNC_EXPORT_KEY = "ASYNC_EXPORT_KEY";
    public  static final String ASYNC_EXPORT = "ASYNC_EXPORT";
    public  static final String SPECIAL_CONVERTER = "SPECIAL_CONVERTER";
    @PostConstruct
    @Override
    public void register() throws IllegalCacheAccessException, IllegalAccessException {
        /**
         * 第一个参数是规范中约定的微服务的模块名称，如流程模块
         * 第二个参数是当前类名，用this.getClass()即可
         **/
        baseCache.register(CacheModuleKey.ACCESS_MODULE,this.getClass());
        baseCache.register(CacheModuleKey.TODO_SERVICE_MODULE,this.getClass());
        baseCache.register(CacheModuleKey.ASYNC_EXPORT_MODULE,this.getClass());
    }
}
