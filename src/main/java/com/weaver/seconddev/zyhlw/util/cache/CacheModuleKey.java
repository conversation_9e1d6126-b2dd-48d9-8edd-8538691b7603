package com.weaver.seconddev.zyhlw.util.cache;
/**
 * 用于拷贝定义所有的模块key，按照模块名规范表进行定义，便于开发引用
 * 要求变量名和变量值必须完全保持一致
 */
public class CacheModuleKey {

    private CacheModuleKey() {}
    public static final  String ACCESS_MODULE = "ACCESS_MODULE";
    /**
     * 待办数据
     */
    public static final  String TODO_SERVICE_MODULE = "TODO_SERVICE_MODULE";
    /**
     *
     */
    public static final  String ASYNC_EXPORT_MODULE = "ASYNC_EXPORT_MODULE";
}
