package com.weaver.seconddev.zyhlw.util.constant;

/**
 * 表单sql查询类型
 *
 * <AUTHOR>
 * @date 2025/5/6 15:24
 */
public interface FormSqlQueryType {
    /**
     * 模糊搜索
     */
    String QUERY_SQL_LIKE = "like";
    /**
     * 不等于（不包含空）
     */
    String QUERY_SQL_NOT = "not";
    /**
     * 不等于（包含空值）
     */
    String QUERY_SQL_NOT_AND_NULL = "not_and_null";
    /**
     * 多项查询
     */
    String QUERY_SQL_IN = "in";
    /**
     * 日期范围
     */
    String QUERY_SQL_DATE_RANGE = "date_range";
    /**
     * 为空的
     */
    String QUERY_SQL_IS_NULL = "null";
    /**
     * 不为空的
     */
    String QUERY_SQL_IS_NOT_NULL = "not_null";
    /**
     * 顺序排序
     */
    String QUERY_SQL_ORDER_ASC = "asc";
    /**
     * 倒序排序
     */
    String QUERY_SQL_ORDER_DESC = "desc";
    /**
     * 与
     */
    String QUERY_SQL_AND = "and";

    /**
     * 或
     */
    String QUERY_SQL_OR = "or";
}
