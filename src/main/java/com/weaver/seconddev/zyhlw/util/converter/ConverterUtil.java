package com.weaver.seconddev.zyhlw.util.converter;


import cn.hutool.core.map.MapUtil;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.impl.export.converter.Converter;
import com.weaver.seconddev.zyhlw.service.impl.export.converter.ConverterBase;
import com.weaver.seconddev.zyhlw.service.impl.export.dao.EcTableInfoDao;
import com.weaver.seconddev.zyhlw.service.impl.export.entity.EcColumnInfo;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025年04月28日 18:49
 */

@Component
public class ConverterUtil {

    @Resource
    private IDataSqlService dataSqlService;

    @Resource
    private EcTableInfoDao ecTableInfoDao;

    @Resource
    private ConverterBase converterBase;

    /**
     * 浏览框转换器
     *
     * @param tableName 表名称
     * @param fieldName 字段名称 明细加前缀dt 明细1 dt1_字段名称
     * @return 浏览框转换器Converter
     */
    public Converter getConverter(String tableName, String fieldName) {
        String getFormIdSql = String
                .format("select form_id as formId from form_table WHERE table_name = '%s'", tableName);
        Map<String, Object> data = dataSqlService.eBuilderFromSqlOne(getFormIdSql, SourceType.LOGIC);
        String formId = StringUtils.null2String(data.get("formId".toLowerCase()), "");

        Map<String, EcColumnInfo> ecColumnInfoMap = ecTableInfoDao.loadTableInfoByFormId(formId);
        EcColumnInfo ecColumnInfo = ecColumnInfoMap.get(fieldName);
        return converterBase.getConvert(ecColumnInfo);
    }

    /**
     * 浏览框转换器
     *
     * @param formId 表单Id
     * @return 浏览框转换器Converter
     */
    public Map<String, Converter> getConverter(String formId) {
        Map<String, EcColumnInfo> ecColumnInfoMap = ecTableInfoDao.loadTableInfoByFormId(formId);
        if (MapUtil.isEmpty(ecColumnInfoMap)) {
            return null;
        }

        // 使用Java 8兼容的方式处理，避免null值进入Collectors.toMap
        Map<String, Converter> result = new HashMap<>();

        ecColumnInfoMap.forEach((key, columnInfo) -> {
            if (columnInfo != null) {
                Converter converter = converterBase.getConvert(columnInfo);
                if (converter != null) {
                    result.put(key, converter);
                }
            }
        });

        return result;
    }
}
