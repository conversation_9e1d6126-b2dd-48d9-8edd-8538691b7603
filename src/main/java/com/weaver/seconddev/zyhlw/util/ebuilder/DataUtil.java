package com.weaver.seconddev.zyhlw.util.ebuilder;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import com.weaver.seconddev.zyhlw.service.impl.DataBaseServiceImpl;
import com.weaver.seconddev.zyhlw.util.ebuilder.table.ColumnInfo;
import com.weaver.seconddev.zyhlw.util.ebuilder.table.DetailFields;
import com.weaver.seconddev.zyhlw.util.ebuilder.table.MainFields;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DataUtil {


    private static final Logger logger = LoggerFactory.getLogger(EBuilderUtil.class);
    private static final String SIMPLE_NAME = DataUtil.class.getSimpleName();



    /**
     * 通过表单ID，获取表单字段集全信息，fieldId为字段ID，fieldKey为字段KEY
     * /api/ebuilder/form/dataset/v1/getFields
     *
     * @param userId         用户id
     * @param objId          表单 id
     * @param isReturnDetail 是否返回明细字段(true:返回，false,不返回）
     * @return 返回字段列表
     */
    public static ColumnInfo getColumnList(Long userId, Long objId, Boolean isReturnDetail,String accessToken) {

         String HOST = new DataBaseServiceImpl().getBaseDataValue("e10开放平台域名", "门户管理");

        String method = String.format("调用%s.getColumnList(%s,%s)-->", SIMPLE_NAME, userId, objId);

        if (StringUtils.isBlank(accessToken)) {
            logger.info("{}获取accessToken失败", method);

        }
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("access_token", accessToken);
        paramMap.put("userid", userId);
        paramMap.put("objId", objId);
        if (isReturnDetail) {
            paramMap.put("isReturnDetail", "y");
        } else {
            paramMap.put("isReturnDetail", "n");
        }
        String resJson = HttpRequest.post(HOST + "/api/ebuilder/form/dataset/v1/getFields").body(JSONObject.toJSONString(paramMap)).headerMap(null, true).execute().body();
        logger.info("{}请求返回：{}", method, resJson);

        JSONObject jsonObject = JSON.parseObject(resJson);
        JSONObject messageJson = jsonObject.getJSONObject("message");
        String code = messageJson.getString("code");
        ColumnInfo columnInfo = new ColumnInfo();
        List<MainFields> mainFieldsList = new ArrayList<>();
        List<DetailFields> detailFieldsList = new ArrayList<>();
        if ("0".equals(code)) {
            logger.info("获取表单字段信息失败，原因：" + messageJson.getString("msg"));
        } else {
            JSONObject dataJson = jsonObject.getJSONObject("data");
            if (dataJson != null) {
                JSONArray mainFieldsJson = dataJson.getJSONArray("mainFields");
                for (int i = 0; i < mainFieldsJson.size(); i++) {
                    MainFields mainFields = mainFieldsJson.getJSONObject(i).toJavaObject(MainFields.class);
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(mainFields.getFieldId())) {
                        mainFieldsList.add(mainFields);
                    }
                }

                JSONArray detailFieldsJson = dataJson.getJSONArray("detailFields");
                if (detailFieldsJson != null) {
                    for (int i = 0; i < detailFieldsJson.size(); i++) {
                        DetailFields detailFields = detailFieldsJson.getJSONObject(i).toJavaObject(DetailFields.class);
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(detailFields.getId())) {
                            detailFieldsList.add(detailFields);
                        }
                    }
                }

            }
        }
        columnInfo.setMainFieldsList(mainFieldsList);
        columnInfo.setDetailFields(detailFieldsList);
        logger.info("{}请求返回的数据集合：{}", method, JSONObject.toJSONString(columnInfo));
        return columnInfo;
    }

    /***
     * 获取主表字段集合
     * @param userId 用户id
     * @param objId  表单 id
     * @return
     */
    public static List<MainFields> getMainFieldsList(Long userId, Long objId,String accessToken)
    {
        List<MainFields> mainFieldsList=new ArrayList<>();
        ColumnInfo columnInfo= DataUtil.getColumnList(userId,objId,false,accessToken);
        if(columnInfo!=null)
        {
            mainFieldsList = columnInfo.getMainFieldsList();
        }
        return  mainFieldsList;
    }

    /***
     * 获取主表字段集合
     * @param userId 用户id
     * @param objId  表单 id
     * @return
     */
    public static List<DetailFields> getDetailFieldsList(Long userId, Long objId,String accessToken)
    {
        List<DetailFields> detailFieldsList=new ArrayList<>();
        ColumnInfo columnInfo= DataUtil.getColumnList(userId,objId,true,accessToken);
        if(columnInfo!=null)
        {
            detailFieldsList = columnInfo.getDetailFields();
        }
        return  detailFieldsList;
    }

    public static void main(String[] args) {

        String resJson="{\n" +
                "    \"message\": {\n" +
                "        \"code\": \"0\",\n" +
                "        \"msg\": \"success\"\n" +
                "    },\n" +
                "    \"data\": {\n" +
                "        \"mainFields\": [\n" +
                "            {\n" +
                "                \"id\": 1,\n" +
                "                \"fieldId\": \"name\",\n" +
                "                \"fieldName\": \"标题\",\n" +
                "                \"fieldType\": \"String\",\n" +
                "                \"number\": 0,\n" +
                "                \"multiSelect\": false,\n" +
                "                \"objId\": 0\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": 2,\n" +
                "                \"fieldId\": \"creator\",\n" +
                "                \"fieldName\": \"创建者\",\n" +
                "                \"fieldType\": \"Employee\",\n" +
                "                \"number\": 0,\n" +
                "                \"multiSelect\": false,\n" +
                "                \"objId\": 0\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": 3,\n" +
                "                \"fieldId\": \"create_time\",\n" +
                "                \"fieldName\": \"创建时间\",\n" +
                "                \"fieldType\": \"Date\",\n" +
                "                \"number\": 0,\n" +
                "                \"multiSelect\": false,\n" +
                "                \"objId\": 0\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": 4,\n" +
                "                \"fieldId\": \"update_time\",\n" +
                "                \"fieldName\": \"更新时间\",\n" +
                "                \"fieldType\": \"Date\",\n" +
                "                \"number\": 0,\n" +
                "                \"multiSelect\": false,\n" +
                "                \"objId\": 0\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": 5,\n" +
                "                \"fieldId\": \"id\",\n" +
                "                \"fieldName\": \"数据ID\",\n" +
                "                \"fieldType\": \"Number\",\n" +
                "                \"number\": 0,\n" +
                "                \"multiSelect\": false,\n" +
                "                \"objId\": 0\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": 8,\n" +
                "                \"fieldId\": \"data_status\",\n" +
                "                \"fieldName\": \"数据状态\",\n" +
                "                \"fieldType\": \"Select\",\n" +
                "                \"number\": 0,\n" +
                "                \"multiSelect\": false,\n" +
                "                \"objId\": 0\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": 9,\n" +
                "                \"fieldId\": \"flow_status\",\n" +
                "                \"fieldName\": \"流程状态\",\n" +
                "                \"fieldType\": \"Select\",\n" +
                "                \"number\": 0,\n" +
                "                \"multiSelect\": false,\n" +
                "                \"objId\": 0\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": 10,\n" +
                "                \"fieldId\": \"current_step\",\n" +
                "                \"fieldName\": \"当前阶段\",\n" +
                "                \"fieldType\": \"String\",\n" +
                "                \"number\": 0,\n" +
                "                \"multiSelect\": false,\n" +
                "                \"objId\": 0\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": 7833394005048344588,\n" +
                "                \"fieldId\": \"7833394005048344588\",\n" +
                "                \"fieldName\": \"文本输入框aaaaaa\",\n" +
                "                \"fieldType\": \"String\",\n" +
                "                \"fieldKey\": \"Text\",\n" +
                "                \"number\": 1,\n" +
                "                \"multiSelect\": false,\n" +
                "                \"objId\": 647423567994028033\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": 7833469261820864374,\n" +
                "                \"fieldId\": \"7833469261820864374\",\n" +
                "                \"fieldName\": \"添加人员\",\n" +
                "                \"fieldType\": \"Employee\",\n" +
                "                \"fieldKey\": \"Employee\",\n" +
                "                \"number\": 2,\n" +
                "                \"multiSelect\": true,\n" +
                "                \"objId\": 647423567994028033\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": 7833601545002899281,\n" +
                "                \"fieldId\": \"7833601545002899281\",\n" +
                "                \"fieldName\": \"关联e-builder\",\n" +
                "                \"fieldType\": \"String\",\n" +
                "                \"fieldKey\": \"Ebuilder\",\n" +
                "                \"number\": 3,\n" +
                "                \"multiSelect\": true,\n" +
                "                \"objId\": 647423567994028033\n" +
                "            }\n" +
                "        ],\n" +
                "        \"detailFields\": [\n" +
                "            {\n" +
                "                \"name\": \"明细表1\",\n" +
                "                \"id\": \"detail1\",\n" +
                "                \"fields\": [\n" +
                "                    {\n" +
                "                        \"fieldName\": \"文本输入框_明细1\",\n" +
                "                        \"tenant_key\": \"T2K74I3RJO\",\n" +
                "                        \"fieldKey\": \"Text\",\n" +
                "                        \"subFormIndex\": \"1\",\n" +
                "                        \"TENANT_KEY\": \"T2K74I3RJO\",\n" +
                "                        \"subFormId\": \"7834430276718965343\",\n" +
                "                        \"objId\": 647423567994028033,\n" +
                "                        \"fieldType\": \"string\",\n" +
                "                        \"subFormName\": \"明细表1\",\n" +
                "                        \"fieldId\": \"7834430276718965344\",\n" +
                "                        \"multiSelect\": \"false\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"fieldName\": \"添加人员_明细1\",\n" +
                "                        \"tenant_key\": \"T2K74I3RJO\",\n" +
                "                        \"fieldKey\": \"Employee\",\n" +
                "                        \"subFormIndex\": \"1\",\n" +
                "                        \"TENANT_KEY\": \"T2K74I3RJO\",\n" +
                "                        \"subFormId\": \"7834430276718965343\",\n" +
                "                        \"objId\": 647423567994028033,\n" +
                "                        \"fieldType\": \"employee\",\n" +
                "                        \"subFormName\": \"明细表1\",\n" +
                "                        \"fieldId\": \"7834430276718965345\",\n" +
                "                        \"multiSelect\": \"true\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            },\n" +
                "            {\n" +
                "                \"name\": \"明细表2\",\n" +
                "                \"id\": \"detail2\",\n" +
                "                \"fields\": [\n" +
                "                    {\n" +
                "                        \"fieldName\": \"文本输入框_明细2\",\n" +
                "                        \"tenant_key\": \"T2K74I3RJO\",\n" +
                "                        \"fieldKey\": \"Text\",\n" +
                "                        \"subFormIndex\": \"2\",\n" +
                "                        \"TENANT_KEY\": \"T2K74I3RJO\",\n" +
                "                        \"subFormId\": \"4764430633300370648\",\n" +
                "                        \"objId\": 647423567994028033,\n" +
                "                        \"fieldType\": \"string\",\n" +
                "                        \"subFormName\": \"明细表2\",\n" +
                "                        \"fieldId\": \"4764430633300370649\",\n" +
                "                        \"multiSelect\": \"false\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"fieldName\": \"添加人员_明细2\",\n" +
                "                        \"tenant_key\": \"T2K74I3RJO\",\n" +
                "                        \"fieldKey\": \"Employee\",\n" +
                "                        \"subFormIndex\": \"2\",\n" +
                "                        \"TENANT_KEY\": \"T2K74I3RJO\",\n" +
                "                        \"subFormId\": \"4764430633300370648\",\n" +
                "                        \"objId\": 647423567994028033,\n" +
                "                        \"fieldType\": \"employee\",\n" +
                "                        \"subFormName\": \"明细表2\",\n" +
                "                        \"fieldId\": \"4764430753619570732\",\n" +
                "                        \"multiSelect\": \"true\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "}";
        JSONObject jsonObject = JSON.parseObject(resJson);
        JSONObject messageJson = jsonObject.getJSONObject("message");
        String code = messageJson.getString("code");
        ColumnInfo columnInfo = new ColumnInfo();
        List<MainFields> mainFieldsList = new ArrayList<>();
        List<DetailFields> detailFieldsList = new ArrayList<>();
        if (!"0".equals(code)) {
            System.out.println("获取表单字段信息失败，原因：" + messageJson.getString("msg"));
        } else {
            JSONObject dataJson = jsonObject.getJSONObject("data");
            if (dataJson != null) {
                JSONArray mainFieldsJson = dataJson.getJSONArray("mainFields");
                for (int i = 0; i < mainFieldsJson.size(); i++) {
                    MainFields mainFields = mainFieldsJson.getJSONObject(i).toJavaObject(MainFields.class);
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(mainFields.getFieldId())) {
                        mainFieldsList.add(mainFields);
                    }
                }

                JSONArray detailFieldsJson = dataJson.getJSONArray("detailFields");
                if (detailFieldsJson != null) {
                    for (int i = 0; i < detailFieldsJson.size(); i++) {
                        DetailFields detailFields = detailFieldsJson.getJSONObject(i).toJavaObject(DetailFields.class);
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(detailFields.getId())) {
                            detailFieldsList.add(detailFields);
                        }
                    }
                }

            }
        }
        columnInfo.setMainFieldsList(mainFieldsList);
        columnInfo.setDetailFields(detailFieldsList);
        System.out.println("请求返回的数据集合："+JSONObject.toJSONString(columnInfo));
    }

}
