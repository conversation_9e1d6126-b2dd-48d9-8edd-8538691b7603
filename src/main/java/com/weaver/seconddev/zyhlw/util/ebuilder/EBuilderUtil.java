package com.weaver.seconddev.zyhlw.util.ebuilder;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import com.weaver.ebuilder.form.client.entity.obj.Obj;
import com.weaver.ebuilder.form.client.service.emobile.IEtFormDatasetService;
import com.weaver.openapi.pojo.eb.res.table.EbTableResultVo;
import com.weaver.openapi.pojo.eb.res.table.vo.Column;
import com.weaver.seconddev.zyhlw.domain.request.eb.EbFormDataReq;
import com.weaver.seconddev.zyhlw.domain.response.form.EbFormDataResultVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * <AUTHOR> by herry on 2024-12-24.
 * Update date:
 * Time: 11:52
 * Project: ecology
 * Package: com.zyhlw.e10
 * Command:
 * <p>
 * Status：Using online
 * <p>
 * Please note:
 * Must be checked once every time you submit a configuration file is correct!
 * Data is priceless! Accidentally deleted the consequences!
 */
public class EBuilderUtil {
//    private final static String HOST = DataBaseUtil.getInstance().getBaseDataValue("e10开放平台域名", "门户管理");
    private static final Logger logger = LoggerFactory.getLogger(EBuilderUtil.class);
    private static final String SIMPLE_NAME = EBuilderUtil.class.getSimpleName();

    /**
     * 根据dataKey保存表单数据 v2
     * /api/ebuilder/form/dataset/v2/saveFormData
     *
     * @param userId 用户id
     * @param objId  表单id
     * @param datas  主信息
     *               [{"mainTable"{"dxwb": "123123"}},"detail1":[{"dxwbdt1": "111"}]]
     * @return 返回字段列表
     */
    public static EbFormDataResultVo saveFormDataV2(String host, Long userId, String objId, List<Map<String, Object>> datas, String accessToken) {
        String method = String.format("调用%s.saveFormDataV2()-->", SIMPLE_NAME);

        JSONObject dataJson = new JSONObject();
        JSONObject header = new JSONObject();
        header.put("objId", objId);
        dataJson.put("header", header);
        JSONObject operationinfo = new JSONObject();
        operationinfo.put("fieldNoFindIgnore", "true");
        operationinfo.put("doAction", "false");
        dataJson.put("operationinfo", operationinfo);
        dataJson.put("datas", datas);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("access_token", accessToken);
        paramMap.put("datajson", dataJson);
        paramMap.put("userid", userId);
        String url = host + "/api/ebuilder/form/dataset/v2/saveFormData";
        logger.info("{}请求url：{}，请求参数：{}", method, url, JSONObject.toJSONString(paramMap));
        String resJson = HttpRequest.post(url).body(JSONObject.toJSONString(paramMap)).headerMap(null, true).execute().body();
        logger.info("{}请求响应：{}", method, resJson);
        EbFormDataResultVo ebFormDataResultVo = JSONObject.parseObject(resJson, EbFormDataResultVo.class);
        return ebFormDataResultVo;
    }

    /**
     * 根据dataKey更新表单数据 v2（ps：dataKey不是e10的数据库字段）
     * /api/ebuilder/form/dataset/v2/saveFormData
     *
     * @param ebFormDataReq 更新实体
     * @return 返回字段列表
     */
    public static EbFormDataResultVo updateFormDataV2(EbFormDataReq ebFormDataReq, String accessToken, String host) {

        String method = String.format("调用%s.updateFormDataV2(%s)-->", SIMPLE_NAME, ebFormDataReq.getUserId());
        JSONObject dataJson = new JSONObject();
        JSONObject objIdJson = new JSONObject();
        objIdJson.put("objId", ebFormDataReq.getObjId());
        dataJson.put("header", objIdJson);

        JSONObject operationinfo = new JSONObject();
        operationinfo.put("fieldNoFindIgnore", ebFormDataReq.getFieldNoFindIgnore());
        operationinfo.put("doAction", ebFormDataReq.getDoAction());
        operationinfo.put("operateType", ebFormDataReq.getOperateType());
        operationinfo.put("needAdd", ebFormDataReq.getNeedAdd());
        operationinfo.put("updateType", ebFormDataReq.getUpdateType());
        operationinfo.put("updateField", ebFormDataReq.getUpdateField());

        dataJson.put("operationinfo", operationinfo);
        dataJson.put("datas", ebFormDataReq.getDatas());
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("access_token", accessToken);
        paramMap.put("datajson", dataJson);
        paramMap.put("userid", ebFormDataReq.getUserId());
        String url = host + "/api/ebuilder/form/dataset/v2/updateFormData";
        logger.info("{}请求url：{}，请求参数：{}", method, url, JSONObject.toJSONString(paramMap));
        String resJson = HttpRequest.post(url).body(JSONObject.toJSONString(paramMap)).headerMap(null, true).execute().body();
        logger.info("{}请求响应：{}", method, resJson);
        return JSONObject.parseObject(resJson, EbFormDataResultVo.class);
    }


    /**
     * 获取表格显示字段集合，其中dataIndex为字段英文名称，fieldId为字段ID
     * /api/ebuilder/form/formcfg/list/v1/getListDatas
     *
     * @param userId 用户id
     * @param listId 表格id(视图id)
     * @return 返回字段列表
     */
    public static List<Column> getColumnListDatas(String host, Long userId, Long listId, String accessToken) {
        String method = String.format("调用%s.getColumnListDatas(%s)-->", SIMPLE_NAME, userId);
        List<Column> listColunm = new ArrayList<>();

        Map<String, Object> paramMap = new HashMap();
        paramMap.put("access_token", accessToken);
        paramMap.put("userid", userId);
        paramMap.put("listId", listId);
        String resJson = HttpRequest.post(host + "/api/ebuilder/form/formcfg/list/v1/getListDatas").body(JSONObject.toJSONString(paramMap)).headerMap(null, true).execute().body();
        logger.info("{}请求返回：{}", method, resJson);
        EbTableResultVo ebTableResultVo = JSONObject.parseObject(resJson, EbTableResultVo.class);
        if (!"0".equals(ebTableResultVo.getMessage().getCode())) {
            logger.info("获取的表格列信息失败，userId：{}，listId：{}，失败原因：{}", userId, listId, ebTableResultVo.getMessage().getMsg());
        } else {
            listColunm = ebTableResultVo.getData().getColumns();
        }

        return listColunm;
    }

    /**
     * 删除表单数据 v2
     * /api/ebuilder/form/dataset/v2/deleteData
     * @param objId 表单id
     * @param mainTable 删除条件(多个条件之间且的关系) 字段值, k-v结构, key: dataKey, value: dataValue
     * @param userid 用户id
     * @return 返回字段列表
     */
    public static EbFormDataResultVo deleteDataV2(String host, String objId, Map<String, Object> mainTable, String userid, String accessToken) {

        String method = String.format("调用%s.deleteDataV2(%s)-->", SIMPLE_NAME, userid);

        JSONObject dataJson = new JSONObject();
        JSONObject objIdJson = new JSONObject();
        objIdJson.put("objId", objId);
        dataJson.put("header", objIdJson);

        JSONObject operationinfo = new JSONObject();
        operationinfo.put("fieldNoFindIgnore", "false");

        dataJson.put("operationinfo", operationinfo);
        dataJson.put("mainTable", mainTable);
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("access_token", accessToken);
        paramMap.put("datajson", dataJson);
        paramMap.put("userid", userid);
        String resJson = HttpRequest.post(host + "/api/ebuilder/form/dataset/v2/deleteData").body(JSONObject.toJSONString(paramMap)).headerMap(null, true).execute().body();
        logger.info("{}请求返回：{}", method, resJson);
        return JSONObject.parseObject(resJson, EbFormDataResultVo.class);
    }

    /**
     * 获取数据详情 v2
     * /api/ebuilder/form/dataset/v2/getDataById
     *
     * @param objId 表单id
     * @param mainTable 删除条件(多个条件之间且的关系) 字段值, k-v结构, key: dataKey, value: dataValue
     * @param userid 用户id
     * @return 返回字段列表
     */
    public static EbFormDataResultVo getDataByIdV2(String host, String objId, Map<String, Object> mainTable, String userid, String accessToken) {

        String method = String.format("调用%s.getDataByIdV2(%s)-->", SIMPLE_NAME, userid);
        JSONObject dataJson = new JSONObject();
        JSONObject objIdJson = new JSONObject();
        objIdJson.put("objId", objId);
        dataJson.put("header", objIdJson);

        JSONObject operationinfo = new JSONObject();
        operationinfo.put("isReturnDetail", "y");
        operationinfo.put("fieldNoFindIgnore", "false");

        dataJson.put("operationinfo", operationinfo);
        dataJson.put("mainTable", mainTable);
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("access_token", accessToken);
        paramMap.put("datajson", dataJson);
        paramMap.put("userid", userid);
        logger.info("{}请求参数{}，url：{}", method, JSONObject.toJSONString(paramMap), host + "/api/ebuilder/form/dataset/v2/getDataById");
        String resJson = HttpRequest.post(host + "/api/ebuilder/form/dataset/v2/getDataById").body(JSONObject.toJSONString(paramMap)).headerMap(null, true).execute().body();
        logger.info("{}请求返回：{}", method, resJson);
        return JSONObject.parseObject(resJson, EbFormDataResultVo.class);
    }

    /**
     * getMenuConfigObjId
     * <p>
     * 获取基础数据表数据的value值，根据表单名称和表单类型名称进行查询 。
     * <p>
     * <AUTHOR>
     * @time 2025年01月23 11:09:13
     * @since 1.0
     * @param name 名称
     * @param typeName 类型
     * @param objId 基础数据表表单id，因环境不同，表单id不一样
     * @return null
     */
    public static String getBaseTableInfo(String host, String uid, String name, String typeName, String accessToken,String objId) {
        Map<String, Object> baseMainTable = new HashMap<>();
        baseMainTable.put("name_e10", name);
        baseMainTable.put("typename", typeName);
        EbFormDataResultVo ebFormDataResultVo = EBuilderUtil.getDataByIdV2(host, objId, baseMainTable, uid, accessToken);
        if (!ebFormDataResultVo.getMessage().getCode().equals("0")) {
            logger.info("查询{}基础数据{}", objId, JSONObject.toJSONString(ebFormDataResultVo));
            throw new RuntimeException("查询" + objId + "基础数据异常");
        }
        Object valueObj = ebFormDataResultVo.getData().getDataJson().get(0).getDatas().get(0).getMainTable().get("value");
        return JSONObject.parseObject(valueObj.toString()).getString("fieldValue");
    }


    public static String getFormObjId(IEtFormDatasetService iEtFormDatasetService, String openPlatformUrl, String uid, String token, String name, String typeName) {
        String baseObjId = getBaseDataObjId(iEtFormDatasetService);
        return getBaseTableInfo(openPlatformUrl, uid, name, typeName, token, baseObjId);
    }

    /**
     * getBaseDataObjId
     * <p>
     *
     * @return null
     * @description 获取基础数据表objId。
     * <p>
     * <AUTHOR>
     * @time 2025年02月05 11:10:20
     * @since 1.0
     */
    private static String getBaseDataObjId(IEtFormDatasetService iEtFormDatasetService) {
        String[] environments = {"100002780000012022", "110002780000013521"};
        for (String env : environments) {
            try {
                List<Obj> objList = iEtFormDatasetService.getTables(env);
                if (!objList.isEmpty()) {
                    Optional<Long> id = objList.stream()
                            .filter(record -> "uf_basedata".equals(record.getTableName()))
                            .map(Obj::getId)
                            .findFirst();
                    logger.info("getBaseDataObjId获取appId:{}成功，id:{}", env, id);
                    logger.info("...{}", id.map(Object::toString).orElse(""));
                    return id.map(Object::toString).orElse("");
                }
            } catch (Exception e) {
                logger.info("getBaseDataObjId获取appId:{}异常: {}，该无需处理", env, e.getMessage());
            }
        }
        return "";
    }
}
