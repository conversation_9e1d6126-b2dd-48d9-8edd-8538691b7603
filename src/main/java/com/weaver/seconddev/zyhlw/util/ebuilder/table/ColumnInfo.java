package com.weaver.seconddev.zyhlw.util.ebuilder.table;

import java.io.Serializable;
import java.util.List;

/**
 * @author: dengks on 20250117
 *
 */
public class ColumnInfo implements Serializable {

    /****
     * 主表字段
     */
    List<MainFields> mainFieldsList;

    /****
     * 明细表字段
     */
    List<DetailFields> detailFields;

    public List<DetailFields> getDetailFields() {
        return detailFields;
    }

    public void setDetailFields(List<DetailFields> detailFields) {
        this.detailFields = detailFields;
    }

    public List<MainFields> getMainFieldsList() {
        return mainFieldsList;
    }

    public void setMainFieldsList(List<MainFields> mainFieldsList) {
        this.mainFieldsList = mainFieldsList;
    }




}

