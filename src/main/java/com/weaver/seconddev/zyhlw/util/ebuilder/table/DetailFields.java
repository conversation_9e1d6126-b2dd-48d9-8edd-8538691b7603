package com.weaver.seconddev.zyhlw.util.ebuilder.table;

import java.io.Serializable;
import java.util.List;

public class DetailFields implements Serializable {

    /**
     * 明细表名称，比如：明细表1
     */
    String name;

    /**
     * 明细表ID，比如：detail1
     */
    String id;

    /**
     * 明细表字段集合
     */
    List<Fields> fields;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<Fields> getFields() {
        return fields;
    }

    public void setFields(List<Fields> fields) {
        this.fields = fields;
    }



}
