package com.weaver.seconddev.zyhlw.util.ebuilder.table;

import java.io.Serializable;

public class MainFields implements Serializable {

    /**
     * 字段ID
     */
    private String fieldId;

    /**
     * 字段名称
     */
    private String fieldName;
    /**
     * 字段类型
     */
    private String fieldType;

    /**
     * 字段主键
     */
    private String fieldKey;

    /**
     * 表单ID
     */
    private String objId;

    public String getObjId() {
        return objId;
    }

    public void setObjId(String objId) {
        this.objId = objId;
    }


    public String getFieldId() {
        return fieldId;
    }

    public void setFieldId(String fieldId) {
        this.fieldId = fieldId;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getFieldType() {
        return fieldType;
    }

    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }

    public String getFieldKey() {
        if (fieldKey == null) {
            //如果为空，则默认为字段ID
            return fieldId;
        } else {
            return fieldKey;
        }
    }

    public void setFieldKey(String fieldKey) {
        if(fieldKey == null)
        {
            this.fieldKey=fieldId;
        }
        else {
            this.fieldKey = fieldKey;
        }
    }


}
