package com.weaver.seconddev.zyhlw.util.encrypt;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2024-07-01 17:57
 */
public class MD5 {

    /**
     * 计算字符串的MD5哈希值
     * @param input 输入字符串
     * @return MD5哈希值的十六进制表示
     */
    public static String encrypt(String input) {
        try {
            // 创建MD5消息摘要对象
            MessageDigest md = MessageDigest.getInstance("MD5");

            // 计算MD5哈希值
            byte[] messageDigest = md.digest(input.getBytes());

            // 将字节数组转换为十六进制字符串
            BigInteger no = new BigInteger(1, messageDigest);
            StringBuilder hashText = new StringBuilder(no.toString(16));

            // 补足前导零，确保十六进制字符串长度为32
            while (hashText.length() < 32) {
                hashText.insert(0, "0");
            }

            return hashText.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5算法不存在", e);
        }
    }
}
