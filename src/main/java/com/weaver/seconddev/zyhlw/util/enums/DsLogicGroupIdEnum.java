package com.weaver.seconddev.zyhlw.util.enums;


import lombok.Getter;

/**
 * 数据源逻辑表分组id
 *
 * <AUTHOR>
 * @date 2025年04月27日 15:13
 */
@Getter
public enum DsLogicGroupIdEnum {

    /**
     * 业务表单
     */
    FORM("form", "weaver-formreport-service"),

    /**
     * 流程
     */
    WORKFLOW("workflow", "weaver-workflow-report-serviceworkflowreport"),

    /**
     * 任务
     */
    TASK("task", "weaver-project-servicetask"),

    /**
     * 日志
     */
    ELOG("elog", "weaver-elog-service"),

    /**
     * 会议
     */
    MEETING("meeting", "weaver-meeting-service"),

    /**
     * 日报
     */
    BLOG("blog", "weaver-blog-service"),

    /**
     * OKR
     */
    GOAL("goal", "weaver-wr-goal-service"),

    /**
     * 流程列表
     */
    WORKFLOW_LIST("workflow", "weaver-workflow-list-service"),

    /**
     * 考勤
     */
    ATTEND("attend", "weaver-attend-service"),

    /**
     * CRM
     */
    CRM("crm", "weaver-crm-service"),

    /**
     * 组织画像
     */
    PORTRAIT("portrait", "weaver-portrait-service"),

    /**
     * 工作流报告
     */
    WORKFLOW_REPORT("workflow", "weaver-workflow-report-serviceworkflow_report"),

    /**
     * 绩效
     */
    KPI("kpi", "weaver-wr-performance-service"),

    /**
     * 项目
     */
    PROJECT("task", "weaver-project-service"),

    /**
     * 基础定时模块
     */
    ESCHEDULER("escheduler", "weaver-basic-schedule-service"),

    /**
     * 文档
     */
    DOCUMENT("document", "weaver-doc-service"),

    /**
     * HRM
     */
    HRM("hrm", "weaver-hrm-service"),

    /**
     * e-builder报表
     */
    EDCREPORTD(null, "weaver-edcreportd-service"),

    /**
     * esb
     */
    ESB("esb", "weaver-esb-setting-service"),

    /**
     * e-builder应用
     */
    EBUILDER_APP("ebuilder", "weaver-ebuilder-app-service"),

    /**
     * e-builder表单
     */
    EBUILDER_FORM("ebuilder", "weaver-ebuilder-form-service"),

    /**
     * 客服
     */
    CUSTOMERSERVICE("customerservice", "weaver-customer-service-service"),

    /**
     * 日程
     */
    CALENDAR("calendar", "weaver-calendar-service"),

    /**
     * 文档
     */
    DOCUMENT_SERVICE("document", "weaver-doc-servicedocument"),

    /**
     * 文件
     */
    FILE("file", "weaver-file-service"),

    /**
     * 公文管理
     */
    ODOC("odoc", "weaver-odoc-service"),

    /**
     * SCRM
     */
    SCRM("scrm", "weaver-crm-market-service"),

    /**
     * 协作区
     */
    COWORK("cowork", "weaver-cowork-service"),

    /**
     * 计划报告
     */
    WORKREPORT("workreport", "weaver-wr-plan-service"),

    /**
     * 电子签
     */
    SIGNCONTRACT("signcontract", "weaver-signcenter-service"),

    /**
     * i18n
     */
    I18N_SERVICE(null, "weaver-i18n-service"),

    /**
     * 动作流监控
     */
    ESB_MONITOR("esb", "weaver-esb-setting-serviceesb"),

    /**
     * 表单加密
     */
    DATASECURITY("datasecurity", "weaver-datasecurity"),

    /**
     * 薪酬
     */
    SALARY_REPORT(null, "weaver-salary-report"),

    /**
     * 人事
     */
    HR_SERVICE(null, "weaver-hr-service"),

    /**
     * 动作流
     */
    ESB_CUSTOM("esb", "weaver-esb-setting-serviceesbCustom"),

    /**
     * 发票云
     */
    INC("inc", "weaver-inc-biz-service"),

    /**
     * 租户
     */
    TENANT(null, "weaver-tenant-service"),

    /**
     * 绩效核算
     */
    EBUILDER_CONTRACT("ebuilder", "weaver-ebuilder-contract-servicecmdatauFJXHS"),

    /**
     * 待办事项
     */
    MY_SERVICE(null, "weaver-my-service"),

    /**
     * 电子费控
     */
    FNA("fna", "weaver-fna-expense-service"),

    /**
     * 签名
     */
    SIGNATURE(null, "weaver-signature-service"),

    /**
     * 邮件
     */
    EMAIL("email", "weaver-mail-base-service"),

    /**
     * 薪酬
     */
    HRMSALARY("hrmsalary", "weaver-hrm-salary"),

    /**
     * 系统安全
     */
    SECURITY_FRAMEWORK(null, "weaver-security-framework-service"),

    /**
     * 公文交换中心
     */
    ODOCEXCHANGE("odocexchange", "weaver-odocexchange-service"),

    /**
     * 档案管理
     */
    ARCHIVE("archive", "weaver-archive-core-service"),

    /**
     * 招聘管理
     */
    RECRUIT("recruit", "weaver-recruit-service"),

    /**
     * 基础在线服务
     */
    BASIC_ONLINE_WEB(null, "weaver-basic-online-web-service"),

    /**
     * 微搜
     */
    ESEARCH_SEARCH(null, "weaver-esearch-search-service"),

    /**
     * 统一审批中心
     */
    INTUNIFYTODOS("intunifytodos", "weaver-intunifytodo-server-config-service"),

    /**
     * 统一待办推送
     */
    INTUNIFYTODOC("intunifytodoc", "weaver-intunifytodo-client-config-service"),

    /**
     * 公共数据源
     */
    COMMON("common", "weaver-component-web-service"),

    /**
     * 数据分析
     */
    ANALYZE("analyze", "weaver-analyze-service"),

    /**
     * 财务模块
     */
    FNA_EXPENSE("fna", "weaver-fna-expense-servicefexs"),

    /**
     * i18n
     */
    I18N_COMMON("i18n", "weaver-i18n-common-service"),

    /**
     * 登录服务
     */
    PASSPORT("passport", "weaver-passport-service"),

    /**
     * 工作流表单
     */
    WORKFLOW_FORM_REPORT("workflow", "weaver-workflow-report-serviceworkflowFormReport"),

    /**
     * 统一认证中心
     */
    INTUNIFYAUTH_SERVER(null, "weaver-intunifyauth-server-base-service");

    private final String serviceMark;
    private final String groupId;

    DsLogicGroupIdEnum(String serviceMark, String groupId) {
        this.serviceMark = serviceMark;
        this.groupId = groupId;
    }

}
