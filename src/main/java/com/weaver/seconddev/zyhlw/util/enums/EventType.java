package com.weaver.seconddev.zyhlw.util.enums;

/**
 * 流程钩子类型
 *
 * <AUTHOR>
 */

public enum EventType implements BaseEnum {

    /**
     * TURN_TO_DO_END：转办结束
     * CIRCULATE_END：传阅结束
     * COPY_END：抄送结束
     * AGENT_END：委托结束
     * CONSUL_TURN_TODO_END：征询转办结束
     * TAK_OUT_END：意见征询结束
     * FORWARD_END：转发结束
     * WITHDRAW_END：撤回结束
     * FORCED_RAWBACK_END：强制收回结束
     */
    TURN_TO_DO_END("TURN_TO_DO_END", "转办结束"),
    CIRCULATE_END("CIRCULATE_END", "传阅结束"),
    COPY_END("COPY_END", "抄送结束"),
    AGENT_END("AGENT_END", "委托结束"),
    CONSUL_TURN_TODO_END("CONSUL_TURN_TODO_END", "征询转办结束"),
    TAK_OUT_END("TAK_OUT_END", "意见征询结束"),
    FORWARD_END("FORWARD_END", "转发结束"),
    WITHDRAW_END("WITHDRAW_END", "撤回结束"),
    FORCED_RAWBACK_END("FORCED_RAWBACK_END", "强制收回结束"),

    ;

    private final String code;
    private final String msg;

    EventType(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }
}
