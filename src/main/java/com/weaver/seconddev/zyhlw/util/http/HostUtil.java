package com.weaver.seconddev.zyhlw.util.http;

import java.util.HashMap;
import java.util.Map;

public class HostUtil {
    public HostUtil() {
    }

    public static String beforeRequestCheckHeaders(String host, Map<String, String> header, String contentType) {
        if (header == null) {
            header = new HashMap();
        }

        ((Map)header).put("Content-Type", contentType);
        if (host == null) {
            return "";
        } else {
            if (host.endsWith("/")) {
                host = host.substring(0, host.lastIndexOf("/"));
            }

            return host;
        }
    }
}
