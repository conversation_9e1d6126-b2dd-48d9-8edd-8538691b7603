package com.weaver.seconddev.zyhlw.util.http;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLSession;

/**
 * 此类是用于主机名验证的基接口。
 * 在握手期间，如果 URL 的主机名和服务器的标识主机名不匹配，则验证机制可以回调此接口的实现程序来确定是否应该允许此连接。
 * 策略可以是基于证书的或依赖于其他验证方案。
 * 当验证URL主机名使用的默认规则失败时使用这些回调。
 */
public class MyHostnameVerifier implements HostnameVerifier {

    /**
     * 验证主机名和服务器验证方案的匹配是可接受的。
     *
     * @param urlHostName 主机名
     * @param session     到主机的连接上使用的 SSLSession
     * @return 如果主机名是可接受的，则返回true，否则返回false。
     */
    @Override
    public boolean verify(String urlHostName, SSLSession session) {
        if (urlHostName.equals(session.getPeerHost())) {
            return true;
        } else {
            return false;
        }
    }

}
