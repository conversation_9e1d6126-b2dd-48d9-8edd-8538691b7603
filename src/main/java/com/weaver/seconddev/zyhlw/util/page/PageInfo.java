package com.weaver.seconddev.zyhlw.util.page;

import java.util.List;

public class PageInfo<T> {
    private int currentPage;
    private int totalPages;
    private int pageSize;
    private List<T> data;
//    public PageInfo(int currentPage, int pageSize, List<T> data) {
//        this.currentPage = currentPage;
//        this.pageSize = pageSize;
//        this.data = data;
//    }
    public PageInfo(int currentPage,int totalPages, int pageSize, List<T> data) {
        this.currentPage = currentPage;
        this.totalPages = totalPages;
        this.pageSize = pageSize;
        this.data = data;
    }
    public int getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(int currentPage) {
        this.currentPage = currentPage;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }
}
