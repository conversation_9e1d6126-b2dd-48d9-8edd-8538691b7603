package com.weaver.seconddev.zyhlw.util.page;

import java.util.List;

public class PaginationUtil {
    public static <T> PageInfo<T> paginate(List<T> list, int page, int size) {
        if (list == null) {
            throw new IllegalArgumentException("List is null");
        }
        if (page < 1) {
            throw new IllegalArgumentException("Page index must be greater than 0");
        }
        if (size <= 0) {
            throw new IllegalArgumentException("Page size must be greater than 0");
        }

        int totalPages = (int) Math.ceil((double) list.size() / size);

        if (page > totalPages) {
            page = totalPages;
        }

        int fromIndex = (page - 1) * size;
        int toIndex = Math.min(fromIndex + size, list.size());

        List<T> pageData = list.subList(fromIndex, toIndex);

        return new PageInfo<>(page, totalPages, size, pageData);
    }
}
