package com.weaver.seconddev.zyhlw.util.park;

import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.seconddev.zyhlw.domain.request.eb.EbFormDataReq;
import com.weaver.seconddev.zyhlw.domain.response.form.EbFormDataResultVo;
import com.weaver.seconddev.zyhlw.domain.response.form.vo.DataJson;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import com.weaver.seconddev.zyhlw.util.ebuilder.EBuilderUtil;
import com.weaver.seconddev.zyhlw.util.enums.DsLogicGroupIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工位管理工具类
 *
 * <AUTHOR>
 * @date 2025年04月27日 17:50
 */
@Slf4j
@Component
public class WorkingSpaceUtil {

    @Resource
    private IDataSqlService dataSqlService;

    @Resource
    private CmicProperties cmicProperties;

    @Resource
    private IOpenPlatformService openPlatformService;

    private static final int BATCH_SIZE = 200;

    /**
     * 获取选择框选项值
     *
     * @param tableName 表名
     * @param field     字段名
     * @return 选项列表
     */
    public List<Map<String, Object>> getSelectBoxOptions(String tableName, String field) {
        String querySql = "SELECT" + //
                "  t1.value_key AS selectvalue," + //
                "  t1.name AS selectname" + //
                "  FROM" + //
                "  field_option t1" + //
                "  LEFT JOIN form_field t2 ON t1.field_id = t2.id" + //
                "  AND t1.delete_type = 0" + //
                "  AND t1.tenant_key = '" + cmicProperties.getHostTenantKey() + "'" + //
                "  AND t2.delete_type = 0" + //
                "  AND t2.tenant_key = '" + cmicProperties.getHostTenantKey() + "'" + //
                "  AND t1.delete_type = 0" + //
                "  AND t1.tenant_key = '" + cmicProperties.getHostTenantKey() + "'" + //
                "  AND t2.delete_type = 0" + //
                "  AND t2.tenant_key = '" + cmicProperties.getHostTenantKey() + "'" + //
                "  LEFT JOIN form_table t3 ON t2.form_id = t3.form_id" + //
                "  AND t3.delete_type = 0" + //
                "  AND t3.tenant_key = '" + cmicProperties.getHostTenantKey() + "'" + //
                "  WHERE" + //
                "  t2.data_key = ?" + //
                "  AND lower(t3.table_name) = lower(?)";

        List<Map<String, Object>> options = new ArrayList<>();
        try {
            List<String> params = new ArrayList<>();
            params.add(field);
            params.add(tableName);

            List<Map<String, Object>> results = dataSqlService.eBuilderFromSqlAll(querySql, SourceType.LOGIC, params);

            for (Map<String, Object> result : results) {
                Map<String, Object> option = new HashMap<>();
                option.put("selectvalue", StringUtils.null2String(result.get("selectvalue".toUpperCase())));
                option.put("selectname", StringUtils.null2String(result.get("selectname".toUpperCase())));
                options.add(option);
            }
        } catch (Exception e) {
            log.error("获取选择框选项异常：{}", e.getMessage(), e);
        }

        return options;
    }

    /**
     * 根据部门ID获取部门全称（包含父子部门关系）
     *
     * @param departmentId 部门ID
     * @return 部门全称
     */
    public String getDepartmentFullNameById(String departmentId) {
        if (StringUtils.isEmpty(departmentId)) {
            return "";
        }

        try {
            String sql = "SELECT parentdep.fullname AS pd, childdep.fullname AS cd " + //
                    "FROM eteams.department childdep " + //
                    "LEFT JOIN eteams.department parentdep ON childdep.parent = parentdep.id " + //
                    "AND childdep.type = 'department' " + //
                    "AND childdep.virtualid = 1 " + //
                    "AND childdep.delete_type = 0 " + //
                    "AND childdep.tenant_key = '" + cmicProperties.getHostTenantKey() + "' " + //
                    "AND parentdep.type = 'department' " + //
                    "AND parentdep.virtualid = 1 " + //
                    "AND parentdep.delete_type = 0 " + //
                    "AND parentdep.tenant_key = '" + cmicProperties.getHostTenantKey() + "'  " + //
                    "WHERE ltrim(rtrim(childdep.id)) = ? ";

            List<String> params = new ArrayList<>();
            params.add(departmentId.trim());

            List<Map<String, Object>> results = dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC, params);

            if (!results.isEmpty()) {
                Map<String, Object> result = results.get(0);
                String pd = StringUtils.null2String(result.get("pd".toUpperCase()));
                String cd = StringUtils.null2String(result.get("cd".toUpperCase()));

                if (StringUtils.isEmpty(pd)) {
                    return cd;
                } else {
                    return pd + "/" + cd;
                }
            }

            return "";
        } catch (Exception e) {
            log.error("获取部门全称异常：{}", e.getMessage(), e);
            return "";
        }
    }

    /**
     * 根据人员ID获取姓名（从用户表获取）
     *
     * @param id 人员ID
     * @return 姓名
     */
    public String getLastnameById2(String id) {
        if (StringUtils.isEmpty(id)) {
            return "";
        }

        try {
            String nameSql = "SELECT hrmresource.username AS lastname " + //
                    "FROM eteams.employee hrmresource " + //
                    "WHERE hrmresource.id = ? " + //
                    "AND hrmresource.delete_type = 0 " + //
                    "AND hrmresource.tenant_key = ?";

            List<String> params = new ArrayList<>();
            params.add(id.trim());
            params.add(cmicProperties.getHostTenantKey());

            List<Map<String, Object>> results = dataSqlService.eBuilderFromSqlAll(nameSql, SourceType.LOGIC, params);

            if (!results.isEmpty()) {
                return StringUtils.null2String(results.get(0).get("lastname".toUpperCase()));
            }

            return "";
        } catch (Exception e) {
            log.error("获取用户姓名异常：{}", e.getMessage(), e);
            return "";
        }
    }

    /**
     * 根据职务ID获取职务名称
     *
     * @param jobId 职务ID
     * @return 职务名称
     */
    public String getJobnameById(String jobId) {
        if (StringUtils.isEmpty(jobId)) {
            return "";
        }

        try {
            String querySql = "SELECT hrmjobtitles.full_name AS jobtitlename " + //
                    "FROM eteams.position hrmjobtitles " + //
                    "WHERE hrmjobtitles.id = ? " + //
                    "AND hrmjobtitles.delete_type = 0 " + //
                    "AND hrmjobtitles.tenant_key = ? ";

            List<String> params = new ArrayList<>();
            params.add(jobId);
            params.add(cmicProperties.getHostTenantKey());

            Map<String, Object> result = dataSqlService.executeCommonSqlOne(querySql,
                    SourceType.LOGIC, DsLogicGroupIdEnum.HRM.getGroupId(), params);

            return StringUtils.null2String(result.get("jobtitlename".toUpperCase()));
        } catch (Exception e) {
            log.error("获取职务名称异常：{}", e.getMessage(), e);
            return "";
        }
    }

    /**
     * 分批处理数据并调用API
     *
     * @param dataList      需要处理的数据列表
     * @param userId        用户ID
     * @param objId         表单对象ID
     * @param operationName 操作名称，用于日志记录
     * @return 处理结果，如果成功返回 null，否则返回错误信息
     */
    public String batchProcessData(List<Map<String, Object>> dataList, String userId,
            String objId, String operationName, List<String> mainTableUpdateFields) {
        if (CollectionUtils.isEmpty(dataList)) {
            log.info("{}数据为空，无需处理", operationName);
            return null;
        }

        int totalSize = dataList.size();
        int batchCount = (totalSize + BATCH_SIZE - 1) / BATCH_SIZE; // 向上取整

        log.info("开始分批{}，总数据量: {}, 分批数: {}", operationName, totalSize, batchCount);

        for (int i = 0; i < batchCount; i++) {
            int fromIndex = i * BATCH_SIZE;
            int toIndex = Math.min((i + 1) * BATCH_SIZE, totalSize);
            List<Map<String, Object>> batchDataList = dataList.subList(fromIndex, toIndex);

            log.info("{}，批次: {}/{}, 数据量: {}", operationName, (i + 1), batchCount, batchDataList.size());

            // 构建请求对象
            EbFormDataReq.Builder builder = new EbFormDataReq.Builder()
                    .userId(userId)
                    .objId(objId)
                    .datas(batchDataList);

            if (!CollectionUtils.isEmpty(mainTableUpdateFields)) {
                Map<String, Object> mainTableField = new HashMap<>(1);
                mainTableField.put("mainTable", mainTableUpdateFields);

                builder.updateType("updatePolicy")
                        .updateField(mainTableField);
            }

            EbFormDataReq req = builder.build();
            // 发送请求
            EbFormDataResultVo result = EBuilderUtil.updateFormDataV2(req,
                    openPlatformService.getAccessToken(),
                    cmicProperties.getOpenPlatformUrl());

            // 检查操作是否成功
            if (checkApiResult(result)) {
                return operationName + "失败，批次: " + (i + 1) + "/" + batchCount + "，错误：" + getApiErrorMessage(result);
            }
        }

        return null; // 处理成功
    }

    /**
     * 获取API调用错误信息
     *
     * @param result API调用结果
     * @return 错误信息
     */
    private String getApiErrorMessage(EbFormDataResultVo result) {
        if (result == null) {
            return "响应为空";
        }

        if (result.getMessage() == null) {
            return "响应消息为空";
        }

        if (!"0".equals(result.getMessage().getCode())) {
            return result.getMessage().getMsg();
        }

        if (result.getData() == null || result.getData().getDataJson() == null
                || result.getData().getDataJson().isEmpty()) {
            return "数据为空";
        }

        // 获取第一个失败的数据项的错误信息
        for (DataJson dataJson : result.getData().getDataJson()) {
            if (!dataJson.getStatus()) {
                return dataJson.getMessage();
            }
        }

        return "未知错误";
    }

    /**
     * 检查API调用结果是否成功
     *
     * @param result API调用结果
     * @return 是否成功
     */
    private boolean checkApiResult(EbFormDataResultVo result) {
        if (result == null || result.getMessage() == null) {
            return true;
        }

        // 检查消息状态码
        if (!"0".equals(result.getMessage().getCode())) {
            return true;
        }

        // 检查数据状态
        if (result.getData() == null || result.getData().getDataJson() == null
                || result.getData().getDataJson().isEmpty()) {
            return true;
        }

        // 检查每个数据项的状态
        for (DataJson dataJson : result.getData().getDataJson()) {
            if (!dataJson.getStatus()) {
                return true;
            }
        }

        return false;
    }
}
