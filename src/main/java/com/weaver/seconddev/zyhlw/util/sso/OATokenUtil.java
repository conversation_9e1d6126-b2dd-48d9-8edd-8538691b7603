package com.weaver.seconddev.zyhlw.util.sso;

import com.alibaba.fastjson.JSONObject;
import com.weaver.ebuilder.common.util.StringUtil;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.client.CookieStore;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.cookie.Cookie;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-02-12
 * @desc OA单点登录工具类
 */
@Slf4j
@Component
public class OATokenUtil {
    @Resource
    CmicProperties cmicProperties;


    @Resource
    IDataBaseService iDataBaseService;

    private final String simpleName = OATokenUtil.class.getSimpleName();

    /**
     * 设置到天工OA系统Cookie值和获取OA系统访问地址
     * @param userId
     * @param domainName
     * @param response
     * @return
     */
    public String setToOaIndexPageSSOCookieNew(String userId, String domainName, HttpServletResponse response) {
        // 1.准备参数
        String url = StringUtils.null2String(iDataBaseService.getBaseDataValue("SSO_TO_OA", "OA系统配置"), "http://oa2.cmic.chinamobile.com/sso.nsf/getSSOUrl?OpenAgent&sysaccount=portalsysuser&syspasswd=portalsyspass");

        Map<String, String> params = new HashMap<String, String>();
        params.put("userid", userId);
        long start = System.currentTimeMillis();
        String requestURL="";
        try {
            // 2.发送请求获取数据
            log.info("开始后台单点请求天工OA系统,userId:{}", userId);
            // 获取OA系统访问地址
            requestURL = doGetParam(url, params);
            log.info("获取到的requestURL：{}###userId:{}", requestURL, userId);
            DefaultHttpClient httpclient = new DefaultHttpClient();
            HttpGet httpGet = new HttpGet(requestURL);
            // 执行get请求
            httpclient.execute(httpGet);
            CookieStore cookieStore = httpclient.getCookieStore();
            if (cookieStore != null) {
                List<Cookie> cookies = cookieStore.getCookies();
                if (cookies != null) {
                    log.info("获取到的cookies个数：{}###userId:{}" + cookies.size() + userId);
                    javax.servlet.http.Cookie targetCookie = null;
                    for (org.apache.http.cookie.Cookie cookie : cookies) {
                        if (cookie != null) {
                            targetCookie = new javax.servlet.http.Cookie(cookie.getName(), cookie.getValue());
                            targetCookie.setPath("/");
                            targetCookie.setDomain(domainName);//cmic.chinamobile.com
                            targetCookie.setMaxAge(10740);
                            response.addCookie(targetCookie);
                            //	response.setHeader("Set-Cookie", String.format("%s=%s;Path=%s;Domain=%s;Max-Age=%d", targetCookie.getName()+"new", targetCookie.getValue(), targetCookie.getPath(), targetCookie.getDomain(), targetCookie.getMaxAge()));
                            //	response.setHeader("Set-Cookie", String.format("%s=%s;Path=%s;Domain=%s;Max-Age=%d", "testcookie", targetCookie.getValue(), targetCookie.getPath(), targetCookie.getDomain(), targetCookie.getMaxAge()));
                            //	response.setHeader("Set-Cookie", String.format("%s=%s;Path=%s;Domain=%s;Max-Age=%d; HttpOnly", "testcookiehttponly", targetCookie.getValue(), targetCookie.getPath(), targetCookie.getDomain(), targetCookie.getMaxAge()));
                            log.info("成功设置cookie，cookieName【" + cookie.getName() + "】-cookieValue【" + cookie.getValue() + "】");
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("访问天工系统获取单点认证Cookie失败异常,userId:{},失败原因：{}", userId, e);
        }
        log.info("后台单点请求天工OA系统结束,userId:{}，访问地址：{}，### 耗时:{}", userId,requestURL, (System.currentTimeMillis() - start));
        return requestURL;
    }

    /**
     * 发送一个带参数的get请求
     *
     * @param url    请求的地址
     * @param params 参数
     * @return 返回的内容
     * @throws Exception
     */
    public String doGetParam(String url, Map<String, String> params) throws Exception {
        log.info("开始调用doGetParam方法，url：{},传入参数：{}", url, params);
        // 1.创建Httpclient对象
        CloseableHttpClient httpclient = HttpClients.createDefault();
        // 2.定义请求的参数
//        URI uri = new URIBuilder(url).build();
//        // 如果带有参数
//        if (params != null && params.size() > 0) {
//            URIBuilder uriBuilder = new URIBuilder(url);
//            for (Map.Entry<String, String> entry : params.entrySet()) {
//                uriBuilder.addParameter(entry.getKey(), entry.getValue());
//            }
//            uri = uriBuilder.build();
//        }
        StringBuilder urlBuilder = new StringBuilder(url);

        for (Map.Entry<String, String> entry : params.entrySet()) {
            try {
                String key = URLEncoder.encode(entry.getKey(), "UTF-8");
                String value = URLEncoder.encode(entry.getValue(), "UTF-8");
                urlBuilder.append("&");
                urlBuilder.append(key).append("=").append(value);
            } catch (Exception e) {
                e.printStackTrace();
                log.info("开始调用doGetParam方法，异常原因：{}", e);
            }
        }

        log.info("开始调用doGetParam方法，组装后的url：{}", urlBuilder.toString());

        // 3.创建http GET请求
        HttpGet httpGet = new HttpGet(urlBuilder.toString());

        CloseableHttpResponse response = null;
        String result = "";
        try {
            // 4.执行请求
            response = httpclient.execute(httpGet);
            // 判断返回状态是否为200
            if (response.getStatusLine().getStatusCode() == 200) {
                String content = EntityUtils.toString(response.getEntity(), "UTF-8");
                result = content;
            }
            //log.info("开始调用doGetParam方法，返回结果response：{}", JSONObject.toJSONString(response));
            log.info("开始调用doGetParam方法，返回结果：{}", result);
        } catch (Exception ex) {
            log.error("调用doGetParam方法异常，原因：{}", ex);

        } finally {
            if (response != null) {
                response.close();
            }
            httpclient.close();
        }
        return result;
    }


    public static String doGetParam2(String url, Map<String, String> params) throws Exception {
        log.info("开始调用doGetParam方法，url：{},传入参数：{}", url, params);
        // 1.创建Httpclient对象
        CloseableHttpClient httpclient = HttpClients.createDefault();
        // 2.定义请求的参数
//        URI uri = new URIBuilder(url).build();
//        // 如果带有参数
//        if (params != null && params.size() > 0) {
//            URIBuilder uriBuilder = new URIBuilder(url);
//            for (Map.Entry<String, String> entry : params.entrySet()) {
//                uriBuilder.addParameter(entry.getKey(), entry.getValue());
//            }
//            uri = uriBuilder.build();
//        }
        StringBuilder urlBuilder = new StringBuilder(url);



        for (Map.Entry<String, String> entry : params.entrySet()) {
            try {
                String key = URLEncoder.encode(entry.getKey(), "UTF-8");
                String value = URLEncoder.encode(entry.getValue(), "UTF-8");
                urlBuilder.append("&");
                urlBuilder.append(key).append("=").append(value);
            } catch (Exception e) {
                e.printStackTrace();
                log.info("开始调用doGetParam方法，异常原因：{}", e);
            }
        }

        log.info("开始调用doGetParam方法，组装后的url：{}", urlBuilder.toString());

        // 3.创建http GET请求
        HttpGet httpGet = new HttpGet(urlBuilder.toString());
        Map<String, String> headers = new HashMap<>();
        headers.put("token","111");

        if (headers != null) {
            Header[] allHeader = new BasicHeader[headers.size()];
            int i = 0;
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                allHeader[i] = new BasicHeader(entry.getKey(), entry.getValue());
                i++;
            }
            httpGet.setHeaders(allHeader);
        }



        CloseableHttpResponse response = null;
        String result = "";
        try {
            // 4.执行请求
            response = httpclient.execute(httpGet);
            // 判断返回状态是否为200
            if (response.getStatusLine().getStatusCode() == 200) {
                String content = EntityUtils.toString(response.getEntity(), "UTF-8");
                result = content;
            }
           // log.info("开始调用doGetParam方法，返回结果response：{}", JSONObject.toJSONString(response));
            log.info("开始调用doGetParam方法，返回结果：{}", result);
        } catch (Exception ex) {
            log.error("调用doGetParam方法异常，原因：{}", ex);

        } finally {
            if (response != null) {
                response.close();
            }
            httpclient.close();
        }
        return result;
    }

    public static void main(String[] args) throws Exception {
        String url="http://106.13.9.156/sim-web/api/open/common/dict/list?1=1";
        Map<String, String> params = new HashMap<String, String>();
        params.put("categoryId","1d4a3b9b92634a1783beadaefd4fe01b");

       String tt=  doGetParam2(url,params);
        System.out.println("tt="+tt);


        String msg = String.format("getCheck校验失败，错误信息：%s", "111");
        System.out.println("msg="+msg);

    }


}
