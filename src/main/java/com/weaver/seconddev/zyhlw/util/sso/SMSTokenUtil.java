package com.weaver.seconddev.zyhlw.util.sso;


import com.alibaba.fastjson.JSONObject;
import com.weaver.seconddev.zyhlw.util.CmicUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Base64;

public class SMSTokenUtil {

    //private static Logger logger = LoggerFactory.getLogger(SMSTokenUtil.class);
    // 测试线：https://10.1.108.31:9143/cmic-portal-auth/token/validateToken
    // 正式线：https://10.1.108.36:9006/cmic-portal-auth/token/validateToken
    private static Logger logger = LoggerFactory.getLogger("cmic");
    private static final String SMS_VALIDATE_TOKEN_URL ="";

          //  StringUtil.vString(new BaseBean().getPropValue("Cmic","SMS_VALIDATE_TOKEN_URL"),"https://10.1.108.36:9006/cmic-portal-auth/token/validateToken");
    //应用程序账号
    private static String appId ="";
                  //StringUtil.vString(new BaseBean().getPropValue("Cmic", "appId"), "140");
    // 应用程序密钥
    private static String appSecret ="";
                          //StringUtil.vString(new BaseBean().getPropValue("Cmic", "appSecret"), "x1HZisCbln38ftog1RgdjkriLSzCOgdX6Dlvr1Ww");

    public static String getMobileOfValidateToken(String token){
        Base64.Decoder decoder = Base64.getDecoder();
        logger.info("[短信小程序Token校验-级别|通知] 请求参数 token:"+token);
        String result = smsTokenValidateForMobile(token,"smsApp",true);
        logger.info("[短信小程序Token校验-级别|通知] 短信小程序Token校验结果，接口返回："+result);
        try{
            JSONObject resultJson = JSONObject.parseObject(result);
            String resultCode = resultJson.getString("code");
            if ("0".equals(resultCode)) {
                String mobile = resultJson.getString("mobile");
                return new String(decoder.decode(mobile), "UTF-8");
            }
            logger.error("[短信小程序Token校验-级别|异常] 短信小程序Token校验失败，resultJson："+resultJson);
            return null;
        }catch (Exception e){
            logger.error("[短信小程序Token校验-级别|异常] 验证短信小程序的token出现异常。",e);
            return null;
        }
    }

    /**
     * 短信小程序token校验,直接返回验证后的手机号和Portal用户id
     * @param token  需要校验的token
     * @param validType 验证类型： 默认使用 smsApp 验证统一扫码token填写qrscan，短信小程序使用smsApp
     * @param isBase64 是否使用了base64加密  默认为 不使用
     * @return
     */
    public static String smsTokenValidateForMobile(String token, String validType, boolean isBase64) {
        //	请求参数
        String encodeType = "";
        if(isBase64){
            encodeType = "B64";
        }
        if(StringUtils.isEmpty(validType)){
            validType = "smsApp";
        }
        String param = "{\"accessToken\":\"" + token + "\",\"encodeType\":\"" + encodeType + "\",\"validType\":\""+validType+"\"}";
        logger.info("[smsTokenValidate]param:"+param);
        //	发送请求信息，并获取响应信息
        return CmicUtil.httpSenPost(SMS_VALIDATE_TOKEN_URL, appId, appSecret, "sso_validate_token", "", param, "application/json; charset=utf-8");
    }

}