package com.weaver.seconddev.zyhlw.util.sso;

import com.alibaba.fastjson.JSON;
import com.weaver.ebuilder.common.util.StringUtil;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.util.*;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

import static com.weaver.seconddev.zyhlw.util.CmicUtil.httpSenPost;

@Slf4j
@Component
public class SSOTokenUtil {

    @Resource
    CmicProperties cmicProperties;


    @Resource
    IDataBaseService iDataBaseService;

    private final String simpleName = SSOTokenUtil.class.getSimpleName();


    private String tickenKey = "ticken";
    private String SSO_CREATE_TOKEN = "sso_create_token";
    private String PORTAL_USER_ID = "portalUserId=";
    private String CONTENT_TYPE = "application/json; charset=utf-8";
    private String CONTENT_TYPE_NOT_CHARSET = "application/json";
    private String SSO_VALIDATE_TOKEN = "sso_validate_token";

    /**
     * 获取e10票据
     *
     * @param loginid
     * @param resultMap
     */
    public void getE10Token(String loginid, Map<String, Object> resultMap) {
        String methodName = String.format("调用%s.getE10Token(%s,%s)", simpleName, loginid, JSON.toJSONString(resultMap));
        log.info("{} 进入获取E10票据", methodName);
        try {
            // e10 token令牌免登
            String token = getE10Token(loginid);
            resultMap.put("code", 200);
            resultMap.put("msg", "获取token成功");
            // resultMap.put("token", token);
            resultMap.put("ticken", token);
        } catch (Exception var13) {
            log.error("{} 获取e10Token成功失败", methodName, var13);
            resultMap.put("code", 202);
            resultMap.put("ticken", "");
            resultMap.put("msg", "获取e10Token成功失败,原因：" + var13.getMessage());
            log.error("{} 获取e10Token成功失败,原因：", methodName);
        }
    }

    /**
     * 互联网公司getTicken2 接口
     *
     * @param loginid
     * @param resultMap
     * @param targetClientId
     * @param clientIdType
     */
    public void getTicken2(String loginid, Map<String, Object> resultMap, String targetClientId, String clientIdType) {
        String appId = cmicProperties.getAppid();
        String appSecret = cmicProperties.getAppSecret();

        String urlstr = StringUtil.vString(iDataBaseService.getBaseDataValue("互联网公司ticken2接口地址", "票据地址"), "http://auth.portal.cmic:30080/cmic-portal-auth/hr/getTicket2");
        String methodName = String.format("调用%s.getTicken2(%s,%s,%s,%s)", simpleName, loginid, JSON.toJSONString(resultMap), targetClientId, clientIdType);
        log.info("{} 进入获取ticket 新接口", methodName);
        if (loginid.equals("")) {
            resultMap.put("code", 202);
            resultMap.put("msg", "请输入loginid");
            resultMap.put("ticken", "");
            log.error("{} 参数loginid 为空", methodName);
            return;
        }

        if (targetClientId.equals("")) {
            resultMap.put("code", 202);
            resultMap.put("msg", "请输入targetClientId（目标系统ClientId）");
            resultMap.put("ticken", "");
            log.error("{} 参数targetClientId 为空", methodName);
            return;
        }

        if (clientIdType.equals("")) {
            resultMap.put("code", 202);
            resultMap.put("msg", "请输入clientIdType（OA或者MOA）");
            resultMap.put("ticken", "");
            log.error("{} 参数clientIdType 为空", methodName);
            return;
        }
        String ticket = "";

        String account = loginid + "@cmic.cmcc";
        String functionId = "getHRTicket";
        String clientType = "MOA";
        String source = null;
        JSONObject paramJSON = new JSONObject();
        boolean equals = clientIdType.equalsIgnoreCase("moa");
        if (equals) {
            paramJSON.put("moa", "MOA");
        }
        paramJSON.put("account", account);
        paramJSON.put("clientType", clientType);
        paramJSON.put("targetClientId", targetClientId);
        String contentType = "application/json";
        try {

            String json = httpSenPost(urlstr, appId, appSecret, functionId, source, paramJSON.toString(), contentType);
            log.error("{} 生成ticket：{}", methodName, json);
            JSONObject object = JSONObject.fromObject(json);
            ticket = object.getString("ticket");
            resultMap.put("code", 200);
            resultMap.put("msg", "获取ticken成功");
            resultMap.put(tickenKey, ticket);
        } catch (Exception e) {
            log.error("{} 获取集团人力ticket失败，原因：", methodName, e);
            resultMap.put("code", 202);
            resultMap.put("msg", "获取集团人力ticket失败，原因：" + e.getMessage());
            resultMap.put("ticken", "");
        }
    }

    /**
     * 安全网关获取票据接口
     *
     * @param appid
     * @param loginid
     * @param resultMap
     */
    public void getGateway(String appid, String loginid, Map<String, Object> resultMap) {
        //添加安全网关认证  on 20230518 by dengks
        String methodName = String.format("调用%s.getGateway(%s,%s,%s)", simpleName, appid, loginid, JSON.toJSONString(resultMap));
        log.info("调用{} 进入安全网关票据", methodName);
        String token = StringUtils.null2String(getSecurityGatewayTicket(loginid, appid));
        if (token.equals("")) {
            resultMap.put("code", 202);
            resultMap.put("msg", "获取安全网关票据为空");
            log.error("{} 获取安全网关票据异常", methodName);
            return;
        }
        resultMap.put("code", 200);
        resultMap.put("msg", "获取安全网关票据成功");
        resultMap.put(tickenKey, token);
    }


    /**
     * portalToken2 票据生成接口
     *
     * @param loginid
     * @param resultMap
     */
    public void getPortalToken2(String loginid, Map<String, Object> resultMap) {
        String methodName = String.format("调用%s.getPortalToken2(%s,%s)", simpleName, loginid, JSON.toJSONString(resultMap));
        log.info("{} 进入获取portalToken票据", methodName);
        String token = StringUtils.null2String(createUserToken(loginid));
        if (token.equals("")) {
            resultMap.put("code", 202);
            resultMap.put("msg", "获取portalToken为空");
            log.error("{} 获取portalToken失败", methodName);
            return;
        }
        resultMap.put("code", 200);
        resultMap.put("msg", "获取portalToken成功");
       // resultMap.put("data", token);

        //因返回的值为json格式数据，需要获取里面的accessToken的值
        String childToken = "";
        Map<String, Object> accessMap = JSONObject.fromObject(token);
        if (accessMap.size() >= 3) {
            childToken = StringUtils.null2String(accessMap.get("accessToken"));

        }
        resultMap.put(tickenKey, childToken);
    }

    /**
     * portalToken票据生成接口
     *
     * @param loginid
     * @param resultMap
     */
    public void getPortalToken(String loginid, Map<String, Object> resultMap) {
        String methodName = String.format("调用%s.getPortalToken(%s,%s)", simpleName, loginid, JSON.toJSONString(resultMap));
        log.info("{} 进入生成统一认证票据", methodName);
        String token = StringUtils.null2String(createCredentials(loginid));
        if (token.equals("")) {
            resultMap.put("code", 202);
            resultMap.put("msg", "获取portalToken为空");
            log.error("{} 获取统一认证失败", methodName);
            return;
        }
        resultMap.put("code", 200);
        resultMap.put("msg", "获取portalToken成功");
        resultMap.put(tickenKey, token);
    }

    public void getTicken(String loginid, Map<String, Object> resultMap) {
        String methodName = String.format("调用%s.getTicken(%s,%s)", simpleName, loginid, JSON.toJSONString(resultMap));
        log.info("{} 进入ticken", methodName);
        String ticken = StringUtils.null2String(getJtHrmTicket(loginid));
        log.info("{} 获取ticken：{}", methodName, ticken);
        if (ticken.equals("")) {
            resultMap.put("code", 202);
            resultMap.put("msg", "获取ticket为空");
            log.error("{} 获取ticket 为空", methodName);
            return;
        }
        resultMap.put("code", 200);
        resultMap.put("msg", "获取ticken成功");
        resultMap.put(tickenKey, ticken);
    }


    public String getSecurityGatewayTicket(String loginId, String targetAppId) {
        String appId = cmicProperties.getAppid();
        String appSecret = cmicProperties.getAppSecret();

        String ticket = "";
        String urlstr = StringUtil.vString(iDataBaseService.getBaseDataValue("安全网关Token接口地址", "票据地址"), "http://auth.portal.cmic:30080/cmic-portal-auth/hr/getTicket2");
        //https://10.1.108.31:9143/cmic-portal-auth/token/createToken
        String functionId = "sso_create_token";
        String source = null;
        com.alibaba.fastjson.JSONObject jsonObject = new com.alibaba.fastjson.JSONObject();
        jsonObject.put("portalUserId", loginId);
        jsonObject.put("validType", "gatewayApp2");
        jsonObject.put("targetAppId", targetAppId);
        String contentType = "application/json; charset=utf-8";
        try {

            String json = httpSenPost(urlstr, appId, appSecret, functionId, source, jsonObject.toString(), contentType);
            log.info("[统一认证票据生成-级别|通知] 获取安全网关Token响应数据 " + json);
            com.alibaba.fastjson.JSONObject object = com.alibaba.fastjson.JSONObject.parseObject(json);
            ticket = object.getString("accessToken");
        } catch (Exception e) {
            log.error("[统一认证票据生成-级别|异常] 获取安全网关Token失败，原因：", e);
        }
        return ticket;
    }

    /**
     * 创建用户Token
     *
     * @param loginId 用户登录名
     * @return 用户token
     */
    public String createUserToken(String loginId) {
        //	获取请求URL
        // String url = StringUtil.vString(log.getPropValue("Cmic", "createTokenUrl"), "https://10.1.108.31:9143/cmic-portal-auth/token/createToken");
        String appId = cmicProperties.getAppid();
        String appSecret = cmicProperties.getAppSecret();

        String url = StringUtil.vString(iDataBaseService.getBaseDataValue("互联网公司portalToken2接口地址", "票据地址"), "http://auth.portal.cmic:30080/cmic-portal-auth/token/createToken");

        //	发送请求信息，并获取响应信息
        return httpSenPost(url, appId, appSecret, SSO_CREATE_TOKEN, "", PORTAL_USER_ID + loginId, null);
    }


    /**
     * 验证Token
     *
     * @param token  Token
     * @param source 默认不填为1、pc端，移动端为2
     * @return 用户token
     */
    public String validateToken(String token, String source) {
        //	获取请求URL
        //  String url = StringUtil.vString(log.getPropValue("Cmic", VALIDATE_TOKEN_URL), VALIDATE_TOKEN_URL_PATH);
        String url = StringUtil.vString(iDataBaseService.getBaseDataValue("互联网公司portalToken2票据校验接口地址", "票据地址"), "http://auth.portal.cmic:30080/cmic-portal-auth/token/validateToken");
        String appId = cmicProperties.getAppid();
        String appSecret = cmicProperties.getAppSecret();

        //	发送请求信息，并获取响应信息
        return httpSenPost(url, appId, appSecret, SSO_VALIDATE_TOKEN, source, "accessToken=" + token, null);
    }


    /**
     * 统一认证-生成凭证
     *
     * @param loginId 用户登录名
     * @return 凭证
     */
    public String createCredentials(String loginId) {

        String credentials = "";
        //	访问方法
        String functionId = SSO_CREATE_TOKEN;
        //	时间戳
        String reqTimestamp = DateUtils.getCurrentTimestamp();
        //	URL路径
        // String url = StringUtil.vString(log.getPropValue("Cmic", CREDENTIALS_URL), "http://10.1.108.31:9000/CMIC_SERVICE/service/cmicTokenService?wsdl");
        String url = StringUtil.vString(iDataBaseService.getBaseDataValue("互联网公司portalToken接口地址", "票据地址"), "http://auth.portal.cmic:30080/CMIC_SERVICE/service/cmicTokenService?wsdl");

        try {

            ReturnBean returnBean = CmicUtil.XMLReader(CmicUtil.clientSendPost(url, CmicUtil.createRequestHead(cmicProperties.getAppid(), cmicProperties.getAppSecret(), reqTimestamp, functionId, loginId)));
            log.info("[统一认证票据生成-级别|通知] 生成凭证接口请求结果：" + JSON.toJSONString(returnBean));
            if (returnBean.getCode() == 0) {
                credentials = returnBean.getMsg();
            } else if (returnBean.getFaultcode() != null && !"".equals(returnBean.getFaultcode())) {
                log.info("生成凭证失败，原因：调用接口异常，异常代码：" + returnBean.getFaultcode() + "，异常原因：" + returnBean.getFaultstring());

            } else {
                log.info("生成凭证失败，原因：调用接口异常，异常代码：" + returnBean.getCode() + "，异常原因：" + returnBean.getMsg());

            }
        } catch (Exception e) {
            credentials = "";
            log.error("生成凭证失败，原因：" + e.getMessage());

        }
        return credentials;
    }


    /**
     * 获取集团人力ticket
     *
     * @return
     */
    public String getJtHrmTicket(String loginid) {

        String ticket = "";
        String appId = cmicProperties.getAppid();
        String appSecret = cmicProperties.getAppSecret();


        String url = StringUtil.vString(iDataBaseService.getBaseDataValue("获取集团人力ticket地址", "票据地址"), "http://auth.portal.cmic:30080/cmic-portal-auth/hr/getTicket");
        //https://10.1.108.31:9143/cmic-portal-auth/hr/getTicket
        String account = loginid + "@cmic.cmcc";
        String functionId = "getHRTicket";
        String clientType = "MOA";
        String moa = "MOA";
        String source = null;
        String param = "{\"account\":\"" + account + "\",\"clientType\":\"" + clientType + "\",\"moa\":\"" + moa + "\"}";
        String contentType = CONTENT_TYPE_NOT_CHARSET;

        try {
            String json = httpSenPost(url, appId, appSecret, functionId, source, param, contentType);

            JSONObject object = JSONObject.fromObject(json);
            ticket = object.getString("ticket");
        } catch (Exception e) {

            log.error("[统一认证票据生成-级别|异常] 获取集团人力ticket失败", e);

        }
        return ticket;
    }


    /**
     * 获取e10 免登 token
     * <p>
     * authType类型:
     * id: eteams人员ID;
     * JOB_NUM: eteams人员工号;
     * EMAIL：eteams人员邮箱
     * MOBILE：eteams人员手机号
     * loginID：eteams登录账号
     * idNos:   eteams人力身份证 
     * loginID：eteams登录名
     * <p>
     * envType: 环境类型
     *
     * @return
     */
    public String getE10Token(String loginid) {
        // 根据envType 获取单点基础配置数据
        String appId = cmicProperties.getAppid();
        String appSecret = cmicProperties.getAppSecret();
        String appKey = cmicProperties.getDefaultAppKey();
        String appSecurity = cmicProperties.getDefaultAppSecret();

        String url = StringUtil.vString(iDataBaseService.getBaseDataValue("获取e10单点token地址", "票据地址"), "http://10.17.25.21:20600/papi/openapi/oauth2/get_logintoken");
        String token = "";
        String authType = "loginID";
        url = url + "?app_key=" + appKey + "&app_security=" + appSecurity + "&account=" + loginid + "&authType=" + authType;
        String param = "";
        String contentType = "application/json";
        try {
            log.info("[e10Token生成-级别|通知] 获取e10Token 开始:");
            String json = httpSenPost(url, appId, appSecret, "", null, param, contentType);
            log.info("[e10Token生成-级别|通知] 获取e10Token 请求结果:" + json);
            JSONObject object = JSONObject.fromObject(json);
            if (object.getString("errcode").equals("0")) {
                token = object.getString("etLoginToken");
            }
        } catch (Exception e) {
            log.info("[e10Token生成-级别|通知] 获取e10Token 失败:" + e.getMessage());
        }
        return token;
    }

    /**
     * 安全网关验证token
     *
     * @param token
     * @param source
     * @param validType
     * @return
     */
    public String gatewayAppToken(String token, String source, String validType) {
        //获取请求URL
        String url = StringUtil.vString(iDataBaseService.getBaseDataValue("安全网关移动端Token接口地址", "票据地址"), "http://auth.portal.cmic:30080/cmic-portal-auth/token/validateToken");
        // String url = VALIDATE_TOKEN_URL_PATH;
        // 发送请求信息，并获取响应信息
        String appId = cmicProperties.getAppid();
        String appSecret = cmicProperties.getAppSecret();

        net.sf.json.JSONObject paramJSON = new net.sf.json.JSONObject();
        paramJSON.put("accessToken", token);
        paramJSON.put("validType", validType);
        log.info("请求参数：" + paramJSON);
        return httpSenPost(url, appId, appSecret, SSO_VALIDATE_TOKEN, source, paramJSON.toString(), CONTENT_TYPE);
    }


    /**
     * 通过认证方式值获取认证方式编码获
     *
     * @param typeValue
     * @return
     */
    public String getTokenType(String typeValue) {

        String tokenCode = "";
        if ("0".equals(typeValue)) {
            tokenCode = "portalToken";
        } else if ("1".equals(typeValue)) {
            tokenCode = "smap";
        } else if ("2".equals(typeValue)) {
            tokenCode = "smap";
        } else if ("3".equals(typeValue)) {
            tokenCode = "ticken";
        } else if ("4".equals(typeValue)) {
            tokenCode = "gateway";
        } else if ("5".equals(typeValue)) {
            tokenCode = "portalToken2";
        } else if ("6".equals(typeValue)) {
            tokenCode = "e10Token";
        } else if ("7".equals(typeValue)) {
            tokenCode = "e9Token";
        }
        return tokenCode;
    }


}
