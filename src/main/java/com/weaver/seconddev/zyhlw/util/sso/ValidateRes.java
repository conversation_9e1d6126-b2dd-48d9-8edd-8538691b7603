package com.weaver.seconddev.zyhlw.util.sso;

import java.io.Serializable;

public class ValidateRes implements Serializable {


    private static final long serialVersionUID = -8988117913798616443L;

    private Body body;
    private Header header;
    public Body getBody() {
        return body;
    }
    public void setBody(Body body) {
        this.body = body;
    }
    public Header getHeader() {
        return header;
    }
    public void setHeader(Header header) {
        this.header = header;
    }

    @Override
    public String toString() {
        return "ValidateRes [body=" + body + ", header=" + header + "]";
    }
}