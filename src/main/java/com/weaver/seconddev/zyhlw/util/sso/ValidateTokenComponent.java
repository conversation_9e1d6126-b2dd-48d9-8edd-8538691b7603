package com.weaver.seconddev.zyhlw.util.sso;


import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.StatusLine;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Slf4j
public class ValidateTokenComponent {


    private static String VERSION = "1.0";
    private static String SOURCE_ID = "001050";
    private static final String SOURCE_KEY = "DxaOG0O1Yn8smaSQ";
    public static final String APP_TYPE = "5";//移动端token校验
    public static final String SUCCESS_RESULT_CODE = "103000";//成功的返回码

//	public static final String url = "http://221.176.34.113:8080/demo/mobile/unitemptoken";//测试线

    //public static final String url = "https://token.cmpassport.com:8300/uniapi/uniTokenValidate";//生产线

    public static final String url = "http://10.1.58.213/mobile/unitemptoken";

    private static final String DES_KEY = "hHMogstx1gcJQLcu";


    private final static ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * @param token
     * @param apptype
     * @return
     */
    public static String getMobileOfValidateToken(String token, String apptype) {
        return SMSTokenUtil.getMobileOfValidateToken(token);
    }

    /**
     * 校验企业短信的token，返回手机号码
     * 2019年6月25日联调
     *
     * @param token
     * @param apptype 1|BOSS 2|web 3|wap 4|pc客户端 5|手机客户端
     *                生产环境：https://token.cmpassport.com:8300/uniapi/uniTokenValidate
     *                联调环境：http:// 112.13.96.207:10080/test/api/uniTokenValidate
     * @return
     */
    public static String getMobileOfValidateTokenTmp(String token, String apptype) {
        long startTime = System.currentTimeMillis();
        String mobile = "";//返回值
        // 1.发送请求获取结果
        Map<String, Object> resMap = new HashMap<>();
        try {
            // 2.组装请求的参数
            JSONObject header = new JSONObject();
            // header
            header.put("version", VERSION);
            String msgid = UUID.randomUUID().toString();
            header.put("msgid", msgid);
            String systemTime = getSystemTime();
            header.put("systemtime", systemTime);
            header.put("sourceid", SOURCE_ID);
            String idType = "0";//id类型：0：sourceid 1:appid。临时凭证校验时，idtype必须为0；
            header.put("idtype", idType);
            header.put("apptype", apptype);
            header.put("extparam", "300");
            String sign = Sign.getTokenSign(VERSION, SOURCE_ID, idType,
                    msgid, token, systemTime, SOURCE_KEY, apptype);

            header.put("sign", sign);
            // body
            JSONObject body = new JSONObject();
            body.put("token", token);

            JSONObject jsonObj = new JSONObject();
            jsonObj.put("header", header);
            jsonObj.put("body", body);
            // 3.打印日志
            String res = "";
            log.info("url=" + url);
            res = doPostParam(url, jsonObj.toString());
            log.info("请求地址：" + url + "---》请求的参数:"
                    + (jsonObj.toString()));
            log.info("请求uniTokenValidate,token=" + token + "|结果：" + res);
            resMap.put("res", res);
            if (StringUtils.isNotBlank(res)) {
                // 4.解析结果封装成对象
                OBJECT_MAPPER.configure(
                        DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,
                        false);
                ValidateRes vr = OBJECT_MAPPER.readValue(res, ValidateRes.class);
                //解密手机号码
                if (vr != null && StringUtils.isNotBlank(DES_KEY)) {
                    Body body1 = vr.getBody();
                    String msisdn = body1.getMsisdn();
                    if (vr.getHeader().getResultcode().equals(SUCCESS_RESULT_CODE) && StringUtils.isNotBlank(msisdn)) {
                        mobile = AES.deCodeAES(msisdn, DES_KEY);
                    }
                }
            }
        } catch (Exception e) {
            log.error("uniTokenValidate:" + e);
            e.printStackTrace();
        }
        log.info("根据token获取手机号码耗时："
                + (System.currentTimeMillis() - startTime) + "，token：" + token
                + "，mobile：" + mobile);
        return mobile;
    }


    /**
     * doPost请求
     *
     * @param urlStr   接口请求地址
     * @param jsonInfo 请求报文（JSON字符串）
     * @return JSON对象
     */
    public static String doPostParam(String urlStr, String jsonInfo) {
        HttpPost httpPost = new HttpPost(urlStr);
        httpPost.setHeader("Content-type", "application/json;charset=utf-8");
        CloseableHttpClient closeableHttpClient = HttpClients.createDefault();
        CloseableHttpResponse httpResponse = null;
        String responseContent = null;
        try {
            StringEntity stringEntity = new StringEntity(jsonInfo, "utf-8");
            httpPost.setEntity(stringEntity);
            RequestConfig requestConfig = setRequestConfig();
            httpPost.setConfig(requestConfig);
            httpResponse = closeableHttpClient.execute(httpPost);
            // 响应状态信息
            StatusLine statusLine = httpResponse.getStatusLine();
            HttpEntity entity = httpResponse.getEntity();
            int sendStatus = statusLine.getStatusCode();
            String getReasonPhrase = statusLine.getReasonPhrase();
            log.info(String.format("【httpPost】请求返回结果, code:%s, reason:%s",
                    sendStatus, getReasonPhrase));
            log.info(String.format("【httpPost】请求返回结果内容:%s", responseContent));
            if (200 == sendStatus) {
                responseContent = EntityUtils.toString(entity, "utf-8");
            }
        } catch (ConnectTimeoutException e) {
            log.error("--【error】httpPost请求发生【ConnectTimeoutException】异常 -->>:",
                    e);
        } catch (SocketTimeoutException e) {
            log.error(
                    "--【error】httpPost请求发生【SocketTimeoutException】异常 -->>:", e);
        } catch (IOException e) {
            log.error("--【error】httpPost请求发生【IOException】异常 -->>:", e);
        } finally {
            if (httpResponse != null) {
                try {
                    httpResponse.close();
                } catch (IOException e) {
                    log.error("--【error】httpPost请求发生【IOException】异常 -->>:",
                            e);
                }
            }
            // 关闭连接、释放资源
            try {
                closeableHttpClient.close();
            } catch (IOException e) {
                log.error("--【error】httpPost请求发生【IOException】异常 -->>:", e);
            }
        }
        return responseContent;
    }

    private static RequestConfig setRequestConfig() {
        // 时间单位：毫秒
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(10000).setConnectionRequestTimeout(10000)
                .setSocketTimeout(10000).build();
        return requestConfig;
    }

    /**
     * 生成当前系统的时间，格式为yyyyMMddHHmmssSSS
     * 用于统一认证验证token的接口(杭州研发中心的接口)
     *
     * @return 17位的系统当前时间
     */
    public static String getSystemTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        return sdf.format(new Date());
    }

    public static void main(String[] args) throws Exception {
        String token = "YZsidd9278fb29f8596f5bdbe1d7ee7ad5";
        //测试线地址
        String mobile = getMobileOfValidateTokenTmp(token, "2");
        System.out.println("mobile：" + mobile);
    }
}
