package com.weaver.seconddev.zyhlw.util.workflow;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.openapi.pojo.MessageResult;
import com.weaver.openapi.pojo.flow.params.FlowVo;
import com.weaver.openapi.pojo.flow.params.vo.FlowFormData;
import com.weaver.openapi.pojo.flow.res.CreateWorkFlowListResultVo;
import com.weaver.openapi.pojo.flow.res.FlowListResultVo;
import com.weaver.openapi.pojo.flow.res.vo.CreateWorkFlow;
import com.weaver.openapi.pojo.flow.res.vo.FlowList;
import com.weaver.openapi.service.FlowService;
import com.weaver.seconddev.zyhlw.util.ebuilder.EBuilderUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <h1>流程相关api类</h1>
 *
 * <p>Description: </p>
 *
 * <p>Note: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
public class WfcUtil {
    private static final String SIMPLE_NAME = WfcUtil.class.getSimpleName();

    /**
     * getMyWorkflowRequestAllList
     * <p>
     *
     * @param accessToken accessToken
     * @param userid      userid
     * @param host        host
     * @return List<FlowList>
     * @description 获取我发起的流程（全部）。
     * <p>
     * <AUTHOR>
     * @time 2025年02月14 10:28:25
     * @since 1.0
     */
    public static List<FlowList> getMyWorkflowRequestAllList(String accessToken, Long userid, String host) {
        String method = String.format("调用%s.getMyWorkflowRequestAllList-->", SIMPLE_NAME);
        log.info("{}开始执行", method);
        List<FlowList> dataResult = new ArrayList<>();
        FlowVo flowVo = new FlowVo();
        flowVo.setAccessToken(accessToken);
        flowVo.setUserid(userid);
        int pageSize = 100;
        int pageNumber = 1;
        while (true) {
            try {
                flowVo.setPageNo(pageNumber);
                flowVo.setPageSize(pageSize);
                FlowListResultVo resultVo = FlowService.getMyWorkflowRequestListV1(flowVo, host, null);
                if (resultVo.getMessage().getErrcode() != 0) {
                    log.info("{}当前页：{}，返回结果异常：{}", method, pageNumber, resultVo.getMessage().getErrmsg());
                    break;
                }
                log.info("{}当前页：{}，返回结果：{}", method, pageNumber, JSON.toJSONString(resultVo));

                dataResult.addAll(resultVo.getData());
                if (resultVo.getData().size() < pageSize) {
                    break;
                }
                pageNumber++;
            } catch (Exception e) {
                log.info("{}当前页：{}，发生异常: {}", method, pageNumber, e);
                throw new RuntimeException(e);
            }
        }
        return dataResult;
    }

    /**
     * getCreateWorkflowList
     * <p>
     *
     * @param accessToken    accessToken
     * @param userid         userid
     * @param host           host
     * @param wfpCode        0个人审批，1团队审批
     * @param flowStatusList 流程流转的状态;0-草稿,1-审批中,2-已退回,3-正常归档,4-强制结束,5-已作废,6-待提交,7-暂停,8-撤销,-99-已删除(例："flowStatusList":[0,1,2,3,4,5] )
     * @param filterHide     是否过滤“新建流程默认隐藏”设置，为1时过滤
     * @return List<FlowList>
     * @description 用户前台可发起的工作流程列表。
     * <p>
     * <AUTHOR>
     * @time 2025年02月14 10:28:25
     * @since 1.0
     */
    public static List<CreateWorkFlow> getCreateWorkflowList(String accessToken, Long userid, String host, int wfpCode, List<Integer> flowStatusList, String filterHide) {
        String method = String.format("调用%s.getCreateWorkflowList-->", SIMPLE_NAME);
        log.info("{}开始执行", method);
        FlowVo flowVo = new FlowVo();
        flowVo.setAccessToken(accessToken);
        flowVo.setUserid(userid);
        flowVo.setWfpCode(wfpCode);
        if (!flowStatusList.isEmpty()) {
            flowVo.setFlowStatusList(flowStatusList);
        }
        if (StringUtils.isNotBlank(filterHide)) {
            flowVo.setFilterHide(filterHide);
        }
        try {
            log.info("{}请求参数：{}", method, JSON.toJSONString(flowVo));
            CreateWorkFlowListResultVo resultVo = FlowService.getCreateWorkflowListV1(flowVo, host, null);
            log.info("{}返回结果：{}", method, JSON.toJSONString(resultVo));
            return resultVo.getData();
        } catch (Exception e) {
            log.info("{}发生异常: {}", method, e);
            throw new RuntimeException(e);
        }
    }


    public static MessageResult saveFormData(String accessToken, Long userId, String requestId, String workflowId, FlowFormData formData, String host) {
        String method = String.format("调用%s.saveFormData-->", SIMPLE_NAME);
        log.info("{}开始执行", method);
        FlowVo flowVo = new FlowVo();
        flowVo.setAccessToken(accessToken);
        flowVo.setUserid(userId);
        flowVo.setRequestId(requestId);
        flowVo.setWorkflowId(workflowId);
        flowVo.setFlowFormData(formData);

        try {
            log.info("{}请求参数：{}", method, JSON.toJSONString(flowVo));
            MessageResult messageResult = FlowService.saveFormData(flowVo, host, null);
            log.info("{}返回结果：{}", method, JSON.toJSONString(messageResult));
            return messageResult;
        } catch (Exception e) {
            log.info("{}发生异常: {}", method, e);
            throw new RuntimeException(e);
        }
    }
}
